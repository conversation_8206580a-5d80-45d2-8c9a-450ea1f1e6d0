export default {
  route: {
    dashboard: '首页',
    documentation: '文档',
    guide: '引导页',
    permission: '权限测试页',
    rolePermission: '角色权限',
    pagePermission: '页面权限',
    directivePermission: '指令权限',
    icons: '图标',
    components: '组件',
    tinymce: '富文本编辑器',
    markdown: 'Markdown',
    jsonEditor: 'JSON 编辑器',
    dndList: '列表拖拽',
    splitPane: 'Splitpane',
    avatarUpload: '头像上传',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: '小组件',
    backToTop: '返回顶部',
    dragDialog: '拖拽 Dialog',
    dragSelect: '拖拽 Select',
    dragKanban: '可拖拽看板',
    charts: '图表',
    keyboardChart: '键盘图表',
    lineChart: '折线图',
    mixChart: '混合图表',
    example: '综合实例',
    nested: '路由嵌套',
    menu1: '菜单1',
    'menu1-1': '菜单 1-1',
    'menu1-2': '菜单 1-2',
    'menu1-2-1': '菜单 1-2-1',
    'menu1-2-2': '菜单 1-2-2',
    'menu1-3': '菜单 1-3',
    menu2: '菜单 2',
    Table: 'Table',
    dynamicTable: '动态 Table',
    dragTable: '拖拽 Table',
    inlineEditTable: 'Table 内编辑',
    complexTable: '综合 Table',
    apiDescription: 'Api 业务描述',
    tab: 'Tab',
    form: '表单',
    createArticle: '创建文章',
    editArticle: '编辑文章',
    articleList: '文章列表',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    errorLog: '错误日志',
    excel: 'Excel',
    exportExcel: '导出 Excel',
    selectExcel: '导出 已选择项',
    mergeHeader: '导出 多级表头',
    uploadExcel: '上传 Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: '换肤',
    clipboardDemo: 'Clipboard',
    i18n: '国际化',
    externalLink: '外链',
    profile: '个人中心',
    devroot: '开发便捷菜单',
    vueicons: '系统支持ICON一览',
    Sys: {
      Sys: '系统管理',
      Sys_User: '用户管理',
      Sys_DbBackup: '数据库备份',
      Sys_Log: '操作日志',
      Sys_Mail: '邮件管理',
      Sys_MailServerConfig: '邮件配置',
      Sys_UserMessage: '消息通知',
      Sys_MessageNotifySetting: '通知设置',
      Sys_Organization: '组织机构',
      Sys_Resource: '菜单管理',
      Sys_Role: '角色管理',
      Sys_SwithConfig: '开关设置',
      Sys_Permission: {
        Add: '添加',
        Edit: '编辑',
        Delete: '删除',
        Export: '导出',
        SetRole: '设置角色',
        ResetPassword: '重置密码',
        Permission: '设置权限',
        Enable: '启用',
        Disable: '停用',
        Backup: '备份',
        Reduction: '还原',
        Set: '设置'
      },
      Sys_UserSapAccount: 'SAP账号'
    },
    MD: {
      MD: '基础设置',
      MD_ReportStation: '报工站点',
      MD_WorkCenterStation: '工作中心站点',
      MD_ProduceLine: '线体对照管理',
      MD_CustomerProduce: '客户生产序号管理',
      MD_CustomerModelDeliveryAdvance: '客户机型交期提前管理',
      MD_LineBatch: '线体批次管理',
      MD_BinLocation: '库位设置',
      MD_Dictionary: '数据字典',
      MD_Region: '库区设置',
      MD_Warehouse: '仓库主数据',
      MD_CustomerAdd: '客户地址设置',
      MD_FreightMileage: '运费里程设置',
      MD_CustomerWeightRateA: 'A重量里程-重量费率',
      MD_CustomerDistanceRateA: 'A重量里程-里程费率',
      MD_CustomerWeightPriceB: 'B重量价格设置',
      MD_CustomerWeightRateC: 'C重量费率设置',
      MD_ProductionDistributionSettingController: '配送任务设置',
      MD_ProcessInspection: '生产过程检验',
      MD_PartMakeCompany: '安全部件制造单位编号',
      MD_PartProduceLine: '安全部件生产线对照',
      MD_ProduceLineCapacity: '生产线产能',
      MD_BasicSpecification: '基础规格',
      MD_NonStandardConfig: '非标配置',
      MD_ReportStationMaterial: '报工站点物料',
      MD_ShippingMarkRule: '唛头打印规则',
      MD_PrintTemplate: '打印模板管理',
      MD_PrintTemplateEdit: '打印模板编辑器',
      MD_PrintTemplateParameter: '打印模板参数管理',
      MD_Permission: {
        Add: '添加',
        Edit: '编辑',
        Delete: '删除',
        Export: '导出',
        DetailAdd: '添加明细',
        DetailEdit: '编辑明细',
        DetailDelete: '删除明细',
        MasterEdit: '维护等级',
        Enable: '启用',
        Disable: '停用',
        SyncProductCategory: '同步产品类别数据',
        SyncMaterial: '同步物料数据',
        SyncSupplier: '同步供应商数据',
        SyncCustomer: '同步客户数据',
        Import: '导入模板',
        DownLoadTemp: '下载模板',
        DownLoad: '下载模板'
      },
      MD_Item: '仓库管理方式设置',
      MD_Supplier: '供应商主数据',
      MD_Customer: '客户主数据',
      MD_SalePacking: '客户物料包装规格',
      MD_SalePackingDetailed: '客户物料包装规格管理',
      MD_LabelTemplate: '标签模板设置',
      MD_POInspectionPlan: '检验计划',
      MD_POInspectionPlanDetailed: '编辑检验计划',
      MD_POInspectionGrade: '检验等级',
      MD_BinLimit: '库位限制'
    },
    PO: {
      PO: '采购管理',
      PO_BarCode: '采购标签', //  已删除
      PO_BarCodeDetailed: '采购标签明细', //  已删除
      PO_DeliveryPlan: '采购交货计划', //  已删除
      PO_DeliveryPlanDetailed: '采购交货计划明细', //  已删除
      PO_DeliveryNote: '采购送货单', //  已删除
      PO_DeliveryNoteDetailed: '采购送货单明细', //  已删除
      PO_Inspection: '采购质检查询', //  已删除
      PO_ShelfScan: '合格品入库管理', //  已删除
      PO_ConsignIn: '寄售采购入库管理', //  已删除
      PO_ConsignInEdit: '寄售采购入库编辑', //  已删除
      PO_DisShelfScan: '不合格品入库管理', //  已删除
      PO_ITransferScan: '退供封存区移动记录', //  已删除
      SAP_PO_PurchaseOrder: '采购订单管理', //  已删除
      PO_ReturnScan: '采购退货',
      PO_ReturnScanDetail: '采购退货明细管理',
      PO_PurchaseReceipt: '采购入库',
      PO_Permission: {
        Add: '添加',
        Edit: '编辑',
        Delete: '删除',
        Assign: '下发',
        Affirm: '确认',
        Merge: '合并',
        Export: '导出',
        Print: '打印',
        CreateNote: '生成送货单',
        Posting: '过账',
        successf: '完成',
        PassPost: '冲销'
      }
    },
    SD: {
      SD: '销售管理',
      SD_DeliveryWave: '销售波次任务单管理', // 已删除
      SD_DeliveryWaveDetailed: '销售波次任务单明细管理', // 已删除
      SD_StockingScan: '销售备货单管理', // 已删除
      SD_Packing: '销售装箱单管理', // 已删除
      SD_PackingDetailed: '销售装箱单明细', // 已删除
      SD_BarCodeCustomer: '客户标签管理', // 已删除
      SD_BarCodeCustomerDetailed: '客户标签明细管理', // 已删除
      SD_BarCodeReturn: '销售退货标签管理', // 已删除
      SD_BarCodeReturnDetailed: '销售退货标签明细管理', // 已删除
      SAP_SD_SaleOrder: '销售订单管理', // 已删除
      SD_SalesDelivery: '销售交货', // 已删除
      SD_DeliveryScan: '销售发货单管理',
      SD_ReturnScan: '销售退货',
      SD_ConsignmentNote: '托运单',
      SD_ConsignmentNoteDetail: '托运单明细管理',
      SD_ShippingPlan: '发运计划',
      SD_ShippingPlanDetail: '发运计划明细管理',
      SD_Permission: {
        Add: '添加',
        Edit: '编辑',
        Delete: '删除',
        Export: '导出',
        Print: '打印',
        Posting: '过账',
        Packing: '生成装箱单',
        Person: '指定责任人',
        Successf: '完成',
        Affirmdy: '确认发货',
        ConsignmentNote: '生成托运单',
        UploadSRM: '上传SRM',
        Import: '上传',
        DownLoadTemp: '下载模板',
        PassPost: '冲销'
      }
    },
    MM: {
      MM: '仓库管理',
      MM_BarCode: '物料标签打印', // 已删除
      MM_BarCodeEdit: '制作物料标签', // 已删除
      MM_InScan: '其他收货记录', // 已删除
      MM_InScanEdit: '其他收货编辑', // 已删除
      MM_OutScan: '其他发货记录', // 已删除
      MM_OutScanEdit: '其他发货编辑', // 已删除
      MM_TransferScan: '物料移动', // 已删除
      MM_TakeStockPlan: '盘点计划单', // 已删除
      MM_BarCodeBox: '包装箱码管理', // 已删除
      MM_TakeStockPlanDetailed: '盘点计划明细管理',
      MM_TakeStockScan: '盘点明细管理',
      MM_TakeStockDiff: '盘点差异确认',
      MM_PInventoryList: '盘点清单',
      MM_InventoryResults: '盘点结果',
      MM_EquipmentPicking: '设备领料',
      MM_EquipmentPickingDetail: '设备领料明细管理',
      MM_SubcontractingApplication: '委外领料',
      MM_SubcontractingApplicationDetail: '委外领料明细',
      MM_OutsourcingDispatch: '委外发料',
      MM_OutsourcingReturn: '委外退料',
      MM_OutsourcingReturnDetail: '委外退料明细',
      MM_OutsourcingWarehousing: '委外入库',
      MM_MagneticMaterialOutsourcingWarehousing: '磁材委外入库',
      MM_LendingOrder: '借出单',
      MM_LendingOrderDetailed: '借出单明细管理',
      MM_LendingOrderReturn: '归还单',
      MM_LendingOrderReturnDetailed: '归还单明细管理',
      MM_DepartmentPickingApplication: '部门领退料',
      MM_DepartmentPickingApplicationDetail: '部门领退料明细管理',
      MM_RedeploymentApplication: '调拨申请',
      MM_ProductionMaterialDeliveryOrder: '生产物料配送单',
      MM_ScrapList: '报废单',
      MM_WasteStorage: '废料入库',
      MM_WasteSalesOutOfWwarehouse: '废料销售出库',
      MM_RedeployApply: '调拨申请',
      MM_RedeployApplyDetail: '调拨申请明细管理',
      MM_SupplierRepair: '供应商返修记录',
      MM_SupplierRepairDetailed: '供应商返修记录明细管理',
      MM_OtherIn: '其他入库',
      MM_OtherInDetail: '其他入库明细管理',
      MM_ScrapApplication: '报废申请',
      MM_ScrapApplicationDetail: '报废申请明细管理',
      MM_StockTodoPicking: {
        Name: '待办领料'
      },
      MM_StockTodo: {
        Name: '待办库存',
        Outbound: '出库',
        Warehousing: '入库',
        Delete: '删除',
        Export: '导出'
      },
      MM_Permission: {
        Add: '添加',
        Edit: '编辑',
        Delete: '删除',
        Export: '导出',
        Posting: '过账',
        Print: '打印',
        Compose: '组托',
        SuccessF: '完成',
        Affirm: '确认',
        Review: '否决',
        UnReview: '驳回',
        Start: '开始',
        Audit: '审核',
        Cancel: '取消',
        Import: '导入模板',
        DownLoadTemp: '导出模板',
        ImportMateriel: '导入物料模板',
        DownLoadTempMateriel: '导出物料模板',
        PrintMateriel: '打印物料号',
        Escheat: '归还',
        PassPost: '冲销',
        upload: '数据导入',
        download: '模板下载',
        Nullify: '作废'
      }
    },
    PP: {
      PP: '生产管理',
      PP_MaterialReq: '生产物料需求管理',
      PP_StockingWave: '生产备货波次单管理',
      PP_StockingWaveDetailed: '生产备货波次明细单管理',
      PP_StockingScan: '生产备货管理',
      PP_Scrapped: '生产报废管理',
      PP_ReturnScan: '车间退料',
      PP_BarCodeReturn: '车间退料标签管理',
      PP_BarCodeReturnEdit: '车间退料标签编辑',
      PP_BarCode: '生产自制品标签管理',
      PP_BarCodeEdit: '生产自制品标签编辑',
      PP_InspectionScan: '生产自制品质检管理',
      PP_OutScan: '生产发料管理',
      PP_InScan: '成品报交管理',
      PP_FTTP: '生产FTT管理',
      PP_FTTPDetailed: '生产FTT明细管理',
      PP_FTTM: '生产FTT原料管理',
      PP_FTTMDetailed: '生产FTT原料明细管理',
      PP_OrderClose: '生产订单关闭',
      PP_ProductionOrder: '生产订单管理',
      PP_OffSiteIngredientReception: '厂外配料接收',
      PP_SerialNumberAssociation: '序列号关联',
      PP_ProductionProcessInspection: '生产过程检验',
      PP_ProductionPackaging: '生产包装',
      PP_SerialNumberReplacement: '序列号(铭牌)替换',
      PP_WarehouseInprocessPartsRework: '仓库在制部件返修(出/还)',
      PP_ProductionSerialNumberAssignment: '序列号分配',
      PP_ProductionExecutionScheduling: '排产打印',
      PP_MaterialDistributionRequirements: '物料配送需求',
      PP_ProductionInput: '生产投料',
      PP_ProductionReport: '工序报工',
      PP_ProductionReturnApplication: '车间退料',
      PP_ProductionReturnApplicationDetail: '车间退料明细管理',
      PP_ProductionStorageLabel: '生产入库标签',
      PP_FinishedProductionStorage: '生产完工入库',
      PP_OnlineRework: '线上返工',
      PP_OverPickingApplication: '超额领料',
      PP_OverPickingApplicationDetail: '超额领料明细管理',
      PP_WorkshopScheduling: '排产打印',
      PP_MaterialDistributionController: '工单物料配送',
      PP_MaterialDistributionControllerDetail: '工单物料配送明细管理',
      PP_ProductionFeeding: '生产投料',
      PP_ProductionFeedingAdd: '生产投料-工序',
      PP_ProductionFeedingDetail: '生产投料明细',
      PP_ProductionReportController: '工序报工',
      PP_ProductionReportControllerDetail: '工序报工明细管理',
      PP_ProductionWarehousingController: '完工入库',
      PP_ProductionReturn: '工单退料',
      PP_ProductionReturnDetail: '工单退料明细管理',
      PP_MaterialReport: '物料报工',
      PP_MaterialReportDetail: '物料报工明细管理',
      PP_SerialNoRelation: '序列号关联',
      PP_SerialNoRelationDetail: '序列号关联明细管理',
      PP_PackSorting: '装箱材料分拣',
      PP_PackSortingDetail: '装箱材料分拣明细管理',
      PP_ProcessInspection: '生产过程检验',
      PP_ProcessInspectionDetail: '生产过程检验明细管理',
      PP_ShippingMark: '唛头打印',
      PP_Permission: {
        Add: '添加',
        Edit: '编辑',
        Delete: '删除',
        Assign: '下发',
        Affirm: '确认',
        Merge: '合并',
        Export: '导出',
        Print: '打印',
        CreateNote: '生成送货单',
        Posting: '过账',
        Sync: '同步',
        Kanban: '看板数据',
        Complete: '完成单据',
        Email: '邮件',
        distribution: '分配',
        PrintIdCard: '打印身份识别卡',
        PrintLabel: '打印二维码',
        Review: '审核',
        Upload: '上传',
        Download: '下载',
        PlanReview: '计划审核',
        DepotReview: '仓库审核',
        CancelReview: '取消审核',
        ExportGather: '导出汇总',
        Import: '上传',
        DownLoadTemp: '下载模板',
        AuditsPosting: '取消过账状态',
        Invalid: '作废'
      }
    },
    QM: {
      QM: '品质管理',
      QM_POInspection: '采购质检管理',
      QM_POInspectionScan: '退供封存区质检记录',
      QM_ProductionProcessInspection: '生产过程检验',
      QM_ProductTraceability: '产品追溯',
      QM_TestItems: '检验项目',
      QM_PurchaseInspection: '采购检验',
      QM_Permission: {
        Delete: '删除',
        Export: '导出',
        Qualify: '合格',
        Disqualify: '不合格',
        BatchDisqualify: '批量不合格',
        Cancellation: '取消质检',
        Print: '打印',
        Posting: '过账'
      }
    },
    RPT: {
      RPT: '业务报表',
      RPT_Stock: '库存报表',
      RPT_StockAge: '库龄报表',
      RPT_DeliverySchedule: '交货进度报表',
      RPT_DeliveryOnTimeRate: '配货及时率',
      RPT_StockDiff: 'SAP库存差异',
      RPT_StockDiffHistory: 'SAP差异调整履历',
      RPT_BarCodeRetrospectS: '正向追溯报表',
      RPT_BarCodeRetrospectE: '反向追溯报表',
      RPT_FTT: 'FTT日报',
      RPT_PO_InOut: '供应商交货 退货报告',
      RPT_StockMove: '库存变动历史',
      RPT_PLineOutPut: '生产线产量报告',
      RPT_PO_Inspection: 'RM-FPY进料合格率',
      RPT_SD_Delivery: '销售出货报告',
      RPT_SupplierItem_View: '供应商物料使用记录',
      RPT_PP_MaterialReqKanban: '生产看板报表',
      RPT_V_NotPostStockMove: '未过账库存报表',
      RPT_ItemMove: '物料移动记录',
      RPT_Permission: {
        Export: '导出'
      }
    },
    Sale: {
      Sale: '销售管理新',
      ShippingPlan: '发运计划',
      Delivery: '销售交货'
    },
    Produce: {
      Produce: '生产管理新',
      UnreleasedOrder: '未释放订单',
      PreProduce: '主机预排产',
      FormalProduce: '主机正式排产',
      ProduceQuery: '排产查询',
      ProduceReport: '生产报工',
      ProduceReportDetail: '生产报工明细',
      ProduceReportScan: '生产报工扫描',
      ProduceWarehousing: '完工入库'
    },
    PDA: {
      PO: {
        PO: '采购管理',
        PO_Inspect: '采购检验',
        PO_Warehousing: '采购入库'
      },
      PP: {
        PP: '生产管理',
        PP_StockingScan: '备货扫描',
        PP_InspectionFTTP: '质检不良品确认',
        PP_FTTP: '仓库不良品确认',
        PP_ReturnScan: '退料扫描',
        PP_InspectionScan: '自制品质检',
        PP_InScan: '成品报交扫描'
      },
      SD: {
        SD: '销售管理',
        SD_StockingScan: '备货扫描',
        SD_DeliveryScan: '销售发货',
        SD_ReturnScan: '销售退货',
        SD_ReturnConfirmScan: '销售退货质检确认',
        SD_ProductionExecutionScheduling: '销售交货'
      },
      MM: {
        MM: '仓库管理',
        MM_EquipmentPicking: '设备领料',
        MM_OutsourcingDispatch: '委外发料',
        MM_OutsourcingWarehousing: '委外入库',
        MM_BarCode: '库存盘点'
      },
      QM: {
        QM: '质量管理'
      },
      RPT: {
        RPT: '报表管理',
        RPT_SalesShipmentPlan: '销售发运计划'
      },
      Cable: {
        Cable: '电缆装箱',
        CablePartPicking: '部件装箱',
        YidaPack: '怡达装箱',
        YidaHoistwayPack: '怡达井道装箱'
      },
      Host: {
        Pack: '主机装箱',
        PackCheck: '装箱核对'
      }
    },
    Cable: {
      Sale: '电缆销售管理',
      Produce: '电缆生产管理',
      PartProduce: '电缆部件生产管理',
      ShippingPlan: {
        Name: '发运计划',
        Delete: '删除',
        Export: '导出'
      },
      Delivery: {
        Name: '销售交货',
        Delete: '删除',
        Post: '过账',
        UnPost: '取消过账',
        Export: '导出',
        Import: '导入',
        ImportTempDown: '模板下载'
      },

      ProduceSerial: '序列号分配',
      PartPackPrint: {
        Name: '部件装箱标签打印',
        Print: '打印'
      },
      CablePartWorkPrint: {
        Name: '工单打印',
        print: '打印'
      },
      WorkPrint: {
        Name: '工单打印',
        Print: '打印'
      },
      PackLabel: {
        Name: '装箱标签打印',
        Print: '打印'
      },
      CreatePlan: {
        Name: '计划订单管理',
        Add: '创建计划订单'
      },
      ProduceOrder: {
        Name: '生产订单管理',
        OrderToProduction: '创建生产订单',
        ProductionConfirm: '生产订单确认'
      },
      ReportWork: {
        Name: '生产报工',
        Submit: '报工',
        Cancel: '取消报工'
      }
    }
  },
  Platform: {
    Layout: {
      Navbar: {
        Account: {},
        UpdatePassword: {
          LoginAccount: '登录账号',
          OldPassword: '请输入旧密码',
          NewPassword: '请输入新密码',
          ConfirmNewPassword: '请再次输入新密码',
          NotConsistent: '两次输入的新密码不一致！'
        },
        Route: {
          UserInfo: '个人中心',
          UpdatePassword: '修改密码'
        }
      }
    },
    Components: {
      HeaderSearch: {
        Placeholder: '功能检索'
      }
    }
  },
  exception: {
    timeRangeError: '请输入正确的时间范围'
  },
  navbar: {
    dashboard: '首页',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    theme: '换肤',
    size: '布局大小'
  },
  login: {
    title: 'WMS系统登录',
    logIn: '登录',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'
  },
  documentation: {
    documentation: '文档',
    github: 'Github 地址'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 el-tab 或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  components: {
    documentation: '文档',
    tinymceTips: '富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见',
    dropzoneTips: '由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone',
    stickyTips: '当页面滚动到预设的位置会吸附在顶部',
    backToTopTips1: '页面滚动到指定位置会在右下角出现返回顶部按钮',
    backToTopTips2: '可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素',
    imageUploadTips: '由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '备注',
    search: '搜索',
    add: '添加',
    export: '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定',
    keyword: '关键字'
  },
  example: {
    warning: '创建和编辑页面是不能被 keep-alive 缓存的，因为keep-alive 的 include 目前不支持根据路由来缓存，所以目前都是基于 component name 来进行缓存的。如果你想类似的实现缓存效果，可以使用 localStorage 等浏览器缓存方案。或者不要使用 keep-alive 的 include，直接缓存所有页面。详情见'
  },
  errorLog: {
    tips: '请点击右上角bug小图标',
    description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',
    documentation: '文档介绍'
  },
  excel: {
    export: '导出',
    selectedExport: '导出已选择项',
    placeholder: '请输入文件名(默认excel-list)'
  },
  zip: {
    export: '导出',
    placeholder: '请输入文件名(默认file)'
  },
  pdf: {
    tips: '这里使用   window.print() 来实现下载pdf的功能'
  },
  theme: {
    change: '换肤',
    documentation: '换肤文档',
    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  },
  Common: {
    CanNotDeleteInUse: '该对象正在使用，无法删除',
    ExistedCode: '编号重复，请输入新的正确编号',
    CreateKanbanData: '生成看板数据',
    CountTotal: '合计',
    title: '标题',
    type: '类型',
    search: '搜索',
    add: '添加',
    printview: '打印预览',
    export: '导出',
    import: '导入',
    date: '时间',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    distribution: '分配',
    email: '邮件',
    delete: '删除',
    cancel: '返回',
    empty: '清空',
    confirm: '提交',
    save: '保存',
    audit: '审核',
    affirm: '确认',
    affirmdy: '确认发货',
    keyword: '关键字',
    success: '成功',
    failed: '失败',
    error: '错误',
    overStep: '超期',
    notOverStep: '未超期',
    NoDateIsNull: '时间不允许清空',
    compose: '组托',
    information: '提示',
    queryNoData: '没有数据',
    sapNoDiff: '没有库存差异',
    operationSuccess: '操作成功',
    operationFailed: '操作失败',
    createSuccess: '创建成功',
    updateSuccess: '更新成功',
    deleteSuccess: '删除成功',
    postSuccess: '过账成功',
    auditSuccess: '审核成功',
    rejectSuccess: '驳回成功',
    select: '选择',
    justSingleSelection: '请选择单条记录',
    noSelection: '请选择记录',
    view: '查看详情',
    Remark: '备注',
    IsDelete: '删除标示',
    CUser: '创建人',
    CTime: '创建时间',
    MUser: '更新人',
    MTime: '更新时间',
    DUser: '删除人',
    DTime: '删除时间',
    Enable: '启用',
    Disable: '停用',
    posting: '过账',
    passPost: '冲销',
    gdelivery: '生成送货单',
    print: '打印',
    log: '登记',
    close: '关闭',
    grant: '下发',
    merge: '合并',
    successf: '完成',
    start: '开始',
    Person: '指定责任人',
    cancellation: '取消质检',
    qtesting: '质检',
    Packing: '生成装箱单',
    printQty: '打印数量',
    timeFrom: '自',
    timeTo: '至',
    startTime: '开始时间',
    endTime: '结束时间',
    previousWeek: '最近一周',
    previousMonth: '最近一个月',
    previousThreeMonths: '最近三个月',
    posted: '已过帐',
    notPosted: '未过账',
    begin: '已开始',
    unbegin: '未开始',
    isConfirm: '已确认',
    unConfirm: '未确认',
    // 是否生成送货单
    ascending: '升序',
    descending: '降序',
    postingStatus: '过账状态',
    confirmStatus: '确认状态',
    all: '全部',
    actionConfirm: '是否确认要执行此操作？',
    // committingConfirm: '是否确认要提交全部数据？\r\n（提交后将无法修改）',
    committingConfirm: '是否确认要提交全部数据？',
    batchDeletingConfirm: '是否确认要删除这些数据？',
    tip: '提示',
    correspondingDetailsAlreadyExist: '已存在对应明细',
    numberRequired: '不能为空，且必须为数字',
    mustBeGreaterThanZero: '必须大于0',
    DocNumIsNull: '单号不能为空',
    primaryTableRecordsNotFound: '未找到主表记录',
    pleaseSelectDate: '请选择日期',
    selectDateIsBigToDay: '选择日期不能大于今天',
    exportTemplate: '导出模板',
    yes: '是',
    no: '否',
    isDelivery: {
      yes: '已装箱',
      no: '未装箱',
      noDelivery: '已装箱不允许再次生成'
    },
    update: '更新',
    selectMaterial: '选择物料',
    selectSupplier: '选择供应商',
    selectProductionOrder: '选择生产订单',
    selectLocation: '选择库位',
    selectRType: '选择类型',
    IsRequired: '不能为空',
    operationNotPermitted: '操作不允许',
    BaseNumIsNull: '请选择生产订单',
    detailIsNull: '明细记录不能为空',
    ERPDocNum: '过账单号',
    Inspection: {
      qualify: '合格',
      disqualify: '不合格',
      BatchDisqualify: '批量不合格'
    },
    firstChooceCustomerNumber: '请先选择客户编号',
    writeCustomerNameOrOrder: '请写入客户编号或者客户名称',
    BinLocationLimitNotPass: '入库的库位不在限制库位中，请选择正确的库位入库',
    onlyOneCustomerOnList: '不允许选择多个用户，如有需要请删除已添加的明细',
    inCorrectQuantity: '请输入正确数量',
    inCorrectSaleQty: '备货总数量不能大于订单备货数量',
    // inCorrectCheckQty: '备货总数量不能大于生产订单备货数量',
    NotSamePline: '请选择同一生产线打印',
    inSiteID: '请输入站点ID',
    handlingPostedDataNotPermitted: '已过账数据不允许操作',
    notFindUseItemCodeInSAP: '过账失败，SAP中未找到可以操作的任务',
    inCorrectNotZero: '备货数量不能为0',
    ValidatorMessage: {
      // 校验中的共同模块
      MustInput: '必须输入!',
      MustSelect: '该项为必选项！',
      LengthRange: '请输入{Range}位字符！'
    },
    notEnoughInventory: '库存数量不足',
    repeatBaseNum: '请不要重复添加同一条销售订单',
    noDataToPost: '没有需要过账的数据',
    postToSAPConfirm: '是否确认要过账到SAP？',
    review: '复核',
    reject: '驳回',
    reviewNotPermitted: '已通过复核不允许操作',
    reviewTwoNotPermitted: '已驳回不允许操作',
    rejectNotPostToSAP: '未通过复核不允许过账',
    waveConfirm: '仓库确认完毕后，不允许删除',
    inspectionConfirm: '仓库确认完毕后，不允许删除',
    StockIsLocked: '库存已锁定',
    ErrNotDelete: '存在已扫描项，不允许操作',
    // eslint-disable-next-line no-dupe-keys
    sync: '同步数据',
    syncStart: '正在同步,如果出现操作超时,请刷新页面并稍候几分钟再次重试.',
    warning: '提示',
    slowRunningWarning: '正在进行耗时较长的操作,如果出现超时的情况,请稍候刷新页面再次尝试',
    barcodeScanRecordExists: '条码的记录已经存在，无法重复扫描',
    reviewNoPostToSAP: '复核通过，过账失败',
    seletedDataInStockNoDelete: '选择数据已入库，不允许删除',
    noConfirmData: '没有可提交的数据',
    someDataHasRepeat: '部分数据存在重复，已排除重复项',
    DocNum: '扫描单号',
    BaseNum: '采购退货申请单号',
    improtNoData: '没有可以上传的数据！',
    excel: {
      selectFiles: '选取文件',
      tip: '只能上传一个文件.xls/.xlsx文件',
      errorFiles: '附件格式错误，请删除后重新上传！',
      uploadFilse: '请上传附件！',
      overMaxNum: '超出最大上传文件数量的限制！'
    }
  },
  Base: {
    BinLocation: {
      BinID: '库位ID',
      BinLocationCode: '库位编号',
      BinLocationName: '库位名称',
      RegionCode: '所属区域编号',
      RegionName: '所属区域名称',
      WhsCode: '所属仓库编号',
      WhsName: '所属仓库名称',
      IsFreeTax: '是否保税',
      IsConsign: '是否寄售',
      title: '库位主信息'
    }
  },
  ui: {
    MD: {
      Customer: {
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        Email: '邮件',
        City: '城市',
        Phone: '联系电话',
        Fax: '传真',
        ContactPerson: '联系人',
        title: '客户明细信息',
        Address: '地址',
        searchCustomer: '查询客户'
      },
      MD_Warehouse: {
        WhsID: '仓库ID',
        CompanyCode: '公司编号',
        WhsCode: '仓库编号',
        WhsName: '仓库名称'
      },
      MD_Region: {
        RegionID: '区域ID',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        WhsCode: '所属仓库编号',
        WhsName: '所属仓库名称',
        WhsName1: '所属仓库'
      },
      MD_BinLocation: {
        BinID: '库位ID',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        RegionCode: '所属区域编号',
        RegionName: '所属区域名称',
        RegionName1: '所属区域',
        WhsCode: '所属仓库编号',
        WhsName: '所属仓库名称',
        WhsName1: '所属仓库',
        IsFreeTax: '是否保税',
        IsConsign: '是否寄售',
        PLine: '生产线'
      },
      MD_Dictionary: {
        DictionaryID: '数据字典ID',
        TypeCode: '类型编号',
        TypeDisc: '类型名称',
        EnumKey: '枚举值',
        EnumValue: '枚举值描述',
        EnumValue1: '枚举值描述1',
        EnumValue2: '枚举值描述2',
        SortNum: '排序号',
        Remark: '备注',
        CUser: '创建者',
        CTime: '创建时间',
        MUser: '更新者',
        MTime: '更新时间'
      },
      MD_Item: {
        ItemID: '物料ID',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        Unit: '库存单位',
        StockingQty: '生产看板备货数',
        AgeQty: '库龄预警值',
        ULimitWarning: '库存上限预警值',
        LLimitWarning: '库存下限预警值',
        RegionCode: '推荐区域',
        ReqWarning: '生产看板预警值',
        ProductCategoryID: '产品类别',
        DType: '发料方式',
        BaseMeasureUnitCode: '基本计量单位',
        InventoryValuationMeasureUnitCode: '库存评估单位',
        PlanningMeasureUnitCode: '计划计量单位',
        QuantityConversion: '单位换算',
        PurchasingMeasureUnitCode: '采购单位',
        SalesMeasureUnitCode: '销售单位',
        IsBarCode: '是否自动生成客户标签',
        IsFreeTax: '是否保税',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        syncMaterialSuccess: '同步SAP数据成功,如果产品类别(物料组)信息为空,请先同步产品类别数据',
        syncProductCategory: '同步产品类别数据',
        syncMaterial: '同步物料数据',
        Isabroad: '是否是国外客户'
      },
      MD_Supplier: {
        SupplierCode: '供应商编号',
        SupplierName: '供应商描述',
        Email: '电子邮件',
        City: '所在城市',
        Phone: '联系电话',
        Fax: '传真',
        ContactPerson: '联系人',
        Address: '地址',
        SyncSupplier: '同步SAP数据'

      },
      MD_Customer: {
        InternalID: '客户编号',
        FamilyName: '个人客户名称',
        LifeCycleStatusCode: '状态',
        EMailURI: '电子邮件',
        CityName: '所在城市',
        Phone: '联系电话',
        Fax: '传真',
        ContactPerson: '联系人',
        Address: '地址',
        FirstLineName: '公司客户名称',
        CategoryCode: '客户类别',
        SyncCustomer: '同步SAP数据'
      },
      MD_LabelTemplate: {
        TempleteID: '模板ID',
        TempleteDesc: '模板描述',
        TempleteFile: '模板文件',
        TempleteType: '模板类型',
        IsEnable: '是否有效',
        Remark: '备注',
        CUser: '创建者',
        CTime: '创建时间',
        MUser: '更新者',
        MTime: '更新时间',
        ClickToUpload: '点击上传',
        UploadTip: '只能上传repx文件，且不超过2M',
        FileTypeWarning: '文件类型不是.repx!，不能上传！',
        PleaseSelectFile: '请选择文件',
        Uploading: '文件上传中...',
        FileMustGt2M: '文件不得大于2M',
        FileUploadLimit: '当前限制只能上传1个文件，本次选择了{CurrentSelect}个文件，共选择了{TotalSelect}个文件'
      },
      Stock: {
        StockID: '库存ID',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BarCode: '条码',
        BatchNum: '批次',
        SupplierBatch: '供应商批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '库存数量',
        Unit: '库存单位',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称'
      },
      MD_POInspectionPlan: {
        PlanID: '计划ID',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        SampleRange: '采样范围',
        GradeCode: '检验等级编号',
        GradeName: '检验等级名称',
        SampleNum: '固定样本数',
        SampleProportion: '固定样本比例',
        ITime: '批次检验时间',
        Placeholder: {
          Supplier: '请选择供应商',
          Item: '请选择物料'
        },
        Message: {
          ExistSupplierItem: '该供应商的该物料已经设置检验计划，请不要重复设置！'
        },
        SelectItemTitle: '选择物料',
        SelectSupplierTitle: '选择供应商',
        Supplier: {
          SupplierCode: '供应商编号',
          SupplierName: '供应商描述',
          Email: '电子邮件',
          City: '所在城市',
          Phone: '联系电话',
          Fax: '传真',
          ContactPerson: '联系人',
          Address: '地址'
        }
      },
      MD_POInspectionGrade: {
        GradeID: '等级ID',
        GradeCode: '检验级别编号',
        GradeName: '检验级别名称',
        OKNum: '连续检验合格批数'
      },
      MD_ProduceLineCapacity: {
        Id: '编号',
        LineName: '线体名称',
        Capacity: '产能',
        title: '生产线产能维护'
      },
      MD_POInspectionGradeDetailed: {
        GradeID: '等级明细ID',
        GradeCode: '检验级别编号',
        IQty: '检验数量',
        SampleNum: '样本数'
      },
      MD_ReportStationMaterial: {
        WorkCenterName: '工作中心名称',
        WorkCenterCode: '工作中心编码',
        ProcessNo: '工序号',
        ProcessShortText: '工序短文本',
        StationCode: '站点代码',
        StationName: '站点名称',
        MaterialCode: '物料编码',
        MaterialDesc: '物料描述',
        Quantity: '数量',
        Unit: '单位'
      },
      MD_BinLimit: {
        limitID: '权限ID',
        MenuCode: '菜单编号',
        MenuName: '菜单名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        functionModule: '功能模块',
        keyword: '区域/库位编号',
        title: '选择库位',
        checkModule: '请选择功能模块',
        checkBinlication: '请选择库位信息'
      },
      MD_SalePacking: {
        SPID: 'SPID',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        Volume: '体积',
        NetWeight: '净重',
        GrossWeight: '毛重',
        PalletNum: '每箱数量',
        Metre: '每根米数',
        title: '客户信息',
        oitmtitle: '物料信息',
        PNum: '每托盘箱数'
      },
      Placeholder: {
        Supplier: '请选择供应商！',
        Item: '请选择物料！'
      },
      Supplier: {
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        Email: '邮件',
        City: '城市',
        Phone: '联系电话',
        Fax: '传真',
        ContactPerson: '联系人',
        Address: '供应商地址'
      },
      Item: {
        ItemCode: '物料件号',
        ItemName: '物料描述',
        ItmsGrpCode: '物料组编码',
        ItmsGrpName: '物料组描述',
        Unit: '基本单位'
      },
      SelectSupplierTitle: '选择供应商',
      SelectItemTitle: '选择物料'
    },
    MM: {
      BarCode: {
        BarID: '条码ID',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '数量',
        Unit: '库存单位',
        PrintTemplate: '标签模板',
        title: '物料标签',
        generatingBarcodeFailed: '生成条码失败'
      },
      InScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        RTypeCode: '收货类型编号',
        RTypeName: '收货类型',
        Subject: '科目',
        CostCenter: '成本中心/项目任务',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '其他收货',
        binLocationCodeIsRequired: '请选择库位',
        itemCodeIsRequired: '请选择物料',
        IsCheck: '复核状态',
        PostShouldBeOneDocNum: '一次过账只能选择一个扫描单号'
      },
      OutScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        RTypeCode: '发货类型编号',
        RTypeName: '发货类型',
        Subject: '科目',
        CostCenter: '成本中心/项目任务',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位',
        IsPosted: '是否过账',
        PDtype: 'SAP视图',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '其他发货',
        notEnoughInventory: '库存不足',
        QMRegionNotAllowed: '不允许在质量管控仓内进行收发货操作',
        IsCheck: '复核状态',
        ERPDocNum: 'SAP单据号'
      },
      TakeStockPlan: {
        PlanID: '计划ID',
        DocNum: '计划单号',
        PUser: '责任人',
        Status: '状态',
        title: '盘点计划',
        planStatusError: '当前盘点计划状态不允许操作',
        planScanRecordsNotFound: '未找到盘点明细记录',
        planNotFound: '未找到盘点计划',
        lockStockFailed: '未能锁定库存',
        TakeStockError: '差异数据已被确认，无法再次确认',
        CheckIsConfirm: '所选的数据已有部分确认差异，不允许复核',
        missingEssential: '区域、库位、物料请至少输入其中一项',
        QMRegionNotAllowed: '不允许在质量管控仓内进行盘点操作',
        IsConsignNotAllowed: '存在寄售条码，不允许生成盘盈差异'
      },
      TakeStockPlanDetailed: {
        PlanID: '计划ID',
        DocNum: '计划单号',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        title: '盘点计划明细',
        DetailsIsEmpty: '明细记录不能为空',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称'
      },
      TransferScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '物料移动'
      },
      Inventoryplan: {
        DocNum: '计划单号',
        PUser: '责任人',
        Status: '状态',
        title: '盘点计划管理'
      },
      InventoryplanDetailed: {
        DocNum: '计划单号',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        title: '盘点计划明细管理'
      },
      TakeStockScan: {
        DocNum: '盘点明细单号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        StockQty: '库存数量',
        ScanQty: '扫描数量',
        DiffQty: '差异数量',
        Unit: '库存单位',
        WhsCode: '仓库编号',
        WhsName: '仓库名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        IsConfirm: '是否已确认',
        title: '盘点明细管理'
      },
      Inventorydiscpy: {
        DocNum: '盘点差异单号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        StockQty: '库存数量',
        ScanQty: '扫描数量',
        DiffQty: '差异数量',
        Unit: '库存单位',
        WhsCode: '仓库编号',
        WhsName: '仓库名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        IsConfirm: '是否已确认',
        title: '盘点差异管理'
      },
      BarCodeBox: {
        BarID: '条码ID',
        BoxBarCode: '包装箱码',
        BoxQRCode: '包装箱二维码',
        IConfirm: '质检确认',
        title: '包装箱码管理',
        PrintDiffrent: '打印包装箱码不是同一物料不允许打印'
      },
      BarCodeBoxDetailed: {
        BarID: '条码ID',
        BoxBarCode: '包装箱码',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '数量',
        IConfirm: '质检确认',
        Unit: '库存单位',
        title: '包装箱码明细'
      },
      DepartmentPickingApplication: {
        RequisitionNum: '领料单号',
        FactoryCode: '工厂',
        DocumentTime: '凭证日期',
        PostTime: '凭证日期',
        DetailTitle: '部门领退料申请明细单',
        BaseLine: '领料单行号',
        ItemCode: '物料件号',
        ItemName: '物料描述',
        MovementType: '移动类型',
        ApplyQty: '申请数量',
        Unit: '单位',
        WhsCode: '仓库编码',
        WhsName: '仓库名称',
        CostCenter: '成本中心',
        LedgerType: '总账科目',
        OrderNum: '订单',
        AssetCard: '资产卡片',
        SpecialQty: '特殊库存',
        SalesOrderNum: '销售订单',
        SalesOrderPro: '销售订单项目',
        AssessmentType: '评估类型',
        Remark: '备注',
        IsPosted: '是否过账'
      },
      DepartmentPickingApplicationDetail: {
        title: '部门领退料申请登记单',
        TwoTitle: '部门领退料申请明细单'
      },
      MM_OtherInDetail: {
        title: '其他入库申请登记单',
        TwoTitle: '其他入库申请明细单',
        DetailTitle: '其他入库申请明细单'
      }
    },
    PO: {
      PO_BarCode: {
        BarID: '条码ID',
        DeliveryDetailId: '交货计划明细ID',
        DeliveryDocNum: '交货计划单号',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '行号',
        SupplierBatch: '供应商批次',
        DeliveryTime: '交货日期',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '包装数量',
        Unit: '库存单位',
        PrintTemplate: '标签模板',
        PrintQty: '打印张数',
        IsDelivery: '是否生成送货单',
        title: '采购标签',
        Remark: '备注'
      },
      PO_Inspection: {
        InspectionID: '质检单ID',
        DocNum: '质检单号',
        NoteNum: '送货单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        SampleRange: '检验计划',
        InspectionGrade: '检验等级',
        IQty: '送检数量',
        SQty: '抽样数量',
        IStatus: '质检状态',
        Inspected: '已质检',
        Uninspected: '未质检',
        IUser: '质检人',
        ITime: '质检时间',
        title: '采购质检单',
        IStatusOption: {
          All: '全部',
          CheckedStatus: '已质检',
          NoCheckedStatus: '未质检'
        },
        ApiMessage: {
          MustFirstPost: '报检数据必须要先过账才能进行质检！',
          HasShelfScanBarCode: '该单据相关物料已经入库上架，不允许取消质检！',
          StockedInItemNotAllowInspectionChange: '已经入库上架物料不允许再更改质检'

        }
      },
      PO_InspectionDetailed: {
        DetailedID: '明细ID',
        DocNum: '质检单号',
        ScanNum: '扫描单号',
        NoteNum: '采购送货单号',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        SupplierBatch: '供应商批次',
        CustomsNum: '报关单号',
        PTime: '生产日期',
        Qty: '送检数量',
        OkQty: '合格数量',
        NoQty: '不合格数量',
        /* 以下字段部分新库没有 */
        IStatus: '质检状态', // (未质检/已质检)
        Inspection: '质检结果', // (合格/让步接收/不合格/挑选使用)
        Reasons: '不合格原因',
        WMSDocNum: 'WMS过账单号',
        ERPDocNum: 'SAP过账单号',
        IDispose: '质检处理', // (未处理/确认收货/退货移动)
        /* 以上字段部分新库没有 */
        IUser: '质检人',
        ITime: '质检时间',
        Unit: '库存单位',
        Status: '质检状态', // (正常/作废)
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '采购质检单明细'
      },
      PO_ConsignIn: {
        ScanID: '扫描ID',
        CompanyCode: '公司编号',
        DocNum: '扫描单号',
        ShelfNum: '上架单号',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '行号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        POUnit: '采购单位',
        Unit: '库存单位',
        ConversionRate: '单位换算率',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        ErpDocEntry: 'ERP过账单编号',
        ErpDocNum: 'ERP过账单号',
        Status: '状态',
        title: '寄售采购入库单',
        CheckQty: '超出可寄售数量，不允许添加'
      },
      PO_ShelfScan: {
        ScanID: '扫描ID',
        CompanyCode: '公司编号',
        DocNum: '扫描单号',
        ShelfNum: '上架单号',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '行号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        POUnit: '采购单位',
        Unit: '库存单位',
        ConversionRate: '单位换算率',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        ErpDocEntry: 'ERP过账单编号',
        ErpDocNum: 'ERP过账单号',
        Status: '状态',
        title: '采购上架单'
      },
      PO_ReturnScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        CompanyCode: '公司编号',
        BoxBarCode: '包装码',
        BaseEntry: '采购退货申请单编号',
        BaseNum: '采购退货申请单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        SupplierBatch: '供应商批次',
        BaseLine: '行号',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        // POUnit: '采购单位',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        Status: '状态',
        title: '采购上架单',
        WmsDocNum: 'WMS过账单据号'
      },
      PO_ReturnScanDetail: {
        title: '采购退货明细单',
        twoTitle: '采购退货单',
        DocNum: '采购退货单号',
        BaseNum: '采购退货申请单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        SupplierBatch: '供应商批次',
        BaseLine: '行号',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        Qty: '数量',
        Unit: '库存单位',
        operation: '操作',
        BarCode: '条码',
        ItemGrpName: '物料组名称',
        Cuser: '创建人',
        CTime: '创建时间',
        EBELN: '采购订单号',
        EBELP: '采购订单行号',
        MATNR: '物料件号',
        MENGE: '数量',
        MEINS: '单位',
        LGORT: '库存地点',
        PurchaseOrderID: '采购订单号',
        ItemID: '采购订单行号',
        ProductID: '物料件号',
        ProductDescription: '物料名称',
        ProductCategoryDescription: '物料组名称',
        Quantity: '退货数量',
        UnitCode: '单位',
        TotalDeliveredQuantity: '交货数量',
        SupplierInternalID: '供应商编号',
        SupplierDescription: '供应商名称',
        PurchaseOrderDetailID: '子表主键ID'
      },
      PO_DeliveryPlan: {
        PlanID: '计划ID',
        DocNum: '计划单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        DeliveryTime: '交货日期',
        Status: '状态',
        title: '采购交货计划单',
        titlesub: '采购交货计划单明细'
      },
      PO_DeliveryPlanDetailed: {
        DetailedID: '明细ID',
        PlanNum: '计划单号',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '采购订单行号',
        DeliveryPlanItemID: '交货计划行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '交货数量',
        Unit: '单位',
        PTime: '生产日期',
        DeliveryTime: '交货日期',
        IsCreatedBarCode: '是否创建条码',
        title: '采购交货计划明细管理',
        select: '选择采购交货计划',
        ValidatorMessage: {
          NotAllowConditionAllEmpty: '采购订单号和供应商编号不可以同时为空！',
          DeliveryPlanQtyMasterLtZero: '交货计划数量必须大于0'

        }
      },
      PurchaseOrder: {
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        BaseLine: '行号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        PTime: '订单日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Quantity: '采购数量',
        UnitCode: '采购单位',
        TotalDeliveredQuantity: '已交货数量',
        TotalDeliveredUnitCode: '已交货单位',
        title: '采购订单',
        select: '选择采购订单'
      },
      PO_DeliveryNote: {
        NoteID: '送货单ID',
        DocNum: '送货单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        DeliveryTime: '交货日期',
        IsInspection: '是否已报检',
        SendInspectionStatus: '报检状态',
        title: '采购送货表'
      },
      PO_DeliveryNoteDetailed: {
        DetailedID: '明细ID',
        DocNum: '送货单号',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        BaseLine: '行号',
        SupplierBatch: '供应商批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        IsSendInspection: '是否报检',
        Qty: '数量',
        Unit: '库存单位',
        title: '采购送货单明细'
      },
      PO_InScan: {
        ScanID: '扫描ID',
        CompanyCode: '公司编号',
        BoxBarCode: '包装码',
        DocNum: '扫描单号',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '行号',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        POUnit: '采购单位',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期'
      },
      PO_Shelf: {
        ShelfID: 'ID',
        CompanyCode: '公司编号',
        DocNum: '上架单号',
        ShelfTime: '上架日期',
        PUser: '责任人',
        Status: '状态' // 正常/取消/完成
      },
      PO_ShelfDetailed: {
        DetailedID: '明细ID',
        CompanyCode: '公司编号',
        ShelfNum: '上架单号',
        BaseEntry: '采购订单编号',
        BaseNum: '采购订单号',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseLine: '行号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '上架数量',
        POUnit: '采购单位',
        Unit: '库存单位',
        ConversionRate: '单位换算率',
        Status: '状态', // 正常/取消/完成
        title: '上架任务单明细'
      },
      PO_InspectionScan: {
        ScanID: '扫描ID',
        DocNum: '质检单号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '确认数量',
        Unit: '库存单位',
        WhsCode: '仓库编号',
        WhsName: '仓库名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        IType: '类型', // (退供区/包装码)
        IsScan: '是否已移动'
      },
      PO_ITransferScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BaseNum: '质检单号',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期'
      },
      SAP_PO_PurchaseOrder: {
        PurchaseOrderID: '采购订单编号',
        SupplierInternalID: '供应商编号',
        SupplierDescription: '供应商描述',
        LastModifiedTime: 'SAP最后更新时间',
        Datasource: {
          fromRemote: '远程查询(SAP)',
          fromLocal: '本地查询'
        },
        syncSuccess: '同步成功'
      },
      SAP_PO_PurchaseOrderDetail: {
        title: '采购订单详情',
        ItemID: '行号',
        ProductID: '物料件号',
        ProductDescription: '物料描述',
        OrderedDateTime: '下单时间',
        Quantity: '数量',
        UnitCode: '单位',
        TotalDeliveredQuantity: '已交货数量',
        TotalDeliveredUnitCode: '已交货单位',
        EndDeliveryTime: '最晚交货时间'
      },
      PO_PurchaseReceipt: {
        DocNum: '扫描单号',
        BaseNum: '采购收货申请单号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BarCode: '条码',
        BatchNum: '批次',
        IsPosted: '是否过账',
        PostUser: '过账人',
        OutBinLocationName: '库位名称',
        OutRegionName: '区域名称',
        OutWhsName: '仓库名称',
        Line: 'WMS行号'
      }
    },
    PP: {
      MaterialReq: {
        ReqID: '需求ID',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        ProductionID: '产品编号',
        BaseLine: '行号',
        ItemCode: '原料编号',
        ItemName: '原料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        BOMReqQty: '原料基本用量',
        BOMUnit: 'BOM单位',
        PLine: '生产线',
        BinLocationCode: '线边仓库位编号',
        BinLocationName: '线边仓库位名称',
        ConversionRate: '单位转换率',
        Unit: '库存单位',
        ReqQty: '需求数量(库存单位)',
        StockingQty: '已备货数量',
        MaterialQty: '生产耗料数量',
        StockQty: '线边仓库位库存',
        SurplusQty: '剩余库存量',
        title: '生产物料需求记录',
        BOM: '用料'
      },
      MaterialReqKanban: {
        ReqID: '需求ID',
        ProductionOrderID: '生产订单号',
        ProductionLine: '生产线',
        ProductID: '产品编号',
        ProductDescription: '产品名称',
        BinLocationCode: '线边仓库位编号',
        BinLocationName: '线边仓库位名称',
        MaterialID: '用料(原料)编号',
        MaterialDescription: '用料(原料)名称',
        BOMUnitCode: 'BOM用料单位',
        BOMQty: 'BOM用料数量',
        PlanQty: '计划用料数量',
        ConversionRate: '单位转换率',
        UnitCode: '库存单位',
        StockingQty: '备料数量',
        BufferQty: '线边仓库存数量',
        PreparedQty: '已备货数量',
        FeededQty: '已投料数量',
        InventoryQty: '仓库库存数量',
        WarningStatus: '预警状态',
        ProductionOrderStatus: '生产订单状态'
      },
      StockingWave: {
        WaveID: '波次ID',
        DocNum: '波次单号',
        STime: '备货日期',
        Status: '状态',
        title: '备货波次单',
        complete: '完成备货',
        noItemCodeSelected: '请选择物料后再进行添加',
        PrimaryIsNull: '波次单信息不能为空',
        Status_notBegin: '未开始',
        Status_stockedUp: '已备货',
        Status_completed: '完成',
        PLine: '生产线',
        sTimeIsNull: '请选择备货日期',
        pLineIsNull: '请选择生产线',
        StatusError: '状态错误',
        finishingPreparationConfirm: '是否确认要完成备货？',
        sameOrderNum: '只能添加同一生产订单号的数据'
      },
      StockingWaveDetailed: {
        DetailedID: '详单ID',
        DocNum: '波次单号',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        BinLocationCode: '线边仓库位编号',
        BinLocationName: '线边仓库位名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '备货数量',
        Unit: '库存单位',
        BaseLine: '行号',
        title: '备货波次明细单',
        DetailsIsEmpty: '波次单明细记录不能为空',
        selectMaterial: '选择物料',
        Remark: '已备货数量'
      },
      StockingScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        WaveNum: '波次单号',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        BaseLine: '行号',
        PLine: '生产线',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        postToSAPConfirm: '是否确认要过账到SAP？',
        noDataToPost: '没有需要过账的数据'
      },
      TransferScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        WaveNum: '波次单号',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        BaseLine: '行号',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期'
      },
      Scrapped: {
        ScrappedID: '报废ID',
        DocNum: '不良单号',
        ScrappedNum: '报废单号',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '报废数量',
        Unit: '库存单位',
        OutRegionCode: '出库区域编号',
        OutRegionName: '出库区域名称',
        OutBinLocationCode: '出库库位编号',
        OutBinLocationName: '出库库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        STypeCode: '处理建议',
        Subject: '科目',
        CostCenter: '成本中心',
        ERPDocNum: '过账单号'
      },
      FTTP: {
        FTTPID: '主键ID',
        DocNum: 'FTT ID',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '产线编号',
        ItemCode: '产品编号',
        ItemName: '产品名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        FType: '状态',
        title: '生产不良自制品登记单',
        IsRepairReturn: '是否维修返回',
        PrimaryIsNull: '登记单信息不能为空',
        CTime: '创建日期',
        confirmRecordExists: '操作不允许，已经进行过仓库确认'
      },
      FTTPDetailed: {
        DocNum: 'FTT ID',
        Station: '站点ID',
        ActualStation: '实际站点(扣料站点)',
        ReasonsCode: '不良描述ID',
        ReasonsDesc: '不良描述',
        InputQty: '投入数量',
        RejectQty: '拒收数量',
        OutputQty: '产出数量',
        DealWith: '处理建议',
        IConfirm: '质检确认',
        WConfirm: '仓库确认',
        title: '生产FTT登记明细单',
        ScrappedNum: '报废单号',
        DetailsIsEmpty: '明细记录不能为空',
        rejectShouldBeLessThanInput: '拒收数量不应超过投入数量'
      },
      FTTM: {
        FTTMID: '主键ID',
        DocNum: 'FTT ID',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '产线编号',
        title: '生产FTT原料登记单'
      },
      FTTMDetailed: {
        DetailedID: '主键ID',
        DocNum: 'FTT ID',
        BaseLine: '行号',
        ReasonsCode: '不良描述ID',
        ReasonsDesc: '不良描述',
        Qty: '数量',
        TreatmentPlan: '处理方案',
        IConfirm: '质检确认',
        WConfirm: '仓库确认',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        title: '生产FTT原料登记明细单'
      },
      ProductionOrder: {
        RequestedEndDate: '要求结束时间',
        ProductionOrderSiteID: '运营地点编号',
        ProductionOrderStatus: '生产订单状态',
        ProductionOrderPriority: '优先级',
        BaseNum: '生产订单编号',
        ItemCode: '产出品物料件号',
        ItemName: '产出品物料名称',
        ProductionOrderOpenQuantity: '未确认数量',
        ProductionOrderReleaseDate: '生产订单下达截止日期',
        ProductionOrderPlannedQuantity: '计划数量',
        ProductionOrderPlannedQuantityUnit: '单位',
        ProductionOrderFullfilledQuantity: '完工数量',
        SubmittedQuantity: '报交数量',
        CUser: '创建者',
        ProductionLine: '生产线',
        title: '生产订单',
        select: '请选择生产订单',
        notFound: '未找到生产订单',
        PlannedStartDate: '计划开始时间',
        CreatedDate: '订单创建时间',
        CreatedDateStart: '创建时间 起',
        CreatedDateEnd: '创建时间 止',
        fromRemote: '远程查询(SAP)',
        fromLocal: '本地查询',
        datasource: '数据来源',
        productionOrderIDIsNull: '未知的生产订单号',
        syncSuccess: '同步SAP数据成功,如果产品类别或者物料信息出现空值,请先同步物料主数据',
        UpdatePline: '更新生产线'
      },
      ProductionOrderDetail: {
        BaseNum: '生产订单号',
        ProductionLine: '生产线',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编码',
        ItmsGrpName: '物料组名称',
        PlanQuantity: '计划数量',
        PlanQuantityUnit: '计划数量单位',
        OpenQuantity: '未领数量',
        OpenQuantityUnit: '未领数量单位',
        FulfilledQuantity: '已领数量',
        FulfilledQuantityUnit: '已领数量单位',
        StockingQty: 'WMS备料数量',
        title: '生产物料清单',
        select: '请选择物料',
        notFound: '未找到订单相关的物料信息',
        LineItemGroupID: '站点(行项目组编号)'
      },
      ProductionMaterialRequirements: {
        ReqID: '需求ID',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        BinLocationCode: '线边仓库位编号',
        BinLocationName: '线边仓库位名称',
        BaseLine: '行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        BOMUnit: 'BOM单位',
        BOMReqQty: '需求数量(BOM单位)',
        ConversionRate: '单位转换率',
        Unit: '库存单位',
        ReqQty: '需求数量(库存单位)',
        StockingQty: '已备货数量',
        StockQty: '线边仓库位库存',
        MaterialQty: '生产耗料数量',
        SurplusQty: '剩余库存量',
        Remark: '备注',
        IsDelete: '删除标示',
        CUser: '创建人',
        CTime: '创建时间',
        MUser: '更新人',
        MTime: '更新时间',
        DUser: '删除人',
        DTime: '删除时间'
      },
      ReturnScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '车间退料扫描记录'
      },
      BarCodeReturn: {
        BarID: '条码ID',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '包装数量',
        Unit: '库存单位',
        PrintTemplate: '标签模板',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        title: '车间退料标签'
      },
      BarCode: {
        BarID: '条码ID',
        BarCode: '条码',
        BoxBarCode: '包装箱码',
        BatchNum: '批次',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '包装数量',
        Unit: '库存单位',
        PrintTemplate: '标签模板',
        IStatus: '质检状态',
        title: '自制品标签',
        editSameOrder: '编辑状态下请选择同一物料的生产订单',
        productType: '成品类型',
        product: {
          half: '半成品',
          whole: '成品',
          raw: '原材料'
        }
      },
      InspectionScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        IStatus: '质检状态',
        title: '生产自制品质检扫描记录'
      },
      OutScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        PTime: '生产日期',
        BaseLine: '行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '生产发料扫描记录',
        ERPDocNum: 'SAP单据号'
      },
      InScan: {
        ScanID: '扫描ID',
        DocNum: '扫描单号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        PLine: '生产线',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '扫描数量',
        Unit: '库存单位',
        InWhsCode: '入库仓库编号',
        InWhsName: '入库仓库名称',
        InRegionCode: '入库区域编号',
        InRegionName: '入库区域名称',
        InBinLocationCode: '入库库位编号',
        InBinLocationName: '入库库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '生产自制品入库扫描记录'
      },
      OrderClose: {
        Oid: 'ID',
        BaseEntry: '生产订单编号',
        BaseNum: '生产订单号',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '生产订单关闭记录',
        ProductionLine: '生产线',
        ProductID: '产品编号',
        ProductDescription: '产品名称',
        PlannedQuantity: '计划数量',
        PlannedStartDate: '计划开始时间',
        BtnSelectProductionOrder: '选择生产订单',
        BtnCloseProductionOrder: '关闭生产订单',
        Message: {
          IsExistedProductionOrder: '已经选择过的生产订单，请直接进行关闭操作！'
        }
      }
    },
    SD: {
      SD_DeliveryWave: {
        DocNum: '波次单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        CustomerAdd: '客户地址',
        ShipTime: '备货日期',
        PUser: '责任人',
        Status: '状态',
        Forwarder: '货代',
        Remark: '备注',
        title: '销售交货波次任务单'
      },
      SD_DeliveryWaveDetailed: {
        DocNum: '波次单号',
        BaseEntry: '销售订单编号',
        BaseNum: '销售订单号',
        BaseLine: '行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '备货数量',
        Boxes: '箱数',
        NetWeight: '净重',
        GrossWeight: '毛重',
        Pallet: '每箱数量',
        PalletNum: '箱数',
        Metre: '每根米数',
        MetreSum: '总米数',
        title: '销售交货波次任务单明细'
      },
      saleOrder: {
        UUID: '销售订单编号',
        ID: '销售订单号',
        ProductID: '物料件号',
        Name: '物料名称',
        Quantity: '备货数量',
        QuantityTypeCode: '单位',
        UnitOfMeasure: '计量单位',
        SalesUnitParty: '销售单位',
        NetWeight: '净重',
        GrossWeight: '毛重',
        PalletNum: '每箱数量',
        Metre: '每根米数',
        LineID: '销售单行号',
        select: '选择销售订单',
        title: '销售订单明细'
      },
      SD_StockingScan: {
        DocNum: '扫描单号',
        WaveNum: '波次单号',
        BaseEntry: '销售订单编号',
        BaseNum: '销售订单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        BaseLine: '行号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '扫描数量',
        SumQty: '汇总数量',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsDelivery: '是否已装箱',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '销售备货单',
        IsDeliveryState: '发货状态',
        DeliveryState: {
          isDelivery: '已发货',
          unDelivery: '未发货'
        }
      },
      SD_DeliveryScan: {
        DocNum: '扫描单号',
        WaveNum: '波次单号',
        StockupDoc: '备货单号',
        BaseEntry: '销售订单编号',
        BaseNum: '销售订单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        BaseLine: '行号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '扫描数量',
        SumQty: '汇总数量',
        OutWhsCode: '转出仓库编号',
        OutWhsName: '转出仓库名称',
        OutRegionCode: '转出区域编号',
        OutRegionName: '转出区域名称',
        OutBinLocationCode: '转出库位编号',
        OutBinLocationName: '转出库位名称',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsDelivery: '是否已装箱',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '销售发货单',
        Message: {
          OnePostShuldBeOneSaleOrder: '一次过账只能针对一个销售订单'
        }
      },
      SD_Packing: {
        DocNum: '装箱单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        CustomerAdd: '客户地址',
        Container: '集装箱号',
        Forwarder: '货代',
        ShipmentNo: '交货单号',
        CustomsNum: '报关号',
        title: '销售装箱单',
        ShipmentID: '运单号',
        titlesub: '装箱单明细',
        CustomerNum: '客户单号'
      },
      SD_PackingDetailed: {
        DocNum: '装箱单号',
        ScanNum: '扫描单号',
        WaveNum: '波次单号',
        BaseEntry: '销售订单编号',
        BaseNum: '销售订单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        BaseLine: '行号',
        BoxBarCode: '包装码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '扫描数量',
        title: '销售装箱单明细'
      },
      SAP_SD_SaleOrder: {
        SalesOrderID: '销售订单号',
        ReceivingPartyCode: '收货方编号',
        ReceivingPartyName: '收货方名称',
        ReceivingPartyAdd: '收货方地址',
        OrderStatus: '订单状态',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        ItemID: '项目行号',
        ProductID: '产品编号',
        ProductDescription: '产品名称',
        Quantity: '数量',
        UnitCode: '单位',
        EndDeliveryTime: '要求日期',
        datasource: '查询类型',
        fromRemote: '远程SAP查询',
        fromLocal: '本地查询',
        title: '销售订单项目明细',
        syncSuccess: '同步成功',
        syncNotData: '未同步到相关数据',
        PlaseInSaleOrder: '请输入需要同步的销售订单号'
      },
      SD_BarCodeCustomer: {
        BoxBarCode: '包装箱码',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        BaseEntry: '销售订单编号',
        BaseNum: '销售订单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        CustomerNum: '客户单号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '数量',
        ShipmentID: '交货单号',
        Version: '版本号',
        PrintTemplate: '标签模板',
        title: '销售客户标签'
      },
      sddelivergoodsOrder: {
        BaseEntry: '销售订单编号',
        BaseNum: '销售订单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        BaseLine: '行号',
        BoxBarCode: '包装箱码',
        BarCode: '条码',
        BatchNum: '批次',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '扫描数量',
        PrintTemplate: '标签模板',
        title: '销售发货装箱信息',
        select: '选择销售发货装箱'
      },
      SD_BarCodeReturn: {
        BaseEntry: '退货任务申请单号',
        BaseNum: '销售订单号',
        BaseLine: '销售订单行号',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '标签日期',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '数量',
        InspectionScanStatus: '质检状态',
        PrintTemplate: '标签模板',
        title: '销售退货标签',
        SelectDate: '选择日期',
        updateinfo: '无法更新',
        addinfo: '无法添加',
        CheckQty: '标签数量超过退货任务数量，不允许保存'
      },
      ReturnRequest: {
        BaseEntry: '退货任务申请单号',
        BaseNum: '销售订单号',
        BaseLine: '销售订单行号',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '退货日期',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        Unit: '单位',
        Qty: '数量',
        title: '选择销售退货任务申请',
        select: '退货任务申请单明细'
      },
      SD_ReturnScan: {
        DocNum: '扫描单号',
        BaseEntry: '销售退货申请单编号',
        BaseNum: '销售退货申请单号',
        CustomerCode: '客户编号',
        CustomerName: '客户名称',
        BarCode: '条码',
        BatchNum: '批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Unit: '库存单位',
        Qty: '数量',
        InWhsCode: '转入仓库编号',
        InWhsName: '转入仓库名称',
        InRegionCode: '转入区域编号',
        InRegionName: '转入区域名称',
        InBinLocationCode: '转入库位编号',
        InBinLocationName: '转入库位名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        title: '销售退货单'
      }
    },
    Sys: {
      Sys_User: {
        UserID: '用户ID',
        OrganizationID: '所属机构',
        OrganizationDesc: '机构描述',
        UserName: '用户姓名',
        FrgnName: '英文名字',
        LoginAccount: '登录账号',
        LoginPassword: '登录密码',
        Gender: '性别',
        Birthday: '出生日期',
        Email: '电子邮箱',
        Mobile: '移动手机',
        Telphone: '联系电话',
        IsEnable: '是否可用',
        IsSupplier: '是否供应商',
        UserRole: '角色分类',
        TipMessage: {
          ResetPasswordConfirm: '您确认要重置所选用户的密码？'
        },
        FormValidator: {
          ValidateIP: '您输入的IP地址不正确！',
          ValidateEmail: '您输入的邮箱格式不正确！',
          ValidateMobile: '请输入正确的手机号'
        },
        Buttons: {
          SetUserRole: '设置角色',
          ResetPassword: '重置密码'
        },
        ResetPasswordDlg: {
          LoginAccount: '重置账户',
          NewPassword: '请输入新密码',
          ConfirmNewPassword: '请再次输入新密码',
          NotConsistent: '两次输入的密码不一致！'
        },
        Titles: {
          SetUserRole: '设置用户角色'
        },
        Placeholder: {
          Keyword: '登陆账号 | 用户姓名'
        }
      },
      Sys_Role: {
        RoleID: '角色ID',
        RoleDesc: '角色描述',
        ResourceElement: {
          PermissionSettingDialogTitle: '权限设置'
        },
        PanelTab: {
          Admin: '管理后台',
          PDA: 'PDA客户端'
        }
      },
      Sys_Mail: {
        MailID: '邮件ID',
        MessageID: '消息ID',
        UserID: '收件人ID',
        UserName: '收件人',
        MessageTypeID: '消息分类ID',
        MessageTypeDesc: '消息分类',
        MailSubject: '邮件标题',
        MailBody: '邮件正文',
        SenderMail: '发件人邮箱',
        ReceiverMail: '收件人邮箱',
        SendTime: '发件时间'
      },
      Sys_Message: {
        MessageID: '消息ID',
        MessageTypeID: '消息分类ID',
        MessageTitle: '标题',
        MessageBody: '消息正文',
        NotifyType: '通知方式',
        MessagePublisher: '发布者',
        MessagePublishTime: '发布时间'
      },
      Sys_Organization: {
        OrganizationID: '机构ID',
        FathOrganizationID: '上级机构',
        OrganizationCode: '机构编号',
        OrganizationLevel: '机构层级',
        OrganizationDesc: '机构描述'
      },
      Sys_SwithConfig: {
        ConfigID: '开关ID',
        ConfigCode: '开关设置编号',
        ConfigDesc: '开关设置描述',
        SwitchValue: '开关值'
      },
      Sys_ApiLogConfig: {
        ApiLogConfigID: 'API配置ID',
        ApiUrl: 'API相对路径',
        ApiType: 'API分类',
        RequestSupportType: '请求支持方式',
        BelongModule: '所属模块',
        ApiDescription: '功能描述'
      },
      Sys_DbBackup: {
        DbBackupID: '数据库备份记录ID',
        DbName: '数据库名称',
        BackupName: '备份名称',
        BackupFilePath: '备份文件路径',
        IsCompressed: '是否压缩备份',
        DbServer: '服务器地址'
      },
      Sys_DbBackupConfig: {
        DbBackupConfigID: '备份配置ID',
        DbName: '数据库名称',
        BackupFilePath: '备份文件路径',
        RestoreFilePath: '还原数据库路径',
        IsCompressed: '是否压缩',
        DbServer: '服务器地址',
        DbAccount: '登录账号',
        DbPassword: '登录密码',
        Remark: '备注',
        BackupSetting: '备份配置'
      },
      Sys_Log: {
        LogID: '系统日志ID',
        ApiUrl: 'API路径',
        LogType: '日志类型', // [1: 业务日志 2:Debug日志]
        ApiType: '功能分类',
        ApiModule: '功能模块',
        ApiDescription: 'API功能描述',
        OperateUser: '操作用户',
        ClientHost: '客户端主机名',
        ClientIP: '客户端IP',
        ClientBrowser: '客户端浏览器',
        ClientOS: '客户端操作系统',
        RequestType: '请求方法', // [GET,POST,DELETE]
        RequestParms: '请求参数',
        ResponseData: '响应数据',
        Result: '成功/失败',
        Placeholder: {
          SearchCondition: '功能模块 | 功能描述'
        }
      },
      Sys_MailServerConfig: {
        MailServerID: '邮件服务器ID',
        MailServerHost: '发件服务器地址',
        MailServerPort: '服务器端口',
        MailServerAccount: '发件邮箱账号',
        MailServerPassword: '发件邮箱密码',
        SenderDisplayName: '发件人显示名'
      },
      Sys_MessageNotifySetting: {
        MessageNotifySettingID: '分类通知设置ID',
        MessageTypeID: '消息分类ID',
        RoleID: '角色ID',
        MessageType: '消息分类',
        IsNotifyByEMail: '邮件通知'
      },
      Sys_MessageType: {
        MessageTypeID: '消息分类ID',
        MessageTypeDesc: '消息分类描述'
      },
      Sys_Resource: {
        ResourceID: '资源ID',
        FathResourceID: '上级资源ID',
        ResourceTitle: '',
        ResourceName: '资源名称',
        ResourceType: '资源类型',
        RedirectUrl: '跳转',
        ResourcePath: '资源路径',
        ResourceLevel: '资源层级',
        ResourceIcon: '图标',
        IsCache: '是否缓存',
        Component: '引用组件',
        Layout: '布局',
        Role: '访问角色',
        AppID: '所属客户端'
      },
      Sys_RoleResource: {
        RoleResourceID: '角色资源ID',
        ResourceID: '资源ID',
        RoleID: '角色ID'
      },
      Sys_UserMessage: {
        UserMessageID: '用户消息ID',
        MessageID: '消息ID',
        MessageTypeID: '消息分类ID',
        MessageTypeDesc: '消息分类',
        UserID: '用户ID',
        UserName: '通知用户',
        MessageTitle: '消息标题',
        MessageContent: '消息内容',
        NotifyType: '通知方式',
        Publisher: '发布者',
        PublishTime: '发布时间',
        IsReaded: '是否阅读',
        ReadTime: '阅读时间',
        Remark: '备注',
        Placeholder: {
          Keyword: '消息标题 | 消息内容'
        }
      },
      Sys_UserRole: {
        UserRoleID: '用户角色ID',
        RoleID: '角色ID',
        UserID: '用户ID'
      },
      Sys_UserSapAccount: {
        UserSapAccountID: '用户SAP账号ID',
        UserID: '用户ID',
        SapAccount: '登录账号',
        SapPassword: '登录密码'
      }
    },
    RPT: {
      // 业务报表
      RPT_Stock: {
        titleDetail: '库存明细',
        StockID: '库存ID',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BoxBarCode: '包装箱码',
        BarCode: '条码',
        BatchNum: '批次',
        SupplierBatch: '供应商批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '库存数量',
        Unit: '库存单位',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称'
      },
      RPT_FTT: {
        CreateDate: '创建日期',
        FType: '成品类别',
        ProductionLineID: '生产线',
        FTTID: 'FTTID',
        ProductID: '产品编号',
        StationID: '站点ID',
        NCConditionDescription: '不良描述',
        HandlingRecommendations: '处理建议',
        RejectQuantity: '拒收数量',
        InputQuantity: '投入数量',
        OutputQuantity: '产出数量',
        M: '半成品',
        P: '成品'
      },
      RPT_PO_InOut: {
        SupplierCode: '供应商编号',
        ItemCode: '物料件号',
        CTime: '日期',
        InQty: '收货数量',
        SQty: '上架数量',
        RQty: '退货数量',
        sumQty: '汇总数量'
      },
      RPT_StockMove: {
        ItemCode: '物料件号',
        ItemName: '物料名称',
        WhsCode: '仓库编码',
        WhsName: '仓库名称',
        RegionCode: '区域编码',
        RegionName: '区域名称',
        BinLocationCode: '库位编码',
        BinLocationName: '库位名称',
        BatchNum: '批次',
        Unit: '单位',
        DocNum: '业务单据编码',
        CUser: '操作员',
        CTime: '创建时间',
        Qty: '数量',
        DType: '业务单据类型'
      },
      RPT_PLineOutPut: {
        CTime: '日期',
        PLine: '生产线',
        ItemCode: '产品编号',
        Qty: '数量',
        SumQty: '总数量'
      },
      RPT_PO_Inspection: {
        BatchNum: '批次',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        Qty: '送检批数',
        OkQty: '合格批数',
        NoQty: '不合格批数',
        Status: '状态(是否质检)',
        IUser: '质检人',
        ITime: '质检时间',
        CTime: '创建时间',
        RM_FPY: '合格率',
        yes: '是',
        no: '否'
      },
      RPT_SD_Delivery: {
        BaseNum: '销售订单号',
        BaseLine: '行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        EndDeliveryTime: '客户需求时间',
        CTime: '出货时间',
        CustomerCode: '客户编码',
        Qty: '出货数量',
        ActualDate: '实际出货时间',
        DiffDate: '与承诺日期的差异',
        ShipmentNo: '交货单号',
        Container: '集装箱号',
        CustomsNum: '报关单号'
      },
      RPT_SupplierItem_View: {
        SupplierCode: '供应商编号',
        ItemCode: '物料件号',
        CTime: '使用时间',
        OKQTY: '合格数量',
        NoQty: '不合格数量',
        Qty: '使用数量',
        StockQTY: '库存数量'
      },
      RPT_V_NotPostStockMove: {
        DocNum: '扫描单号',
        BarCode: '条码号',
        BatchNum: '批次号',
        BaseNum: '订单号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        WhsCode: '仓库编号',
        WhsName: '仓库名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        Qty: '数量',
        Unit: '单位',
        OperationType: '操作类别',
        CTime: '创建时间'
      },
      RPT_StockAge: {
        StockID: '库存ID',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BoxBarCode: '包装箱码',
        BarCode: '条码',
        BatchNum: '批次',
        SupplierBatch: '供应商批次',
        PTime: '生产日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        Qty: '库存数量',
        Unit: '库存单位',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        AgeQty: '库龄预警值',
        StockAge: '库龄'
      },
      RPT_DeliverySchedule: {
        PlanNum: '交货计划单号',
        DeliveryTime: '交货计划日期',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        BaseNum: '采购订单号',
        BaseLine: '采购订单行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        PurchaseOrderQty: '采购订单数量',
        PlanQty: '计划数量',
        DeliveryedQty: '已交货数量',
        OkQty: '合格数量',
        NoQty: '不合格数量',
        NoInspectionQty: '未检验数量',
        StockInQty: '已入库数量',
        DeliveryPlanStatus: '交货状态'
      },
      RPT_BarCodeRetrospect: {
        docType: '业务类型',
        DocNum: '扫描单号',
        BaseNum: '订单号',
        CustomerCode: '供应商/客户编号',
        CustomerName: '供应商/客户名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        BoxBarCode: '包装箱码',
        BarCode: '条码号',
        BatchNum: '批次',
        Qty: '数量',
        WhsCode: '仓库编号',
        WhsName: '仓库名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        BinLocationCode: '库位编号',
        BinLocationName: '库位名称',
        CTime: '创建时间',
        CUser: '创建人',
        ButtonGroup: {
          PO_In: '采购入库',
          PP_Out: '生产发货',
          SD_Out: '销售发货',
          PP_In: '生产入库'
        },
        BarCodeE: '采购条码|物料号',
        BarCodeS: '自制品条码|包装箱码'
      },
      RPT_DeliveryOnTimeRate: {
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        TotalDeliveryItem: '计划总交货行数',
        OnTimeDeliveryItem: '及时交货行数',
        UnOnTimeDeliveryItem: '未及时交货行数',
        TotalQty: '计划总数量',
        TotalDeliveryedQty: '及时交货数量',
        TotalUnOnTimeDeliveryQty: '未及时交货数量',
        OTDRate: '及时交货率'
      },
      RPT_StockDiff: {
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        WmsQty: 'WMS库存数量',
        WmsUnit: 'WMS库存单位',
        NotPostQty: 'WMS未过账数量',
        SapQty: 'SAP库存数量',
        SapUnit: 'SAP库存单位',
        DiffQty: '库存差异数量',
        DiffUnit: '库存差异单位',
        ConsignQty: '寄售数量',
        ButtonGroup: {
          PostAdjust: '调整'
        }
      },
      RPT_StockDiffHistory: {
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        RegionCode: '区域编号',
        RegionName: '区域名称',
        AdjustQty: '调整数量',
        AdjustUnit: '调整单位',
        AdjustType: '调整方向'
      }
    },
    SAP: {
      OWOR: {
        // SAP生产订单
        DocEntry: '编号',
        DocNum: '生产订单号',
        PostDate: '订单日期',
        DocType: '单据类型',
        DocStatus: '单据状态',
        PItemCode: '产品物料件号',
        PItemName: '产品物料名称',
        PlannedQty: '订单数量',
        CmpltQty: '已入库数',
        PInvntryUom: '产品库存单位',
        LineStatus: '行状态',
        LineNum: '行号',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        BaseQty: '基本数量',
        Quantity: '发料数量',
        IssuedQty: '已发料数',
        InvntryUom: '库存单位'
      },
      OPOR: {
        // SAP采购订单
        DocEntry: '编号',
        DocNum: '采购单号',
        TaxDate: '订单日期',
        DocDueDate: '交货日期',
        DocType: '单据类型',
        DocStatus: '单据状态',
        CardCode: '供应商编号',
        CardName: '供应商名称',
        LineStatus: '行状态',
        LineNum: '行号',
        Quantity: '订单数量',
        OpenQty: '未清数量',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        BuyUnitMsr: '采购单位',
        NumInBuy: '单位转换率',
        InvntryUom: '库存单位'
      },
      ORDR: {
        // SAP销售订单
        DocEntry: '编号',
        DocNum: '单号',
        TaxDate: '订单日期',
        DocDueDate: '交货日期',
        DocType: '单据类型',
        DocStatus: '单据状态',
        CardCode: '客户编号',
        CardName: '客户名称',
        LineStatus: '行状态',
        LineNum: '行号',
        Quantity: '订单数量',
        OpenQty: '未清数量',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        SalUintMsr: '销售单位',
        NumInSale: '单位转换率',
        InvntryUom: '库存单位',
        title: '销售订单'
      },
      ORRR: {
        // SAP销售退货申请
        DocEntry: '编号',
        DocNum: '退货单号',
        TaxDate: '订单日期',
        DocType: '单据类型',
        DocStatus: '单据状态',
        CardCode: '供应商编号',
        CardName: '供应商名称',
        LineStatus: '行状态',
        LineNum: '行号',
        Quantity: '订单数量',
        OpenQty: '未清数量',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        SalUintMsr: '销售单位',
        NumInSale: '单位转换率',
        InvntryUom: '库存单位',
        title: '销售退货申请'
      },
      OITM: {
        // SAP物料主数据
        ItemCode: '物料件号',
        ItemName: '物料名称',
        ItmsGrpCode: '物料组编号',
        ItmsGrpName: '物料组名称',
        ItemType: '物料类型',
        BuyUnitMsr: '采购单位',
        NumInBuy: '采购单位转换率',
        InvntryUom: '库存单位',
        SalUintMsr: '销售单位',
        NumInSale: '销售单位转换率'
      }
    },
    Message: {
      // 后台返回错误信息处理的语言包
      PODeliveryPlanAssignWarning: '只有未下发状态的交货计划计划才可以下发！',
      PODeliveryPlanConfirmWarning: '只有下发状态的计划才可以确认！',
      PODeliveryPlanUpdateWarning_supplier: '一个交货计划内不可以有多个供应商',
      PODeliveryPlanUpdateWarning_time: '一个交货计划必须是同一天时间',
      PODeliveryPlanNumberWarning: '采购交货计划数量不允许超过采购订单数量',
      PODeliveryNoteDeleteWarning: '送货单已进行收获报检，不得删除！',
      PODeliveryNoteCreatedWarning: '所选中的标签已生成送货单，请不要重复生成！',
      POBarCodeNumberWarning: '标签数量不能超过交货计划数量！',
      POBarCodeEditWarning: '当前标签已创建送货单，不能进行编辑！',
      POBarCodeDeleteWarning: '选中标签已创建送货单，不能进行删除！',
      POInspectionDelete_inspection: '要删除的采购报检明细中含有质检信息，不允许删除！',
      POInspectionDelete_posting: '要删除的采购报检明细中含有已过账数据，不允许删除！',
      POInspectionScanDeleteWarning: '已经移库的数据不允许删除！',
      POInspectionScanSubmitWarning: '提交的数据中存在异常数据',
      POInspectionScanStockWarning: '质检合格数量不得超过库存数量',
      PODisShelfWarning: '寄售不合格商品不能扫描进行采购上架!',
      PODeletePostingWarning: '已过账数据不允许删除!',
      POITransferScanDeleteWarning: '已经过账的数据不允许删除！',
      POITransferScanStockWarning: '移库数量不得超过库存数量',
      POITransferScanQualifiedWarning: '移库数量不得超过质检合格数量',
      POITransferScanSubmitWarning: '提交的数据中存在异常数据',
      PODisDeleteInspectionWarning: '已进行退供区质检，不得执行删除操作!'
    },
    QM: {
      QM_ConsignmentNote: {
        InspectionNum: '报检单号',
        BaseLine: '行号',
        PurchaseNum: '采购单号',
        Batch: '批次',
        InspectionTime: '报检日期',
        ItemCode: '物料件号',
        ItemName: '物料名称',
        Unit: '单位',
        InspectionCount: '报检次数',
        QualifiedQty: '合格数量',
        UnqualifiedQty: '不合格数量',
        SupplierCode: '供应商编号',
        SupplierName: '供应商名称',
        IsPosted: '是否过账',
        PostUser: '过账人',
        PostTime: '凭证日期',
        CUser: '创建人',
        CTime: '创建时间'

      }
    }
  },
  api: {
    Sys: {
      Sys_User: {
        ExistedUser: '该用户账号已经存在!',
        AccountEnabled: '该账号已冻结，请联系系统管理员！',
        AuthenticationFailed: '用户名或者密码错误！',
        NeedUpdatePassword: '密码重置或者首次登录需要更新密码！',
        OldPasswordIsWrong: '旧密码错误请输入正确密码！'
      }
    },
    MD: {
      MD_Stock: {
        NotHaveStockData: '所选数据没有在当前库位下没有数据',
        NotHaveEnoughStockData: '所选数据没有在当前库位下库存不足'
      }
    }
  },
  Dictionary: {
    GenderMap: {
      // 性别
      One: '男',
      Two: '女'
    },
    AccountIsEnableMap: {
      // 用户账号是否可用
      TrueValue: '正常',
      FalseValue: '冻结'
    },
    YesNoMap: {
      YesValue: '是',
      NoValue: '否'
    },
    EnableMap: {
      // 用户账号是否可用
      TrueValue: '有效',
      FalseValue: '禁用'
    },
    UserRoleMap: {
      One: '系统管理员',
      Two: '业务人员'
    },
    IsReadedMap: {
      TrueValue: '已读',
      FalseValue: '未读'
    },
    PO_DeliveryPlan_Status: {
      All: '全部',
      One: '未下发',
      Two: '已下发',
      Three: '已确认',
      Four: '已创建条码',
      Five: '已创建送货单',
      Six: '已完成'
    },
    PP_Order_Status: {
      One: '准备中',
      Two: '下达',
      Three: '开始',
      Four: '完成',
      Five: '关闭',
      Six: '取消'
    },
    SD_Order_Status: {
      One: '未处理',
      Two: '处理中',
      Three: '已完成',
      Four: '准备中'
    },
    SD_DeliveryWave_Status: {
      Zero: '未开始',
      One: '已备货',
      Two: '已完成'
    },
    IsDelivery: {
      yes: '已生成',
      no: '未生成'
    }
  }
}
