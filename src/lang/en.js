export default {
  route: {
    dashboard: 'Home',
    documentation: 'Document',
    guide: 'Guide pages',
    permission: 'Permission test page',
    rolePermission: 'Role Permissions',
    pagePermission: 'Page permissions',
    directivePermission: 'Command authority',
    icons: 'icon',
    components: 'Component',
    tinymce: 'Rich text editor',
    markdown: 'Markdown',
    jsonEditor: 'JSON editor',
    dndList: 'List drag and drop',
    splitPane: 'Splitpane',
    avatarUpload: 'Avatar upload',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: 'Widget',
    backToTop: 'Back to top',
    dragDialog: 'Drag and drop Dialog',
    dragSelect: 'Drag and drop Select',
    dragKanban: 'Drag and drop board',
    charts: 'chart',
    keyboardChart: 'Keyboard chart',
    lineChart: 'line chart',
    mixChart: 'Mixed chart',
    example: 'Comprehensive example',
    nested: 'Route nesting',
    menu1: 'Menu 1',
    'menu1-1': 'Menu 1-1',
    'menu1-2': 'Menu 1-2',
    'menu1-2-1': 'Menu 1-2-1',
    'menu1-2-2': 'Menu 1-2-2',
    'menu1-3': 'Menu 1-3',
    menu2: 'Menu 2',
    Table: 'Table',
    dynamicTable: 'dynamic Table',
    dragTable: 'Drag and drop Table',
    inlineEditTable: 'Edit within Table',
    complexTable: 'Comprehensive Table',
    apiDescription: 'Api Business description',
    tab: 'Tab',
    form: 'Form',
    createArticle: 'Create an article',
    editArticle: 'Edit article',
    articleList: 'Article list',
    errorPages: 'Error page',
    page401: '401',
    page404: '404',
    errorLog: 'Error log',
    excel: 'Excel',
    exportExcel: 'Export Excel',
    selectExcel: 'Export Selected item',
    mergeHeader: 'Export multi-level header',
    uploadExcel: 'Upload Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: 'Skinning',
    clipboardDemo: 'Clipboard',
    i18n: 'globalization',
    externalLink: 'Outer chain',
    profile: 'Personal center',
    devroot: 'Develop a convenient menu',
    vueicons: 'System support ICON overview',
    Sys: {
      Sys: 'System Management',
      Sys_User: 'User Management',
      Sys_DbBackup: 'database backup',
      Sys_Log: 'Operation log',
      Sys_Mail: 'Mail management',
      Sys_MailServerConfig: 'Mail configuration',
      Sys_UserMessage: 'notification',
      Sys_MessageNotifySetting: 'Notification settings',
      Sys_Organization: 'organization',
      Sys_Resource: 'Menu management',
      Sys_Role: 'Role management',
      Sys_SwithConfig: 'Switch setting',
      Sys_Permission: {
        Add: 'Add to',
        Edit: 'Edit',
        Delete: 'Delete',
        Export: 'Export',
        SetRole: 'Set role',
        ResetPassword: 'Reset Password',
        Permission: 'Setting permissions',
        Enable: 'Enable',
        Disable: 'Disable',
        Backup: 'Backup',
        Reduction: 'Reduction',
        Set: 'Setting'
      },
      Sys_UserSapAccount: 'SAP account'
    },
    MD: {
      MD: 'Basic data',
      MD_BinLocation: 'Location master data',
      MD_Dictionary: 'Data Dictionary',
      MD_Region: 'Regional master data',
      MD_Warehouse: 'Warehouse master data',
      MD_Permission: {
        Add: 'Add to',
        Edit: 'Edit',
        Delete: 'Delete',
        Export: 'Export'
      },
      MD_Item: 'Material data',
      MD_Supplier: 'Supplier data',
      MD_Customer: 'Customer data',
      MD_SalePacking: 'Customer material packaging specifications',
      MD_SalePackingDetailed: 'Customer Material Packaging Specification Management',
      MD_LabelTemplate: 'Label template',
      MD_POInspectionPlan: 'Inspection plan',
      MD_POInspectionPlanDetailed: 'Edit inspection plan',
      MD_POInspectionGrade: 'Inspection level',
      MD_BinLimit: 'Location limit',
      MD_ProduceLineCapacity: 'Production Line Capacity'
    },
    PO: {
      PO: 'Purchasing management',
      PO_BarCode: 'Purchase label',
      PO_BarCodeDetailed: 'Purchase label details',
      PO_DeliveryPlan: 'Purchase delivery schedule',
      PO_DeliveryPlanDetailed: 'Purchase delivery schedule details',
      PO_DeliveryNote: 'Purchase delivery note',
      PO_DeliveryNoteDetailed: 'Purchase delivery note details',
      PO_Inspection: 'Purchasing quality inspection',
      PO_ShelfScan: 'Quatified goods storage',
      PO_ConsignIn: 'Consignment purchase warehousing',
      PO_ConsignInEdit: 'Consignment purchase warehousing Edit',
      PO_DisShelfScan: 'Non-conforming product',
      SAP_PO_PurchaseOrder: 'Purchase Order',
      PO_ReturnScan: 'Purchase return',
      PO_ITransferScan: 'Return zone movement record',
      PO_Permission: {
        Add: 'Add to',
        Edit: 'Edit',
        Delete: 'Delete',
        Assign: 'Issued',
        Affirm: 'Confirm',
        Merge: 'Merge',
        Export: 'Export',
        Print: 'Print',
        CreateNote: 'Generate delivery note',
        Posting: 'Posting'
      }
    },
    SD: {
      SD: 'Sales management',
      SD_DeliveryWave: 'Sales wave task list',
      SD_DeliveryWaveDetailed: 'Sales wave task list detail',
      SD_StockingScan: 'Sales stock list',
      SD_DeliveryScan: 'Sales invoice',
      SD_Packing: 'Sales packing list',
      SD_PackingDetailed: 'Sales packing list details',
      SD_BarCodeCustomer: 'Customer label',
      SD_BarCodeCustomerDetailed: 'Customer label detail',
      SD_BarCodeReturn: 'Sales return label',
      SD_BarCodeReturnDetailed: 'Sales return label detail',
      SD_ReturnScan: 'Sales return', SAP_SD_SaleOrder: 'Sales Order'
    },
    MM: {
      MM: 'Warehouse management',
      MM_BarCode: 'Material label',
      MM_BarCodeEdit: 'Making material labels',
      MM_InScan: 'Other receipt records',
      MM_InScanEdit: 'Other receipt editor',
      MM_OutScan: 'Other shipping records',
      MM_OutScanEdit: 'Other shipping editors',
      MM_TransferScan: 'Material movement',
      MM_TakeStockPlan: 'Inventory',
      MM_TakeStockPlanDetailed: 'Inventory plan detail',
      MM_TakeStockScan: 'Inventory scan',
      MM_TakeStockDiff: 'Count difference confirmation',
      MM_Permission: {
        Add: 'Add to',
        Edit: 'Edit',
        Delete: 'Delete',
        Export: 'Export',
        Posting: 'Posting',
        Print: 'Print',
        Compose: 'Group support',
        Affirm: 'Confirm'
      },
      MM_BarCodeBox: 'Box code management'

    },
    PP: {
      PP: 'Production management',
      PP_MaterialReq: 'Production material demand',
      PP_StockingWave: 'Production stocking wave order',
      PP_StockingWaveDetailed: 'Production stocking list',
      PP_StockingScan: 'Production stock',
      PP_Scrapped: 'Production retirement',
      PP_ReturnScan: 'Production return',
      PP_BarCodeReturn: 'Production return label',
      PP_BarCodeReturnEdit: 'Production return label editing',
      PP_BarCode: 'Production label',
      PP_BarCodeEdit: 'Production self-made label editing',
      PP_InspectionScan: 'Production of self-made quality inspection',
      PP_OutScan: 'Production and distribution',
      PP_InScan: 'Finished product submission',
      PP_FTTP: 'Poor production',
      PP_FTTPDetailed: 'Production defect',
      PP_FTTM: 'Production of defective raw materials',
      PP_FTTMDetailed: 'Production of defective raw materials',
      PP_OrderClose: 'Production order close',
      PP_ProductionOrder: 'Production order management'
    },
    QM: {
      QM: 'Quality Control',
      QM_POInspection: 'Purchasing quality inspection management',
      QM_POInspectionScan: 'Return inspection area quality inspection record',
      QM_Permission: {
        Delete: 'Delete',
        Export: 'Export',
        Qualify: 'Qualified',
        Disqualify: 'Failed',
        BatchDisqualify: 'Batch unqualified',
        Cancellation: 'Cancel quality inspection'
      }
    },
    RPT: {
      RPT: 'Business report',
      RPT_Stock: 'Inventory report',
      RPT_StockAge: 'Bank age report',
      RPT_DeliverySchedule: 'Delivery progress report',
      RPT_DeliveryOnTimeRate: 'Timely rate of distribution',
      RPT_StockDiff: 'SAP inventory variance',
      RPT_StockDiffHistory: 'History of SAP variance adjustment',
      RPT_BarCodeRetrospectS: 'Forward traceability Report',
      RPT_BarCodeRetrospectE: 'Reverse traceability Report',
      RPT_FTT: 'FTT traceability Report',
      RPT_PO_InOut: 'Supplier delivery return Report',
      RPT_StockMove: 'Inventory change history',
      RPT_PLineOutPut: 'Production line output report',
      RPT_PO_Inspection: 'RM-FPY Feed qualification rate',
      RPT_SD_Delivery: 'Sales delivery report',
      RPT_SupplierItem_View: 'Supplier material usage record',
      RPT_PP_MaterialReqKanban: 'Production Kanban Report',
      RPT_V_NotPostStockMove: 'Inventory report not posted'
    },
    SHM: {
      SHM: 'SupplierHealthManagement'
    },
    PDA: {
      PO: {
        PO: 'Purchasing management',
        PO_Inspection: 'Inspection scan',
        PO_ShelfScan: 'Qualified goods storage',
        PO_DisShelfScan: 'Non-conforming product',
        PO_ReturnScan: 'Return scan',
        PO_InspectionScan: 'Refund area inspection',
        PO_ITransferScan: 'Returning material movement'
      },
      PP: {
        PP: 'Production management',
        PP_StockingScan: 'Stock scanning',
        PP_InspectionFTTP: 'Quality inspection defective product confirmation',
        PP_FTTP: 'Warehouse defective product confirmation',
        PP_ReturnScan: 'Return scan',
        PP_InspectionScan: 'Homemade quality inspection',
        PP_InScan: 'Finished product submission scan'
      },
      SD: {
        SD: 'Sales management',
        SD_StockingScan: 'Stock scanning',
        SD_DeliveryScan: 'Sales delivery',
        SD_ReturnScan: 'Sales return'
      },
      MM: {
        MM: 'Warehouse management',
        MM_InScan: 'Other receipt',
        MM_OutScan: 'Other shipment',
        MM_TransferScan: 'Stock dump',
        MM_TakeStockScan: 'Count scan',
        MM_BoxBarCode: 'Package quality inspection scan',
        MM_BarCodeBox: 'Box code',
        MM_Vanning: 'Packing box',
        MM_Devanning: 'Unpacking the box'
      }
    }
  },
  Platform: {
    Layout: {
      Navbar: {
        Account: {},
        UpdatePassword: {
          LoginAccount: 'Login account',
          OldPassword: 'Please enter the old password',
          NewPassword: 'Please enter a new password',
          ConfirmNewPassword: 'Please enter your new password again'
        },
        Route: {
          UserInfo: 'Personal center',
          UpdatePassword: 'change Password'
        }
      }
    },
    Components: {
      HeaderSearch: {
        Placeholder: 'Functional search'
      }
    }
  },
  exception: {
    timeRangeError: 'Please enter the correct time frame'
  },
  navbar: {
    dashboard: 'Home',
    github: 'project address',
    logOut: 'sign out',
    profile: 'Personal center',
    theme: 'Skinning',
    size: 'Layout size'
  },
  login: {
    title: 'system login',
    logIn: 'log in',
    username: 'account number',
    password: 'password',
    any: 'Just fill it out',
    thirdparty: 'worth mentioning',
    thirdpartyTips: 'Local can not be simulated, please simulate with your own business！！！'
  },
  documentation: {
    documentation: 'Document',
    github: 'Github address'
  },
  permission: {
    addRole: 'New role',
    editPermission: 'Edit permission',
    roles: 'Your permission',
    switchRoles: 'Switching permissions',
    tips: 'In some cases, v-permission is not suitable. For example: element-UI\'s el-tab or el-table-column and other scenes that dynamically render dom. You can only do this by manually setting v-if.',
    delete: 'Delete',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  guide: {
    description: 'The guide page is useful for some people who enter the project for the first time. You can briefly describe the function of the project. This demo is based on',
    button: 'Open boot'
  },
  components: {
    documentation: 'document',
    tinymceTips: 'Rich text is a core feature of the management backend, but at the same time it is a place with lots of pits. In the process of selecting rich texts, I also took a lot of detours. The common rich texts on the market were basically used. I finally chose Tinymce for a balance. See the more detailed rich text comparison and introduction.',
    dropzoneTips: 'Due to the special needs of our business, we have to pass seven cattle, so we did not use third parties and chose our own packaging. The code is very simple, the specific code you can see here @/components/Dropzone',
    stickyTips: 'When the page is scrolled to the preset position, it will be attached to the top.',
    backToTopTips1: 'Scrolling to the specified position will bring up the top button in the lower right corner',
    backToTopTips2: 'Customizable button style, show/hide, height of appearance, position of return For text prompts, use element\'s el - tooltip element externally',
    imageUploadTips: 'Since I only have the vue@1 version when I use it, and it is not compatible with mockjs, I have modified it myself. If you want to use it, the official version is preferred.'
  },
  table: {
    dynamicTips1: 'Fixed header, sorted by header order',
    dynamicTips2: 'Do not pin headers, sort by click order',
    dragTips1: 'Default order',
    dragTips2: 'Drag and drop order',
    title: 'title',
    importance: 'importance',
    type: 'type',
    remark: 'remark',
    search: 'search',
    add: 'Add to',
    export: 'Export',
    reviewer: 'Reviewer',
    id: 'Serial number',
    date: 'time',
    author: 'Author',
    readings: 'Reading number',
    status: 'status',
    actions: 'operating',
    edit: 'Edit',
    publish: 'release',
    draft: 'draft',
    delete: 'Delete',
    cancel: 'cancel',
    confirm: 'confirm',
    keyword: 'keyword'
  },
  example: {
    warning: 'Creating and editing pages cannot be cached by keep-alive because keep-alive include does not currently support caching based on routing, so it is currently cached based on component name. If you want to achieve a similar caching effect, you can use a browser caching scheme such as localStorage. Or don\'t use keep - alive include to cache all pages directly.See details'
  },
  errorLog: {
    tips: 'Please click on the small bug icon in the upper right corner',
    description: 'The current management background is basically in the form of spa, which enhances the user experience, but it also increases the possibility of page problems, and a small negligence can lead to deadlock of the entire page. Fortunately, Vue official website provides a way to capture processing exceptions, where you can handle error handling or exception reporting.',
    documentation: 'Document introduction'
  },
  excel: {
    export: 'Export',
    selectedExport: 'Export selected items',
    placeholder: 'Please enter a filename (default excel-list)'
  },
  zip: {
    export: 'Export',
    placeholder: 'Please enter a file name (default file)'
  },
  pdf: {
    tips: 'Here we use window.print() to implement the function of downloading pdf.'
  },
  theme: {
    change: 'Skinning',
    documentation: 'Skinning document',
    tips: 'Tips: It is different from theme-pick on navbar, which is two different skinning methods, each with different application scenarios. Please refer to the documentation for details.。'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close other',
    closeAll: 'Close all'
  },
  settings: {
    title: 'System layout configuration',
    theme: 'Theme color',
    tagsView: 'Open Tags-View',
    fixedHeader: 'fixed Header',
    sidebarLogo: 'Sidebar Logo'
  },
  Common: {
    title: 'title',
    type: 'type',
    search: 'search',
    add: 'Add to',
    printview: 'Printing preview',
    export: 'Export',
    CountTotal: 'CountTotal',
    date: 'time',
    status: 'status',
    actions: 'operating',
    edit: 'edit',
    delete: 'delete',
    cancel: 'return',
    empty: 'empty',
    confirm: 'submit',
    confirmStatus: 'confirmStatus',
    isConfirm: 'Confirm',
    unConfirm: 'unConfirm',
    save: 'save',
    affirm: 'confirm',
    affirmdy: 'Confirm delivery',
    keyword: 'keyword',
    success: 'success',
    failed: 'failure',
    error: 'error', start: 'Start',
    NoDateIsNull: 'Time cannot be cleared',
    sapNoDiff: 'No inventory variance',
    compose: 'Group support',
    operationSuccess: 'Successful operation',
    operationFailed: 'operation failed',
    createSuccess: 'Created successfully',
    updateSuccess: 'update completed',
    deleteSuccess: 'successfully deleted',
    postSuccess: 'Posting success',
    select: 'select',
    justSingleSelection: 'Please select a single record',
    noSelection: 'Please select a record',
    view: 'see details',
    Remark: 'Remarks',
    IsDelete: 'Delete ID',
    CUser: 'Creator',
    CTime: 'Creation time',
    MUser: 'updater',
    MTime: 'Update time',
    DUser: 'Delete person',
    DTime: 'Delete time',
    Enable: 'Enable',
    Disable: 'Disable',
    posting: 'Posting',
    gdelivery: 'Generate delivery note',
    print: 'print',
    log: 'Registration',
    close: 'Close',
    grant: 'Issued',
    merge: 'merge',
    successf: 'carry out',
    Person: 'Designated person',
    cancellation: 'Cancel quality inspection',
    qtesting: 'Quality inspection',
    Packing: 'Generate packing list',
    printQty: 'Number of prints',
    timeFrom: 'TimeFrom',
    timeTo: 'TimeTo',
    startTime: 'Starting time',
    endTime: 'End Time',
    previousWeek: 'Last week',
    previousMonth: 'Last month',
    previousThreeMonths: 'Last three months',
    posted: 'Posted',
    notPosted: 'Unposted',
    // Whether to generate a delivery note
    ascending: 'Ascending',
    descending: 'Descending',
    postingStatus: 'Posting status',
    all: 'All',
    actionConfirm: 'Are you sure you want to do this?？',
    committingConfirm: 'Are you sure you want to submit all the data? \r\n (cannot be modified after submission)',
    batchDeletingConfirm: 'Are you sure you want to delete this data?',
    tip: 'prompt',
    numberRequired: 'Cannot be empty and must be a number',
    mustBeGreaterThanZero: 'Must be greater than 0',
    DocNumIsNull: 'Single number cannot be empty',
    primaryTableRecordsNotFound: 'Master table record not found',
    pleaseSelectDate: 'Please select a date',
    yes: 'Yes',
    no: 'No',
    update: 'Update',
    selectMaterial: 'Select material',
    selectSupplier: 'Select Supplier',
    selectProductionOrder: 'Select production order',
    selectLocation: 'Select location',
    selectRType: 'select type',
    IsRequired: 'Can not be empty',
    operationNotPermitted: 'Operation not allowed',
    BaseNumIsNull: 'Please select a production order',
    detailIsNull: 'Detail record cannot be empty',
    Inspection: {
      qualify: 'qualified',
      disqualify: 'Failed',
      BatchDisqualify: 'Batch unqualified'
    },
    handlingPostedDataNotPermitted: 'Posted data does not allow operation',
    notFindUseItemCodeInSAP: 'Failed to post, no task can be operated in SAP',
    ValidatorMessage: { // Common module in verification
      MustInput: 'Must be entered!',
      MustSelect: 'This item is required!',
      LengthRange: 'Please enter {Range} characters！'
    },
    notEnoughInventory: 'Insufficient inventory',
    repeatBaseNum: 'Please do not repeat the same sales order',
    review: 'review',
    reject: 'reject',
    reviewNotPermitted: 'Operation not allowed after passing the review',
    reviewTwoNotPermitted: 'Operation not allowed after passing the Rejected',
    rejectNotPostToSAP: 'Posting is not allowed without review',
    waveConfirm: 'Cannot delete after warehouse confirmation',
    inspectionConfirm: 'Cannot delete after inspection confirmation',
    StockIsLocked: 'Inventory locked',
    ErrNotDelete: 'There are scanned items, operation not allowed',
    sync: 'Synchronize data',
    syncStart: 'Synchronizing. If the operation times out, refresh the page and try again after a few minutes',
    warning: 'Tips',
    slowRunningWarning: 'A time-consuming operation is in progress. If the timeout occurs, please refresh the page later and try again',
    barcodeScanRecordExists: 'Barcode record already exists, cannot scan repeatedly',
    reviewNoPostToSAP: 'Review passed, posting failed',
    seletedDataInStockNoDelete: 'The selected data has been received and cannot be deleted',
    noConfirmData: 'No data to submit',
    someDataHasRepeat: 'Some data has duplicates, duplicates have been excluded'

  },
  Base: {
    BinLocation: {
      BinID: 'LocationID',
      BinLocationCode: 'Location number',
      BinLocationName: 'Location name',
      RegionCode: 'Area code',
      RegionName: 'Name of the area',
      WhsCode: 'Warehouse number',
      WhsName: 'Warehouse name',
      IsFreeTax: 'IsFreeTax',
      IsConsign: 'IsConsign',
      title: 'Location master information'
    }
  },
  ui: {
    MD: {
      Customer: {
        CustomerCode: 'Customer Number',
        CustomerName: 'Customer  Name',
        Email: 'Email',
        City: 'City',
        Phone: 'Contact number',
        Fax: 'Fax',
        ContactPerson: 'Contact',
        title: 'Customer details',
        Address: 'Address',
        searchCustomer: 'Query customer'
      },
      MD_Warehouse: {
        WhsID: 'warehouseID',
        CompanyCode: 'Company Number',
        WhsCode: 'Warehouse number',
        WhsName: 'warehouse name'
      },
      MD_Region: {
        RegionID: 'RegionID',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        WhsCode: 'WarehouseCode',
        WhsName: 'WarehouseName',
        WhsName1: 'Own warehouse'
      },
      MD_BinLocation: {
        BinID: 'LocationID',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        RegionName1: 'RegionName:',
        WhsCode: 'WarehouseCode',
        WhsName: 'WarehouseName',
        WhsName1: 'Own Warehouse',
        IsFreeTax: 'IsFreeTax',
        IsConsign: 'IsConsign',
        PLine: 'Production line'
      },
      MD_Dictionary: {
        DictionaryID: '数据字典ID',
        TypeCode: 'Type Code',
        TypeDisc: 'type name',
        EnumKey: 'Enumeration Key',
        EnumValue: 'Enumeration value',
        EnumValue1: 'Enumeration value1',
        EnumValue2: 'Enumeration value2',
        SortNum: 'Sort',
        Remark: 'Remarks',
        CUser: 'Creator',
        CTime: 'Creation time',
        MUser: 'Updater',
        MTime: 'Update time'
      },
      MD_Item: {
        ItemID: 'Material ID',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        Unit: 'Inventory unit',
        StockingQty: 'Production kanban stocking',
        AgeQty: 'Reservoir age warning value',
        ULimitWarning: 'Inventory cap warning value',
        LLimitWarning: 'Lower stock limit warning value',
        RegionCode: 'Recommended area',
        ReqWarning: 'Production kanban warning value',
        ProductCategoryID: 'Product Category',
        BaseMeasureUnitCode: 'Basic unit of measure',
        InventoryValuationMeasureUnitCode: 'Inventory evaluation unit',
        PlanningMeasureUnitCode: 'Plan unit of measure',
        QuantityConversion: 'Unit conversion',
        PurchasingMeasureUnitCode: 'Purchasing unit',
        SalesMeasureUnitCode: 'Sales unit',
        IsBarCode: 'Whether to automatically generate customer tags',
        IsFreeTax: 'Whether it is bonded',
        ItmsGrpCode: 'Material group number',
        ItmsGrpName: 'Material group name',
        syncProductCategory: 'Synchronize product category data',
        syncMaterial: 'Synchronize item data',
        Isabroad: 'Is it a foreign customer'
      },
      MD_Supplier: {
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        Email: 'Email',
        City: 'City',
        Phone: 'Phone',
        Fax: 'Fax',
        ContactPerson: 'ContactPerson',
        Address: 'Address',
        SyncSupplier: 'Synchronize SAP data'

      }, MD_Customer: {
        InternalID: 'Customer Code',
        FamilyName: 'Customer Name',
        LifeCycleStatusCode: 'Status',
        EMailURI: 'E-Mail',
        CityName: 'City',
        Phone: 'Phone',
        Fax: 'Fax',
        ContactPerson: 'ContactPerson',
        Address: 'Address',
        FirstLineName: 'Company customer name',
        CategoryCode: 'CategoryCode',
        SyncCustomer: 'SyncCustomer'
      },
      MD_LabelTemplate: {
        TempleteID: 'Template ID',
        TempleteDesc: 'Template description',
        TempleteFile: 'Template file',
        TempleteType: 'Template type',
        IsEnable: 'IsEnable',
        Remark: 'Remarks',
        CUser: 'Creator',
        CTime: 'Creation time',
        MUser: 'Updater',
        MTime: 'Update time',
        ClickToUpload: 'Click To Upload',
        UploadTip: 'Only Repx files can be uploaded, and no more than 2m',
        FileTypeWarning: 'File type is not. Repx!, cannot upload!',
        PleaseSelectFile: 'Please select a file',
        Uploading: 'Uploading...',
        FileMustGt2M: 'File must not be larger than 2m',
        FileUploadLimit: 'Currently, only one file can be uploaded. you have selected{CurrentSelect}，A total of selected{TotalSelect}'
      },
      Stock: {
        StockID: 'StockID',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BarCode: 'BarCode',
        BatchNum: 'batch',
        SupplierBatch: 'Supplier Batch',
        PTime: 'Production Date',
        ItemCode: 'Material Code',
        ItemName: 'Material Name',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Stock quantity',
        Unit: 'Inventory unit',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName'
      },
      MD_POInspectionPlan: {
        PlanID: 'PlanID',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        SampleRange: 'Sampling range',
        GradeCode: 'Inspection level number',
        GradeName: 'Inspection level name',
        SampleNum: 'Fixed number of samples',
        SampleProportion: 'Fixed sample ratio',
        ITime: 'Batch inspection time',
        Placeholder: {
          Supplier: 'Please select a supplier',
          Item: 'Please select material'
        },
        SelectItemTitle: 'Select material',
        SelectSupplierTitle: 'Select supplier',
        Supplier: {
          SupplierCode: 'SupplierCode',
          SupplierName: 'Supplier description',
          Email: 'Email',
          City: 'City',
          Phone: 'Contact number',
          Fax: 'Fax',
          ContactPerson: 'Contact person',
          Address: ' Address'
        }
      },
      MD_POInspectionGrade: {
        GradeID: 'GradeID',
        GradeCode: 'Inspection level code',
        GradeName: 'Inspection level name',
        OKNum: 'Continuous inspection qualified batches',
        NextGradeCode: 'Next inspection level',
        NoNum: 'Continuous inspection of unqualified batches',
        NoNextGradeCode: 'Unqualified next inspection level',
        Remark: 'Remarks',
        Buttons: {
          MasterEdit: 'Maintenance level',
          DetailAdd: 'Add details',
          DetailEdit: 'Edit details',
          DetailDelete: 'Delete details'
        },
        MasterTitle: 'Inspection level maintenance',
        DetailTitle: 'Inspection level detail maintenance'

      },
      MD_POInspectionGradeDetailed: {
        GradeID: 'Level detail ID',
        GradeCode: 'Inspection level number',
        IQty: 'Inspection quantity',
        SampleNum: 'Number of samples'
      },
      MD_BinLimit: {
        limitID: 'PermissionID',
        MenuCode: 'MenuCode',
        MenuName: 'MenuName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        functionModule: 'Functional Module',
        keyword: 'Region/LocationCode',
        title: 'Select location',
        checkModule: 'Please select function module',
        checkBinlication: 'Please select Binlication'
      },
      MD_SalePacking: {
        SPID: 'SPID',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        Volume: 'volume',
        NetWeight: 'NetWeight',
        GrossWeight: 'GrossWeight',
        PalletNum: 'Number of trays',
        Metre: 'Each meter',
        title: 'Customer Information',
        oitmtitle: 'Material information'
      },
      Placeholder: {
        Supplier: 'Please select a supplier！',
        Item: 'Please select material！'
      },
      Supplier: {
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        Email: 'Email',
        City: 'City',
        Phone: 'Contact number',
        Fax: 'Fax',
        ContactPerson: 'Contact person',
        Address: 'Supplier address'
      },
      Item: {
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'basic unit'
      },
      SelectSupplierTitle: 'Select supplier',
      SelectItemTitle: 'Select material'

    },
    MM: {
      BarCode: {
        BarID: 'BarcodeID',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Quantity',
        Unit: 'Inventory unit',
        PrintTemplate: 'Label template',
        title: 'Material label',
        ErrNotDelete: 'Scanned items exist, operation not allowed'
      },
      InScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        RTypeCode: 'Receipt type number',
        RTypeName: 'Receipt type',
        Subject: 'Subject',
        CostCenter: 'Cost Center/Project Tasks',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Other receipt'
      },
      OutScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BoxBarCode: 'Packing code',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        RTypeCode: 'Shipping type number',
        RTypeName: 'Shipping type',
        Subject: 'Subject',
        CostCenter: 'Cost Center/Project Tasks',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        IsPosted: 'posted or not',
        PDtype: 'SAPView',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Other shipment',
        notEnoughInventory: 'Inventory shortage'
      },
      TakeStockPlan: {
        PlanID: 'PlanID',
        DocNum: 'Plan number',
        PUser: 'Responsible',
        Status: 'Status',
        title: 'Inventory plan',
        planStatusError: 'Current inventory planning status does not allow operation',
        planScanRecordsNotFound: 'Inventory count record not found',
        planNotFound: 'No inventory plan found',
        IsConsignNotAllowed: 'There is a consignment barcode, and inventory gain variance cannot be generated'
      },
      TakeStockPlanDetailed: {
        PlanID: 'PlanID',
        DocNum: 'Plan number',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        title: 'Inventory plan details',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName'
      },
      TransferScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Material movement'
      },
      Inventoryplan: {
        DocNum: 'Plan number',
        PUser: 'Responsible',
        Status: 'Status',
        title: 'Inventory management'
      },
      InventoryplanDetailed: {
        DocNum: 'Plan number',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        title: 'Inventory plan detail management'
      },
      TakeStockScan: {
        DocNum: 'Inventory scan number',
        BoxBarCode: 'Packing code',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        StockQty: 'Stock quantity',
        ScanQty: 'Number of scans',
        DiffQty: 'Number of differences',
        Unit: 'Inventory unit',
        WhsCode: 'WarehouseCode',
        WhsName: 'WarehouseName',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        IsConfirm: 'Confirmed or not',
        title: 'Inventory scan management'
      },
      Inventorydiscpy: {
        DocNum: 'Count difference number',
        BoxBarCode: 'PackingCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        StockQty: 'Stock quantity',
        ScanQty: 'Number of scans',
        DiffQty: 'Number of differences',
        Unit: 'Inventory unit',
        WhsCode: 'WarehouseCode',
        WhsName: 'WarehouseName',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        IsConfirm: 'Confirmed or not',
        title: 'Inventory management'
      },
      BarCodeBox: {
        BarID: 'BarcodeID',
        BoxBarCode: 'BoxBarCode',
        BoxQRCode: 'BoxQRCode',
        IConfirm: 'Quality Confirm',
        title: 'Box code management',
        PrintDiffrent: 'Printing package code is not the same material. Printing is not allowed'
      },
      BarCodeBoxDetailed: {
        BarID: 'BarcodeID',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Quantity',
        IConfirm: 'Quality Confirm',
        Unit: 'Inventory unit',
        title: 'Box code details'
      },
      MD_ProduceLineCapacity: {
        Id: 'ID',
        LineName: 'Line Name',
        Capacity: 'Capacity',
        title: 'Production Line Capacity Maintenance'
      }
    },
    PO: {
      PO_BarCode: {
        BarID: 'BarCodeID',
        DeliveryDetailId: 'Delivery schedule detailsID',
        DeliveryDocNum: 'Delivery plan number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        SupplierBatch: 'SupplierBatch',
        DeliveryTime: 'DeliveryDate',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Quantity',
        Unit: 'Inventory unit',
        PrintTemplate: 'Label template',
        PrintQty: 'Number of prints',
        IsDelivery: 'Whether to generate a delivery note',
        title: 'Purchase label',
        Remark: 'Remarks'
      },
      PO_Inspection: {
        InspectionID: 'InspectionID',
        DocNum: 'InspectionNumber',
        NoteNum: 'DeliveryNumber',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        SampleRange: 'InspectionPlan',
        InspectionGrade: 'InspectionGrade',
        IQty: 'Number of inspections',
        SQty: 'Number of samples',
        IStatus: 'InspectionStatus',
        Inspected: 'Inspected',
        Uninspected: 'Uninspected',
        IUser: 'Inspector',
        ITime: 'InspectDate',
        title: 'Purchase quality inspection form',
        IStatusOption: {
          All: 'All',
          CheckedStatus: 'Checked',
          NoCheckedStatus: 'Not Check'
        },
        ApiMessage: {
          MustFirstPost: 'Must Post First',
          HasShelfScanBarCode: 'The related materials of this document have been put on the shelves. Quality inspection cannot be cancelled!',
          StockedInItemNotAllowInspectionChange: 'It is not allowed to change the quality inspection of materials that have been put on the shelves'

        }
      },
      PO_InspectionDetailed: {
        DetailedID: 'DetailID',
        DocNum: 'Quality inspection number',
        ScanNum: 'Scanning number',
        NoteNum: 'Purchase delivery note number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        CustomsNum: 'Customs declaration No',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        SupplierBatch: 'SupplierBatch',
        PTime: 'Production Date',
        Qty: 'Number of inspections',
        OkQty: 'Qualified quantity',
        NoQty: 'Unqualified quantity',
        /* The following fields are not in the new library */
        IStatus: 'InspectionStatus', // (Uninspected/Inspected)
        Inspection: 'Quality inspection result', // (Qualified/Concession reception/Failed/Pick and use)
        Reasons: 'Reason for failure',
        IDispose: 'Quality inspection', // (Unprocessed/Confirm receipt/Return movement)
        /* The above field part of the new library does not have */
        IUser: 'Inspector',
        ITime: 'InspectDate',
        Unit: 'Inventory unit',
        Status: 'Status', // (Normal / Void)
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Purchase quality inspection list details'
      },
      PO_ConsignIn: {
        ScanID: 'ScanID',
        CompanyCode: 'CompanyCode',
        DocNum: 'Scanning number',
        ShelfNum: 'List number',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        POUnit: 'Purchasing unit',
        Unit: 'Inventory unit',
        ConversionRate: 'Unit conversion rate',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        ErpDocEntry: 'ERP posting number',
        ErpDocNum: 'ERPposting number',
        Status: 'Status',
        title: 'Consignment purchase receipt',
        CheckQty: 'Exceeding the quantity of consignment, adding is not allowed'
      },
      PO_ShelfScan: {
        ScanID: 'ScanID',
        CompanyCode: 'CompanyCode',
        DocNum: 'Scanning number',
        ShelfNum: 'List number',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        POUnit: 'Purchasing unit',
        Unit: 'Inventory unit',
        ConversionRate: 'Unit conversion rate',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        ErpDocEntry: 'ERP posting number',
        ErpDocNum: 'ERPposting number',
        Status: 'Status',
        title: 'Purchase order'
      },
      PO_ReturnScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        CompanyCode: 'CompanyCode',
        BoxBarCode: 'BoxBarCode',
        BaseEntry: 'Purchase Return Request Number',
        BaseNum: 'Purchase Return Request Number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        // POUnit: 'Purchasing unit',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        Status: 'Status',
        title: 'Purchase order'
      },
      PO_DeliveryPlan: {
        PlanID: 'PlanID',
        DocNum: 'Plan number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        DeliveryTime: 'Delivery date',
        Status: 'Status',
        title: 'Purchase delivery schedule',
        titlesub: 'Purchase delivery schedule details'
      },
      PO_DeliveryPlanDetailed: {
        DetailedID: 'DetailedID',
        PlanNum: 'Plan number',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Purchase order line number',
        DeliveryPlanItemID: 'Delivery plan line number',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Quantity of delivery',
        Unit: 'Unit',
        PTime: 'Production daate',
        IsCreatedBarCode: 'Create barcode',
        DeliveryTime: 'Delivery date',
        title: 'Purchase delivery schedule detail management',
        select: 'Choose a purchase delivery schedule'
      },
      PurchaseOrder: {
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        BaseLine: 'Line',

        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        PTime: 'Order date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Quantity: 'Purchase quantity',
        UnitCode: 'Purchase unit',
        TotalDeliveredQuantity: 'Delivered quantity',
        TotalDeliveredUnitCode: 'Delivered unit',
        title: 'purchase order',
        select: 'Select purchase order'
      },
      PO_DeliveryNote: {
        NoteID: 'Delivery noteID',
        DocNum: 'Delivery note number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        DeliveryTime: 'Delivery date',
        IsInspection: 'Whether to purchase quality inspection',
        title: 'Purchase delivery form',
        SendInspectionStatus: 'Inspection status'
      },
      PO_DeliveryNoteDetailed: {
        DetailedID: 'DetailID',
        DocNum: 'Delivery note number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        BaseLine: 'Line',
        SupplierBatch: 'SupplierBatch',
        PTime: 'Production date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Quantity',
        Unit: 'Inventory unit',
        title: 'Purchase delivery note details',
        IsSendInspection: 'IsInspection'
      },
      PO_InScan: {
        ScanID: 'ScanID',
        CompanyCode: 'CompanyCode',
        BoxBarCode: ' BoxBarCode',
        DocNum: 'Scanning number',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        POUnit: 'Purchasing unit',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate'
      },
      PO_Shelf: {
        ShelfID: 'ID',
        CompanyCode: 'CompanyCode',
        DocNum: 'List number',
        ShelfTime: 'ShelfDate',
        PUser: 'Responsible',
        Status: 'Status' // Normal / Cancel / Complete
      },
      PO_ShelfDetailed: {
        DetailedID: 'DetailID',
        CompanyCode: 'CompanyCode',
        ShelfNum: 'Shelf number',
        BaseEntry: 'Purchase order number',
        BaseNum: 'Purchase order number',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Quantity',
        POUnit: 'Purchasing unit',
        Unit: 'Inventory unit',
        ConversionRate: 'Unit conversion rate',
        Status: 'Status', // Normal / Cancel / Complete
        title: 'List of work orders'
      },
      PO_InspectionScan: {
        ScanID: 'ScanID',
        DocNum: 'Quality inspection number',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Confirmed quantity',
        Unit: 'Inventory unit',
        WhsCode: 'WarehouseCode',
        WhsName: 'WarehouseName',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        IType: 'Type', // (Return zone/BoxBarCode)
        IsScan: 'Has it moved?'
      },
      PO_ITransferScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BaseNum: 'Quality inspection number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate'
      },
      SAP_PO_PurchaseOrder: {
        PurchaseOrderID: 'Purchase order number',
        SupplierInternalID: 'Supplier No',
        SupplierDescription: 'Supplier escription',
        LastModifiedTime: 'SAP last update time',
        Datasource: {
          fromRemote: 'Remote query (SAP)',
          fromLocal: 'Local query'
        },
        syncSuccess: 'Sync Success'
      },
      SAP_PO_PurchaseOrderDetail: {
        title: 'Purchase order details',
        ItemID: 'Line number',
        ProductID: 'Material number',
        ProductDescription: 'Material name',
        OrderedDateTime: 'Order time',
        Quantity: 'Quantity',
        UnitCode: 'Unit',
        TotalDeliveredQuantity: 'Delivered quantity',
        TotalDeliveredUnitCode: 'Delivered unit',
        EndDeliveryTime: 'Latest delivery time'
      }
    },
    PP: {
      MaterialReq: {
        ReqID: 'DemandID',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        BinLocationCode: 'Line side warehouse number',
        BinLocationName: 'Line side warehouse name',
        BaseLine: 'Line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        BOMUnit: 'BOMUnit',
        BOMReqQty: 'Demand quantity (BOM unit)',
        ConversionRate: 'Unit conversion rate',
        Unit: 'Inventory unit',
        ReqQty: 'Demand quantity (inventory unit)',
        StockingQty: 'Stock quantity',
        StockQty: 'Line side warehouse stock',
        MaterialQty: 'Production quantity',
        SurplusQty: 'Remaining stock',
        title: 'Production material demand record'
      },
      MaterialReqKanban: {
        ReqID: 'demandID',
        ProductionOrderID: 'Order No',
        ProductionLine: 'Production line',
        ProductID: 'Product number',
        ProductDescription: 'Product name',
        BinLocationCode: 'BinLocationCode',
        BinLocationName: 'BinLocationName',
        MaterialID: 'Material No',
        MaterialDescription: 'Material name',
        BOMUnitCode: 'BOM Unit',
        BOMQty: 'BOM quantity ',
        PlanQty: 'Planned quantity',
        ConversionRate: 'Unit conversion rate',
        UnitCode: 'Unit',
        StockingQty: 'Stocking quantity',
        BufferQty: 'Inventory quantity',
        PreparedQty: 'Line Stock quantity',
        FeededQty: 'Delivered quantity',
        InventoryQty: 'stock quantity',
        WarningStatus: 'Early warning state',
        ProductionOrderStatus: 'Production order status'
      },
      StockingWave: {
        WaveID: 'WaveID',
        DocNum: 'Wave number',
        STime: 'Stocking date',
        Status: 'Status',
        title: 'Stocking wave order',
        complete: 'Complete stocking',
        noItemCodeSelected: 'Please select the material before adding',
        PrimaryIsNull: 'Wave order information cannot be empty',
        Status_notBegin: 'Has not started',
        Status_stockedUp: 'Already stocked',
        Status_completed: 'Carry out',
        PLine: 'Production lin',
        sTimeIsNull: 'Please select the stocking date',
        pLineIsNull: 'Please select the production line'
      },
      StockingWaveDetailed: {
        DetailedID: 'DetailID',
        DocNum: 'Wave number',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        BinLocationCode: 'Line side warehouse number',
        BinLocationName: 'Line side warehouse location name',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Stock quantity',
        Unit: 'Inventory unit',
        BaseLine: 'Line',
        title: 'Stocking list',
        DetailsIsEmpty: 'Wave order detail record cannot be empty',
        selectMaterial: 'Select material',
        Remark: 'Stock quantity'
      },
      StockingScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        WaveNum: 'Wave number',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        BaseLine: 'Line',
        PLine: 'Production line',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate'
      },
      TransferScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        WaveNum: 'Wave number',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        BaseLine: 'Line:',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        OutRegionCode: 'The code of warehouse transfer from',
        OutRegionName: 'The name of warehouse transfer from',
        OutBinLocationCode: 'The code of region transfer from',
        OutBinLocationName: 'The name of region transfer from',
        InRegionCode: 'The code of location transfer to',
        InRegionName: 'The name of location transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate'
      },
      Scrapped: {
        ScrappedID: 'Scrap ID',
        DocNum: 'Bad number',
        ScrappedNum: 'Retirement order number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Scrapped quantity',
        Unit: 'Inventory unit',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate'
      },
      FTTP: {
        FTTPID: 'Primary keyID',
        DocNum: 'Registration number',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        FType: 'Status',
        title: 'Production of bad self-product registration'
      },
      FTTPDetailed: {
        DocNum: 'Registration number',
        Station: 'Registration site',
        ActualStation: 'Actual site (deduction site)',
        ReasonsCode: 'Site defect reason number',
        ReasonsDesc: 'Bad site reason',
        InputQty: 'Input quantity',
        RejectQty: 'Poor amount',
        OutputQty: 'Output quantity',
        DealWith: 'Processing method',
        IConfirm: 'Quality inspection confirmation',
        WConfirm: 'Warehouse confirmation',
        title: 'Production of bad self-product registration details'
      },
      FTTM: {
        FTTMID: 'Primary keyID',
        DocNum: 'Registration number',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        title: 'Production of bad raw materials registration form'
      },
      FTTMDetailed: {
        DetailedID: 'Primary keyID',
        DocNum: 'Registration number',
        BaseLine: 'Line',
        ReasonsCode: 'Bad cause number',
        ReasonsDesc: 'Bad reasons',
        Qty: 'Quantity',
        IConfirm: 'Quality inspection confirmation',
        WConfirm: 'Warehouse confirmation',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        title: 'Production defect registration list'
      },
      ProductionOrder: {
        RequestedEndDate: 'Request end time',
        ProductionOrderSiteID: 'Site number',
        ProductionOrderStatus: 'Production order status',
        ProductionOrderPriority: 'Priority',
        BaseNum: 'Production order number',
        ItemCode: 'Output material code',
        ItemName: 'Output material name',
        ProductionOrderOpenQuantity: 'ProductionOrderOpenQuantity',
        ProductionOrderReleaseDate: 'Production order release deadline',
        ProductionOrderPlannedQuantity: 'ProductionOrderPlannedQuantity',
        ProductionOrderFullfilledQuantity: 'ProductionOrderFullfilledQuantity',
        SubmittedQuantity: 'SubmittedQuantity',
        CUser: 'Creator',
        ProductionLine: 'Production line',
        title: 'Production Order',
        select: 'Please select a production order',
        notFound: 'Production order not found',
        PlannedStartDate: 'Scheduled start time',
        CreatedDate: 'Order creation time',
        CreatedDateStart: 'Order creation time Start',
        CreatedDateEnd: 'Order creation time End',
        fromRemote: 'Remote query(SAP)',
        fromLocal: 'Local query',
        datasource: 'Data sources',
        productionOrderIDIsNull: 'Unknown production order number',
        syncSuccess: 'SAP data synchronization succeeded. If the product category or material information is null, please synchronize the material master data first',
        UpdatePline: 'Update ProductLine'
      },
      ProductionOrderDetail: {
        BaseNum: 'Production order number',
        ProductionLine: 'Production line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Product category code',
        ItmsGrpName: 'Product category name',
        notFound: 'Order related item information not found',
        LineItemGroupID: 'Site (line item group number)',
        BinLocationCode: 'Recommended location code',
        BinLocationName: 'Recommended location name',
        Qty: 'Quantity of preparation',
        Unit: 'Unit',
        title: 'Production bill of materials',
        select: 'Please select material',
        PlanQuantity: 'Planned quantity',
        PlanQuantityUnit: 'Planned quantity Unit ',
        OpenQuantity: 'OpenQuantity',
        OpenQuantityUnit: 'OpenQuantity Unit',
        FulfilledQuantity: 'Fulfilled Quantity',
        FulfilledQuantityUnit: 'Fulfilled Quantity Unit',
        StockingQty: 'WMS stock quantity'
      },
      ProductionMaterialRequirements: {
        ReqID: 'DemandID',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        BinLocationCode: 'Line side warehouse number',
        BinLocationName: 'Line side warehouse location name',
        BaseLine: 'Line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        BOMUnit: 'BOM unit',
        BOMReqQty: 'Demand quantity (BOM unit)',
        ConversionRate: 'Unit conversion rate',
        Unit: 'Inventory unit',
        ReqQty: 'Demand quantity (Inventory unit)',
        StockingQty: 'Stock quantity',
        StockQty: 'Line side warehouse stock',
        MaterialQty: 'Production quantity',
        SurplusQty: 'Remaining stock',
        Remark: 'Remarks',
        IsDelete: 'Delete mark',
        CUser: 'Creator',
        CTime: 'Creat Date',
        MUser: 'Updater',
        MTime: 'Update Date',
        DUser: 'Delete user',
        DTime: 'Delete Date'
      },
      ReturnScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Production return scan record'
      },
      BarCodeReturn: {
        BarID: 'BarCodeID',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Packing quantity',
        Unit: 'Inventory unit',
        PrintTemplate: 'Label template',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        title: 'Production return label'
      },
      BarCode: {
        BarID: 'BarCodeID',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Packing quantity',
        Unit: 'Inventory unit',
        PrintTemplate: 'Label template',
        IStatus: 'Quality inspection status',
        title: 'Self-product label',
        editSameOrder: 'Please select the production order of the same material in editing status',
        productType: 'Finished product type',
        product: {
          half: 'Partially Prepared Products',
          whole: 'Finished product',
          raw: 'Raw material'
        }
      },
      InspectionScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'production line',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        IStatus: 'Quality inspection status',
        title: 'Production of self-made quality inspection scan records'
      },
      OutScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        PTime: 'Production Date',
        BaseLine: 'Line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Production issue scan record'
      },
      InScan: {
        ScanID: 'ScanID',
        DocNum: 'Scanning number',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order number',
        PLine: 'Production line',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Number of scans',
        Unit: 'Inventory unit',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Production of self-injection scanning records'
      },
      OrderClose: {
        Oid: 'ID',
        BaseEntry: 'Production order number',
        BaseNum: 'Production order No',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostTime',
        title: 'Production order closing record',
        ProductionLine: 'Production line',
        ProductID: 'Product number',
        ProductDescription: 'Product name',
        PlannedQuantity: 'Planned quantity',
        PlannedStartDate: 'Scheduled start time',
        BtnSelectProductionOrder: 'Select production order',
        BtnCloseProductionOrder: 'Close production order',
        Message: {
          IsExistedProductionOrder: 'Please close the selected production order directly！'
        }
      }
    },
    SD: {
      SD_DeliveryWave: {
        DocNum: 'Wave number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        CustomerAdd: 'CustomerAdd',
        ShipTime: 'Stocking Date',
        PUser: 'Responsible',
        Status: 'Status',
        Forwarder: 'Freight forwarding',
        Remark: 'Remark',
        title: 'Sales delivery wave task list'
      },
      SD_DeliveryWaveDetailed: {
        DocNum: 'Wave number',
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        BaseLine: 'Line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Stock quantity',
        Boxes: 'Number of boxes',
        NetWeight: 'Net weight',
        GrossWeight: 'Gross weight',
        Pallet: 'Number per tray',
        PalletNum: 'Number of trays',
        Metre: 'Each meter',
        MetreSum: 'Total number of meters',
        title: 'Sales delivery wave task list details'
      },
      saleOrder: {
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        BaseLine: 'Line',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Stock quantity',
        Boxes: 'Number of boxes',
        NetWeight: 'Net weight',
        GrossWeight: 'Gross weight',
        Pallet: 'Number per tray',
        MetreSum: 'Total number of meters',
        select: 'Select sales order',
        title: 'Sales order details'
      },
      SD_StockingScan: {
        DocNum: 'Scanning number',
        WaveNum: 'Wave number',
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Qty',
        SumQty: 'SumQty',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsDelivery: 'Is it boxed?',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Sales stock list'
      },
      SD_DeliveryScan: {
        DocNum: 'Scanning number',
        WaveNum: 'Wave number',
        StockupDoc: 'Stock list number',
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Number of scans',
        OutWhsCode: 'The code of warehouse transfer from',
        OutWhsName: 'The name of warehouse transfer from',
        OutRegionCode: 'The code of region transfer from',
        OutRegionName: 'The name of region transfer from',
        OutBinLocationCode: 'The code of location transfer from',
        OutBinLocationName: 'The name of location transfer from',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsDelivery: 'Is it boxed?',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Sales invoice'
      },
      SD_Packing: {
        DocNum: 'Packing list',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        CustomerAdd: 'CustomerAdd',
        Container: 'Container Number',
        Forwarder: 'Freight forwarding',
        CustomsNum: 'Customs number',
        title: 'Sales packing list',
        ShipmentID: 'Waybill number',
        titlesub: 'Packing list details'
      },
      SD_PackingDetailed: {
        DocNum: 'Packing list',
        ScanNum: 'Scanning number',
        WaveNum: 'Wave number',
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Number of scans',
        title: 'Sales packing list details'
      },
      SAP_SD_SaleOrder: {
        SalesOrderID: 'Sales Order No',
        ReceivingPartyCode: 'ReceivingParty Code',
        ReceivingPartyName: 'ReceivingParty Name',
        ReceivingPartyAdd: 'ReceivingParty Address',
        OrderStatus: 'Order State',
        CustomerCode: 'Customer Code',
        CustomerName: 'Customer Name',
        ItemID: 'Item Line',
        ProductID: 'Product',
        ProductDescription: 'Product Name',
        Quantity: 'Quantity',
        UnitCode: 'UnitCode',
        EndDeliveryTime: 'Required Date',
        datasource: 'Query Type',
        fromRemote: 'Remote SAP Query',
        fromLocal: 'Local Query',
        title: 'Sales Order Item Details',
        syncSuccess: 'Synchronous Success',
        syncNotData: 'Not synchronized to related data',
        PlaseInSaleOrder: 'Please enter the sales order number to be synchronized'
      },
      SD_BarCodeCustomer: {
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        CustomerNum: 'Customer order number',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Quantity',
        ShipmentID: 'Shipment number',
        Version: 'Version',
        PrintTemplate: 'Label template',
        title: 'Sales customer label'
      },
      sddelivergoodsOrder: {
        BaseEntry: 'Sales order number',
        BaseNum: 'Sales order number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        BaseLine: 'Line',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Number of scans',
        PrintTemplate: 'Label template',
        title: 'Sales and shipping information',
        select: 'Choose sales delivery box'
      },
      SD_BarCodeReturn: {
        BaseEntry: 'Return task request number',
        BaseNum: 'Return request number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Quantity',
        PrintTemplate: 'Label template',
        title: 'Sales return label',
        SelectDate: 'Selection date'
      },
      ReturnRequest: {
        BaseEntry: 'Return task request number',
        BaseLine: 'Return request line',
        BaseNum: 'Return request number',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Quantity',
        title: 'Select sales return request',
        select: 'Return request form details'
      },
      SD_ReturnScan: {
        DocNum: 'Scanning number',
        BaseEntry: 'Sales return request number',
        BaseNum: 'Sales return request number',
        CustomerCode: 'CustomerCode',
        CustomerName: 'CustomerName',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Unit: 'Inventory unit',
        Qty: 'Quantity',
        InWhsCode: 'The code of warehouse transfer to',
        InWhsName: 'The name of warehouse transfer to',
        InRegionCode: 'The code of region transfer to',
        InRegionName: 'The name of region transfer to',
        InBinLocationCode: 'The code of location transfer to',
        InBinLocationName: 'The name of location transfer to',
        IsPosted: 'Posted or not',
        PostUser: 'PostUser',
        PostTime: 'PostDate',
        title: 'Sales return order'
      }
    },
    Sys: {
      Sys_User: {
        UserID: 'UserID',
        OrganizationID: 'Organization',
        OrganizationDesc: 'Organization description',
        UserName: 'UserName',
        FrgnName: 'English name',
        LoginAccount: 'Login account',
        LoginPassword: 'Login pPassword',
        Gender: 'gender',
        Birthday: 'Birthday',
        Email: 'Email',
        Mobile: 'Mobile',
        Telphone: 'Telphone',
        IsSupplier: 'Supplier Or Not',
        IsEnable: 'IsEnable',
        UserRole: 'UserRole',
        TipMessage: {
          ResetPasswordConfirm: 'You confirm that you want to reset the password of the selected user？'
        },
        FormValidator: {
          ValidateIP: 'The IP address you entered is incorrect！',
          ValidateEmail: 'The mailbox you entered is not in the correct format！',
          ValidateMobile: 'Please enter a valid phone number'
        },
        Buttons: {
          SetUserRole: 'SetUserRole',
          ResetPassword: 'ResetPassword'
        },
        Titles: {
          SetUserRole: 'SetUserRole'
        },
        Placeholder: {
          Keyword: 'Login account | UserName'
        }
      },
      Sys_Role: {
        RoleID: 'RoleID',
        RoleDesc: 'RoleDescription',
        ResourceElement: {
          PermissionSettingDialogTitle: 'Permission settings'
        },
        PanelTab: {
          Admin: 'Administrator',
          PDA: 'PDA Client'
        }
      },
      Sys_Mail: {
        MailID: 'MailID',
        MessageID: 'MessageID',
        UserID: 'RecipientID',
        UserName: 'Recipient',
        MessageTypeID: 'MessageTypeID',
        MessageTypeDesc: 'MessageType',
        MailSubject: 'MailSubject',
        MailBody: 'MailBody',
        SenderMail: 'SenderMail',
        ReceiverMail: 'RecipientMail',
        SendTime: 'SendTime'
      },
      Sys_Message: {
        MessageID: 'MessageID',
        MessageTypeID: 'MessageTypeID',
        MessageTitle: 'MessageTitle',
        MessageBody: 'MessageBody',
        NotifyType: 'NotifyType',
        MessagePublisher: 'MessagePublisher',
        MessagePublishTime: 'MessagePublishTime'
      },
      Sys_Organization: {
        OrganizationID: 'OrganizationID',
        FathOrganizationID: 'FatherOrganization ',
        OrganizationCode: 'OrganizationCode',
        OrganizationLevel: 'OrganizationLevel',
        OrganizationDesc: 'OrganizationDescription'
      },
      Sys_SwithConfig: {
        ConfigID: 'switchID',
        ConfigCode: 'Switch setting number',
        ConfigDesc: 'Switch setting description',
        SwitchValue: 'Switch value'
      },
      Sys_ApiLogConfig: {
        ApiLogConfigID: 'APIConfigID',
        ApiUrl: 'APIrelative path',
        ApiType: 'APIType',
        RequestSupportType: 'RequestSupportType',
        BelongModule: 'Module',
        ApiDescription: 'Functional description'
      },
      Sys_DbBackup: {
        DbBackupID: 'Database backup recordID',
        DbName: 'DatabaseName',
        BackupName: 'BackupName',
        BackupFilePath: 'BackupFilePath',
        IsCompressed: 'IsCompressed',
        DbServer: 'ServerAddress'
      },
      Sys_DbBackupConfig: {
        DbBackupConfigID: 'Backup configurationID',
        DbName: 'DatabaseName',
        BackupFilePath: 'BackupFilePath',
        RestoreFilePath: 'RestoreFilePath',
        IsCompressed: 'IsCompressed',
        DbServer: 'ServerAddress',
        DbAccount: 'Login account',
        DbPassword: 'login password',
        Remark: 'Remarks',
        BackupSetting: 'Backup Setting'
      },
      Sys_Log: {
        LogID: 'SystemLogID',
        ApiUrl: 'APIPath',
        LogType: 'LogType', // [1: BusinessLog 2:DebugLog]
        ApiType: 'FunctionalType',
        ApiModule: 'FunctionalModule',
        ApiDescription: 'APIFunctional description',
        OperateUser: 'OperateUser',
        ClientHost: 'ClientHost',
        ClientIP: 'ClientIP',
        ClientBrowser: 'ClientBrowser',
        ClientOS: 'ClientOS',
        RequestType: 'RequestMethod', // [GET,POST,DELETE]
        RequestParms: 'Requestparameters',
        ResponseData: 'ResponseData',
        Result: 'Success/Failure',
        Placeholder: {
          SearchCondition: 'Functional module | Functiona description'
        }
      },
      Sys_MailServerConfig: {
        MailServerID: 'MailServerID',
        MailServerHost: 'MailServerHost',
        MailServerPort: 'MailServerPort',
        MailServerAccount: 'MailServerAccount',
        MailServerPassword: 'MailServerPassword',
        SenderDisplayName: 'SenderDisplayName'
      },
      Sys_MessageNotifySetting: {
        MessageNotifySettingID: 'MessageNotifySettingID',
        MessageTypeID: 'MessageTypeID',
        RoleID: 'RoleID',
        MessageType: 'Message Type',
        IsNotifyByEMail: 'Notify By Email'
      },
      Sys_MessageType: {
        MessageTypeID: 'MessageTypeID',
        MessageTypeDesc: 'MessageTypeDescription'
      },
      Sys_Resource: {
        ResourceID: 'ResourceID',
        FathResourceID: 'FathResourceID',
        ResourceTitle: '',
        ResourceName: 'ResourceName',
        ResourceType: 'ResourceType',
        RedirectUrl: 'RedirectUrl',
        ResourcePath: 'ResourcePath',
        ResourceLevel: 'ResourceLevel',
        ResourceIcon: 'ResourceIcon',
        IsCache: 'IsCache',
        Component: 'Component件',
        Layout: 'Layout',
        Role: 'Role',
        AppID: 'Client'
      },
      Sys_RoleResource: {
        RoleResourceID: 'RoleResourceID',
        ResourceID: 'ResourceID',
        RoleID: 'RoleID'
      },
      Sys_UserMessage: {
        UserMessageID: 'UserMessageID',
        MessageID: 'MessageID',
        MessageTypeID: 'MessageTypeID',
        MessageTypeDesc: 'MessageType',
        UserID: 'UserID',
        UserName: 'UserName',
        MessageTitle: 'MessageTitle',
        MessageContent: 'MessageContent',
        NotifyType: 'NotifyType',
        Publisher: 'Publisher',
        PublishTime: 'PublishTime',
        IsReaded: 'IsReaded',
        ReadTime: 'ReadTime',
        Remark: 'Remarks',
        Placeholder: {
          Keyword: 'MessageTitle | MessageContent'
        }
      },
      Sys_UserRole: {
        UserRoleID: 'UserRoleID',
        RoleID: 'RoleID',
        UserID: 'UserID'
      },
      Sys_UserSapAccount: {
        UserSapAccountID: 'UserSapAccountID',
        UserID: 'UserID',
        SapAccount: 'SapAccount',
        SapPassword: 'SapPassword'
      }
    },
    RPT: { // Business report
      RPT_Stock: {
        StockID: 'StockID',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        SupplierBatch: 'SupplierBatch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'Material group code',
        ItmsGrpName: 'Material group name',
        Qty: 'Inventory quantity',
        Unit: 'Inventory unit',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        titleDetail: 'Inventory details'
      },
      RPT_FTT: {
        CreateDate: 'CreateDate',
        FType: 'Type',
        ProductionLineID: 'ProductionLineID',
        FTTID: 'FTTID',
        ProductID: 'ProductID',
        StationID: 'StationID',
        NCConditionDescription: 'NCConditionDescription',
        HandlingRecommendations: 'HandlingRecommendations',
        RejectQuantity: 'RejectQuantity',
        InputQuantity: 'InputQuantity',
        OutputQuantity: 'OutputQuantity',
        M: 'Materiel',
        P: 'Product'
      }, RPT_PO_InOut: {
        SupplierCode: 'SupplierCode',
        ItemCode: 'ItemCode',
        CTime: 'CTime',
        InQty: 'Received quantity',
        SQty: 'Shelves quantity',
        RQty: 'Return quantity',
        sumQty: 'Sum quantity'
      },
      RPT_StockMove: {
        ItemCode: 'Item Code',
        ItemName: 'Item Name',
        WhsCode: 'Warehouse Code',
        WhsName: 'Warehouse Name',
        RegionCode: 'Region Code',
        RegionName: 'Region Name',
        BinLocationCode: 'BinLocation Code',
        BinLocationName: 'BinLocation Name',
        BatchNum: 'BatchNum',
        Unit: 'Unit',
        DocNum: 'Business document code',
        CUser: 'Create User',
        CTime: 'Create Date',
        Qty: 'Quantity',
        DType: 'Business document type'
      },
      RPT_PLineOutPut: {
        CTime: 'Create Date',
        PLine: 'Product Line',
        ItemCode: 'Product Code',
        Qty: 'Quantity',
        SumQty: 'Total quantity'
      },
      RPT_PO_Inspection: {
        BatchNum: 'Batch Number',
        SupplierCode: 'Supplier Code',
        SupplierName: 'Supplier Name',
        ItemCode: 'Item Code',
        ItemName: 'Item Name',
        Qty: 'Inspection quantity',
        OkQty: 'Qualified quantity',
        NoQty: 'UnQualified quantity',
        Status: 'States(Quality)',
        IUser: 'Quality inspector',
        ITime: 'Quality Date',
        CTime: 'Create Date',
        RM_FPY: 'Qualified rate', yes: 'yes', no: 'no'
      },
      RPT_SD_Delivery: {
        BaseNum: 'Sales Order No',
        BaseLine: 'Line number',
        ItemCode: 'Item Code',
        ItemName: 'Item Name',
        EndDeliveryTime: 'Customer demand time',
        CTime: 'Delivery time',
        CustomerCode: 'Customer Code',
        Qty: 'Shipment quantity',
        ActualDate: 'Actual delivery time',
        DiffDate: 'Difference from commitment date',
        ShipmentNo: 'Shipment ID',
        Container: 'Container No',
        CustomsNum: 'CD No'
      },
      RPT_SupplierItem_View: {
        SupplierCode: 'Supplier Code',
        ItemCode: 'Item Code',
        CTime: 'Usage time',
        OKQTY: 'Qualified quantity',
        NoQty: 'Unqualified quantity',
        Qty: 'Quantity used',
        StockQTY: 'Inventory quantity'
      },
      RPT_V_NotPostStockMove: {
        DocNum: 'DocNum',
        BarCode: 'BarCode',
        BatchNum: 'BatchNum',
        BaseNum: 'BaseNum',
        ItemCode: 'ItemCode',
        ItemName: 'ItemName',
        ItmsGrpCode: 'ItmsGrpCode',
        ItmsGrpName: 'ItmsGrpName',
        WhsCode: 'WhsCode',
        WhsName: 'WhsName',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'BinLocationCode',
        BinLocationName: 'BinLocationName',
        Qty: 'Quantity',
        Unit: 'Unit',
        OperationType: 'OperationType',
        CTime: 'CreateTime'
      },
      RPT_StockAge: {
        StockID: 'StockID',
        SupplierCode: 'SupplierCode',
        SupplierName: 'SupplierName',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        SupplierBatch: 'SupplierBatch',
        PTime: 'Production Date',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'MaterialGroupCode',
        ItmsGrpName: 'MaterialGroupName',
        Qty: 'Quantity',
        Unit: 'Unit',
        RegionCode: 'RegionCode',
        RegionName: 'RegionName',
        BinLocationCode: 'LocationCode',
        BinLocationName: 'LocationName',
        AgeQty: 'AgeQuantity',
        StockAge: 'StockAge'
      },
      RPT_DeliverySchedule: {
        PlanNum: ' PlanNum ',
        DeliveryTime: ' DeliveryTime ',
        SupplierCode: ' SupplierCode ',
        SupplierName: ' SupplierName ',
        BaseNum: 'PurchaseOrderNumber',
        BaseLine: ' PurchaseOrderLineNumber ',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'MaterialGroupCode',
        ItmsGrpName: 'MaterialGroupName',
        PurchaseOrderQty: ' PurchaseOrderQuantity ',
        PlanQty: 'PlanQuantity',
        DeliveryedQty: 'DeliveryQuantity',
        OkQty: 'QuanlitiedQuantity',
        NoQty: ' UnquanlitiedQuantity ',
        NoInspectionQty: 'NoInspectionQuantity',
        StockInQty: 'StockInQuantity',
        DeliveryPlanStatus: ' DeliveryPlanStatus '
      },
      RPT_DeliveryOnTimeRate: {
        SupplierCode: ' SupplierCode ',
        SupplierName: ' SupplierName ',
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'MaterialGroupCode',
        ItmsGrpName: 'MaterialGroupName',
        TotalDeliveryItem: ' TotalDeliveryItem ',
        OnTimeDeliveryItem: ' OnTimeDeliveryItem ',
        UnOnTimeDeliveryItem: ' UnOnTimeDeliveryItem ',
        TotalQty: ' PlanTotalQuantity ',
        TotalDeliveryedQty: ' TotalDeliveryedQty ',
        TotalUnOnTimeDeliveryQty: ' TotalUnOnTimeDeliveryQty ',
        OTDRate: 'OnTimeDeliveryRate'
      },
      RPT_StockDiff: {
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'MaterialGroupCode',
        ItmsGrpName: 'MaterialGroupName',
        RegionCode: ' RegionCode ',
        RegionName: ' RegionName ',
        WmsQty: 'WMSQuantity',
        WmsUnit: ' WmsUnit ',
        SapQty: 'SAPQuantity',
        SapUnit: 'SAPUnit',
        DiffQty: 'DiffrentQuantity',
        DiffUnit: 'DiffrentUnit', NotPostQty: 'NotPostQty',
        ConsignQty: 'Consign quantity',
        ButtonGroup: {
          PostAdjust: 'Adjust'
        }
      },
      RPT_StockDiffHistory: {
        ItemCode: 'MaterialCode',
        ItemName: 'MaterialName',
        ItmsGrpCode: 'MaterialGroupCode',
        ItmsGrpName: 'MaterialGroupName',
        RegionCode: 'RegionCode ',
        RegionName: ' RegionName',
        AdjustQty: 'AdjustQuantity',
        AdjustUnit: ' AdjustUnit',
        AdjustType: ' AdjustType'
      },
      RPT_BarCodeRetrospect: {
        docType: 'Business type',
        DocNum: 'Scan number',
        BaseNum: 'Order number',
        CustomerCode: 'Supplier/Customer number',
        CustomerName: 'Supplier/Customer name',
        ItemCode: 'Material number',
        ItemName: 'Material name',
        BoxBarCode: 'BoxBarCode',
        BarCode: 'BarCode',
        BatchNum: 'Batch',
        Qty: 'Quantity',
        WhsCode: 'Warehouse number',
        WhsName: 'Warehouse name',
        RegionCode: 'Area code',
        RegionName: 'Area name',
        BinLocationCode: 'Library number',
        BinLocationName: 'Library name',
        CTime: 'CreateTime',
        CUser: 'Founder',
        ButtonGroup: {
          PO_In: 'Purchase and warehousing',
          PP_Out: 'Production and delivery',
          SD_Out: 'Sales shipment',
          PP_In: 'Production warehousing'
        },
        BarCodeE: 'Purchase bar code|Item Code',
        BarCodeS: 'Self made bar code|Box bar code'
      }

    },
    RPT_StockDiffHistory: {
      ItemCode: '物料件号',
      ItemName: '物料名称',
      ItmsGrpCode: '物料组编号',
      ItmsGrpName: '物料组名称',
      RegionCode: '区域编号',
      RegionName: '区域名称',
      AdjustQty: '调整数量',
      AdjustUnit: '调整单位',
      AdjustType: '调整方向'
    }
  },
  SAP: {
    OWOR: { // SAPProduction Order
      DocEntry: 'Number',
      DocNum: 'Production order number',
      PostDate: 'Order date',
      DocType: 'Document Type',
      DocStatus: 'Document status',
      PItemCode: 'ProductMaterialCode',
      PItemName: 'ProductMaterialName',
      PlannedQty: 'Quantity of order',
      CmpltQty: 'Number of warehousing',
      PInvntryUom: 'ProductInventory unit',
      LineStatus: 'LineStatus',
      LineNum: 'Line',
      ItemCode: 'MaterialCode',
      ItemName: 'MaterialName',
      ItmsGrpCode: 'Material group code',
      ItmsGrpName: 'Material group name',
      BaseQty: 'Basic quantity',
      Quantity: 'Number of deliveries',
      IssuedQty: 'Number of issues',
      InvntryUom: 'Inventory unit'
    },
    OPOR: { // SAP purchase order
      DocEntry: 'Number',
      DocNum: 'Purchase Order No',
      TaxDate: 'Order date',
      DocDueDate: 'Delivery date',
      DocType: 'Document type',
      DocStatus: 'Document status',
      CardCode: 'SupplierCode',
      CardName: 'SupplierName',
      LineStatus: 'LineStatus',
      LineNum: 'Line',
      Quantity: 'Quantity',
      OpenQty: 'OpenQuantity',
      ItemCode: 'MaterialCode',
      ItemName: 'MaterialName',
      ItmsGrpCode: 'Material group code',
      ItmsGrpName: 'Material group name',
      BuyUnitMsr: 'Purchasing unit',
      NumInBuy: 'Unit conversion rate',
      InvntryUom: 'Inventory unit'
    },
    ORDR: { // SAP Sales order
      DocEntry: 'Number',
      DocNum: 'Single number',
      TaxDate: 'Order date',
      DocDueDate: 'Delivery date',
      DocType: 'Document Type',
      DocStatus: 'Document status',
      CardCode: 'SupplierCode',
      CardName: 'SupplierName',
      LineStatus: 'LineStatus',
      LineNum: 'Line',
      Quantity: 'Quantity',
      OpenQty: 'OpenQuantity',
      ItemCode: 'MaterialCode',
      ItemName: 'MaterialName',
      ItmsGrpCode: 'Material group code',
      ItmsGrpName: 'Material group name',
      SalUintMsr: 'Sales unit',
      NumInSale: 'Unit conversion rate',
      InvntryUom: 'Inventory unit',
      title: 'Sales order'
    },
    ORRR: { // SAPSales return application
      DocEntry: 'Number',
      DocNum: 'Return order number',
      TaxDate: 'Order date',
      DocType: 'Document Type',
      DocStatus: 'Document status',
      CardCode: 'SupplierCode',
      CardName: 'SupplierName',
      LineStatus: 'LineStatus',
      LineNum: 'Line',
      Quantity: 'Quantity',
      OpenQty: 'OpenQuantity',
      ItemCode: 'MaterialCode',
      ItemName: 'MaterialName',
      ItmsGrpCode: 'Material group code',
      ItmsGrpName: 'Material group name',
      SalUintMsr: 'Sales unit',
      NumInSale: 'Unit conversion rate',
      InvntryUom: 'Inventory unit',
      title: 'Sales return application'
    },
    OITM: { // SAPMaterial master data
      ItemCode: 'MaterialCode',
      ItemName: 'MaterialName',
      ItmsGrpCode: 'Material group code',
      ItmsGrpName: 'Material group name',
      ItemType: 'Material type',
      BuyUnitMsr: 'Purchasing unit',
      NumInBuy: 'Purchasing unit conversion rate',
      InvntryUom: 'Inventory unit',
      SalUintMsr: 'Sales unit',
      NumInSale: 'Sales unit conversion rate'
    }
  },
  Message: { // Back to the language pack for error message processing
    PODeliveryPlanAssignWarning: 'Only the delivery plan that has not been delivered can be issued.',
    PODeliveryPlanConfirmWarning: 'Only the plan with the issued status can be confirmed！',
    PODeliveryPlanUpdateWarning_supplier: 'There can be no more than one supplier in a delivery schedule',
    PODeliveryPlanUpdateWarning_time: 'A delivery schedule must be the same day',
    PODeliveryPlanNumberWarning: 'The number of purchase delivery plans is not allowed to exceed the number of purchase orders',
    PODeliveryNoteDeleteWarning: 'The delivery note has been harvested and must not be deleted！',
    PODeliveryNoteCreatedWarning: 'The selected label has generated a delivery note, please do not repeat it！',
    POBarCodeNumberWarning: 'The number of labels cannot exceed the number of delivery plans！',
    POBarCodeEditWarning: 'The current label has created a delivery note and cannot be edited！',
    POBarCodeDeleteWarning: 'Check the label has been created, can not be deleted！',
    POInspectionDelete_inspection: 'The purchase inspection details to be deleted contain quality inspection information and are not allowed to be deleted！',
    POInspectionDelete_posting: 'The purchase inspection details to be deleted contain the posted data and are not allowed to be deleted！',
    POInspectionScanDeleteWarning: 'Data that has been moved is not allowed to be deleted.！',
    POInspectionScanSubmitWarning: 'Abnormal data exists in the submitted data',
    POInspectionScanStockWarning: 'The number of qualified quality inspections shall not exceed the quantity of inventory',
    PODisShelfWarning: 'Consignment of unqualified goods cannot be scanned for purchase!',
    PODeletePostingWarning: 'Posted data is not allowed to be deleted!',
    POITransferScanDeleteWarning: 'Data that has already been posted is not allowed to be deleted！',
    POITransferScanStockWarning: 'The number of transfer libraries must not exceed the quantity of stock',
    POITransferScanQualifiedWarning: 'The number of transferred warehouses must not exceed the number of quality inspections.',
    POITransferScanSubmitWarning: 'Abnormal data exists in the submitted data'
  },
  Dictionary: {
    GenderMap: { // Gender
      One: 'Male',
      Two: 'Female'
    },
    AccountIsEnableMap: { // Is the user account available
      TrueValue: 'Normal',
      FalseValue: 'Freeze'
    },
    YesNoMap: {
      YesValue: 'Yes',
      NoValue: 'No'
    },
    EnableMap: { // Is the user account available
      TrueValue: 'Effective',
      FalseValue: 'Disable'
    },
    UserRoleMap: {
      One: 'System administrator',
      Two: 'Business people'
    },
    IsReadedMap: {
      TrueValue: 'Have read',
      FalseValue: 'Unread'
    },
    PO_DeliveryPlan_Status: {
      One: 'Not issued',
      Two: 'Has been issued',
      Three: 'Confirmed',
      Four: 'BarCode has been created',
      Five: 'A delivery note has been created'
    },
    SD_DeliveryWave_Status: {
      Zero: 'Has not started',
      One: 'Already stocked',
      Two: 'Completed'
    },
    IsDelivery: {
      yes: 'Generated',
      no: 'Not generated'
    }
  }
}
