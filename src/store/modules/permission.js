import {
  constantRoutes
} from '@/router'
import Layout from '@/layout' // 动态加载路由需要
import {
  getUserRoutes as asyncRoutes
} from '@/api/Sys/Sys_User' // 该部分将来考虑移入到router中统一管理（zhangxu 2019-07-11）
import {
  getToken
} from '@/utils/auth'
const dynamicLoadRouter = require('@/router/_import_' + process.env.NODE_ENV) // 动态加载组件方法

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
// function hasPermission(roles, route) {
//   if (route.meta && route.meta.roles) {
//     return roles.some(role => route.meta.roles.includes(role))
//   } else {
//     return true
//   }
// }

/**
//  * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
// export function filterAsyncRoutes(routes, roles) {
//   const res = []

//   routes.forEach(route => {
//     const tmp = { ...route }
//     if (hasPermission(roles, tmp)) {
//       if (tmp.children) {
//         // tmp.children = filterAsyncRoutes(tmp.children, roles)    // 暂时不做前端匹配映射，通过后台API服务控制（张旭 后续改造）
//       }
//       res.push(tmp)
//     }
//   })

//   return res
// }

/**
 * 递归转换通过API获取的用户权限菜单
 * @param asyncRouterMap 通过API获取的用户权限菜单
 */

function dynamicLoadComponent(asyncRouterMap) {
  const accessedRouters = asyncRouterMap.filter(route => {
    if (route.component) {
      if (route.component === 'Layout') { // Layout组件特殊处理
        route.component = Layout
      } else {
        // 加上try...catch可以解决路由指向的资源文件不存在时界面无法加载的情况
        try {
          route.component = dynamicLoadRouter(route.component)
        } catch (e) {
          console.log('component load exception: ' + route.component)
          route.component = null
        }
      }
    }
    if (route.children && route.children.length) {
      route.children = dynamicLoadComponent(route.children)
    }
    return true
  })
  return accessedRouters
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({
    commit
  }) {
    return new Promise(resolve => {
      // let accessedRoutes
      // if (roles.includes('admin')) {
      //   accessedRoutes = asyncRoutes || []
      // } else {
      //   accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      // }
      // commit('SET_ROUTES', accessedRoutes)
      // resolve(accessedRoutes)
      const token = getToken()
      asyncRoutes({
        token
      }).then(response => {
        // console.log(response)
        // console.log('ddddfadafdaf')
        const accessedRoutes = dynamicLoadComponent(response.Data || [])
        // console.log(accessedRoutes)
        commit('SET_ROUTES', accessedRoutes)
        resolve(accessedRoutes)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
