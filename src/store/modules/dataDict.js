import { fetchList as fetchDataDict } from '@/api/Sys/Sys_Dictionary'

const state = {
  dict: []
}

const mutations = {
  SET_DATA_DICT: (state, dict) => {
    state.dict = dict
  }
}

const actions = {
  getDataDict({ commit }) {
    return new Promise((resolve, reject) => {
      fetchDataDict({ typeCode: '' }).then(response => {
        if (response.Code === 2000) {
          commit('SET_DATA_DICT', response.Data)
        }
        resolve(response.Data)
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
