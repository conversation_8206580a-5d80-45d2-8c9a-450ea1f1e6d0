import {
  doLogin,
  logout
} from '@/api/Sys/Sys_User'
import {
  getToken,
  setToken,
  removeToken
} from '@/utils/auth'
import router, {
  resetRouter
} from '@/router'

const state = {
  token: getToken(),
  name: '',
  avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
  introduction: '',
  roles: ['内置系统管理员'], // 用户角色
  permissionbuttons: [], // 用户按钮资源清单'Sys.Sys_User.Add'
  userinfo: null,
  userRole: null // 判断是否为系统角色
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token // 存储在cookie中，不另外做持久化处理
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
    sessionStorage.setItem('introduction', introduction)
  },
  SET_NAME: (state, name) => {
    state.name = name
    sessionStorage.setItem('name', name)
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
    sessionStorage.setItem('avatar', avatar)
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
    sessionStorage.setItem('roles', JSON.stringify(roles))
  },
  SET_PERMISSIONBUTTONS: (state, permissionbuttons) => {
    state.permissionbuttons = permissionbuttons
    sessionStorage.setItem('permissionbuttons', JSON.stringify(permissionbuttons))
  },
  SET_USERINFO: (state, userinfo) => {
    state.userinfo = userinfo
    sessionStorage.setItem('userinfo', JSON.stringify(userinfo))
  },
  SET_USERROLES: (state, userRole) => {
    state.userRole = userRole
    sessionStorage.setItem('userRole', userRole)
  }
}

const actions = {
  // user login
  login({
    commit
  }, userInfo) {
    const {
      username,
      password
    } = userInfo
    return new Promise((resolve, reject) => {
      doLogin({
        username: username.trim(),
        password: password
      }).then(response => {
        console.log(response)
        const {
          Token,
          UserInfo,
          PermissionButtons
        } = response.Data
        commit('SET_TOKEN', Token)
        commit('SET_ROLES', ['admin']) // 暂时未启用
        commit('SET_NAME', UserInfo.UserName) // data.UserInfo.UserName
        commit('SET_AVATAR', 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif') // 头像(暂时未启用)
        commit('SET_INTRODUCTION', UserInfo.Remark) // introduction 用户简介
        commit('SET_PERMISSIONBUTTONS', PermissionButtons) // data.PermissionButtons
        commit('SET_USERINFO', UserInfo) // data.PermissionButtons
        commit('SET_USERROLES', UserInfo.UserRole)
        setToken(Token)
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 退出登录
  logout({
    commit,
    state
  }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', []) // 不需要
        commit('SET_USERROLES', '')
        removeToken()
        resetRouter()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 移除Token
  resetToken({
    commit
  }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_USERROLES', '')
      removeToken()
      resolve()
    })
  },

  // 动态修改权限(在后台配置修改用户权限时，客户端不需要重新登录的情况) 张旭
  changeRoles({
    commit,
    dispatch
  }, role) {
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      const {
        roles
      } = await dispatch('getInfo')
      resetRouter()

      // 根据角色后台获取角色权限（多角色）
      const accessRoutes = await dispatch('permission/generateRoutes', roles, {
        root: true
      })

      // 动态添加路由
      router.addRoutes(accessRoutes)

      // 重置已访问视图和缓存视图
      dispatch('tagsView/delAllViews', null, {
        root: true
      })

      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
