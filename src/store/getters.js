const getters = {
  sidebar: state => state.app.sidebar,
  language: state => state.app.language, // 用户选择语言
  size: state => state.app.size, // 全局字体大小
  device: state => state.app.device, // 用户设备(desktop)
  visitedViews: state => state.tagsView.visitedViews, //
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token, // 登录Token
  avatar: state => state.user.avatar ? state.user.avatar : sessionStorage.getItem('avatar'), // 用户头像
  name: state => state.user.name ? state.user.name : sessionStorage.getItem('name'), // 用户名
  introduction: state => state.user.introduction ? state.user.introduction : sessionStorage.getItem('introduction'), // 用户简介
  roles: state => state.user.roles, // 用户角色
  permission_routes: state => state.permission.routes, // 用户权限路由permission.js,异步重新获取机制，不考虑持久化处理
  permission_buttons: state => state.user.permissionbuttons.length > 0 ? state.user.permissionbuttons : JSON.parse(sessionStorage.getItem('permissionbuttons')), // 用户菜单权限
  userinfo: state => state.user.userinfo ? state.user.userinfo : JSON.parse(sessionStorage.getItem('userinfo')),
  userRole: state => state.user.userRole ? state.user.userRole : JSON.parse(sessionStorage.getItem('userRole')),
  errorLogs: state => state.errorLog.logs, // 错误日志

  dataDict: state => state.dataDict.dict
}
export default getters
