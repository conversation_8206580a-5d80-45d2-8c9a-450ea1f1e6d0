/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const mdRouter = {
  path: '/MD',
  component: Layout,
  redirect: '/MD/MD_PrintTemplate',
  name: 'MD',
  meta: {
    title: '打印模板管理',
    icon: 'table'
  },
  children: [
    {
      path: 'MD_PrintTemplate',
      component: () => import('@/views/MD/MD_PrintTemplate/list'),
      name: 'MD_PrintTemplate',
      meta: {
        title: '打印模板管理',
        icon: 'table',
        noCache: true
      }
    },
    {
      path: 'MD_PrintTemplateParameter',
      component: () => import('@/views/MD/MD_PrintTemplateParameter/list'),
      name: 'MD_PrintTemplateParameter',
      meta: {
        title: '打印参数模板管理',
        icon: 'setting',
        noCache: true
      }
    },
    {
      path: 'MD_PrintTemplateEdit',
      component: () => import('@/views/MD/MD_PrintTemplate/index'),
      name: 'MD_PrintTemplateEdit',
      meta: {
        title: '打印模板编辑器',
        icon: 'edit',
        noCache: true,
        hidden: true // 隐藏菜单，只能通过程序跳转访问
      }
    },
    {
      path: 'MD_NonStandardConfig',
      component: () => import('@/views/MD/MD_NonStandardConfig/list'),
      name: 'MD_NonStandardConfig',
      meta: {
        title: '非标参数配置',
        icon: 'setting',
        noCache: true
      }
    }
  ]
}

export default mdRouter
