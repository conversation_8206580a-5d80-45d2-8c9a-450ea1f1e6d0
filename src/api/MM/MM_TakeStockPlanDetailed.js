import request from '@/utils/request'

// 获取实体信息
export function getDetailEntity(query) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/GetEntity',
    method: 'get',
    params: query
  })
}
// 获取用户列表清单fetchUserList
export function fetchDetailList(query) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/GetPageList',
    method: 'get',
    params: query
  })
}

export function addDetail(entity) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/Add',
    method: 'post',
    data: entity
  })
}

export function updateDetail(entity) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDeleteDetail(ids) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchDetailPage(query) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/GetPage',
    method: 'get',
    params: query
  })
}

export function addDetails(data) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/AddList',
    method: 'post',
    data: data
  })
}
export function updateDetails(data) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/UpdateList',
    method: 'post',
    data: data
  })
}
export function updatePlanList(data) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/UpdatePlanList',
    method: 'post',
    data: data
  })
}

export function fetchListByDocNum(query) {
  return request({
    url: '/MM/MM_TakeStockPlanDetailed/GetListByDocNum',
    method: 'get',
    params: query
  })
}
