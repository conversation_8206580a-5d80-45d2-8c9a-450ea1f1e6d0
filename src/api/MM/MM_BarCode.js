import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_BarCode/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_BarCode/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_BarCode/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_BarCode/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_BarCode/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MM/MM_BarCode/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchBatchNum() {
  return request({
    url: '/MM/MM_BarCode/GetBatchNum',
    method: 'get'
  })
}

export function batchAdd(data) {
  return request({
    url: '/MM/MM_BarCode/BatchAdd',
    method: 'post',
    data: data
  })
}

export function fetchEarliestStock(query) {
  return request({
    url: '/MM/MM_BarCode/GetEarliestStock',
    method: 'get',
    params: query
  })
}

export function addList(data) {
  return request({
    url: '/MM/MM_BarCode/Add',
    method: 'post',
    data: data
  })
}

// entity==>BarCode 数组
export function printToPDF(entity) {
  return request({
    url: '/PO/MM_BarCode/Print',
    method: 'post',
    data: entity
  })
}

export function fetchNewBarcode() {
  return request({
    url: '/MM/MM_BarCode/GetNewBarcode',
    method: 'get'
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_BarCode/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
export function isScanList(query) {
  return request({
    url: '/MM/MM_BarCode/GetInScanListForOperation',
    method: 'get',
    params: query
  })
}
// 查询SAP物料主数据信息
export function GetXZSAP_MARC(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_MARC',
    method: 'get',
    params: query
  })
}
// 打印
export function printBarCodeToPDF(query) {
  return request({
    url: '/MM/MM_BarCode/Print',
    method: 'post',
    data: query
  })
}
// 标签打印物料件号（有条码）
export function PrintItemCode(query) {
  return request({
    url: '/MM/MM_BarCode/PrintItemCode',
    method: 'post',
    data: query
  })
}
// 标签打印物料件号(无条码)
export function PrintItemCode2(query) {
  return request({
    url: '/MM/MM_BarCode/PrintItemCode_2',
    method: 'post',
    data: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/MM/MM_BarCode/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/MM/MM_BarCode/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
// 导出模板
export function ExportToExcelModelItem(query) {
  return request({
    url: '/MM/MM_BarCode/ExportToExcelModelItem',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function ImportExcelToDataItem(entitys) {
  return request({
    url: '/MM/MM_BarCode/ImportExcelToDataItem',
    method: 'post',
    data: entitys
  })
}
