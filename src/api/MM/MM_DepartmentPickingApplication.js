import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_DepRequisition/GetPageList',
    method: 'get',
    params: query
  })
}
// 分页查询子表
export function GetPageList(query) {
  return request({
    url: '/MM/MM_DepRequisition/GetDetailPageList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_DepRequisition/Save',
    method: 'post',
    data: entity
  })
}
// 批量新增
export function BatchSubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_DepRequisition/BatchSave',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_DepRequisition/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_DepRequisition/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_DepRequisition/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入模板
// export function exportToExcelModel(query) {
//   return request({
//     url: '/MM/MM_DepRequisition/ExportToExcelModelMain',
//     method: 'get',
//     params: query,
//     responseType: 'blob'
//   })
// }

// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_DepRequisition/GetDocNum',
    method: 'get',
    params: query
  })
}
// 获取中间表
export function GetPageListBySAP(query) {
  return request({
    url: '/MM/MM_DepRequisition/GetStockInfo',
    method: 'get',
    params: query
  })
}
// 查询详情字表信息
export function GetList(query) {
  return request({
    url: '/MM/MM_DepRequisition/GetList',
    method: 'get',
    params: query
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_DepRequisition/DoPost',
    method: 'post',
    data: entity
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_DepRequisition/Print',
    method: 'get',
    params: entity
  })
}
// 查询SAP移动类型信息
export function GetDictionaryForDepReq(entity) {
  return request({
    url: '/MM/MM_DepRequisition/GetDictionaryForDepReq',
    method: 'get',
    params: entity
  })
}

// 查询所有成本中心
export function GetXZ_SAP_CSKS(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_CSKS',
    method: 'get',
    params: entity
  })
}

// 查询所有总账科目
export function GetXZ_SAP_SKA1(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_SKA1',
    method: 'get',
    params: entity
  })
}
// 查询所有总账科目(Z35,Z36)
export function GetXZ_SAP_SKA12(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_SKA12',
    method: 'get',
    params: entity
  })
}

// 查询所有内部订单
export function GetSAP_001(entity) {
  return request({
    url: '/SAP/XZSAP/GetSAPAUFK',
    method: 'get',
    params: entity
  })
}
// 查询所有资产卡片
export function GetSAP_002(entity) {
  return request({
    url: '/SAP/XZSAP/GetSAPANLA',
    method: 'get',
    params: entity
  })
}
// 审核
export function Audit(entity) {
  return request({
    url: '/MM/MM_DepRequisition/Audit',
    method: 'get',
    params: entity
  })
}
// 取消
export function Reject(entity) {
  return request({
    url: '/MM/MM_DepRequisition/Reject',
    method: 'get',
    params: entity
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/MM/MM_DepRequisition/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 作废
export function getCancel(query) {
  return request({
    url: '/MM/MM_DepRequisition/Cancel',
    method: 'get',
    params: query
  })
}
