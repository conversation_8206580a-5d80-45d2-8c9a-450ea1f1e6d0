import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_Return/GetPageList',
    method: 'get',
    params: query
  })
}
// 分页查询子表
export function GetPageList(query) {
  return request({
    url: '/MM/MM_Return/GetPageDetailList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_Return/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_Return/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_Return/DoPost',
    method: 'post',
    data: entity
  })
}
// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_Return/Print',
    method: 'get',
    params: entity
  })
}

// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_Return/GetDocNum',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_Return/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_Return/Update',
    method: 'post',
    data: entity
  })
}

// 查询SAP中间库信息
export function GetXZSAP_PurchaseOrder(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_EKKO',
    method: 'get',
    params: query
  })
}

// 编辑根据条件查询明细信息
export function GetList(query) {
  return request({
    url: '/MM/MM_Return/GetList',
    method: 'get',
    params: query
  })
}

// 获取供应商库存
export function GetSupplierStock(query) {
  return request({
    url: '/MM/MM_Return/GetSupplierStock',
    method: 'get',
    params: query
  })
}
// 冲销
export function PassPost(entity) {
  return request({
    url: '/MM/MM_Return/PassPost',
    method: 'post',
    data: entity
  })
}
