import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_TakeStockPlan/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_TakeStockPlan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_TakeStockPlan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_TakeStockPlan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_TakeStockPlan/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MM/MM_TakeStockPlan/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchDocNum() {
  return request({
    url: '/MM/MM_TakeStockPlan/GetDocNum',
    method: 'get'
  })
}

export function finishTakeStockPlan(planID) {
  return request({
    url: '/MM/MM_TakeStockPlan/FinishTakeStockPlan',
    method: 'post',
    data: planID
  })
}

export function reviewTakeStockPlan(data) {
  return request({
    url: '/MM/MM_TakeStockPlan/ReviewTakeStockPLan',
    method: 'post',
    data: data
  })
}

export function confirmTakeStockPlan(data) {
  return request({
    url: '/MM/MM_TakeStockPlan/ConfirmTakeStockPLan',
    method: 'post',
    data: data
  })
}

// entity==>BarCode 数组
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_TakeStockPlan/Print',
    method: 'get',
    params: entity
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_TakeStockScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function exportExcelFile1(query) {
  return request({
    url: '/MM/MM_TakeStockPlan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
