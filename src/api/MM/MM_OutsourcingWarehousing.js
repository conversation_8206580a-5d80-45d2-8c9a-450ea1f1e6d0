import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_Warehousing/GetPageList',
    method: 'get',
    params: query
  })
}

// 分页查询主表
export function GetDetailedPageList(query) {
  return request({
    url: '/MM/MM_Warehousing/GetDetailedPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_Warehousing/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_Warehousing/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_Warehousing/DoPost',
    method: 'post',
    data: entity
  })
}
// 过账
export function Update(entity) {
  return request({
    url: '/MM/MM_Warehousing/Update',
    method: 'post',
    data: entity
  })
}
// 冲销
export function PassPost(entity) {
  return request({
    url: '/MM/MM_Warehousing/PassPost',
    method: 'post',
    data: entity
  })
}
