import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_TakeStockScan/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_TakeStockScan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_TakeStockScan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_TakeStockScan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_TakeStockScan/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MM/MM_TakeStockScan/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchPageCY(query) {
  return request({
    url: '/MM/MM_TakeStockScan/GetPageChaYi',
    method: 'get',
    params: query
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_TakeStockScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
export function exportExcelFileCY(query) {
  return request({
    url: '/MM/MM_TakeStockScan/ExportToExcelFileCY',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_TakeStockScan/DoPost',
    method: 'post',
    data: entity
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/MM/MM_TakeStockScan/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/MM/MM_TakeStockScan/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
