import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_OutScan/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_OutScan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_OutScan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_OutScan/Update',
    method: 'post',
    data: entity
  })
}
export function reivew(data) {
  return request({
    url: '/MM/MM_OutScan/Review',
    method: 'post',
    data: data
  })
}
// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_OutScan/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MM/MM_OutScan/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchDocNum() {
  return request({
    url: '/MM/MM_OutScan/GetDocNum',
    method: 'get'
  })
}

export function addDetails(data) {
  return request({
    url: '/MM/MM_OutScan/AddList',
    method: 'post',
    data: data
  })
}

export function postToSAP(data) {
  return request({
    url: '/MM/MM_OutScan/PostToSAP',
    method: 'post',
    data: data
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_OutScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
