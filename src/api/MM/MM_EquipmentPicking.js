import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_EquipmentPicking/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_EquipmentPicking/DoPost',
    method: 'post',
    data: entity
  })
}
// 审核
export function getAudit(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/Audit',
    method: 'get',
    params: query
  })
}
// 驳回
export function getReject(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/Reject',
    method: 'get',
    params: query
  })
}
// 作废
export function getCancel(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/Cancel',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_EquipmentPicking/SavePC',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_EquipmentPicking/Update',
    method: 'post',
    data: entity
  })
}
// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/GetDocNum',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 查询移动类型
export function GetDictionaryForEquipment(entity) {
  return request({
    url: '/MM/MM_EquipmentPicking/GetDictionaryForEquipment',
    method: 'get',
    params: entity
  })
}
// 成本中心
export function GetXZ_SAP_CSKS(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_CSKS',
    method: 'get',
    params: entity
  })
}
// 总账科目
export function GetXZ_SAP_SKA1(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_SKA1',
    method: 'get',
    params: entity
  })
}
// 查询所有资产卡片
export function GetSAP_002(entity) {
  return request({
    url: '/SAP/XZSAP/GetSAPANLA',
    method: 'get',
    params: entity
  })
}
// 查询详情字表信息
export function GetList(query) {
  return request({
    url: '/MM/MM_EquipmentPicking/GetList',
    method: 'get',
    params: query
  })
}
