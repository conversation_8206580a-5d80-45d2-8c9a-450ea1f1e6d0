import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_Dispatch/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_Dispatch/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_Dispatch/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_Dispatch/Print',
    method: 'get',
    params: entity
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_Dispatch/DoPost',
    method: 'post',
    data: entity
  })
}
// 冲销
export function PassPost(entity) {
  return request({
    url: '/MM/MM_Dispatch/PassPost',
    method: 'post',
    data: entity
  })
}
