import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_InScan/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_InScan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_InScan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_InScan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_InScan/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MM/MM_InScan/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchBatchNum() {
  return request({
    url: '/MM/MM_InScan/GetBatchNum',
    method: 'get'
  })
}

export function batchAdd(data) {
  return request({
    url: '/MM/MM_InScan/BatchAdd',
    method: 'post',
    data: data
  })
}

export function fetchDocNum() {
  return request({
    url: '/MM/MM_InScan/GetDocNum',
    method: 'get'
  })
}

export function addDetails(data) {
  return request({
    url: '/MM/MM_InScan/AddList',
    method: 'post',
    data: data
  })
}

export function postToSAP(data) {
  return request({
    url: '/MM/MM_InScan/PostToSAP',
    method: 'post',
    data: data
  })
}
export function reivew(data) {
  return request({
    url: '/MM/MM_InScan/Review',
    method: 'post',
    data: data
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_InScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
