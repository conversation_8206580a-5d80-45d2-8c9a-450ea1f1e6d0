import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_BoxInfo/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_BoxInfo/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_BoxInfo/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_BoxInfo/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_BoxInfo/Delete',
    method: 'delete',
    data: ids
  })
}

