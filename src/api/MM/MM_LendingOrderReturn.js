import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_LendingOrderReturn/GetPageList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_LendingOrderReturn/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_LendingOrderReturn/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_LendingOrderReturn/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_LendingOrderReturn/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_LendingOrderReturn/GetDocNum',
    method: 'get',
    params: query
  })
}
// 获取中间表
export function GetPageListBySAP(query) {
  return request({
    url: '/MM/MM_LendingOrderReturn/GetItem',
    method: 'get',
    params: query
  })
}
// 查询详情字表信息
export function GetList(query) {
  return request({
    url: '/MM/MM_LendingOrderReturn/GetList',
    method: 'get',
    params: query
  })
}
// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_LendingOrderReturn/Print',
    method: 'get',
    params: entity
  })
}
