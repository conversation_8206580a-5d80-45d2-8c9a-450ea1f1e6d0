import request from '@/utils/request'

// 获取登录用户权限路由
export function getPageList(data) {
  return request({
    url: '/MM/MM_StockTodo/GetPageList',
    method: 'get',
    params: data
  })
}

// 获取登录用户权限路由
export function getDetailPageList(data) {
  return request({
    url: '/MM/MM_StockTodo/GetDetailPageList',
    method: 'get',
    params: data
  })
}

// 获取件号列表
export function getPartList(data) {
  return request({
    url: '/MM/MM_StockTodo/GetPartList',
    method: 'get',
    params: data
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_StockTodo/Create',
    method: 'post',
    data: entity
  })
}

// 更新用户基本信息
export function update(entity) {
  return request({
    url: '/MM/MM_StockTodo/Update',
    method: 'post',
    data: entity
  })
}

// 批量删除
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_StockTodo/Delete',
    method: 'delete',
    data: ids
  })
}

export function warehousing(entity) {
  return request({
    url: '/MM/MM_StockTodo/Warehousing',
    method: 'post',
    data: entity
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_StockTodo/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
