import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MM/MM_BarCodeBox/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MM/MM_BarCodeBox/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MM/MM_BarCodeBox/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MM/MM_BarCodeBox/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_BarCodeBox/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MM/MM_BarCodeBox/GetPage',
    method: 'get',
    params: query
  })
}
export function getPrintModel(query) {
  return request({
    url: '/MM/MM_BarCodeBox/GetPrintModel',
    method: 'get',
    params: query
  })
}
