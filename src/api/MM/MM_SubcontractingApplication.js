import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_PickingApply/GetPageList',
    method: 'get',
    params: query
  })
}
// 分页查询子表
export function GetPageList(query) {
  return request({
    url: '/MM/MM_PickingApply/GetPageDetailList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_PickingApply/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_PickingApply/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_PickingApply/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_PickingApply/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_PickingApply/GetDocNum',
    method: 'get',
    params: query
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_PickingApply/Print',
    method: 'get',
    params: entity
  })
}

// 查询SAP中间库采购订单信息
export function GetXZSAP_PurchaseOrder(query) {
  return request({
    url: '/SAP/XZSAP/GetSAP_EKKOForSubcontractingApplication',
    method: 'get',
    params: query
  })
}

// 查询SAP中间库物料主数据信息
export function GetXZSAP_MARC(query) {
  return request({
    url: '/SAP/XZSAP/GetSAP_MARCForSubcontractingApplication',
    method: 'get',
    params: query
  })
}

// 编辑根据条件查询明细信息
export function GetList(query) {
  return request({
    url: '/MM/MM_PickingApply/GetList',
    method: 'get',
    params: query
  })
}

// 删除明细信息
export function DeleteDetail(ids) {
  return request({
    url: '/MM/MM_PickingApply/DeleteDetail',
    method: 'delete',
    data: ids
  })
}
// 委外发料校验是否发料
export function CheckDeleteAndUpdate(query) {
  return request({
    url: '/MM/MM_PickingApply/CheckDeleteAndUpdate',
    method: 'get',
    params: query
  })
}
export function CheckDelete(query) {
  return request({
    url: '/MM/MM_PickingApply/CheckDelete',
    method: 'delete',
    data: query
  })
}
// 查询供应商主数据
export function GetSRM_SupplierInfo(query) {
  return request({
    url: '/SRM/XZSRM/GetSRM_SupplierInfo',
    method: 'get',
    params: query
  })
}
// 查询采购订单组件
export function GetXZSAP_EKKO(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_RESBM',
    method: 'post',
    data: query
  })
}
// 查询供应商主数据
export function ValidateStockOut(query) {
  return request({
    url: '/MM/MM_PickingApply/ValidateStockOut',
    method: 'get',
    params: query
  })
}
