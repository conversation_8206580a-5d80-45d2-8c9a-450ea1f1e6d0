import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_ScrapApplication/GetPageList',
    method: 'get',
    params: query
  })
}
// 分页查询子表
export function GetPageList(query) {
  return request({
    url: '/MM/MM_ScrapApplication/GetDetailPageList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_ScrapApplication/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_ScrapApplication/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_ScrapApplication/GetDocNum',
    method: 'get',
    params: query
  })
}
// 获取中间表
export function GetPageListBySAP(query) {
  return request({
    url: '/MM/MM_ScrapApplication/GetStockInfo',
    method: 'get',
    params: query
  })
}
// 查询详情字表信息
export function GetList(query) {
  return request({
    url: '/MM/MM_ScrapApplication/GetList',
    method: 'get',
    params: query
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/DoPost',
    method: 'post',
    data: entity
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/Print',
    method: 'get',
    params: entity
  })
}

// 审核
export function Audit(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/Audit',
    method: 'get',
    params: entity
  })
}
// 取消
export function Reject(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/Reject',
    method: 'get',
    params: entity
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/MM/MM_ScrapApplication/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/MM/MM_ScrapApplication/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}

// 过账
export function PassPost(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/PassPost',
    method: 'post',
    data: entity
  })
}
// 查询SAP移动类型信息
export function GetDictionaryForDepReq(entity) {
  return request({
    url: '/MM/MM_ScrapApplication/GetDictionaryForDepReq',
    method: 'get',
    params: entity
  })
}

// 查询所有成本中心
export function GetXZ_SAP_CSKS(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_CSKS',
    method: 'get',
    params: entity
  })
}
// 作废
export function getCancel(query) {
  return request({
    url: '/MM/MM_ScrapApplication/Cancel',
    method: 'get',
    params: query
  })
}
