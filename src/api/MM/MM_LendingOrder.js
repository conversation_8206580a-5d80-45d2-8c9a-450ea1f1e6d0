import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_LendingOrder/GetPageList',
    method: 'get',
    params: query
  })
}
// 分页查询子表
export function GetPageList(query) {
  return request({
    url: '/MM/MM_LendingOrder/GetDetailPageList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/MM/MM_LendingOrder/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/MM/MM_LendingOrder/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_LendingOrder/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_LendingOrder/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/MM/MM_LendingOrder/GetDocNum',
    method: 'get',
    params: query
  })
}
// 获取中间表
export function GetPageListBySAP(query) {
  return request({
    url: '/MM/MM_LendingOrder/GetStockInfo',
    method: 'get',
    params: query
  })
}
// 查询详情字表信息
export function GetList(query) {
  return request({
    url: '/MM/MM_LendingOrder/GetList',
    method: 'get',
    params: query
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_LendingOrder/DoPost',
    method: 'post',
    data: entity
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/MM/MM_LendingOrder/Print',
    method: 'get',
    params: entity
  })
}

// 查询所有成本中心
export function GetXZ_SAP_CSKS(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_CSKS',
    method: 'get',
    params: entity
  })
}

// 查询所有总账科目
export function GetXZ_SAP_SKA1(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_SKA1',
    method: 'get',
    params: entity
  })
}

// 审核
export function Audit(entity) {
  return request({
    url: '/MM/MM_LendingOrder/Audit',
    method: 'get',
    params: entity
  })
}
// 取消
export function Reject(entity) {
  return request({
    url: '/MM/MM_LendingOrder/Reject',
    method: 'get',
    params: entity
  })
}
// 查询借出单-交易对象信息
export function GetDictionaryForLenOrder(entity) {
  return request({
    url: '/MM/MM_LendingOrder/GetDictionaryForLenOrder',
    method: 'get',
    params: entity
  })
}
// 查询所有供应商主数据
export function GetSRM_SupplierInfo(entity) {
  return request({
    url: '/SRM/XZSRM/GetSRM_SupplierInfo',
    method: 'get',
    params: entity
  })
}
// 客户主数据
export function GetXZ_SAP_KNA1(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_KNA1',
    method: 'get',
    params: entity
  })
}
// 查询所有供应商主数据
export function GetXZ_SAP_KNA1ForEmp(entity) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_KNA1ForEmp',
    method: 'get',
    params: entity
  })
}
// 查询物料信息
export function GetItem(entity) {
  return request({
    url: '/MM/MM_LendingOrder/GetItem',
    method: 'get',
    params: entity
  })
}

