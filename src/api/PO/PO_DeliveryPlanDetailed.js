import request from '@/utils/request'

export function fetchDetailedList(query) {
  return request({
    url: '/PO/PO_DeliveryPlanDetailed/GetList',
    method: 'get',
    params: query
  })
}

export function fetchPurchaseOrderDetailList(query) {
  return request({
    url: '/PO/PO_DeliveryPlan/GetPurchaseOrderDetailList',
    method: 'get',
    params: query
  })
}

export function getDeliveryPlanDetailList(query) {
  return request({
    url: '/PO/PO_DeliveryPlan/GetPurchaseOrderDeliveryPlanDetailList',
    method: 'post',
    data: query
  })
}
