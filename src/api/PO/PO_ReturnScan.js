import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PO/PO_ReturnScan/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PO/PO_ReturnScan/Delete',
    method: 'delete',
    data: ids
  })
}

export function doPost(requestData) {
  return request({
    url: '/PO/PO_ReturnScan/DoPost',
    method: 'post',
    data: requestData
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_ReturnScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 采购退货子表信息查询
export function GetDetailedPageList(query) {
  return request({
    url: '/PO/PO_ReturnScan/GetDetailedPageList',
    method: 'get',
    params: query
  })
}
// 采购退货SAP信息查询
export function GetDetailListForTest(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_EKKOForBSART',
    method: 'get',
    params: query
  })
}
// 采购退货明细信息提交
export function SubmitScanInfo(requestData) {
  return request({
    url: '/PO/PO_ReturnScan/SubmitScanInfo',
    method: 'post',
    data: requestData
  })
}

export function GetReturnScanForDocNum(query) {
  return request({
    url: '/PO/PO_ReturnScan/GetReturnScanForDocNum',
    method: 'get',
    params: query
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PO/PO_ReturnScan/GetDocNum',
    method: 'get',
    params: query
  })
}

// 编辑时根据单号查询明细信息
export function Update(requestData) {
  return request({
    url: '/PO/PO_ReturnScan/Update',
    method: 'post',
    data: requestData
  })
}

// 获取系统生成的单号
export function GetList(query) {
  return request({
    url: '/PO/PO_ReturnScan/GetList',
    method: 'get',
    params: query
  })
}

// 查询SAP中间库仓库
export function GetXZ_SAP(query) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_T001L',
    method: 'get',
    params: query
  })
}
// 查询仓库下的所有区域
export function GetWarehouseRegion(data) {
  return request({
    url: '/MD/MD_Warehouse/GetWarehouseRegion',
    method: 'get',
    params: data
  });
}
// 查询区域下的所有库位信息
export function GetRegionBinLocation(data) {
  return request({
    url: '/MD/MD_Region/GetRegionBinLocation',
    method: 'get',
    params: data
  });
}

// 采购退货管理 新增/编辑界面 查询所有库位信息
export function GetBinLocationAll(data) {
  return request({
    url: '/MD/MD_BinLocation/GetBinLocationAll',
    method: 'get',
    params: data
  });
}

// 打印
export function Print(data) {
  return request({
    url: '/PO/PO_ReturnScan/Print',
    method: 'get',
    params: data
  });
}

