import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PO/PO_BarCode/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PO/PO_BarCode/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PO/PO_BarCode/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PO/PO_BarCode/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PO/PO_BarCode/Delete',
    method: 'delete',
    data: ids
  })
}

// 获取交货计划单子表列表信息
export function getDeliveryPlanDetailList(query) {
  return request({
    url: '/PO/PO_BarCode/GetDeliveryPlanDetails',
    method: 'get',
    params: query
  })
}

// entity==>BarCode 数组
export function printToPDF(entity) {
  return request({
    url: '/PO/PO_BarCode/Print',
    method: 'post',
    data: entity
  })
}
export function exportExcelFile(query) {
  return request({
    url: 'PO/PO_BarCode/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
