import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/PO/PO_Inspection/GetPageList',
    method: 'get',
    params: query
  })
}

export function fetchDetailList(query) {
  return request({
    url: '/PO/PO_InspectionDetailed/GetList',
    method: 'get',
    params: query
  })
}

export function batchDelete(query) {
  return request({
    url: '/PO/PO_Inspection/Delete',
    method: 'delete',
    data: query
  })
}

export function doPost(requestData) {
  return request({
    url: '/PO/PO_Inspection/DoPost',
    method: 'post',
    data: requestData
  })
}
export function update(requestData) {
  return request({
    url: '/PO/PO_Inspection/UpdatePoInspectionDetail',
    method: 'post',
    data: requestData
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_Inspection/ExportToExcelFileSreach',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
