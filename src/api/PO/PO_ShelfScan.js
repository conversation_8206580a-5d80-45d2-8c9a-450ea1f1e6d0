import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PO/PO_ShelfScan/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PO/PO_ShelfScan/Delete',
    method: 'delete',
    data: ids
  })
}

export function doPost(requestData) {
  return request({
    url: '/PO/PO_ShelfScan/DoPost',
    method: 'post',
    data: requestData
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_ShelfScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
