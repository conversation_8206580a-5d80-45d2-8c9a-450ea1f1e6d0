import request from '@/utils/request'

// 采购收货查询
export function fetchList(query) {
  return request({
    url: '/PO/PO_PurchaseReceipt/GetPageList',
    method: 'get',
    params: query
  })
}

// 采购收货过账
export function doPost(requestData) {
  return request({
    url: '/PO/PO_PurchaseReceipt/DoPost',
    method: 'post',
    data: requestData
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PO/PO_PurchaseReceipt/Delete',
    method: 'delete',
    data: ids
  })
}

// 采购收货导出
export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_PurchaseReceipt/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 采购收货编辑
export function Update(data) {
  return request({
    url: '/PO/PO_PurchaseReceipt/Update',
    method: 'post',
    data: data
  })
}
// 冲销
export function PassPost(data) {
  return request({
    url: '/PO/PO_PurchaseReceipt/PassPost',
    method: 'post',
    data: data
  })
}
