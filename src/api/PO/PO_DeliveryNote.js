import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/PO/PO_DeliveryNote/GetPageList',
    method: 'get',
    params: query
  })
}

export function batchDelete(query) {
  return request({
    url: '/PO/PO_DeliveryNote/Delete',
    method: 'delete',
    data: query
  })
}

export function addDeliveryNote(query) {
  return request({
    url: '/PO/PO_DeliveryNote/Add',
    method: 'post',
    data: query
  })
}

// entity==>BarCode 数组
export function printToPDF(query) {
  return request({
    url: '/PO/PO_DeliveryNote/Print',
    method: 'get',
    params: query
  })
}
export function exportExcelFile(query) {
  return request({
    url: 'PO/PO_DeliveryNote/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
