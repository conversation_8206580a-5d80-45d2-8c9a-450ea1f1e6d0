import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PO/PO_ConsignIn/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PO/PO_ConsignIn/Delete',
    method: 'delete',
    data: ids
  })
}

export function doPost(requestData) {
  return request({
    url: '/PO/PO_ConsignIn/DoPost',
    method: 'post',
    data: requestData
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_ConsignIn/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function getEntityBySupplier(query) {
  return request({
    url: '/PO/PO_ConsignIn/GetEntityBySupplier',
    method: 'get',
    params: query
  })
}
export function AddList(requestData) {
  return request({
    url: '/PO/PO_ConsignIn/AddList',
    method: 'post',
    data: requestData
  })
}
