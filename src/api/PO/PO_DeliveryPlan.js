import request from '@/utils/request'

// 获取列表信息
export function fetchList(query) {
  return request({
    url: '/PO/PO_DeliveryPlan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(query) {
  return request({
    url: '/PO/PO_DeliveryPlan/Add',
    method: 'post',
    data: query
  })
}

export function update(entity) {
  return request({
    url: '/PO/PO_DeliveryPlan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PO/PO_DeliveryPlan/Delete',
    method: 'delete',
    data: ids
  })
}

// 下发
export function assignPlan(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/AssignDeliveryPlan',
    method: 'get',
    params: query
  })
}

// 确认
export function confirmPlan(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/ConfirmDeliveryPlan',
    method: 'get',
    params: query
  })
}
// 完成
export function successPlan(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/SuccessDeliveryPlan',
    method: 'get',
    params: query
  })
}
// 合并
export function mergePlan(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/MergeDeliveryPlan',
    method: 'get',
    params: query
  })
}

export function submitForAdd(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/SubmitDeliveryPlanForAdd',
    method: 'post',
    data: query
  })
}
export function submitForUpdate(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/SubmitDeliveryPlanForUpdate',
    method: 'post',
    data: query
  })
}
export function exportExcelFile(query) {
  return request({
    url: 'PO/PO_DeliveryPlan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
