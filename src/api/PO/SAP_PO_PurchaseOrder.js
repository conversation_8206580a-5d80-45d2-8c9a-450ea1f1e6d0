import request from '@/utils/request'

// 参数示例：{purchaseOrderID:15043}
export function syncPurchaseOrder(query) {
  return request({
    url: '/PO/SAP_PO_PurchaseOrder/SyncPurchaseOrder',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PO/SAP_PO_PurchaseOrder/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchDetailList(query) {
  return request({
    url: '/PO/SAP_PO_PurchaseOrder/GetDetailList',
    method: 'get',
    params: query
  })
}

// 参数示例：{purchaseOrderID:15043}
export function fetchPurchaseOrderSAP(query) {
  return request({
    url: '/PO/SAP_PO_PurchaseOrder/GetListSAP',
    method: 'get',
    params: query
  })
}
