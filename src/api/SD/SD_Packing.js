import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/SD/SD_Packing/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(query) {
  return request({
    url: '/SD/SD_Packing/Add',
    method: 'post',
    data: query
  })
}
export function addSDPacking(query) {
  return request({
    url: '/SD/SD_Packing/addPacking',
    method: 'post',
    data: query
  })
}

export function update(entity) {
  return request({
    url: '/SD/SD_Packing/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_Packing/Delete',
    method: 'delete',
    data: ids
  })
}
export function submitForAdd(query) {
  return request({
    url: '/SD/SD_Packing/SubmitSD_PackingForAdd',
    method: 'post',
    data: query
  })
}
export function submitForUpdate(query) {
  console.log('传到后台', query)
  return request({
    url: '/SD/SD_Packing/SubmitSD_PackingForUpdate',
    method: 'post',
    data: query
  })
}
export function fetchPage(query) {
  return request({
    url: '/SD/SD_Packing/GetPage',
    method: 'get',
    params: query
  })
}
export function printToPDF(query) {
  return request({
    url: '/SD/SD_Packing/Print',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/SD/SD_Packing/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

