import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/SD/SD_DeliveryWave/GetPageList',
    method: 'get',
    params: query
  })
}

export function fetchDetailList(query) {
  return request({
    url: '/SD/SD_DeliveryWaveDetailed/GetList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/SD/SD_DeliveryWave/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/SD/SD_DeliveryWave/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_DeliveryWave/Delete',
    method: 'delete',
    data: ids
  })
}

export function submitForUpdate(query) {
  return request({
    url: '/SD/SD_DeliveryWave/SubmitDeliveryWaveForUpdate',
    method: 'post',
    data: query
  })
}
export function updateCheckSaleQty(query) {
  return request({
    url: '/SD/SD_DeliveryWave/UpdateCheckSaleQty',
    method: 'post',
    data: query
  })
}

export function fetchUser(query) {
  return request({
    url: '/Sys/Sys_User/GetSDUserList',
    method: 'get',
    params: query
  })
}

export function fetchDocNum() {
  return request({
    url: '/SD/SD_DeliveryWave/GetDocNum',
    method: 'get'
  })
}

// 完成
export function Complete(query) {
  return request({
    url: '/SD/SD_DeliveryWave/Complete',
    method: 'get',
    params: query
  })
}

// 完成
export function designatePerson(query) {
  return request({
    url: '/SD/SD_DeliveryWave/DesignatedPerson',
    method: 'get',
    params: query
  })
}

export function fetchCustomerNextPage(query) {
  return request({
    url: '/SD/SD_DeliveryWave/GetCustomerList',
    method: 'get',
    params: query
  })
}
export function fetchCustomerLocalPage(query) {
  return request({
    url: '/SD/SD_DeliveryWave/GetLocalCustomerList',
    method: 'get',
    params: query
  })
}
export function fetchSaleOrderNextPage(query) {
  return request({
    url: '/SD/SD_DeliveryWave/GetSaleOrderList',
    method: 'get',
    params: query
  })
}

// entity==>BarCode 数组
export function printToPDF(query) {
  return request({
    url: '/SD/SD_DeliveryWave/Print',
    method: 'get',
    params: query
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/SD/SD_DeliveryWave/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
