import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/GetPageList',
    method: 'get',
    params: query
  })
}
// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/GetPageDetailList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_ConsignmentNote/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 打印
export function printToPDF(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/Print',
    method: 'get',
    params: query
  })
}
// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetList(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/GetList',
    method: 'get',
    params: query
  })
}
// 保存
export function SubmitScanInfo(entity) {
  return request({
    url: '/SD/SD_ConsignmentNote/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function Update(entity) {
  return request({
    url: '/SD/SD_ConsignmentNote/Update',
    method: 'post',
    data: entity
  })
}
// 供应商
export function GetSRM_SupplierInfo(query) {
  return request({
    url: '/SRM/XZSRM/GetSRM_SupplierInfo',
    method: 'get',
    params: query
  })
}
// 物料供应商
export function GetSRM_WLSupplierInfo(query) {
  return request({
    url: '/SRM/XZSRM/GetSRM_WLSupplierInfo',
    method: 'get',
    params: query
  })
}
// 托运部门
export function GetXZ_SAP_CSKS(query) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_CSKS',
    method: 'get',
    params: query
  })
}
// 查询所有客户地址信息
export function GetAllList(query) {
  return request({
    url: '/MD/MD_CustomerAdd/GetAllList',
    method: 'get',
    params: query
  })
}
// 条件查询物料主数据
export function GetXZSAP_MARCForCondition(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_MARCForCondition',
    method: 'get',
    params: query
  })
}
// 完成
export function Finish(query) {
  return request({
    url: '/SD/SD_ConsignmentNote/Finish',
    method: 'get',
    params: query
  })
}
