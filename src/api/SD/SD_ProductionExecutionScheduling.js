import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetPageList',
    method: 'get',
    params: query
  })
}

// 分页查询主表
export function GetPageDetailList(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetPageDetailList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_DeliveryScan/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/SD/SD_DeliveryScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/SD/SD_DeliveryScan/DoPost',
    method: 'post',
    data: entity
  })
}
// 冲销
export function PassPost(entity) {
  return request({
    url: '/SD/SD_DeliveryScan/PassPost',
    method: 'post',
    data: entity
  })
}
// 过账
export function ManualMakeConsignmentNote(entity) {
  return request({
    url: '/SD/SD_DeliveryScan/ManualMakeConsignmentNote',
    method: 'post',
    data: entity
  })
}
// 打印
export function printOrderToPDF(query) {
  return request({
    url: '/SD/SD_DeliveryScan/Print',
    method: 'get',
    params: query
  })
}
// 打印
export function printOrderToPDFByLd(query) {
  return request({
    url: '/SD/SD_DeliveryScan/LdPrint',
    method: 'get',
    params: query
  })
}
// 获取打印数据
export function getPrintInfo(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetPrintInfo',
    method: 'post',
    data: query
  })
}
export function Print_SettlementAdd(query) {
  return request({
    url: '/SD/SD_DeliveryScan/Print_SettlementAdd',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/SD/SD_DeliveryScan/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/SD/SD_DeliveryScan/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
