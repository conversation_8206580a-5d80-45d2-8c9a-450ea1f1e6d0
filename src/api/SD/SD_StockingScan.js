import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/SD/SD_StockingScan/GetEntity',
    method: 'get',
    params: query
  })
}

export function fetchPage(query) {
  return request({
    url: '/SD/SD_StockingScan/GetPage',
    method: 'get',
    params: query
  })
}
export function fetchDetailList(query) {
  return request({
    url: '/SD/SD_StockingScan/GetDetailList',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/SD/SD_StockingScan/GetPageList',
    method: 'get',
    params: query
  })
}
export function fetchCountList(query) {
  return request({
    url: '/SD/SD_StockingScan/GetPageCountList',
    method: 'get',
    params: query
  })
}
export function add(entity) {
  return request({
    url: '/SD/SD_StockingScan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/SD/SD_StockingScan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_StockingScan/Delete',
    method: 'post',
    data: ids
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/SD_StockingScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
