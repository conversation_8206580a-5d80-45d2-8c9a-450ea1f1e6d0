import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/SD/SD_BarCodeReturn/GetEntity',
    method: 'get',
	  params: query
  })
}

export function GetTHRW(query) {
  return request({
    url: '/SD/SD_BarCodeReturn/GetTHRW',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/SD/SD_BarCodeReturn/GetPage',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/SD/SD_BarCodeReturn/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/SD/SD_BarCodeReturn/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_BarCodeReturn/Delete',
    method: 'delete',
    data: ids
  })
}

// entity==>BarCode 数组
export function printToPDF(entity) {
  return request({
    url: '/SD/SD_BarCodeReturn/Print',
    method: 'post',
    data: entity
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/SD_BarCodeReturn/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
