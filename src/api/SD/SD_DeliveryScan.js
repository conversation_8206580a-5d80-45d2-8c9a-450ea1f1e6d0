import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetEntity',
    method: 'get',
	 params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetPageList',
    method: 'get',
    params: query
  })
}
export function fetchCountList(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetPageCountList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/SD/SD_DeliveryScan/Add',
    method: 'post',
    data: entityD
  })
}

export function update(entity) {
  return request({
    url: '/SD/SD_DeliveryScan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_DeliveryScan/Delete',
    method: 'post',
    data: ids
  })
}

export function addDeliveryNote(query) {
  return request({
    url: '/SD/SD_DeliveryScan/AddDeliveryScan',
    method: 'post',
    data: query
  })
}

export function doPost(requestData) {
  return request({
    url: '/SD/SD_DeliveryScan/DoPost',
    method: 'post',
    data: requestData
  })
}

export function fetchDetailList(query) {
  return request({
    url: '/SD/SD_DeliveryScan/GetDetailList',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/SD_DeliveryScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
