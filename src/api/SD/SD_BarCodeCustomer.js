import request from '@/utils/request'

// 获取列表清单
export function fetchList(query) {
  return request({
    url: '/SD/SD_BarCodeCustomer/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_BarCodeCustomer/Delete',
    method: 'delete',
    data: ids
  })
}

// entity==>BarCode 数组
export function printToPDF(entity) {
  return request({
    url: '/SD/SD_BarCodeCustomer/Print',
    method: 'post',
    data: entity
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/SD_BarCodeCustomer/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
export function update(requestData) {
  return request({
    url: '/SD/SD_BarCodeCustomer/UpdateList',
    method: 'post',
    data: requestData
  })
}
