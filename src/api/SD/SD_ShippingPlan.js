import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/SD/SD_ShippingPlan/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/SD/SD_ShippingPlan/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/SD/SD_ShippingPlan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 明细
export function GetPageList(query) {
  return request({
    url: '/SD/SD_ShippingPlan/GetPageDetailList',
    method: 'get',
    params: query
  })
}

// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/SD/SD_ShippingPlan/GetDocNum',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/SD/SD_ShippingPlan/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/SD/SD_ShippingPlan/Update',
    method: 'post',
    data: entity
  })
}

// 查询SAP中间库信息
export function GetXZSAP_PurchaseOrder(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_VBAK',
    method: 'get',
    params: query
  })
}

// 编辑根据条件查询明细信息
export function GetList(query) {
  return request({
    url: '/SD/SD_ShippingPlan/GetList',
    method: 'get',
    params: query
  })
}

// 打印
export function printOrderToPDF(query) {
  return request({
    url: '/SD/SD_ShippingPlan/Print',
    method: 'get',
    params: query
  })
}

// 上传SRM
export function UploadSRM(entity) {
  return request({
    url: '/SD/SD_ShippingPlan/UploadSRM',
    method: 'post',
    data: entity
  })
}

// 完成
export function Finish(query) {
  return request({
    url: '/SD/SD_ShippingPlan/Finish',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/SD/SD_ShippingPlan/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/SD/SD_ShippingPlan/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
