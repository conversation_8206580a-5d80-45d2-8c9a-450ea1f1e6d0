import request from '@/utils/request'

export function syncSaleOrder(data) {
  return request({
    url: '/SD/SAP_SD_SaleOrder/SyncSaleDataFromSAP',
    method: 'post',
    data: data
  })
}
export function fetchList(query) {
  return request({
    url: '/SD/SAP_SD_SaleOrder/GetPageList',
    method: 'get',
    params: query
  })
}
export function fetchSAPList(query) {
  return request({
    url: '/SD/SAP_SD_SaleOrder/GetSAPList',
    method: 'get',
    params: query
  })
}
export function fetchGetDetailList(query) {
  return request({
    url: '/SD/SAP_SD_SaleOrder/GetDetailList',
    method: 'get',
    params: query
  })
}
