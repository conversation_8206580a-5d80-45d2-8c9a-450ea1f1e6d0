import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/PO/PO_Inspection/GetInspPageList',
    method: 'get',
    params: query
  })
}

export function fetchDetailList(query) {
  return request({
    url: '/PO/PO_InspectionDetailed/GetList',
    method: 'get',
    params: query
  })
}

export function cancelInspection(query) {
  return request({
    url: '/PO/PO_Inspection/CancelInspectionCheck',
    method: 'get',
    params: query
  })
}

export function qualifyPass(entity) {
  return request({
    url: '/PO/PO_Inspection/InspectionCheckPass',
    method: 'post',
    data: entity
  })
}

export function qualifyAbort(entity) {
  return request({
    url: '/PO/PO_Inspection/InspectionCheckNG',
    method: 'post',
    data: entity
  })
}

export function batchQualifyAbort(entities) {
  return request({
    url: '/PO/PO_Inspection/BatchInspectionCheckNG',
    method: 'post',
    data: entities
  })
}

export function printToPDF(entity) {
  return request({
    url: '/PO/PO_Inspection/Print',
    method: 'get',
    params: entity
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_Inspection/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 过账
export function doPost(entity) {
  return request({
    url: '/PO/PO_Inspection/DoPost',
    method: 'post',
    data: entity
  })
}
