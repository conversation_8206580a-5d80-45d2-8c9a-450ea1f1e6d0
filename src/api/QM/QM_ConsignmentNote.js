import request from '@/utils/request'

// 采购入库检验查询信息
export function fetchList(query) {
  return request({
    url: '/QM/QM_PurchaseInspection/GetPageList',
    method: 'get',
    params: query
  })
}

// 采购入库检验过账
export function doPost(requestData) {
  return request({
    url: '/QM/QM_PurchaseInspection/DoPost',
    method: 'post',
    data: requestData
  })
}

// 采购入库检验删除 ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/QM/QM_PurchaseInspection/Delete',
    method: 'delete',
    data: ids
  })
}

// 采购入库检验导出
export function exportExcelFile(query) {
  return request({
    url: '/QM/QM_PurchaseInspection/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
