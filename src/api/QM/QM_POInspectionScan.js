import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/PO/PO_InspectionScan/GetPageList',
    method: 'get',
    params: query
  })
}

export function batchDelete(query) {
  return request({
    url: '/PO/PO_InspectionScan/Delete',
    method: 'delete',
    data: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PO/PO_InspectionScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function doPost(requestData) {
  return request({
    url: '/PO/PO_InspectionScan/DoPost',
    method: 'post',
    data: requestData
  })
}
