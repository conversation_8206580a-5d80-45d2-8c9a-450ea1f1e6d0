import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/GetPageList',
    method: 'get',
    params: query
  })
}

// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/GetDetailedPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetList(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/GetList',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetDetailList(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/GetDetailList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Save',
    method: 'post',
    data: entity
  })
}

// 编辑
export function update(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Update',
    method: 'post',
    data: entity
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/PrintReturnApply',
    method: 'get',
    params: entity
  })
}

// 上传
export function Upload(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Upload',
    method: 'post',
    data: entity
  })
}

// 下载
export function Download(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Download',
    method: 'post',
    data: entity
  })
}
// 新增时获取中间表数据
export function GetSapOrderList(query) {
  return request({
    // url: '/PP/PP_ReturnScanApplication/GetPageSapOrderDetailList', // 生产订单中间库
    url: 'PP/PP_ReturnScanApplication/GetStockInfo', // 库存
    method: 'get',
    params: query
  })
}
// 审核
export function Audit(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Audit',
    method: 'post',
    data: entity
  })
}
// 取消审核
export function CancelExamine(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/CancelExamine',
    method: 'post',
    data: entity
  })
}
