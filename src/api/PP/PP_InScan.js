import request from '@/utils/request'

export function getEntity(query) {
  return request({
    url: '/PP/PP_InScan/GetEntity',
    method: 'get',
    params: query
  })
}

export function fetchList(query) {
  return request({
    url: '/PP/PP_InScan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_InScan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_InScan/Update',
    method: 'post',
    data: entity
  })
}

export function batchDelete(data) {
  return request({
    url: '/PP/PP_InScan/BatchDelete',
    method: 'post',
    data: data
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_InScan/GetPage',
    method: 'get',
    params: query
  })
}

export function postToSAP(data) {
  return request({
    url: '/PP/PP_InScan/PostToSAP',
    method: 'post',
    data: data
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_InScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
