import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ProductionReturn/GetPageList',
    method: 'get',
    params: query
  })
}
// 获取明细列表
export function GetDetailList(query) {
  return request({
    url: '/PP/PP_ProductionReturn/GetDetailList',
    method: 'get',
    params: query
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProductionReturn/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_ProductionReturn/Add',
    method: 'post',
    data: entity
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/PP/PP_ProductionReturn/DownExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/PP/PP_ProductionReturn/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
