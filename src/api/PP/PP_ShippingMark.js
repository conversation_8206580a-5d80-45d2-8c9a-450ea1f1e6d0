import request from '@/utils/request'

// 获取分页列表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ShippingMark/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取详情
export function getEntity(id) {
  return request({
    url: '/PP/PP_ShippingMark/GetEntity',
    method: 'get',
    params: { ID: id }
  })
}

// 添加
export function add(entity) {
  return request({
    url: '/PP/PP_ShippingMark/Insert',
    method: 'post',
    data: entity
  })
}

// 更新
export function update(entity) {
  return request({
    url: '/PP/PP_ShippingMark/Update',
    method: 'post',
    data: entity
  })
}

// 删除单条记录
export function deleteSingle(id) {
  return request({
    url: '/PP/PP_ShippingMark/DeleteSingle',
    method: 'post',
    data: id
  })
}

// 批量删除
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ShippingMark/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出数据
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ShippingMark/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出模板
export function exportExcelTemplate() {
  return request({
    url: '/PP/PP_ShippingMark/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入数据
export function importExcelData(formData) {
  return request({
    url: '/PP/PP_ShippingMark/ImportExcelToData',
    method: 'post',
    data: formData
  })
}

// 批量创建打印记录
export function createPrintRecords(shippingMarkIds) {
  return request({
    url: '/PP/PP_ShippingMark/CreatePrintRecords',
    method: 'post',
    data: shippingMarkIds
  })
}
