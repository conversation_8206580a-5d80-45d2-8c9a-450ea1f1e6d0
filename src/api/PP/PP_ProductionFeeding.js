import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetPageList',
    method: 'get',
    params: query
  })
}

// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetDetailPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ProductionFeeding/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetList(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetDetailList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_ProductionFeeding/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function Update(entity) {
  return request({
    url: '/PP/PP_ProductionFeeding/Update',
    method: 'post',
    data: entity
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/PP/PP_ProductionFeeding/DoPost',
    method: 'post',
    data: entity
  })
}

// 新增时获取中间表数据
export function GetSapOrderList(query) {
  return request({
    url: '/PP/PP_OverReceive/GetSapOrderList',
    method: 'get',
    params: query
  })
}

// 根据序列号查询生产订单
export function GetOrderBySerialNo(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}

// 查询物料
export function GetMaterial(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetMaterial',
    method: 'get',
    params: query
  })
}
// 查询物料
export function GetOrderNoSerialNo(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetOrderNoSerialNo',
    method: 'get',
    params: query
  })
}
// 查询物料
export function GetMaterialByOrder(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetMaterialByOrder',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/DownExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/PP/PP_ProductionFeeding/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
// 根据生产订单号获取未启用序列号的生产订单信息
export function GetOrderNoSerialNoByNo(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetOrderNoSerialNoByNo',
    method: 'get',
    params: query
  })
}
