import request from '@/utils/request'

export function fetchAllList(query) {
  return request({
    url: '/PP/PP_MaterialReqKanban/GetList',
    method: 'get',
    params: query
  })
}

export function generateData() {
  return request({
    url: '/PP/PP_MaterialReqKanban/GenerateData',
    method: 'get'
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_MaterialReqKanban/GetPage',
    method: 'get',
    params: query
  })
}

export function generateKanbanData(ids) {
  return request({
    url: '/PP/PP_MaterialReqKanban/GenerateData',
    method: 'post',
    data: ids
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_MaterialReqKanban/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
export function fetchRPTPage(query) {
  return request({
    url: '/PP/PP_MaterialReqKanban/GetRPTPage',
    method: 'get',
    params: query
  })
}
