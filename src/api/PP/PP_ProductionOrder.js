import request from '@/utils/request'

export function fetchListSAP(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetListSAP',
    method: 'get',
    params: query
  })
}

export function fetchList(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetList',
    method: 'get',
    params: query
  })
}
export function fetchCloseList(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetCloseList',
    method: 'get',
    params: query
  })
}
export function fetchDetailList(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetDetailList',
    method: 'get',
    params: query
  })
}

export function fetchDetailListSAP(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetDetailListSAP',
    method: 'get',
    params: query
  })
}

export function fetchStatusList(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetStatusList',
    method: 'get',
    params: query
  })
}

export function fetchNextPageSAP(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetNextPage',
    method: 'get',
    params: query
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_ProductionOrder/GetPage',
    method: 'get',
    params: query
  })
}

export function syncProductionOrder(data) {
  return request({
    url: '/MD/PP_ProductionOrder/SyncProductionOrder',
    method: 'post',
    data: data
  })
}
export function UpdatePLine(data) {
  return request({
    url: '/PP/PP_ProductionOrder/UpdatePLine',
    method: 'post',
    data: data
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProductionOrder/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
