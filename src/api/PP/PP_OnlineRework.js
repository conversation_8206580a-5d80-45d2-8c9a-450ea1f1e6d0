import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/MM/MM_MagneticMaterialOutsourcingWarehousing/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MM/MM_MagneticMaterialOutsourcingWarehousing/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/MM/MM_MagneticMaterialOutsourcingWarehousing/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/MM/MM_MagneticMaterialOutsourcingWarehousing/DoPost',
    method: 'post',
    data: entity
  })
}
