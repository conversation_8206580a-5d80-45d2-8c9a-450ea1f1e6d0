import request from '@/utils/request'

// 获取实体信息
export function getDetailEntity(query) {
  return request({
    url: '/PP/PP_FTTPDetailed/GetEntity',
    method: 'get',
    params: query
  })
}
// 获取用户列表清单fetchUserList
export function fetchDetailList(query) {
  return request({
    url: '/PP/PP_FTTPDetailed/GetPageList',
    method: 'get',
    params: query
  })
}

export function addDetail(entity) {
  return request({
    url: '/PP/PP_FTTPDetailed/Add',
    method: 'post',
    data: entity
  })
}

export function updateDetail(entity) {
  return request({
    url: '/PP/PP_FTTPDetailed/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDeleteDetail(ids) {
  return request({
    url: '/PP/PP_FTTPDetailed/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchDetailPage(query) {
  return request({
    url: '/PP/PP_FTTPDetailed/GetPage',
    method: 'get',
    params: query
  })
}

export function addDetails(data) {
  return request({
    url: '/PP/PP_FTTPDetailed/AddList',
    method: 'post',
    data: data
  })
}

export function updateDetails(data) {
  return request({
    url: '/PP/PP_FTTPDetailed/UpdateList',
    method: 'post',
    data: data
  })
}
