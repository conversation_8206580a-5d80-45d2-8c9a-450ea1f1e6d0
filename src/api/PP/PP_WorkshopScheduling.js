import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/GetPageList',
    method: 'get',
    params: query
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 打印
export function PrintScheduling(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/PrintScheduling',
    method: 'get',
    params: query
  })
}
// 打印定子组件身份卡
export function PrintStatorLable(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/PrintStatorLable',
    method: 'get',
    params: query
  })
}
// 打印转子组件身份卡
export function PrintRotorLable(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/PrintRotorLable',
    method: 'get',
    params: query
  })
}
// 打印入库标签
export function PrintStorageLabel(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/PrintStorageLabel',
    method: 'get',
    params: query
  })
}
// 导出主机总装线/巨通线/蒂森线
export function ExportToExcelFileForHostLine(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/ExportToExcelFileForHostLine',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导出DT线
export function ExportToExcelFileForDTLine(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/ExportToExcelFileForDTLine',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导出试制返修线
export function ExportToExcelFileForRepairLine(query) {
  return request({
    url: '/PP/PP_WorkshopScheduling/ExportToExcelFileForRepairLine',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 同步完工
export function SetShippingToDelivery(query) {
  return request({
    url: '/PP/PP_ProductionWarehousing/SetShippingToDelivery',
    method: 'get',
    params: query
  })
}
