import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ProductionReport/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProductionReport/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑时根据主键获取
export function GetEntity(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetEntity',
    method: 'get',
    params: query
  })
}
// 根据生产订单获取工序
export function GetWorkingProcedure(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetWorkingProcedure',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_ProductionReport/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function Update(entity) {
  return request({
    url: '/PP/PP_ProductionReport/Update',
    method: 'post',
    data: entity
  })
}

// 根据生产订单获取工序
export function GetOrderBySerialNo(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}
// 生产报工-根据序列号查询生产订单
export function GetOrderBySerialNo1(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}
// 获取所有未启用序列号的生产订单
export function GetOrderNoSerialNo(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetOrderNoSerialNo',
    method: 'get',
    params: query
  })
}
// 根据生产订单号获取未启用序列号的生产订单信息
export function GetOrderNoSerialNoByNo(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetOrderNoSerialNoByNo',
    method: 'get',
    params: query
  })
}
// 根据订单和工序查询剩余数量
export function GetQty(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetQty',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/PP/PP_ProductionReport/DownExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/PP/PP_ProductionReport/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
