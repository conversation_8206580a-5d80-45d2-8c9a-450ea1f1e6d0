import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_StockingWave/GetEntity',
    method: 'get',
    params: query
  })
}
// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PP/PP_StockingWave/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_StockingWave/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_StockingWave/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_StockingWave/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_StockingWave/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchDocNum() {
  return request({
    url: '/PP/PP_StockingWave/GetDocNum',
    method: 'get'
  })
}

export function fetchFIFOStock(query) {
  return request({
    url: '/PP/PP_StockingWave/GetFIFOStock',
    method: 'get',
    params: query
  })
}

export function finishPreparation(docNum) {
  return request({
    url: '/PP/PP_StockingWave/FinishPreparation',
    method: 'post',
    data: { DocNum: docNum }
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_StockingWave/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
