import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_OverReceive/GetPageList',
    method: 'get',
    params: query
  })
}

// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/PP/PP_OverReceive/GetDetailPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_OverReceive/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_OverReceive/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_OverReceive/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetList(query) {
  return request({
    url: '/PP/PP_OverReceive/GetDetailList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_OverReceive/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function update(entity) {
  return request({
    url: '/PP/PP_OverReceive/Update',
    method: 'post',
    data: entity
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/PP/PP_OverReceive/PrintApply',
    method: 'get',
    params: entity
  })
}

// 上传
export function Upload(entity) {
  return request({
    url: '/PP/PP_OverReceive/Upload',
    method: 'post',
    data: entity
  })
}

// 下载
export function Download(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Download',
    method: 'post',
    data: entity
  })
}
// 审核
export function Examine(entity) {
  return request({
    url: '/PP/PP_OverReceive/Examine',
    method: 'post',
    data: entity
  })
}
// 取消审核
export function CancelExamine(entity) {
  return request({
    url: '/PP/PP_OverReceive/CancelExamine',
    method: 'post',
    data: entity
  })
}
// 过账
export function DoPost(entity) {
  return request({
    url: '/PP/PP_OverReceive/DoPost',
    method: 'post',
    data: entity
  })
}
// 新增时获取中间表数据
export function GetSapOrderList(query) {
  return request({
    url: '/PP/PP_OverReceive/GetSapOrderList',
    method: 'get',
    params: query
  })
}
