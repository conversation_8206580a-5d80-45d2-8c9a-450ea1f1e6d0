import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_StockingScan/GetEntity',
    method: 'get',
    params: query
  })
}
// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PP/PP_StockingScan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_StockingScan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_StockingScan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_StockingScan/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_StockingScan/GetPage',
    method: 'get',
    params: query
  })
}

export function postToSAP(docNums) {
  return request({
    url: '/PP/PP_StockingScan/PostToSAP',
    method: 'post',
    data: { DocNums: docNums }
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_StockingScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// ids 主键数组
export function delList(data) {
  return request({
    url: '/PP/PP_StockingScan/DelList',
    method: 'post',
    data: data
  })
}
