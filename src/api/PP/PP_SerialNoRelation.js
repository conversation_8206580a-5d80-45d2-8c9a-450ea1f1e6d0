import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetPageList',
    method: 'get',
    params: query
  })
}

// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetDetailPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_SerialNoRelation/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetList(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetDetailList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_SerialNoRelation/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function Update(entity) {
  return request({
    url: '/PP/PP_SerialNoRelation/Update',
    method: 'post',
    data: entity
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/PP/PP_SerialNoRelation/DoPost',
    method: 'post',
    data: entity
  })
}

// 新增时获取中间表数据
export function GetSapOrderList(query) {
  return request({
    url: '/PP/PP_OverReceive/GetSapOrderList',
    method: 'get',
    params: query
  })
}

// 根据序列号查询生产订单
export function GetOrderBySerialNo(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}

// 查询物料
export function GetMaterial(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetMaterial',
    method: 'get',
    params: query
  })
}
// 查询物料
export function GetOrderNoSerialNo(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetOrderNoSerialNo',
    method: 'get',
    params: query
  })
}
// 查询物料
export function GetMaterialByOrder(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/GetMaterialByOrder',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/PP/PP_SerialNoRelation/DownExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/PP/PP_SerialNoRelation/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
