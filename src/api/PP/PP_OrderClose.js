import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_OrderClose/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PP/PP_OrderClose/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_OrderClose/Add',
    method: 'post',
    data: entity
  })
}
export function addList(entitys) {
  return request({
    url: '/PP/PP_OrderClose/AddList',
    method: 'post',
    data: entitys
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_OrderClose/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_OrderClose/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_OrderClose/GetPage',
    method: 'get',
    params: query
  })
}

export function postToSAP(entity) {
  return request({
    url: '/PP/PP_OrderClose/PostToSAP',
    method: 'post',
    data: entity
  })
}
