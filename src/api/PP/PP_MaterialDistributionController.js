import request from '@/utils/request'

// 获取主表分页信息
export function fetchList(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取子表信息
export function GetDetailPageList(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/GetDetailPageList',
    method: 'get',
    params: query
  })
}

// 获取子表信息
export function GetDetailList(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/GetDetailList',
    method: 'get',
    params: query
  })
}

// 新增时获取中间表数据
export function GetSapOrderList(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/GetSapOrderList',
    method: 'get',
    params: query
  })
}
// 获取所有线体
export function GetAllLine(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/GetAllLine',
    method: 'get',
    params: query
  })
}
export function Add(entity) {
  return request({
    url: '/PP/PP_MaterialDistribution/Add',
    method: 'post',
    data: entity
  })
}
export function Update(entity) {
  return request({
    url: '/PP/PP_MaterialDistribution/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_MaterialDistribution/DeleteDelivery',
    method: 'delete',
    data: ids
  })
}

// 编辑只有删除子表，调这个接口
export function DeleteDeliveryDetail(ids) {
  return request({
    url: '/PP/PP_MaterialDistribution/DeleteDeliveryDetail',
    method: 'delete',
    data: ids
  })
}
// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/PP/PP_MaterialDistribution/DoPost',
    method: 'post',
    data: entity
  })
}
// 导出汇总表
export function ExportSummary(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/ExportSummary',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导出SAP配送信息
export function ExportToExcelFileForSAP(query) {
  return request({
    url: '/PP/PP_MaterialDistribution/ExportToExcelFileForSAP',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
