import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetPageList',
    method: 'get',
    params: query
  })
}

// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetDetailPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ProcessInspection/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProcessInspection/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑根据单号查询明细信息
export function GetList(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetDetailList',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_ProcessInspection/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function Update(entity) {
  return request({
    url: '/PP/PP_ProcessInspection/Update',
    method: 'post',
    data: entity
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/PP/PP_ProcessInspection/Print',
    method: 'get',
    params: entity
  })
}
// 根据序列号查询生产订单
export function GetOrderBySerialNo(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}
// 根据订单号查询生产订单
export function GetOrderByOrderNo(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetOrderByOrderNo',
    method: 'get',
    params: query
  })
}
// 根据关联订单号查询生产订单
export function GetOrderByRelevant(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetOrderByRelevant',
    method: 'get',
    params: query
  })
}
// 获取线体
export function GetProductionLine(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetProductionLine',
    method: 'get',
    params: query
  })
}
export function GetWorkingProcedure(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetWorkingProcedure',
    method: 'get',
    params: query
  })
}
export function GetInspection(query) {
  return request({
    url: '/PP/PP_ProcessInspection/GetInspection',
    method: 'get',
    params: query
  })
}
export function SubmitCheck(query) {
  return request({
    url: '/PP/PP_ProcessInspection/SubmitCheck',
    method: 'post',
    data: query
  })
}
// 作废
export function Invalid(query) {
  return request({
    url: '/PP/PP_ProcessInspection/Invalid',
    method: 'post',
    data: query
  })
}

