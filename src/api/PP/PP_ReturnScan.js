import request from '@/utils/request'
// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ReturnScan/GetPageList',
    method: 'get',
    params: query
  })
}

// 查询子表的分页列表
export function GetDetailedPageList(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/GetDetailedPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ReturnScanApplication/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 过账
export function doPost(requestData) {
  return request({
    url: '/PP/PP_ReturnScan/DoPost',
    method: 'post',
    data: requestData
  })
}

// 打印
export function printToPDF(entity) {
  return request({
    url: '/PP/PP_ReturnScanApplication/Print',
    method: 'get',
    params: entity
  })
}
