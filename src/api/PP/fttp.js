import request from '@/utils/request'

export function fetchScrappedPage(pageNumber, pageSize, sort, keyword) {
  return request({
    url: '/PP/FTTP/GetScrappedPage',
    method: 'get',
    params: { pageNumber, pageSize, sort, keyword }
  })
}

export function fetchPage(pageNumber, pageSize, sort, keyword) {
  return request({
    url: '/PP/FTTP/GetPage',
    method: 'get',
    params: { pageNumber, pageSize, sort, keyword }
  })
}

export function fetchDetailPage(pageNumber, pageSize, sort, waveNum) {
  return request({
    url: '/PP/FTTP/GetDetailPage',
    method: 'get',
    params: { pageNumber, pageSize, sort, waveNum }
  })
}

export function log(obj) {
  return request({
    url: '/PP/FTTP/Add',
    method: 'post',
    data: obj
  })
}
