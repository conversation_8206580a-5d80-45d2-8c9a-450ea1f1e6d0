import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_MaterialReport/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_MaterialReport/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_MaterialReport/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑时根据主键获取
export function GetEntity(query) {
  return request({
    url: '/PP/PP_ProductionReport/GetEntity',
    method: 'get',
    params: query
  })
}
// 根据工作中心获取工序
export function GetWorkingProcedure(query) {
  return request({
    url: '/PP/PP_MaterialReport/GetWorkingProcedure',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/PP/PP_MaterialReport/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function Update(entity) {
  return request({
    url: '/PP/PP_MaterialReport/Update',
    method: 'post',
    data: entity
  })
}

// 根据生产订单获取工序
export function GetOrderBySerialNo(query) {
  return request({
    url: '/PP/PP_MaterialReport/GetMaterialByCode',
    method: 'get',
    params: query
  })
}

// 获取线体
export function GetProductionLine(query) {
  return request({
    url: '/PP/PP_MaterialReport/GetProductionLine',
    method: 'get',
    params: query
  })
}
