import request from '@/utils/request';

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_OutScan/GetEntity',
    method: 'get',
    params: query
  });
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PP/PP_OutScan/GetPageList',
    method: 'get',
    params: query
  });
}

export function add(entity) {
  return request({
    url: '/PP/PP_OutScan/Add',
    method: 'post',
    data: entity
  });
}

export function update(entity) {
  return request({
    url: '/PP/PP_OutScan/Update',
    method: 'post',
    data: entity
  });
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_OutScan/Delete',
    method: 'delete',
    data: ids
  });
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_OutScan/GetPage',
    method: 'get',
    params: query
  });
}

export function postToSAP(data) {
  return request({
    url: '/PP/PP_OutScan/PostToSAP',
    method: 'post',
    data: data
  });
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_OutScan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  });
}
