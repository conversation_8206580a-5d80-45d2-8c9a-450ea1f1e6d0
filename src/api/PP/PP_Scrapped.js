import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_Scrapped/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PP/PP_Scrapped/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_Scrapped/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_Scrapped/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_Scrapped/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_Scrapped/GetPage',
    method: 'get',
    params: query
  })
}
export function fetchDetailPage(query) {
  return request({
    url: '/PP/PP_Scrapped/GetDetailPage',
    method: 'get',
    params: query
  })
} export function fetchCountPage(query) {
  return request({
    url: '/PP/PP_Scrapped/GetCountPage',
    method: 'get',
    params: query
  })
}
export function fetchBatchNum() {
  return request({
    url: '/PP/PP_Scrapped/GetBatchNum',
    method: 'get'
  })
}

export function batchAdd(data) {
  return request({
    url: '/PP/PP_Scrapped/BatchAdd',
    method: 'post',
    data: data
  })
}

export function postToSAP(data) {
  return request({
    url: '/PP/PP_Scrapped/PostToSAP',
    method: 'post',
    data: data
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_Scrapped/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
