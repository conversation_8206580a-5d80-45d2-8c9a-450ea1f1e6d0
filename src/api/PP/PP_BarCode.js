import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_BarCode/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/PP/PP_BarCode/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_BarCode/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_BarCode/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_BarCode/DeleteWithValidating',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_BarCode/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchBatchNum() {
  return request({
    url: '/PP/PP_BarCode/GetBatchNum',
    method: 'get'
  })
}

export function batchAdd(data) {
  return request({
    url: '/PP/PP_BarCode/BatchAdd',
    method: 'post',
    data: data
  })
}

export function fetchProductionOrderMaterials(query) {
  return request({
    url: '/PP/PP_BarCode/GetProductionOrderMaterials',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_BarCode/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
