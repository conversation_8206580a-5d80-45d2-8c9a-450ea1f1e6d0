import request from '@/utils/request'

export function fetchPage(pageNumber, pageSize, sort, keyword) {
  return request({
    url: '/PP/FTTM/GetPage',
    method: 'get',
    params: { pageNumber, pageSize, sort, keyword }
  })
}

export function fetchDetailPage(pageNumber, pageSize, sort, waveNum) {
  return request({
    url: '/PP/FTTM/GetDetailPage',
    method: 'get',
    params: { pageNumber, pageSize, sort, waveNum }
  })
}
