import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/PP/PP_MaterialReq/GetEntity',
    method: 'get',
    params: query
  })
}

export function fetchList(query) {
  return request({
    url: '/PP/PP_MaterialReq/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/PP/PP_MaterialReq/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/PP/PP_MaterialReq/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_MaterialReq/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/PP/PP_MaterialReq/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchListByPLine(query) {
  return request({
    url: '/PP/PP_MaterialReq/GetPageListByPLine',
    method: 'get',
    params: query
  })
}

export function fetchAllList(query) {
  return request({
    url: '/PP/PP_MaterialReq/GetViewList',
    method: 'get',
    params: query
  })
}
