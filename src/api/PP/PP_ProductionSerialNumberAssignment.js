import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/GetPageList',
    method: 'get',
    params: query
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出
export function exportExcelFileSerialNumber(query) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/ExportToExcelFileSerialNumber',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 分配
export function SyncProductionOrder(query) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/SyncProductionOrder',
    method: 'get',
    params: query
  })
}

// 邮件
export function IssueNotice(data) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/IssueNotice',
    method: 'post',
    data: data
  })
}
// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/Delete',
    method: 'delete',
    data: ids
  })
}
