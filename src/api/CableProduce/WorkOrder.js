import request from '@/utils/request'

// 分页查询主表
export function getPageList(query) {
  return request({
    url: '/CableProduce/WorkOrder/GetPageList',
    method: 'get',
    params: query
  })
}
// 分页查询主表
export function generateProductNo(data) {
  return request({
    url: '/CableProduce/WorkOrder/GenerateProductNo',
    method: 'post',
    data: data
  })
}

// 打印标记更新
export function updatePrint(data) {
  return request({
    url: '/CableProduce/WorkOrder/UpdatePrint',
    method: 'post',
    data: data
  })
}

/**
 * 合同明细导出
 * @param query
 */
export function contractDetailExport(query) {
  return request({
    url: '/CableProduce/WorkOrder/ContractDetailExport',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

