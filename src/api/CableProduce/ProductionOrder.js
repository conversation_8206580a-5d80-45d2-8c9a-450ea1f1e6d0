import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/CableProduce/ProductionOrder/GetPageList',
    method: 'get',
    params: query
  })
}

// 创建计划订单
export function createPlanOrder(data) {
  return request({
    url: '/CableProduce/ProductionOrder/CreatePlanOrder',
    method: 'post',
    data: data
  })
}

// 计划转生产订单
export function planConvertProduceOrder(data) {
  return request({
    url: '/CableProduce/ProductionOrder/PlanConvertProduceOrder',
    method: 'post',
    data: data
  })
}

// 生产订单确认
export function produceOrderConfirm(data) {
  return request({
    url: '/CableProduce/ProductionOrder/ProduceOrderConfirm',
    method: 'post',
    data: data
  })
}

// 序列号分配
export function dispenseSerialNo(data) {
  return request({
    url: '/CableProduce/ProductionOrder/DispenseSerialNo',
    method: 'post',
    data: data
  })
}

// 取消序列号分配
export function unDispenseSerialNo(data) {
  return request({
    url: '/CableProduce/ProductionOrder/UnDispenseSerialNo',
    method: 'post',
    data: data
  })
}

// 生产报工
export function produceReportWork(data) {
  return request({
    url: '/CableProduce/ProductionOrder/ProduceReportWork',
    method: 'post',
    data: data
  })
}

// 取消生产报工
export function unProduceReportWork(data) {
  return request({
    url: '/CableProduce/ProductionOrder/UnProduceReportWork',
    method: 'post',
    data: data
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/PP/PP_ProductionSerialNumberAssignment/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
