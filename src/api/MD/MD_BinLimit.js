import request from '@/utils/request'

export function fetchModule(query) {
  return request({
    url: '/MD/MD_BinLimit/GetBinLimitByMenuCode',
    method: 'get',
    params: query
  })
}

export function submitBinLimit(query) {
  return request({
    url: '/MD/MD_BinLimit/AddBinLimitSetting',
    method: 'post',
    data: query
  })
}

export function fetchBinLocation(query) {
  return request({
    url: '/MD/MD_BinLimit/GetExistBinFromBinLocation',
    method: 'get',
    params: query
  })
}
// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_BinLimit/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_BinLimit/Delete',
    method: 'delete',
    data: ids
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_BinLimit/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
