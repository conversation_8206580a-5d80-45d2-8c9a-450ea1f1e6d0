import request from '@/utils/request'

//
export function fetchPageList(query) {
  return request({
    url: '/MD/MD_LabelTemplate/GetPageList',
    method: 'get',
    params: query
  })
}

//
export function fetchList() {
  return request({
    url: '/MD/MD_LabelTemplate/GetList',
    method: 'get'
  })
}

// {TemplateType:1}
export function fetchTemplate(typeCode) {
  return request({
    url: '/MD/MD_LabelTemplate/GetTemplateList',
    method: 'get',
    params: typeCode
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_LabelTemplate/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_LabelTemplate/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_LabelTemplate/Delete',
    method: 'delete',
    data: ids
  })
}

// {TemplateID:''}
export function enableLabelTemplate(entity) {
  return request({
    url: '/MD/MD_LabelTemplate/EnableLabelTemplate',
    method: 'post',
    params: entity
  })
}

// {TemplateID:''}
export function disableLabelTemplate(entity) {
  return request({
    url: '/MD/MD_LabelTemplate/DisableLabelTemplate',
    method: 'post',
    data: entity
  })
}

export function uploadFile(file) {
  return request({
    url: '/MD/MD_LabelTemplate/UploadLabelTemplate',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data;boundary=' + new Date().getTime()
    }
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_LabelTemplate/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
