import request from '@/utils/request'

export function fetchDetailList(query) {
  return request({
    url: '/MD/MD_POInspectionGradeDetailed/GetGradeDetailList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_POInspectionGradeDetailed/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_POInspectionGradeDetailed/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_POInspectionGradeDetailed/Delete',
    method: 'delete',
    data: ids
  })
}
