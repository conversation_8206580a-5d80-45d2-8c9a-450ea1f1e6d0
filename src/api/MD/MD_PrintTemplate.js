import request from '@/utils/request'

/**
 * 获取打印模板分页列表
 * @param {Object} query - 查询参数
 * @returns {Promise}
 */
export function fetchPageList(query) {
  return request({
    url: '/MD/MD_PrintTemplate/GetPageList',
    method: 'get',
    params: query
  })
}

/**
 * 根据ID获取打印模板实体
 * @param {string} id - 模板ID
 * @returns {Promise}
 */
export function getById(id) {
  return request({
    url: '/MD/MD_PrintTemplate/GetById',
    method: 'get',
    params: { id }
  })
}

/**
 * 添加打印模板
 * @param {Object} entity - 模板实体
 * @returns {Promise}
 */
export function add(entity) {
  return request({
    url: '/MD/MD_PrintTemplate/Insert',
    method: 'post',
    data: entity
  })
}

/**
 * 更新打印模板
 * @param {Object} entity - 模板实体
 * @returns {Promise}
 */
export function update(entity) {
  return request({
    url: '/MD/MD_PrintTemplate/Update',
    method: 'post',
    data: entity
  })
}

/**
 * 批量删除打印模板
 * @param {Array} ids - 模板ID数组
 * @returns {Promise}
 */
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_PrintTemplate/Delete',
    method: 'post',
    data: ids
  })
}

/**
 * 导出模板数据到Excel
 * @param {Object} query - 查询参数
 * @returns {Promise}
 */
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_PrintTemplate/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

/**
 * 导出Excel导入模板
 * @returns {Promise}
 */
export function exportExcelModel() {
  return request({
    url: '/MD/MD_PrintTemplate/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导入Excel数据
 * @param {Array} entitys - 导入的实体数组
 * @returns {Promise}
 */
export function importExcelData(entitys) {
  return request({
    url: '/MD/MD_PrintTemplate/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}

/**
 * 根据模板Key获取打印模板列表
 * @param {string} templateKey - 模板Key
 * @returns {Promise}
 */
export function getByTemplateKey(templateKey) {
  return request({
    url: '/MD/MD_PrintTemplate/GetByTemplateKey',
    method: 'get',
    params: { templateKey }
  })
}

/**
 * 获取所有启用的打印模板列表（用于下拉选择）
 * @returns {Promise}
 */
export function getEnabledList() {
  return request({
    url: '/MD/MD_PrintTemplate/GetPageList',
    method: 'get',
    params: {
      PageNumber: 1,
      PageSize: 1000,
      Enable: true
    }
  })
}
