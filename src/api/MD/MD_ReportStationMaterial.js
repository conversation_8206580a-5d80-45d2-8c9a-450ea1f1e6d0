import request from '@/utils/request'

// 获取分页列表
export function fetchList(query) {
  return request({
    url: '/MD/MD_ReportStationMaterial/GetPageList',
    method: 'get',
    params: query
  })
}

// 添加
export function add(entity) {
  return request({
    url: '/MD/MD_ReportStationMaterial/Add',
    method: 'post',
    data: entity
  })
}

// 更新
export function update(entity) {
  return request({
    url: '/MD/MD_ReportStationMaterial/Update',
    method: 'post',
    data: entity
  })
}

// 删除
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_ReportStationMaterial/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportToExcelFile(query) {
  return request({
    url: '/MD/MD_ReportStationMaterial/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入数据
export function importExcelToData(data) {
  return request({
    url: '/MD/MD_ReportStationMaterial/ImportExcelToData',
    method: 'post',
    data: data
  })
}

// 导出模板
export function exportToExcelModel() {
  return request({
    url: '/MD/MD_ReportStationMaterial/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}
