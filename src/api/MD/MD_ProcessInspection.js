import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/MD/MD_ProcessInspection/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_ProcessInspection/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_ProcessInspection/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_ProcessInspection/Delete',
    method: 'delete',
    data: ids
  })
}
export function GetProductionLine(query) {
  return request({
    url: '/MD/MD_ProcessInspection/GetProductionLine',
    method: 'get',
    params: query
  })
}
export function GetWorkingProcedure(query) {
  return request({
    url: '/MD/MD_ProcessInspection/GetWorkingProcedure',
    method: 'get',
    params: query
  })
}
