import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_PartMakeCompany/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_PartMakeCompany/Insert',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_PartMakeCompany/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_PartMakeCompany/Delete',
    method: 'delete',
    data: ids
  })
}

// Export to Excel
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_PartMakeCompany/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// Export Excel Template
export function exportExcelTemplate() {
  return request({
    url: '/MD/MD_PartMakeCompany/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// Import Excel Data
export function importExcelData(formData) {
  return request({
    url: '/MD/MD_PartMakeCompany/ImportExcelToData',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
