import request from '@/utils/request'

// Get entity by ID
export function getEntity(id) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/GetEntity',
    method: 'get',
    params: { ID: id }
  })
}

// Get paginated list
export function fetchList(query) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/GetPageList',
    method: 'get',
    params: query
  })
}

// Add new entity
export function add(entity) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/Insert',
    method: 'post',
    data: entity
  })
}

// Update entity
export function update(entity) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/Update',
    method: 'post',
    data: entity
  })
}

// Delete entities by IDs
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/Delete',
    method: 'delete',
    data: ids
  })
}

// Export to Excel
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// Export Excel Template
export function exportExcelTemplate() {
  return request({
    url: '/MD/MD_ProduceLineCapacity/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// Import Excel Data
export function importExcelData(entities) {
  return request({
    url: '/MD/MD_ProduceLineCapacity/ImportExcelToData',
    method: 'post',
    data: entities
  })
}
