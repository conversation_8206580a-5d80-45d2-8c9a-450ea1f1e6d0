import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/Insert',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/Delete',
    method: 'delete',
    data: ids
  })
}

// Export to Excel
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// Export Excel Template
export function exportExcelTemplate() {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// Import Excel Data
export function importExcelData(formData) {
  return request({
    url: '/MD/MD_CustomerModelDeliveryAdvance/ImportExcelToData',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
