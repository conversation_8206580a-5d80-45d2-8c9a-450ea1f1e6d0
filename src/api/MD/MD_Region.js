import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_Region/GetEntity/' + query,
    method: 'get'
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_Region/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_Region/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_Region/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_Region/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchAllList(query) {
  return request({
    url: '/MD/MD_Region/GetAllList',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_Region/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 查询SAP中间库仓库
export function GetXZ_SAP(query) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_T001L',
    method: 'get',
    params: query
  })
}
