import request from '@/utils/request'

// 获取分页列表
export function fetchPageList(query) {
  return request({
    url: '/MD/MD_NonStandardConfig/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取单个实体
export function getEntity(id) {
  return request({
    url: '/MD/MD_NonStandardConfig/GetEntity',
    method: 'get',
    params: { id }
  })
}

// 根据评审单号获取配置信息
export function getByReviewNo(reviewNo) {
  return request({
    url: '/MD/MD_NonStandardConfig/GetByReviewNo',
    method: 'get',
    params: { reviewNo }
  })
}

// 添加
export function add(entity) {
  return request({
    url: '/MD/MD_NonStandardConfig/Insert',
    method: 'post',
    data: entity
  })
}

// 修改
export function update(entity) {
  return request({
    url: '/MD/MD_NonStandardConfig/Update',
    method: 'post',
    data: entity
  })
}

// 批量删除
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_NonStandardConfig/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出模板
export function exportExcelModel() {
  return request({
    url: '/MD/MD_NonStandardConfig/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// 导出数据
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_NonStandardConfig/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入数据
export function importExcelData(entitys) {
  return request({
    url: '/MD/MD_NonStandardConfig/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
