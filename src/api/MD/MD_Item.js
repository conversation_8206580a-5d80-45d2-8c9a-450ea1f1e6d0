import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_Item/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_Item/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_Item/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_Item/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_Item/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MD/MD_Item/GetPageList',
    method: 'get',
    params: query
  })
}

export function fetchAllList(query) {
  return request({
    url: '/MD/MD_Item/GetAllList',
    method: 'get',
    params: query
  })
}

export function updateOrInsert(entity) {
  return request({
    url: '/MD/MD_Item/UpdateOrInsert',
    method: 'post',
    data: entity
  })
}

export function fetchNextPage(query) {
  return request({
    url: '/MD/MD_Item/GetNextPage',
    method: 'get',
    params: query
  })
}

export function syncProductCategory(ids) {
  return request({
    url: '/MD/MD_Item/SyncProductCategory',
    method: 'post',
    data: ids
  })
}

export function syncMaterial(ids) {
  return request({
    url: '/MD/MD_Item/SyncMaterial',
    method: 'post',
    data: ids
  })
}

// 查询数据字典
export function GetDictionary(query) {
  return request({
    url: '/MD/MD_Item/GetDictionary',
    method: 'get',
    params: query
  })
}

// 启用
export function UpdateOrInsertStockManWay(data) {
  return request({
    url: '/MD/MD_Item//UpdateOrInsertStockManWay',
    method: 'post',
    data: data
  })
}
