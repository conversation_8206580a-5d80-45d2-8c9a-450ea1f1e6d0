import request from '@/utils/request'

// 获取基本规格参数分页列表
export function fetchList(query) {
  return request({
    url: '/MD/MD_BasicSpecification/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取基本规格参数详情
export function getEntity(id) {
  return request({
    url: '/MD/MD_BasicSpecification/GetEntity',
    method: 'get',
    params: { ID: id }
  })
}

// 添加基本规格参数
export function add(entity) {
  return request({
    url: '/MD/MD_BasicSpecification/Insert',
    method: 'post',
    data: entity
  })
}

// 更新基本规格参数
export function update(entity) {
  return request({
    url: '/MD/MD_BasicSpecification/Update',
    method: 'post',
    data: entity
  })
}

// 删除单条基本规格参数记录
export function deleteSingle(id) {
  return request({
    url: '/MD/MD_BasicSpecification/DeleteSingle',
    method: 'post',
    data: id
  })
}

// 批量删除基本规格参数
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_BasicSpecification/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出基本规格参数数据
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_BasicSpecification/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出基本规格参数模板
export function exportExcelTemplate() {
  return request({
    url: '/MD/MD_BasicSpecification/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入基本规格参数数据
export function importExcelData(data) {
  return request({
    url: '/MD/MD_BasicSpecification/ImportExcelToData',
    method: 'post',
    data: data
  })
}
