import request from '@/utils/request'

// 获取列表
export function fetchList(query) {
  return request({
    url: '/MD/MD_ShippingMarkRule/GetList',
    method: 'get',
    params: query
  })
}

// 获取分页列表
export function fetchPageList(query) {
  return request({
    url: '/MD/MD_ShippingMarkRule/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取单个实体
export function getEntity(id) {
  return request({
    url: '/MD/MD_ShippingMarkRule/GetEntity',
    method: 'get',
    params: { id }
  })
}

// 添加或更新
export function updateOrInsert(entity) {
  return request({
    url: '/MD/MD_ShippingMarkRule/UpdateOrInsert',
    method: 'post',
    data: entity
  })
}

// 批量删除
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_ShippingMarkRule/Delete',
    method: 'post',
    data: ids
  })
}

// 导出Excel
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_ShippingMarkRule/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出Excel模板
export function exportExcelModel() {
  return request({
    url: '/MD/MD_ShippingMarkRule/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入Excel数据
export function importExcelData(entitys) {
  return request({
    url: '/MD/MD_ShippingMarkRule/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
