import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_FreightMileage/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_FreightMileage/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_FreightMileage/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_FreightMileage/Delete',
    method: 'delete',
    data: ids
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_FreightMileage/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 查询供应商主数据
export function GetXZ_SAP(query) {
  return request({
    url: '/SRM/XZSRM/GetSRM_SupplierInfo',
    method: 'get',
    params: query
  })
}
// 物料供应商
export function GetSRM_WLSupplierInfo(query) {
  return request({
    url: '/SRM/XZSRM/GetSRM_WLSupplierInfo',
    method: 'get',
    params: query
  })
}
// 省市区
export function getProvinceCityRegion(query) {
  return request({
    url: '/MD/MD_FreightMileage/GetProvince_City_District',
    method: 'get',
    params: query
  })
}

