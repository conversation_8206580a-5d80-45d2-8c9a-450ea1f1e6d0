import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_Stock/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_Stock/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_Stock/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_Stock/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_Stock/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MD/MD_Stock/GetPage',
    method: 'get',
    params: query
  })
}
