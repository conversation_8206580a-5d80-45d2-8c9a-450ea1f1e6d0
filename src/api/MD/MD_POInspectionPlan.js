import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_POInspectionPlan/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_POInspectionPlan/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_POInspectionPlan/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_POInspectionPlan/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_POInspectionPlan/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MD/MD_POInspectionPlan/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchSupplierNextPage(query) {
  return request({
    url: '/MD/MD_POInspectionPlan/GetSupplierList',
    method: 'get',
    params: query
  })
}

export function fetchItemList() {
  return request({
    url: '/MD/MD_POInspectionPlan/GetItemList',
    method: 'get'
  })
}

export function fetchItemNextPage(query) {
  return request({
    url: '/MD/MD_POInspectionPlan/GetItemNextPage',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_POInspectionPlan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

