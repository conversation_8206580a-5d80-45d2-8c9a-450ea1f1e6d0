import request from '@/utils/request'

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/SaveEntity',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/DeleteEntity',
    method: 'delete',
    data: ids
  })
}
// 查询物料组
export function GetMaterialGroupList(query) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/GetMaterialGroup',
    method: 'get',
    params: query
  })
}
// 获取线体
export function GetProductionLineList(query) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/GetProductionLine',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/MD/MD_ProductionDistributionSetting/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
