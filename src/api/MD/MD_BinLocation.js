import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_BinLocation/GetEntity/' + query,
    method: 'get'
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_BinLocation/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_BinLocation/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_BinLocation/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_BinLocation/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchAllList(query) {
  return request({
    url: '/MD/MD_BinLocation/GetList',
    method: 'get',
    params: query
  })
}

export function fetchPage(query) {
  return request({
    url: '/MD/MD_BinLocation/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchEntityByPLine(query) {
  return request({
    url: '/MD/MD_BinLocation/GetEntityByPLine',
    method: 'get',
    params: query
  })
}

// entity==>BarCode 数组
export function printToPDF(entity) {
  return request({
    url: '/PO/MD_BinLocation/Print',
    method: 'post',
    data: entity
  })
}

export function fetchPageByRegionCode(query) {
  return request({
    url: '/MD/MD_BinLocation/GetPageByRegionCode',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_BinLocation/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function fetchListByRegionCode(query) {
  return request({
    url: '/MD/MD_BinLocation/GetListByRegionCode',
    method: 'get',
    params: query
  })
}
