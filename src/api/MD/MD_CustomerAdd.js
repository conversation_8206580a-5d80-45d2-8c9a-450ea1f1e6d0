import request from '@/utils/request'

// 销售客户地址
export function fetchList(query) {
  return request({
    url: '/MD/MD_CustomerAdd/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_CustomerAdd/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_CustomerAdd/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_CustomerAdd/Delete',
    method: 'delete',
    data: ids
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_CustomerAdd/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 查询客户主数据
export function GetXZ_SAP_KNA1(query) {
  return request({
    url: '/SAP/XZSAP/GetXZ_SAP_KNA1',
    method: 'get',
    params: query
  })
}
