import request from '@/utils/request'

// 获取分页列表
export function fetchList(query) {
  return request({
    url: '/MD/MD_WorkCenterStation/GetPageList',
    method: 'get',
    params: query
  })
}

// 获取详情
export function getEntity(id) {
  return request({
    url: '/MD/MD_WorkCenterStation/GetEntity',
    method: 'get',
    params: { ID: id }
  })
}

// 添加
export function insert(entity) {
  return request({
    url: '/MD/MD_WorkCenterStation/Insert',
    method: 'post',
    data: entity
  })
}

// 更新
export function update(entity) {
  return request({
    url: '/MD/MD_WorkCenterStation/Update',
    method: 'post',
    data: entity
  })
}

// 删除单条记录
export function deleteSingle(id) {
  return request({
    url: '/MD/MD_WorkCenterStation/DeleteSingle',
    method: 'post',
    data: id
  })
}

// 批量删除
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_WorkCenterStation/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出数据
export function exportToExcelFile(query) {
  return request({
    url: '/MD/MD_WorkCenterStation/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出模板
export function exportToExcelModel() {
  return request({
    url: '/MD/MD_WorkCenterStation/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入数据
export function importExcelToData(data) {
  return request({
    url: '/MD/MD_WorkCenterStation/ImportExcelToData',
    method: 'post',
    data: data
  })
}

// 获取所有工作中心下拉列表
export function getWorkCenterList() {
  return request({
    url: '/MD/MD_WorkCenterStation/GetWorkCenterList',
    method: 'get'
  })
}

// 根据工作中心获取站点下拉列表
export function getStationListByWorkCenter(workCenterCode) {
  return request({
    url: '/MD/MD_WorkCenterStation/GetStationListByWorkCenter',
    method: 'get',
    params: { workCenterCode }
  })
}
