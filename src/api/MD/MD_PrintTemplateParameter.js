import request from '@/utils/request'

/**
 * 获取打印参数模板分页列表
 * @param {Object} query - 查询参数
 * @returns {Promise}
 */
export function fetchPageList(query) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/GetPageList',
    method: 'get',
    params: query
  })
}

/**
 * 根据ID获取打印参数模板实体
 * @param {string} id - 模板ID
 * @returns {Promise}
 */
export function getById(id) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/GetById',
    method: 'get',
    params: { id }
  })
}

/**
 * 添加打印参数模板
 * @param {Object} entity - 模板实体
 * @returns {Promise}
 */
export function add(entity) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/Add',
    method: 'post',
    data: entity
  })
}

/**
 * 更新打印参数模板
 * @param {Object} entity - 模板实体
 * @returns {Promise}
 */
export function update(entity) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/Update',
    method: 'post',
    data: entity
  })
}

/**
 * 批量删除打印参数模板
 * @param {Array} ids - 模板ID数组
 * @returns {Promise}
 */
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/Delete',
    method: 'post',
    data: ids
  })
}

/**
 * 导出模板数据到Excel
 * @param {Object} query - 查询参数
 * @returns {Promise}
 */
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

/**
 * 导出Excel导入模板
 * @returns {Promise}
 */
export function exportExcelModel() {
  return request({
    url: '/MD/MD_PrintTemplateParameter/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导入Excel数据
 * @param {Array} entitys - 导入的实体数组
 * @returns {Promise}
 */
export function importExcelData(entitys) {
  return request({
    url: '/MD/MD_PrintTemplateParameter/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}

/**
 * 获取所有启用的参数模板列表（用于下拉选择）
 * @returns {Promise}
 */
export function getEnabledList() {
  return request({
    url: '/MD/MD_PrintTemplateParameter/GetPageList',
    method: 'get',
    params: {
      PageNumber: 1,
      PageSize: 1000,
      Enable: true
    }
  })
}
