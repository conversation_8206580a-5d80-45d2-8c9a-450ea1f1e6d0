import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_SalePacking/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/MD/MD_SalePacking/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_SalePacking/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_SalePacking/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_SalePacking/Delete',
    method: 'delete',
    data: ids
  })
}

export function fetchPage(query) {
  return request({
    url: '/MD/MD_SalePacking/GetPage',
    method: 'get',
    params: query
  })
}

export function fetchOitmNextPage(query) {
  return request({
    url: '/MD/MD_SalePacking/GetOitmNextPage',
    method: 'get',
    params: query
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_SalePacking/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

