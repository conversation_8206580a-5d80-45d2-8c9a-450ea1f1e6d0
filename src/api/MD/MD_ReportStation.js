import request from '@/utils/request'

// 获取分页列表
export function fetchList(query) {
  return request({
    url: '/MD/MD_ReportStation/GetPageList',
    method: 'get',
    params: query
  })
}

// 添加
export function add(entity) {
  return request({
    url: '/MD/MD_ReportStation/Add',
    method: 'post',
    data: entity
  })
}

// 更新
export function update(entity) {
  return request({
    url: '/MD/MD_ReportStation/Update',
    method: 'post',
    data: entity
  })
}

// 删除
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_ReportStation/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportToExcelFile(query) {
  return request({
    url: '/MD/MD_ReportStation/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入数据
export function importExcelToData(data) {
  return request({
    url: '/MD/MD_ReportStation/ImportExcelToData',
    method: 'post',
    data: data
  })
}

// 导出模板
export function exportToExcelModel() {
  return request({
    url: '/MD/MD_ReportStation/ExportToExcelModel',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取站点关联的用户列表
export function getStationUsers(stationId) {
  return request({
    url: '/MD/MD_ReportStation/GetStationUsers',
    method: 'get',
    params: { stationId }
  })
}

// 添加站点和用户关联
export function addWithUsers(entity, userIds) {
  return request({
    url: '/MD/MD_ReportStation/Add',
    method: 'post',
    data: {
      entity: entity,
      userIds: userIds
    }
  })
}

// 更新站点和用户关联
export function updateWithUsers(entity, userIds) {
  return request({
    url: '/MD/MD_ReportStation/Update',
    method: 'post',
    data: {
      entity: entity,
      userIds: userIds
    }
  })
}
