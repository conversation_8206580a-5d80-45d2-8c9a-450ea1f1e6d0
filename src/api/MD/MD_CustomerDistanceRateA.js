import request from '@/utils/request'

// 销售客户地址
export function fetchList(query) {
  return request({
    url: '/MD/MD_CustomerDistanceRateA/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/MD/MD_CustomerDistanceRateA/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_CustomerDistanceRateA/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_CustomerDistanceRateA/Delete',
    method: 'delete',
    data: ids
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_CustomerDistanceRateA/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 查询供应商主数据
export function GetXZ_SAP_KNA1(query) {
  return request({
    url: '/SRM/XZSRM/GetSRM_SupplierInfo',
    method: 'get',
    params: query
  })
}
