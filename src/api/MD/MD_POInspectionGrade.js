import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/MD/MD_Stock/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList() {
  return request({
    url: '/MD/MD_POInspectionGrade/GetList',
    method: 'get'
  })
}

export function update(entity) {
  return request({
    url: '/MD/MD_POInspectionGrade/Update',
    method: 'post',
    data: entity
  })
}

