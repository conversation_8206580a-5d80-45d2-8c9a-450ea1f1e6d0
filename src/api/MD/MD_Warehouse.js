import request from '@/utils/request'

// 获取分页列表
export function fetchList(query) {
  return request({
    url: '/MD/MD_Warehouse/GetPageList',
    method: 'get',
    params: query
  })
}

// 新增一条仓库主数据
export function add(entity) {
  return request({
    url: '/MD/MD_Warehouse/Add',
    method: 'post',
    data: entity
  })
}

// 更新编辑,body为entity
export function update(entity) {
  return request({
    url: '/MD/MD_Warehouse/Update',
    method: 'post',
    data: entity
  })
}

// 删除数据,body为ids
export function batchDelete(ids) {
  return request({
    url: '/MD/MD_Warehouse/Delete',
    method: 'delete',
    data: ids
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_Warehouse/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
