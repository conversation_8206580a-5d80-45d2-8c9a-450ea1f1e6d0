import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/Produce/Produce_Report/GetPageList',
    method: 'get',
    params: query
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Produce/Produce_Report/Delete',
    method: 'delete',
    data: ids
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/Produce/Produce_Report/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取系统生成的单号
export function GetDocNum(query) {
  return request({
    url: '/Produce/Produce_Report/GetDocNum',
    method: 'get',
    params: query
  })
}
// 编辑时根据主键获取
export function GetEntity(query) {
  return request({
    url: '/Produce/Produce_Report/GetEntity',
    method: 'get',
    params: query
  })
}
// 根据生产订单获取工序
export function GetWorkingProcedure(query) {
  return request({
    url: '/Produce/Produce_Report/GetWorkingProcedure',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/Produce/Produce_Report/Add',
    method: 'post',
    data: entity
  })
}

// 编辑
export function Update(entity) {
  return request({
    url: '/Produce/Produce_Report/Update',
    method: 'post',
    data: entity
  })
}

// 根据生产订单获取工序
export function GetOrderBySerialNo(query) {
  return request({
    url: '/PP/PP_ProductionFeeding/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}
// 生产报工-根据序列号查询生产订单
export function GetOrderBySerialNo1(query) {
  return request({
    url: '/Produce/Produce_Report/GetOrderBySerialNo',
    method: 'get',
    params: query
  })
}
// 获取所有未启用序列号的生产订单
export function GetOrderNoSerialNo(query) {
  return request({
    url: '/Produce/Produce_Report/GetOrderNoSerialNo',
    method: 'get',
    params: query
  })
}
// 根据生产订单号获取未启用序列号的生产订单信息
export function GetOrderNoSerialNoByNo(query) {
  return request({
    url: '/Produce/Produce_Report/GetOrderNoSerialNoByNo',
    method: 'get',
    params: query
  })
}
// 根据订单和工序查询剩余数量
export function GetQty(query) {
  return request({
    url: '/Produce/Produce_Report/GetQty',
    method: 'get',
    params: query
  })
}
// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/Produce/Produce_Report/DownExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/Produce/Produce_Report/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}

// 获取工序列表
export function getWorkProcessList(produceOrderNo) {
  return request({
    url: '/Produce/Produce_Report/GetWorkProcessList',
    method: 'get',
    params: { produceOrderNo }
  })
}

// 报工扫描
export function scanReport(produceOrderNo) {
  return request({
    url: '/Produce/Produce_Report/ScanReport',
    method: 'get',
    params: { produceOrderNo }
  })
}

// 执行报工
export function doReport(data) {
  return request({
    url: '/Produce/Produce_Report/DoReport',
    method: 'post',
    data
  })
}

// 创建初始报工信息
export function createInitialReportInfo(schedulingIds) {
  return request({
    url: '/Produce/Produce_Report/CreateInitialReportInfo',
    method: 'post',
    data: schedulingIds
  })
}

// 获取导入模板
export function getImportTemplate() {
  return request({
    url: '/Produce/Produce_Report/GetImportTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入数据
export function importData(formData) {
  return request({
    url: '/Produce/Produce_Report/ImportData',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取报工明细列表
export function getDetailList(id) {
  return request({
    url: '/Produce/Produce_Report/GetDetailList?id=' + id,
    method: 'get'
  })
}

// 站点扫描
export function scanStation(data) {
  return request({
    url: '/Produce/Produce_Report/ScanStation',
    method: 'post',
    data
  })
}

// 获取当前用户关联的站点列表
export function getSelfStationList() {
  return request({
    url: '/Produce/Produce_Report/GetSelfStationList',
    method: 'get'
  })
}

// 根据生产订单号获取工单信息
export function getEntityByProduceOrderNo(produceOrderNo) {
  return request({
    url: '/Produce/Produce_Report/GetEntityByProduceOrderNo',
    method: 'get',
    params: { produceOrderNo }
  })
}
