import request from '@/utils/request'

// 分页查询主表
export function fetchList(data) {
  return request({
    url: '/Produce/Produce_Scheduling/GetPageList?PageNumber=' + data.PageNumber + '&PageSize=' + data.PageSize,
    method: 'post',
    data: data
  })
}

// 分页查询主表
export function getProduceVersionList(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetProduceVersionList',
    method: 'get',
    params: query
  })
}

// 查询统计树
export function GetTree(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetTree',
    method: 'get',
    params: query
  })
}

// 查询统计树 不含线体
export function GetTreeNotLine(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetTreeNotLine',
    method: 'get',
    params: query
  })
}

// 获取统计树 日期开始
export function getTreeBySchedulingDate(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetTreeBySchedulingDate',
    method: 'get',
    params: query
  })
}

// 同步交货信息
export function SyncSaleDelivery(data) {
  return request({
    url: '/Produce/Produce_Scheduling/SyncSaleDelivery',
    method: 'post',
    data: data
  })
}

// 设置排产日期
export function setProduceSchedulingDate(data) {
  return request({
    url: '/Produce/Produce_Scheduling/SetProduceSchedulingDate',
    method: 'post',
    data: data
  })
}

// 设定生产线
export function setProduceLine(data) {
  return request({
    url: '/Produce/Produce_Scheduling/SetProduceLine',
    method: 'post',
    data: data
  })
}

// 完成预排产
export function completePreProduce(data) {
  return request({
    url: '/Produce/Produce_Scheduling/CompletePreProduce',
    method: 'post',
    data: data
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/Produce/Produce_Scheduling/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出更新模板
export function exportUpdateTemp(query) {
  return request({
    url: '/Produce/Produce_Scheduling/ExportUpdateTemp',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 导出配送清单
export function exportDeliveryList(query) {
  return request({
    url: '/Produce/Produce_Scheduling/ExportDeliveryList',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 明细
export function GetPageList(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetPageDetailList',
    method: 'get',
    params: query
  })
}

// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetDocNum',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/Produce/Produce_Scheduling/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/Produce/Produce_Scheduling/Update',
    method: 'post',
    data: entity
  })
}

// 查询SAP中间库信息
export function GetXZSAP_PurchaseOrder(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_VBAK',
    method: 'get',
    params: query
  })
}

// 编辑根据条件查询明细信息
export function GetList(query) {
  return request({
    url: '/Produce/Produce_Scheduling/GetList',
    method: 'get',
    params: query
  })
}

// 打印
export function printOrderToPDF(query) {
  return request({
    url: '/Produce/Produce_Scheduling/Print',
    method: 'get',
    params: query
  })
}

// 上传SRM
export function UploadSRM(entity) {
  return request({
    url: '/Produce/Produce_Scheduling/UploadSRM',
    method: 'post',
    data: entity
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/Produce/Produce_Scheduling/DoPost',
    method: 'post',
    data: entity
  })
}

// 冲销
export function PassPost(entity) {
  return request({
    url: '/Produce/Produce_Scheduling/PassPost',
    method: 'post',
    data: entity
  })
}

// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/Produce/Produce_Scheduling/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/Produce/Produce_Scheduling/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}

// 设置序号&批次
export function SetSortAndBatch(data) {
  return request({
    url: '/Produce/Produce_Scheduling/SetSortAndBatch',
    method: 'post',
    data: data
  })
}

// 设置序号&批次
export function ConfirmFormalProduce(data) {
  return request({
    url: '/Produce/Produce_Scheduling/ConfirmFormalProduce',
    method: 'post',
    data: data
  })
}

// 根据父ID获取排产明细列表
export function GetDetailsByPid(pid) {
  return request({
    url: '/Produce/Produce_SchedulingDetail/GetDetailsByPid',
    method: 'get',
    params: { pid: pid }
  })
}

// 根据父ID获取采购明细列表
export function GetPurchaseDetailsByPid(pid) {
  return request({
    url: '/Produce/Produce_PurchaseOrder/GetDetailsByPid',
    method: 'get',
    params: { pid: pid }
  })
}
