import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/Produce/Produce_Warehousing/GetPageList',
    method: 'get',
    params: query
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/Produce/Produce_Warehousing/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/Produce/Produce_Warehousing/DoPost',
    method: 'post',
    data: entity
  })
}

// 取消过账
export function Audits(query) {
  return request({
    url: '/Produce/Produce_Warehousing/Audit',
    method: 'get',
    params: query
  })
}

// 导入模板
export function ImportExcelToData(entitys) {
  return request({
    url: '/Produce/Produce_Warehousing/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}

