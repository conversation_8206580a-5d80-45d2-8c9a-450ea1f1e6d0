// 自适应调整调整表格高度封装函数

export function adjustHeight(that, offsetTop) {
  /* console.log('111111', that.$refs.table.$options.propsData.height)
  console.log('222222', that.$refs.table.$options)
  console.log('333333', that) */
  that.$nextTick(function() {
    that.tableHeight = window.innerHeight - offsetTop - 110

    // 监听窗口大小变化
    // const self = that
    window.onresize = function() {
      that.tableHeight = window.innerHeight - offsetTop - 110
    }
  })
  // this.$refs.table.$el.offsetTop：表格距离浏览器的高度
}
