import request from '@/utils/request'
// 库存报表
export function fetchStockDiffList(query) {
  return request({
    url: '/RPT/StockDiff/GetStockDiff',
    method: 'get',
    params: query
  })
}

//
export function adjustForWMSToSap(entities) {
  return request({
    url: '/RPT/StockDiff/AdjustForWMSToSap',
    method: 'post',
    data: entities
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/RPT/StockDiff/ExportToExcelFileDiff',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
