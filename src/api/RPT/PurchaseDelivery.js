import request from '@/utils/request'

export function fetchPurchaseDeliverySchedule(query) {
  return request({
    url: '/MD/PurchaseDelivery/GetPurchaseDeliverySchedule',
    method: 'get',
    params: query
  })
}

export function fetchPurchaseDeliveryOnTimeRate(query) {
  return request({
    url: '/MD/PurchaseDelivery/GetPurchaseDeliveryOnTimeRate',
    method: 'get',
    params: query
  })
}

export function exportExcelFileSchedule(query) {
  return request({
    url: '/MD/PurchaseDelivery/ExportToExcelFileSchedule',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/MD/PurchaseDelivery/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

