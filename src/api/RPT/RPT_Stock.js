import request from '@/utils/request'
// 库存报表
export function fetchList(query) {
  return request({
    url: '/MD/MD_Stock/GetPage',
    method: 'get',
    params: query
  })
}
// 库存报表
export function GetStockList(query) {
  return request({
    url: '/MD/MD_Stock/GetStockList',
    method: 'get',
    params: query
  })
}
export function GetStockList_PP(query) {
  return request({
    url: '/MD/MD_Stock/GetStockList_PP',
    method: 'get',
    params: query
  })
}
export function fetchMainList(query) {
  return request({
    url: '/MD/MD_Stock/GetPageToMain',
    method: 'get',
    params: query
  })
}
// 库龄报表
export function fetchStockAgeList(query) {
  return request({
    url: '/MD/MD_Stock/GetStockAge',
    method: 'get',
    params: query
  })
}
export function exportExcelFileAge(query) {
  return request({
    url: '/MD/MD_Stock/ExportToExcelFileAge',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/MD/MD_Stock/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
