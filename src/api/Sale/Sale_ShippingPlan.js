import request from '@/utils/request'

// 分页查询主表
export function fetchList(data) {
  return request({
    url: '/Sale/Sale_ShippingPlan/GetPageList?PageNumber=' + data.PageNumber + '&PageSize=' + data.PageSize,
    method: 'post',
    data: data
  })
}

// 分页查询主表
export function GetTree(query) {
  return request({
    url: '/Sale/Sale_ShippingPlan/GetTree',
    method: 'get',
    params: query
  })
}

// 上传SRM
export function UploadSRM(entity) {
  return request({
    url: '/Sale/Sale_ShippingPlan/UploadSRM',
    method: 'post',
    data: entity
  })
}

// 同步交货信息
export function SyncSaleDelivery(data) {
  return request({
    url: '/SD/Sale_ShippingPlan/SyncSaleDelivery',
    method: 'post',
    data: data
  })
}

// 导出
export function exportExcelFile(query) {
  return request({
    url: '/SD/Sale_ShippingPlan/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 明细
export function GetPageList(query) {
  return request({
    url: '/SD/Sale_ShippingPlan/GetPageDetailList',
    method: 'get',
    params: query
  })
}

// 获取单号
export function GetDocNum(query) {
  return request({
    url: '/SD/Sale_ShippingPlan/GetDocNum',
    method: 'get',
    params: query
  })
}
// 新增
export function SubmitScanInfo(entity) {
  return request({
    url: '/SD/Sale_ShippingPlan/Save',
    method: 'post',
    data: entity
  })
}
// 编辑
export function update(entity) {
  return request({
    url: '/SD/Sale_ShippingPlan/Update',
    method: 'post',
    data: entity
  })
}

// 查询SAP中间库信息
export function GetXZSAP_PurchaseOrder(query) {
  return request({
    url: '/SAP/XZSAP/GetXZSAP_VBAK',
    method: 'get',
    params: query
  })
}

// 编辑根据条件查询明细信息
export function GetList(query) {
  return request({
    url: '/SD/Sale_ShippingPlan/GetList',
    method: 'get',
    params: query
  })
}

// 打印
export function printOrderToPDF(query) {
  return request({
    url: '/SD/Sale_ShippingPlan/Print',
    method: 'get',
    params: query
  })
}

// 过账
export function DoPost(entity) {
  return request({
    url: '/SD/Sale_ShippingPlan/DoPost',
    method: 'post',
    data: entity
  })
}

// 冲销
export function PassPost(entity) {
  return request({
    url: '/SD/Sale_ShippingPlan/PassPost',
    method: 'post',
    data: entity
  })
}

// 导出模板
export function exportExcelModel(query) {
  return request({
    url: '/SD/Sale_ShippingPlan/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导入模板
export function improtExcelFile(entitys) {
  return request({
    url: '/SD/Sale_ShippingPlan/ImportExcelToData',
    method: 'post',
    data: entitys
  })
}
