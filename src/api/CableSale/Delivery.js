import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/CableSale/Delivery/GetPageList',
    method: 'get',
    params: query
  })
}

// 批量创建
export function createBatch(data) {
  return request({
    url: '/CableSale/Delivery/CreateBatch',
    method: 'post',
    data: data
  })
}

// 导出
export function Export(query) {
  return request({
    url: '/CableSale/Delivery/Export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出
export function exportExcelModel(query) {
  return request({
    url: '/CableSale/Delivery/ExportToExcelModel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 过账
export function post(data) {
  return request({
    url: '/CableSale/Delivery/Post',
    method: 'post',
    data: data
  })
}

// 取消过账
export function cancelPost(data) {
  return request({
    url: '/CableSale/Delivery/CancelPost',
    method: 'post',
    data: data
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/CableSale/Delivery/Delete',
    method: 'delete',
    data: ids
  })
}
