import request from '@/utils/request'

// 分页查询主表
export function fetchList(query) {
  return request({
    url: '/CableSale/ShippingPlan/GetPageList',
    method: 'get',
    params: query
  })
}

// 导出
export function exportExcel(query) {
  return request({
    url: '/CableSale/ShippingPlan/Export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/CableSale/ShippingPlan/Delete',
    method: 'delete',
    data: ids
  })
}
