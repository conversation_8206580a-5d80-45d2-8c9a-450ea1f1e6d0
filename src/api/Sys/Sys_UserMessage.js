import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_UserMessage/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_UserMessage/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_UserMessage/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_UserMessage/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_UserMessage/Delete',
    method: 'delete',
    data: ids
  })
}

// 获取用户列表清单fetchUserList
export function fetchUnreadMessageList(query) {
  return request({
    url: '/Sys/Sys_UserMessage/GetUserUnReadMessageList',
    method: 'get',
    params: query // unReadList
  })
}

// 对用户已读的消息返回修改状态
export function readMessage(query) {
  return request({
    url: '/Sys/Sys_UserMessage/ReadUserMessage',
    method: 'get',
    params: query // userMessageID
  })
}
