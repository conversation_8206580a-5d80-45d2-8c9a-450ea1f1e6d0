import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_RoleResource/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_RoleResource/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_RoleResource/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_RoleResource/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_RoleResource/Delete',
    method: 'delete',
    data: ids
  })
}

// 功能描述：重设用户角色菜单
// 请求参数：{id:'P0001',ids:['001','0002']}  id:角色ID ids:资源ID清单
// 返回参数：{Code:2000,Message:'',Data:null}
// 做者：张旭
// 时间：2019-08-21
export function ResetRoleResources(data) {
  // console.log('ResetRoleResources',data)
  return request({
    url: '/Sys/Sys_RoleResource/ResetRoleResources',
    method: 'post',
    data: data
  })
}
