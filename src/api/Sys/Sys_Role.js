import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_Role/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_Role/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_Role/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_Role/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_Role/Delete',
    method: 'delete',
    data: ids
  })
}

// ids 主键数组
export function getUserRoles(userid) {
  return request({
    url: '/Sys/Sys_Role/GetUserRoles',
    method: 'get',
    params: userid
  })
}

// 导出功能
export function exportExcelFile(query) {
  return request({
    url: '/Sys/Sys_Role/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
