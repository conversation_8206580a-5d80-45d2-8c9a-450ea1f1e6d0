import request from '@/utils/request'
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_Dictionary/GetEntity',
    method: 'get',
    params: query
  })
}

export function fetchPage(query) {
  return request({
    url: '/Sys/Sys_Dictionary/GetPageList',
    method: 'get',
    params: query
  })
}

export function fetchList(query) {
  return request({
    url: '/Sys/Sys_Dictionary/GetList',
    method: 'get',
    params: query
  })
}
export function getAllType(query) {
  return request({
    url: '/Sys/Sys_Dictionary/GetTypeList',
    method: 'get',
    params: query
  });
}
export function add(entity) {
  return request({
    url: '/Sys/Sys_Dictionary/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_Dictionary/Update',
    method: 'post',
    data: entity
  })
}

export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_Dictionary/Delete',
    method: 'delete',
    data: ids
  })
}
export function exportExcelFile(query) {
  return request({
    url: '/Sys/Sys_Dictionary/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
