import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_UserRole/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_UserRole/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_UserRole/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_UserRole/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_UserRole/Delete',
    method: 'delete',
    data: ids
  })
}

// 重置用户角色
export function resetUserRoles(dataEntity) {
  return request({
    url: '/Sys/Sys_UserRole/ResetUserRoles',
    method: 'get',
    params: dataEntity
  })
}
