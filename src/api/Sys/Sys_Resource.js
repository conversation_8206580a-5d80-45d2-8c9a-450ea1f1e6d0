import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_Resource/GetEntity',
    method: 'get',
	  params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_Resource/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_Resource/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_Resource/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组

export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_Resource/Delete',
    method: 'delete',
    data: ids
  })
}

// 功能描述：根据客户端ID 获取响应资源清单
// 请求参数：{appid:'P0001'}
// 返回参数：{Code:2000,Message:'',Data:List<Sys_Resource>}
// 做者：张旭
// 时间：2019-08-21
export function GetResourcesByAppID(query) {
  return request({
    url: '/Sys/Sys_Resource/GetResourcesByAppID',
    method: 'get',
    params: query
  })
}

// 功能描述：根据客户端ID 获取响应资源清单
// 请求参数：{appid:'P0001',roleid:'553130b1-6ef5-41f2-9382-9076d549a21c'}
// 返回参数：{Code:2000,Message:'',Data:List<Sys_Resource>}
// 做者：张旭
// 时间：2019-08-21
export function GetResourceByAppRole(query) {
  return request({
    url: '/Sys/Sys_Resource/GetResourceByAppRole',
    method: 'get',
    params: query
  })
}
