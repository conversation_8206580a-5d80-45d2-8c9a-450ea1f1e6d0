import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_Message/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_Message/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_Message/Add',
    method: 'post',
    data: entity
  })
}

export function update(entity) {
  return request({
    url: '/Sys/Sys_Message/Update',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_Message/Delete',
    method: 'delete',
    data: ids
  })
}

