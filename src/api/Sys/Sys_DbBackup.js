import request from '@/utils/request'

// 获取实体信息
export function getEntity(query) {
  return request({
    url: '/Sys/Sys_DbBackup/GetEntity',
    method: 'get',
    params: query
  })
}

// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_DbBackup/GetPageList',
    method: 'get',
    params: query
  })
}

export function backUpDB() {
  return request({
    url: '/Sys/Sys_DbBackup/BackupDb',
    method: 'post',
    timeout: ''
  })
}
// { BackupID:''}
export function restoreDb(entity) {
  console.log(entity)
  return request({
    url: '/Sys/Sys_DbBackup/RestoreDB',
    method: 'get',
    params: entity
  })
}

// ids 主键数组
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_DbBackup/Delete',
    method: 'delete',
    data: ids
  })
}

