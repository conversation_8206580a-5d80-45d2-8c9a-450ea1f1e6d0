import request from '@/utils/request'

export function SaveMessageNotifySetting(entity) {
  return request({
    url: '/Sys/Sys_MessageNotifySetting/SaveMessageNotifySetting',
    method: 'post',
    data: entity
  })
}

// ids 主键数组
export function GetMessageNotifySettingDetailInfo(query) {
  return request({
    url: '/Sys/Sys_MessageNotifySetting/GetMessageNotifySettingDetailInfo',
    method: 'get',
    params: query
  })
}

