import request from '@/utils/request'

// 权限相关↓↓↓
export function doLogin(query) {
  return request({
    url: '/Sys/Sys_User/doLogin',
    method: 'get',
    params: query
  })
}
// 根据Token获取用户基本信息
export function getUserInfo() {
  return request({
    url: '/Sys/Sys_User/GetUserInfo',
    method: 'get'
  })
}

// 获取登录用户权限路由
export function getUserRoutes() {
  return request({
    url: '/Sys/Sys_User/GetUserRoutes',
    method: 'get'
  })
}

// 退出登陆
export function logout() {
  return request({
    url: '/Sys/Sys_User/Logout',
    method: 'get'
  })
}

// 用户管理相关↓↓↓
// 获取用户列表清单fetchUserList
export function fetchList(query) {
  return request({
    url: '/Sys/Sys_User/GetPageList',
    method: 'get',
    params: query
  })
}

export function add(entity) {
  return request({
    url: '/Sys/Sys_User/Add',
    method: 'post',
    data: entity
  })
}

// 更新用户基本信息
export function update(entity) {
  return request({
    url: '/Sys/Sys_User/Update',
    method: 'post',
    data: entity
  })
}

// 批量删除
export function batchDelete(ids) {
  return request({
    url: '/Sys/Sys_User/Delete',
    method: 'delete',
    data: ids
  })
}

// 重置用户密码
export function resetPassword(ids) {
  return request({
    url: '/Sys/Sys_User/ResetUserPassword',
    method: 'put',
    data: ids
  })
}

// 重置用户密码
export function resetUserRoles(ids) {
  return request({
    url: '/Sys/Sys_User/ResetUserPassword',
    method: 'put',
    data: ids
  })
}
// 获取所有用户列表
export function fetchAllUser() {
  return request({
    url: '/Sys/Sys_User/GetList',
    method: 'get',
    params: {
      'keyword': ''
    }
  })
}

// 获取登录用户所有权限按钮
export function fetchUserPermissionButtons() {
  return request({
    url: '/Sys/Sys_User/GetUserPermissionButtons',
    method: 'get'
  })
}

// 用户修改个人密码
export function modifyPassword(entity) {
  console.log('modifyPassword', entity)
  return request({
    url: '/Sys/Sys_User/ModifyPassword',
    method: 'get',
    params: entity
  })
}

export function exportExcelFile(query) {
  return request({
    url: '/Sys/Sys_User/ExportToExcelFile',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
