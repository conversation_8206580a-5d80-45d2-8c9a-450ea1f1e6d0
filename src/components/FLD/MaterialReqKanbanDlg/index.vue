<template>
  <el-dialog ref="dlg" :visible.sync="showDlg" :title="title" width="1000px">
    <div class="filter-container">
      <el-input
        v-model="listQuery.ItemCode"
        type="text"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="getList"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-refresh"
        @click="getList"
      >{{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        :v-if="showCheck"
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductionOrderID')"
        prop="ProductionOrderID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductionLine')"
        prop="ProductionLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductID')"
        prop="ProductID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductDescription')"
        prop="ProductDescription"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BinLocationCode')"
        prop="BinLocationCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BinLocationName')"
        prop="BinLocationName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.MaterialID')"
        prop="MaterialID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MaterialID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.MaterialDescription')"
        prop="MaterialDescription"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MaterialDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BOMUnitCode')"
        prop="BOMUnitCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BOMUnitCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BOMQty')"
        prop="BOMQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BOMQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.PlanQty')"
        prop="PlanQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlanQty }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.MaterialReqKanban.ConversionRate')" prop="ConversionRate"  align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.ConversionRate}}</span>
            </template>
      </el-table-column>-->
      <!-- <el-table-column :label="$t('ui.PP.MaterialReqKanban.UnitCode')" prop="UnitCode"  align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.UnitCode}}</span>
            </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.StockingQty')"
        prop="StockingQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.StockingQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BufferQty')"
        prop="BufferQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BufferQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.PreparedQty')"
        prop="PreparedQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PreparedQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.FeededQty')"
        prop="FeededQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.FeededQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.InventoryQty')"
        prop="InventoryQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InventoryQty }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <span slot="footer" class="dialog-footer">
      <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button>
      <el-button
        type="primary"
        icon="el-icon-check"
        @click="handleSelectMaterialBtnClick"
      >{{ $t('Common.select') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  fetchPage
} from '@/api/PP/PP_MaterialReqKanban';
import Pagination from '@/components/Pagination/index';

export default {
  name: 'MaterialReqKanbanDlg',
  components: {
    Pagination
  },
  props: {
    isMultiple: Boolean,
    show: Boolean,
    pLine: String
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        productionLine: this.pLine,
        PageNumber: 1,
        PageSize: 10
      },
      multipleSelection: [],
      singleSelection: {},
      showCheck: this.isMultiple,
      showDlg: this.show,
      total: 0,
      title: ''
    };
  },
  watch: {
    show: function(newVal, oldVal) {
      this.showDlg = newVal;
    },
    showDlg: function(newVal, oldVal) {
      this.$emit('update:show', newVal);
      if (newVal === false) {
        if (this.showCheck) {
          this.selection = this.multipleSelection;
        } else {
          this.selection = this.singleSelection;
        }
        this.$emit('close', this.selection);
      } else {
        this.reset();
        this.getList()
      }
    }
  },
  created() { },
  methods: {
    getList() {
      this.listLoading = true;
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    reset() {
      this.title = this.$i18n.t('Common.selectMaterial'); // 对话框标题
      this.selection = undefined; // 选中的物料项,如果多选为数组,如果单选为对象
      this.showCheck = false; // 是否多选
      this.list = [];
      this.listQuery = {
        productionLine: this.pLine,
        PageNumber: 1,
        PageSize: 10
      };
      this.multipleSelection = undefined;
      this.singleSelection = undefined;
      this.listLoading = false;
      this.showCheck = this.isMultiple
    },
    handleFilter() {
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSelectMaterialBtnClick() {
      this.showDlg = false;
    },
    handleRowClick(row) {
      this.singleSelection = Object.assign({}, row);
    }
  }
};
</script>
