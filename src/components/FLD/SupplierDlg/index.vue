<template>
  <el-dialog ref="dlg" :visible.sync="showDlg" :title="title" width="1000px">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        type="text"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column v-if="false" :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.Supplier.SupplierCode')"
        prop="SupplierCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.Supplier.SupplierName')"
        prop="SupplierName"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.Supplier.Email')" prop="Email" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Email }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.Supplier.City')" prop="City" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.City }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.Supplier.Phone')" prop="Phone" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Phone }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.Supplier.Fax')" prop="Fax" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Fax }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.Supplier.ContactPerson')"
        prop="ContactPerson"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ContactPerson }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.Supplier.Address')"
        prop="Qty"
        sortable="Address"
        align="center"
        width="300"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Address }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <span slot="footer" class="dialog-footer">
      <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectMaterialBtnClick">{{ $t('Common.select') }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  fetchList
} from '@/api/MD/MD_Supplier'
import Pagination from '@/components/Pagination/index'

export default {
  name: 'MaterialDlg',
  components: {
    Pagination
  },
  props: {
    isMultiple: Boolean,
    show: Boolean,
    title: String
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      multipleSelection: undefined,
      singleSelection: undefined,
      showCheck: this.isMultiple,
      showDlg: this.show,
      total: 0
    }
  },
  watch: {
    show: function(newVal, oldVal) {
      this.showDlg = newVal
    },
    showDlg: function(newVal, oldVal) {
      this.$emit('update:show', newVal)
      if (newVal === false) {
        if (this.showCheck) {
          this.selection = this.multipleSelection
        } else {
          this.selection = this.singleSelection
        }
        this.$emit('close', this.selection)
      } else {
        this.reset()
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items
          this.total = response.Data.total
        } else {
          this.showNotify('error', response.Message)
        }
        this.listLoading = false
      })
    },
    reset() {
      this.title = this.$i18n.t('Common.selectSupplier') // 对话框标题
      this.selection = undefined // 选中的物料项,如果多选为数组,如果单选为对象
      this.showCheck = false // 是否多选
      this.list = []
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }
      this.multipleSelection = undefined
      this.singleSelection = undefined
      this.listLoading = false
      this.total = 0
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.listQuery.PageSize = 10
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectMaterialBtnClick() {
      this.showDlg = false
    },
    handleRowClick(row) {
      this.singleSelection = Object.assign({}, row)
    }
  }
}
</script>
