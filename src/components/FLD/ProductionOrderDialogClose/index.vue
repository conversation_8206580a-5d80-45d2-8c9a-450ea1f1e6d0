<template>
  <div>
    <el-dialog
      ref="dlg"
      :visible.sync="showDlg"
      :title="$t('ui.PP.ProductionOrder.select')"
      width="1000px"
    >
      <!-- <p>{{$t('ui.PP.ProductionOrder.title')}}</p> -->
      <div class="filter-container">
        <el-input
          v-if="showTextSearch"
          v-model="listQuery.productionOrderID"
          type="text"
          :placeholder="$t('ui.PP.ProductionOrder.BaseNum')"
          style="width: 140px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-if="showStatusSearch"
          v-model="listQuery.productionOrderStatus"
          style="width: 140px"
          class="filter-item"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.Key"
            :label="item.Value"
            :value="item.Key"
          />
        </el-select>
        <el-select
          v-if="showProductionLineSearch"
          v-model="listQuery.productionLine"
          style="width: 260px"
          class="filter-item"
        >
          <el-option
            v-for="item in pLineOptions"
            :key="item.EnumValue"
            :value="item.EnumValue"
            :label="item.Remark"
          />
        </el-select>
        <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
        <hr>
      </div>

      <el-table
        v-loading="listLoading"
        :class="classObject"
        :data="list"
        border
        fit
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%"
        @selection-change="handleMultiSelection"
      >
        <!-- @row-click="handleRowClick" -->
        <el-table-column
          v-if="true"
          :label="$t('Common.select')"
          type="selection"
          align="center"
          width="40"
          fixed
        />
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionLine')"
          prop="ProductionLine"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionLine }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="false"
          :label="$t('ui.PP.ProductionOrder.ProductionOrderStatus')"
          prop="ProductionOrderStatus"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderStatus }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column :label="$t('ui.PP.ProductionOrder.ProductionOrderPriority')" prop="ProductionOrderPriority"  align="center" width="120">
                <template slot-scope="scope">
                    <span>{{ scope.row.ProductionOrderPriority}}</span>
                </template>
        </el-table-column>-->
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.BaseNum')"
          prop="BaseNum"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BaseNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ItemCode')"
          prop="ItemCode"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionOrderOpenQuantity')"
          prop="ProductionOrderOpenQuantity"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderOpenQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionOrderPlannedQuantity')"
          prop="ProductionOrderPlannedQuantity"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderPlannedQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionOrderFullfilledQuantity')"
          prop="ProductionOrderFullfilledQuantity"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderFullfilledQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.SubmittedQuantity')"
          prop="SubmittedQuantity"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SubmittedQuantity }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ItemName')"
          prop="ItemName"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.RequestedEndDate')"
          prop="RequestedEndDate"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.RequestedEndDate|datetime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionOrderSiteID')"
          prop="ProductionOrderSiteID"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderSiteID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionOrderReleaseDate')"
          prop="ProductionOrderReleaseDate"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderReleaseDate|datetime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PP.ProductionOrder.ProductionOrderPlannedQuantityUnit')"
          prop="ProductionOrderPlannedQuantityUnit"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductionOrderPlannedQuantityUnit }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('ui.PP.ProductionOrder.CUser')"
          prop="CUser"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.CUser }}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <!-- 关闭按钮 -->
        <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSelectBtnClick"
        >{{ $t('Common.select') }}</el-button>
      </span>
      <div v-if="showDetail">
        <p>{{ $t('ui.PP.MaterialReq.title') }}</p>
        <el-table
          v-loading="listLoadingDetail"
          :height="300"
          :class="classObjectDetail"
          :data="listDetail"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          highlight-current-row
          @row-click="handleDetailRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="multiSelection"
            :label="$t('Common.select')"
            type="selection"
            align="center"
            width="40"
            fixed
          />
          <el-table-column
            v-if="false"
            :label="$t('ui.PP.MaterialReq.ReqID')"
            prop="ReqID"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.ReqID }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('ui.PP.MaterialReq.BaseEntry')" prop="BaseEntry"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BaseEntry}}</span> </template> </el-table-column> -->
          <el-table-column
            :label="$t('ui.PP.MaterialReq.BaseNum')"
            prop="BaseNum"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.BaseNum }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.PLine')"
            prop="PLine"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.PLine }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('ui.PP.MaterialReq.BinLocationCode')" prop="BinLocationCode"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BinLocationCode}}</span> </template> </el-table-column> -->
          <!-- <el-table-column :label="$t('ui.PP.MaterialReq.BinLocationName')" prop="BinLocationName"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BinLocationName}}</span> </template> </el-table-column> -->
          <!-- <el-table-column :label="$t('ui.PP.MaterialReq.BaseLine')" prop="BaseLine"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BaseLine}}</span> </template> </el-table-column> -->
          <el-table-column
            :label="$t('ui.PP.MaterialReq.ItemCode')"
            prop="ItemCode"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.ItemCode }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.ItemName')"
            prop="ItemName"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.ItemName }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('ui.PP.MaterialReq.ItmsGrpCode')" prop="ItmsGrpCode"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode}}</span> </template> </el-table-column> -->
          <!-- <el-table-column :label="$t('ui.PP.MaterialReq.ItmsGrpName')" prop="ItmsGrpName"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName}}</span> </template> </el-table-column> -->
          <el-table-column
            :label="$t('ui.PP.MaterialReq.BOMUnit')"
            prop="BOMUnit"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.BOMUnit }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.BOMReqQty')"
            prop="BOMReqQty"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.BOMReqQty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.ConversionRate')"
            prop="ConversionRate"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.ConversionRate }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.Unit')"
            prop="Unit"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.Unit }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.ReqQty')"
            prop="ReqQty"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.ReqQty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.StockingQty')"
            prop="StockingQty"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.StockingQty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.StockQty')"
            prop="StockQty"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.StockQty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.MaterialQty')"
            prop="MaterialQty"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.MaterialQty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('ui.PP.MaterialReq.SurplusQty')"
            prop="SurplusQty"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.SurplusQty }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('Common.Remark')" prop="Remark"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.Remark}}</span> </template> </el-table-column> -->
        </el-table>
      </div>
      <!-- <span slot="footer" class="dialog-footer"> -->
      <!-- 关闭按钮 -->
      <!-- <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button> -->
      <!-- <el-button type="primary" icon="el-icon-check" @click="handleSelectBtnClick">{{ $t('Common.select') }}</el-button> -->
      <!-- </span> -->
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchStatusList,
  fetchCloseList as fetchList,
  fetchDetailList
} from '@/api/PP/PP_ProductionOrder';
_ = require('lodash');

export default {
  name: 'ProductionOrderDialog',
  props: {
    multiSelection: {
      type: Boolean,
      default: false
    },
    show: Boolean,
    productionLine: String,
    showDetail: {
      type: Boolean,
      default: false
    },
    showTextSearch: {
      type: Boolean,
      default: true
    },
    showStatusSearch: {
      type: Boolean,
      default: true
    },
    showProductionLineSearch: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      classObject: {
        height: '200px'
      },
      classObjectDetail: {
        height: '200px'
      },
      list: [],
      listLoading: false,
      listQuery: {
        productionOrderStatus: undefined,
        productionOrderID: '',
        productionLine: undefined
      },
      listDetail: [],
      listLoadingDetail: false,
      listQueryDetail: {
        productionOrderID: '',
        productionLine: undefined
      },
      productionOrder: undefined,
      showDlg: this.show,
      statusOptions: [],
      pLineOptions: [],
      multiSelections: [],
      productionOrder: undefined,
      productionOrderDetails: undefined
    };
  },
  watch: {
    show: function(newVal, oldVal) {
      this.showDlg = newVal;
    },
    showDlg: function(newVal, oldVal) {
      this.$emit('update:show', newVal);
      if (newVal === false) {
        this.$emit('close', this.productionOrder, this.productionOrderDetails);
      } else this.reset();
    },
    productionLine: function(newVal, oldVal) {
      this.listQuery.productionLine = newVal;
    }
  },
  created() {
    this.getStatusOptions();
    this.getPLineOptions();
  },
  methods: {
    getPage() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data;
          if (this.list && this.list.length > 0) {
          }
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getDetailList(queryDetail) {
      if (this.showDetail === false) {
        return;
      }
      this.listLoadingDetail = true;
      var newQuery = this.listQueryDetail;
      if (queryDetail) {
        newQuery = newQuery;
      }
      fetchDetailList(newQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data;
          if (this.listDetail) {
            this.listDetail.forEach(x => {
              x.BaseNum = this.productionOrder.BaseNum;
              x.ProductionLine = this.productionOrder.ProductionLine;
              x.PLine = this.productionOrder.ProductionLine;
            });
          }
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoadingDetail = false;
      });
    },
    getStatusOptions() {
      if (this.showStatusSearch === false) {
        return;
      }
      fetchStatusList().then(response => {
        if (response.Code === 2000) {
          //   this.statusOptions = response.Data;
          var excludeOptions = response.Data.filter(
            x => x.Key != 4 && x.Key != 5 && x.Key != 6
          );
          this.statusOptions = excludeOptions;
          this.statusOptions.unshift({
            Key: '',
            Value: this.$i18n.t('Common.all')
          });
          if (this.statusOptions && this.statusOptions.length > 0) {
            this.listQuery.status = this.statusOptions[0].Key;
          }
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getPLineOptions() {
      if (this.showProductionLineSearch === false) {
        return;
      }
      this.getDict('PP005').then(data => {
        data = _.uniqBy(data, x => x.EnumValue);
        data = _.sortBy(data, x => x.EnumValue);
        this.pLineOptions = data;
        if (this.listQuery.productionLine) {
        } else {
          // 如果主单没有生产线信息,默认选择下拉列表中的第一项
          if (this.pLineOptions && this.pLineOptions.length > 0) {
            this.pLineOptions.unshift({
              EnumValue: '',
              Remark: this.$i18n.t('Common.all')
            });
            this.listQuery.productionLine = this.pLineOptions[0].EnumValue;
          }
        }
      });
    },
    reset() {
      this.list = [];
      this.listQuery = {
        productionOrderID: '',
        productionOrderStatus: '',
        pageSize: 30,
        productionLine: this.productionLine
      };
      this.productionOrder = undefined;
      this.listLoadingDetail = false;
      this.listDetail = [];
      this.listQueryDetail = {
        productionOrderID: '',
        productionLine: this.productionLine
      };
      this.listLoadingDetail = false;
      if (this.listQuery.productionLine) {
      } else {
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        if (this.pLineOptions && this.pLineOptions.length > 0) {
          this.listQuery.productionLine = this.pLineOptions[0].EnumValue;
        }
      }
    },

    handleFilter() {
      this.getPage();
    },
    handleSelectBtnClick() {
      var Rows = this.multiSelections;
      this.productionOrder = Rows;
      if (this.showDetail === true) {
        var productionIDs = Rows.map(x => x.BaseNum);
        var newQuery = {
          productionOrderID: productionIDs,
          productionLine: this.listQuery.productionLine
        };
        this.getDetailList(newQuery);
      }
      this.showDlg = false;
    },
    handleRowClick(row) {
      this.productionOrder = Object.assign({}, row);
      if (this.showDetail === true) {
        // 根据所选生产订单的产出品加载BOM信息
        this.listQueryDetail.productionOrderID = row.BaseNum;
        this.listQueryDetail.productionLine = this.listQuery.productionLine;
        this.getDetailList();
      }
      this.showDlg = false;
    },
    handleMultiSelection(val) {
      this.multiSelections = val;
    },
    handleDetailRowClick(row) {
      if (this.multiSelection) {
        this.productionOrderDetails = Object.assign({}, row);
      }
    },
    handleSelectionChange(val) {
      this.productionOrderDetails = val;
    }
  }
};
</script>
