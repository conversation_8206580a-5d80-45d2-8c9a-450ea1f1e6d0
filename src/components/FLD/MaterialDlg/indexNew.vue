<template>
  <el-dialog ref="dlg" :visible.sync="showDlg" title="物料信息" width="1000px">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        type="text"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物料件号" prop="MATNR" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MAKTX" align="center" show-overflow-tooltip />
      <el-table-column label="基本单位" prop="MEINS" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料组" prop="MATKL" align="center" width="160" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <span slot="footer" class="dialog-footer">
      <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectMaterialBtnClick">{{ $t('Common.select') }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
// import {
//   fetchList,
//   fetchNextPage
// } from '@/api/MD/MD_Item'
import { GetXZSAP_MARC } from '@/api/MM/MM_BarCode'
import Pagination from '@/components/Pagination/index'

export default {
  name: 'MaterialDlg',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      multipleSelection: undefined,
      singleSelection: undefined,
      showCheck: this.isMultiple,
      showDlg: false,
      total: 0
    }
  },

  created() {

  },
  methods: {
    edit() {
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }
      this.handleFilter()
      this.showDlg = true
    },
    getList() {
      this.listLoading = true
      GetXZSAP_MARC(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items
          this.total = response.Data.total
        } else {
          this.showNotify('error', response.Message)
        }
        this.listLoading = false
      })
    },
    reset() {
      this.title = this.$i18n.t('Common.selectMaterial') // 对话框标题
      this.selection = undefined // 选中的物料项,如果多选为数组,如果单选为对象
      this.showCheck = false // 是否多选
      this.list = []
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }
      this.multipleSelection = undefined
      this.singleSelection = undefined
      this.listLoading = false
      this.total = 0
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.listQuery.PageSize = 10
      this.getList()
    },
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.showNotify('warning', '请勿选择多个物料信息');
      } else {
        this.multipleSelection = val
      }
    },
    handleSelectMaterialBtnClick() {
      this.$emit('ok', this.multipleSelection)
      this.showDlg = false
    },
    handleRowClick(row) {
      this.singleSelection = Object.assign({}, row)
    }
  }
}

</script>
