<template>
  <el-dialog ref="dlg" :visible.sync="showDlg" :title="title" width="1000px">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        type="text"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column v-if="true" :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column
        :label="$t('ui.MD.MD_BinLocation.BinLocationCode')"
        prop="BinLocationCode"
        align="center"
        width="120"
      ><template slot-scope="scope"><span>{{ scope.row.BinLocationCode }}</span></template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_BinLocation.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="120"
      ><template slot-scope="scope"><span>{{ scope.row.BinLocationName }}</span></template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_BinLocation.RegionCode')" prop="RegionCode" align="center" width="120">
        <template slot-scope="scope"><span>{{ scope.row.RegionCode }}</span></template> </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_BinLocation.RegionName')" prop="RegionName" align="center" width="120">
        <template slot-scope="scope"><span>{{ scope.row.RegionName }}</span></template> </el-table-column>
      <!-- <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsCode')" prop="WhsCode"  align="center" width="120"><template slot-scope="scope"><span>{{ scope.row.WhsCode }}</span></template> </el-table-column>
        <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsName')" prop="WhsName"  align="center" width="120"><template slot-scope="scope"><span>{{ scope.row.WhsName }}</span></template> </el-table-column> -->
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.Remark }}</span> </template> </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.CUser }}</span> </template> </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.CTime|datetime }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.IsDelete }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.MUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.MTime }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.DUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.DTime }}</span> </template> </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <span slot="footer" class="dialog-footer">
      <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectBtnClick">{{ $t('Common.select') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  fetchPageByRegionCode
} from '@/api/MD/MD_BinLocation'
import Pagination from '@/components/Pagination/index'

export default {
  name: 'BinLocationDlg',
  components: {
    Pagination
  },
  props: {
    isMultiple: {
      type: Boolean,
      default: false
    },
    show: Boolean,
    title: String,
    region: String
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        regionCode: ''
      },
      multipleSelection: undefined,
      singleSelection: undefined,
      showCheck: this.isMultiple,
      showDlg: this.show,
      total: 0
    }
  },
  watch: {
    show: function(newVal, oldVal) {
      this.showDlg = newVal
    },
    showDlg: function(newVal, oldVal) {
      this.$emit('update:show', newVal)
      if (newVal === false) {
        if (this.showCheck) {
          this.selection = this.multipleSelection
        } else {
          this.selection = this.singleSelection
        }
        this.$emit('close', this.selection)
      } else {
        this.reset()
      }
    }
  },
  created() {
    // this.handleFilter()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchPageByRegionCode(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items
          this.total = response.Data.total
        } else {
          this.showNotify('error', response.Message)
        }
        this.listLoading = false
      })
    },
    reset() {
      this.title = this.$i18n.t('Common.selectLocation') // 对话框标题
      this.selection = undefined // 选中的项,如果多选为数组,如果单选为对象
      this.showCheck = this.isMultiple // 是否多选
      this.list = []
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        regionCode: ''
      }
      if (this.region) { this.listQuery.regionCode = this.region }
      this.total = 0
      this.multipleSelection = undefined
      this.singleSelection = undefined
      this.listLoading = false
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.listQuery.PageSize = 10
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectBtnClick() {
      this.showDlg = false
    },
    handleRowClick(row) {
      this.singleSelection = Object.assign({}, row)
    }
  }
}
</script>
