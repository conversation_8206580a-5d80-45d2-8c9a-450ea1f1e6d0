<template>
  <el-dialog ref="dlg" :visible.sync="showDlg" :title="title" width="1000px">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        type="text"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        :v-if="showCheck"
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <!-- <el-table-column :label="$t('ui.MD.Stock.SupplierCode')" prop="SupplierCode"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.SupplierCode}}</span> </template> </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.MD.Stock.BoxBarCode')" prop="BoxBarCode"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BoxBarCode}}</span> </template> </el-table-column> -->
      <el-table-column :label="$t('ui.MD.Stock.BarCode')" prop="BarCode" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.BarCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.ItemCode')" prop="ItemCode" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.ItemCode }}</span> </template> </el-table-column>
      <!-- <el-table-column :label="$t('ui.MD.Stock.ItemName')" prop="ItemName"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItemName}}</span> </template> </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.MD.Stock.ItmsGrpCode')" prop="ItmsGrpCode"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode}}</span> </template> </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.MD.Stock.ItmsGrpName')" prop="ItmsGrpName"  align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName}}</span> </template> </el-table-column> -->
      <el-table-column :label="$t('ui.MD.Stock.BinLocationCode')" prop="BinLocationCode" align="center" width="120">
        <template slot-scope="scope"> <span>{{ scope.row.BinLocationCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.BinLocationName')" prop="BinLocationName" align="center" width="120">
        <template slot-scope="scope"> <span>{{ scope.row.BinLocationName }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.RegionCode')" prop="RegionCode" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.RegionCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.RegionName')" prop="RegionName" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.RegionName }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.BatchNum')" prop="BatchNum" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.BatchNum }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.Qty')" prop="Qty" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.Qty }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.Unit')" prop="Unit" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.Unit }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.PTime')" prop="PTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.PTime|datetime }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.SupplierName')" prop="SupplierName" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.SupplierName }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.MD.Stock.SupplierBatch')" prop="SupplierBatch" align="center" width="120">
        <template slot-scope="scope"> <span>{{ scope.row.SupplierBatch }}</span> </template> </el-table-column>
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.Remark }}</span> </template> </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.CUser }}</span> </template> </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.CTime|datetime }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.IsDelete }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.MUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.MTime }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.DUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120"> <template
        slot-scope="scope"
      > <span>{{ scope.row.DTime }}</span> </template> </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <span slot="footer" class="dialog-footer">
      <el-button @click="showDlg=false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectMaterialBtnClick">{{ $t('Common.select') }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  fetchPage
} from '@/api/MD/MD_Stock'
import Pagination from '@/components/Pagination/index'

export default {
  name: 'StockDlg',
  components: {
    Pagination
  },
  props: {
    isMultiple: {
      type: Boolean,
      default: false
    },
    show: Boolean,
    title: String,
    region: String,
    binLocation: String
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        regionCode: '',
        binLocationCode: ''
      },
      multipleSelection: [],
      singleSelection: {},
      showCheck: this.isMultiple,
      showDlg: this.show,
      selection: undefined,
      total: 0
    }
  },
  watch: {
    show: function(newVal, oldVal) {
      this.showDlg = newVal
    },
    showDlg: function(newVal, oldVal) {
      this.$emit('update:show', newVal)
      if (newVal === false) {
        if (this.showCheck) {
          this.selection = this.multipleSelection
        } else {
          this.selection = this.singleSelection
        }
        this.$emit('close', this.selection)
      } else {
        this.reset()
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items
          this.total = response.Data.total
        } else {
          this.showNotify('error', response.Message)
        }
        this.listLoading = false
      })
    },
    reset() {
      this.title = this.$i18n.t('Common.selectMaterial') // 对话框标题
      this.selection = undefined // 选中的物料项,如果多选为数组,如果单选为对象
      this.list = []
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        regionCode: '',
        binLocationCode: ''
      }
      if (this.region) { this.listQuery.regionCode = this.region }
      if (this.binLocation) { this.listQuery.binLocationCode = this.binLocation }
      this.multipleSelection = []
      this.singleSelection = {}
      this.listLoading = false
      this.total = 0
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.listQuery.PageSize = 10
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectMaterialBtnClick() {
      this.showDlg = false
    },
    handleRowClick(row) {
      this.singleSelection = Object.assign({}, row)
    }
  }
}
</script>
