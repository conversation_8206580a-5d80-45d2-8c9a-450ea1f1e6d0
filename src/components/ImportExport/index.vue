<template>
  <div class="import-export-container">
    <el-button
      v-waves
      class="filter-item"
      type="success"
      icon="el-icon-download"
      size="small"
      @click="handleExport"
    >导出</el-button>
    <el-button
      v-waves
      class="filter-item"
      type="warning"
      icon="el-icon-download"
      size="small"
      @click="handleExportTemplate"
    >导出模板</el-button>
    <el-upload
      class="filter-item"
      :action="'#'"
      :show-file-list="false"
      :before-upload="beforeUpload"
    >
      <el-button v-waves type="primary" icon="el-icon-upload" size="small">导入</el-button>
    </el-upload>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import { exportToExcel } from '@/utils/excel-export'

export default {
  name: 'ImportExport',
  directives: {
    waves
  },
  props: {
    moduleName: {
      type: String,
      required: true
    },
    exportApi: {
      type: Function,
      required: true
    },
    exportTemplateApi: {
      type: Function,
      required: true
    },
    importApi: {
      type: Function,
      required: true
    },
    listQuery: {
      type: Object,
      default: () => ({})
    },
    refreshCallback: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      isProcessing: false
    }
  },
  methods: {
    handleExport() {
      this.$emit('update:isProcessing', true)
      this.exportApi(this.listQuery).then(res => {
        exportToExcel(res, this.moduleName)
        this.$emit('update:isProcessing', false)
      }).catch(() => {
        this.$emit('update:isProcessing', false)
      })
    },
    handleExportTemplate() {
      this.$emit('update:isProcessing', true)
      this.exportTemplateApi().then(res => {
        exportToExcel(res, `${this.moduleName}模板`)
        this.$emit('update:isProcessing', false)
      }).catch(() => {
        this.$emit('update:isProcessing', false)
      })
    },
    beforeUpload(file) {
      this.$emit('update:isProcessing', true)
      const formData = new FormData()
      formData.append('file', file)
      this.importApi(formData).then(response => {
        if (response.Code === 2000) {
          this.$notify({
            title: '成功',
            message: '导入成功',
            type: 'success',
            duration: 2000
          })
          this.refreshCallback()
        } else {
          this.$notify({
            title: '错误',
            message: response.Message,
            type: 'error',
            duration: 2000
          })
        }
        this.$emit('update:isProcessing', false)
      }).catch(() => {
        this.$emit('update:isProcessing', false)
      })
      return false
    }
  }
}
</script>

<style scoped>
.import-export-container {
  display: inline-block;
}
</style>
