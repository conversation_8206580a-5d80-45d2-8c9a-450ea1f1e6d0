/*!
 * vue-infinite-loading v2.4.4
 * (c) 2016-2019 PeachScript
 * MIT License
 */
!(function(t, e) { typeof exports === 'object' && typeof module === 'object' ? module.exports = e() : typeof define === 'function' && define.amd ? define([], e) : typeof exports === 'object' ? exports.VueInfiniteLoading = e() : t.VueInfiniteLoading = e() }(this, function() { return (function(t) { var e = {};function n(i) { if (e[i]) return e[i].exports;var r = e[i] = { i: i, l: !1, exports: {}};return t[i].call(r.exports, r, r.exports, n), r.l = !0, r.exports } return n.m = t, n.c = e, n.d = function(t, e, i) { n.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: i }) }, n.r = function(t) { typeof Symbol !== 'undefined' && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: 'Module' }), Object.defineProperty(t, '__esModule', { value: !0 }) }, n.t = function(t, e) { if (1 & e && (t = n(t)), 8 & e) return t;if (4 & e && typeof t === 'object' && t && t.__esModule) return t;var i = Object.create(null);if (n.r(i), Object.defineProperty(i, 'default', { enumerable: !0, value: t }), 2 & e && typeof t !== 'string') for (var r in t)n.d(i, r, function(e) { return t[e] }.bind(null, r));return i }, n.n = function(t) { var e = t && t.__esModule ? function() { return t.default } : function() { return t };return n.d(e, 'a', e), e }, n.o = function(t, e) { return Object.prototype.hasOwnProperty.call(t, e) }, n.p = '', n(n.s = 9) }([function(t, e, n) { var i = n(6);typeof i === 'string' && (i = [[t.i, i, '']]), i.locals && (t.exports = i.locals);(0, n(3).default)('09280948', i, !0, {}) }, function(t, e, n) { var i = n(8);typeof i === 'string' && (i = [[t.i, i, '']]), i.locals && (t.exports = i.locals);(0, n(3).default)('51e4c3f9', i, !0, {}) }, function(t, e) { t.exports = function(t) { var e = [];return e.toString = function() { return this.map(function(e) { var n = (function(t, e) { var n = t[1] || '';var i = t[3];if (!i) return n;if (e && typeof btoa === 'function') { var r = (o = i, '/*# sourceMappingURL=data:application/json;charset=utf-8;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(o)))) + ' */');var a = i.sources.map(function(t) { return '/*# sourceURL=' + i.sourceRoot + t + ' */' });return [n].concat(a).concat([r]).join('\n') } var o;return [n].join('\n') }(e, t));return e[2] ? '@media ' + e[2] + '{' + n + '}' : n }).join('') }, e.i = function(t, n) { typeof t === 'string' && (t = [[null, t, '']]);for (var i = {}, r = 0;r < this.length;r++) { var a = this[r][0];typeof a === 'number' && (i[a] = !0) } for (r = 0;r < t.length;r++) { var o = t[r];typeof o[0] === 'number' && i[o[0]] || (n && !o[2] ? o[2] = n : n && (o[2] = '(' + o[2] + ') and (' + n + ')'), e.push(o)) } }, e } }, function(t, e, n) { 'use strict';function i(t, e) { for (var n = [], i = {}, r = 0;r < e.length;r++) { var a = e[r];var o = a[0];var s = { id: t + ':' + r, css: a[1], media: a[2], sourceMap: a[3] };i[o] ? i[o].parts.push(s) : n.push(i[o] = { id: o, parts: [s] }) } return n }n.r(e), n.d(e, 'default', function() { return b });var r = typeof document !== 'undefined';if (typeof DEBUG !== 'undefined' && DEBUG && !r) throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a = {};var o = r && (document.head || document.getElementsByTagName('head')[0]);var s = null;var l = 0;var d = !1;var c = function() {};var u = null;var p = 'data-vue-ssr-id';var f = typeof navigator !== 'undefined' && /msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function b(t, e, n, r) { d = n, u = r || {};var o = i(t, e);return h(o), function(e) { for (var n = [], r = 0;r < o.length;r++) { var s = o[r];(l = a[s.id]).refs--, n.push(l) }e ? h(o = i(t, e)) : o = [];for (r = 0;r < n.length;r++) { var l;if ((l = n[r]).refs === 0) { for (var d = 0;d < l.parts.length;d++)l.parts[d]();delete a[l.id] } } } } function h(t) { for (var e = 0;e < t.length;e++) { var n = t[e];var i = a[n.id];if (i) { i.refs++;for (var r = 0;r < i.parts.length;r++)i.parts[r](n.parts[r]);for (;r < n.parts.length;r++)i.parts.push(g(n.parts[r]));i.parts.length > n.parts.length && (i.parts.length = n.parts.length) } else { var o = [];for (r = 0;r < n.parts.length;r++)o.push(g(n.parts[r]));a[n.id] = { id: n.id, refs: 1, parts: o } } } } function m() { var t = document.createElement('style');return t.type = 'text/css', o.appendChild(t), t } function g(t) { var e;var n;var i = document.querySelector('style[' + p + '~="' + t.id + '"]');if (i) { if (d) return c;i.parentNode.removeChild(i) } if (f) { var r = l++;i = s || (s = m()), e = w.bind(null, i, r, !1), n = w.bind(null, i, r, !0) } else i = m(), e = function(t, e) { var n = e.css;var i = e.media;var r = e.sourceMap;i && t.setAttribute('media', i);u.ssrId && t.setAttribute(p, e.id);r && (n += '\n/*# sourceURL=' + r.sources[0] + ' */', n += '\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(r)))) + ' */');if (t.styleSheet)t.styleSheet.cssText = n;else { for (;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n)) } }.bind(null, i), n = function() { i.parentNode.removeChild(i) };return e(t), function(i) { if (i) { if (i.css === t.css && i.media === t.media && i.sourceMap === t.sourceMap) return;e(t = i) } else n() } } var v;var y = (v = [], function(t, e) { return v[t] = e, v.filter(Boolean).join('\n') });function w(t, e, n, i) { var r = n ? '' : i.css;if (t.styleSheet)t.styleSheet.cssText = y(e, r);else { var a = document.createTextNode(r);var o = t.childNodes;o[e] && t.removeChild(o[e]), o.length ? t.insertBefore(a, o[e]) : t.appendChild(a) } } }, function(t, e) { function n(t) { return (n = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? function(t) { return typeof t } : function(t) { return t && typeof Symbol === 'function' && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : typeof t })(t) } function i(e) { return typeof Symbol === 'function' && n(Symbol.iterator) === 'symbol' ? t.exports = i = function(t) { return n(t) } : t.exports = i = function(t) { return t && typeof Symbol === 'function' && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : n(t) }, i(e) }t.exports = i }, function(t, e, n) { 'use strict';n.r(e);var i = n(0);var r = n.n(i);for (var a in i)a !== 'default' && (function(t) { n.d(e, t, function() { return i[t] }) }(a));e.default = r.a }, function(t, e, n) { (t.exports = n(2)(!1)).push([t.i, '.loading-wave-dots[data-v-46b20d22]{position:relative}.loading-wave-dots[data-v-46b20d22] .wave-item{position:absolute;top:50%;left:50%;display:inline-block;margin-top:-4px;width:8px;height:8px;border-radius:50%;-webkit-animation:loading-wave-dots-data-v-46b20d22 linear 2.8s infinite;animation:loading-wave-dots-data-v-46b20d22 linear 2.8s infinite}.loading-wave-dots[data-v-46b20d22] .wave-item:first-child{margin-left:-36px}.loading-wave-dots[data-v-46b20d22] .wave-item:nth-child(2){margin-left:-20px;-webkit-animation-delay:.14s;animation-delay:.14s}.loading-wave-dots[data-v-46b20d22] .wave-item:nth-child(3){margin-left:-4px;-webkit-animation-delay:.28s;animation-delay:.28s}.loading-wave-dots[data-v-46b20d22] .wave-item:nth-child(4){margin-left:12px;-webkit-animation-delay:.42s;animation-delay:.42s}.loading-wave-dots[data-v-46b20d22] .wave-item:last-child{margin-left:28px;-webkit-animation-delay:.56s;animation-delay:.56s}@-webkit-keyframes loading-wave-dots-data-v-46b20d22{0%{-webkit-transform:translateY(0);transform:translateY(0);background:#bbb}10%{-webkit-transform:translateY(-6px);transform:translateY(-6px);background:#999}20%{-webkit-transform:translateY(0);transform:translateY(0);background:#bbb}to{-webkit-transform:translateY(0);transform:translateY(0);background:#bbb}}@keyframes loading-wave-dots-data-v-46b20d22{0%{-webkit-transform:translateY(0);transform:translateY(0);background:#bbb}10%{-webkit-transform:translateY(-6px);transform:translateY(-6px);background:#999}20%{-webkit-transform:translateY(0);transform:translateY(0);background:#bbb}to{-webkit-transform:translateY(0);transform:translateY(0);background:#bbb}}.loading-circles[data-v-46b20d22] .circle-item{width:5px;height:5px;-webkit-animation:loading-circles-data-v-46b20d22 linear .75s infinite;animation:loading-circles-data-v-46b20d22 linear .75s infinite}.loading-circles[data-v-46b20d22] .circle-item:first-child{margin-top:-14.5px;margin-left:-2.5px}.loading-circles[data-v-46b20d22] .circle-item:nth-child(2){margin-top:-11.26px;margin-left:6.26px}.loading-circles[data-v-46b20d22] .circle-item:nth-child(3){margin-top:-2.5px;margin-left:9.5px}.loading-circles[data-v-46b20d22] .circle-item:nth-child(4){margin-top:6.26px;margin-left:6.26px}.loading-circles[data-v-46b20d22] .circle-item:nth-child(5){margin-top:9.5px;margin-left:-2.5px}.loading-circles[data-v-46b20d22] .circle-item:nth-child(6){margin-top:6.26px;margin-left:-11.26px}.loading-circles[data-v-46b20d22] .circle-item:nth-child(7){margin-top:-2.5px;margin-left:-14.5px}.loading-circles[data-v-46b20d22] .circle-item:last-child{margin-top:-11.26px;margin-left:-11.26px}@-webkit-keyframes loading-circles-data-v-46b20d22{0%{background:#dfdfdf}90%{background:#505050}to{background:#dfdfdf}}@keyframes loading-circles-data-v-46b20d22{0%{background:#dfdfdf}90%{background:#505050}to{background:#dfdfdf}}.loading-bubbles[data-v-46b20d22] .bubble-item{background:#666;-webkit-animation:loading-bubbles-data-v-46b20d22 linear .75s infinite;animation:loading-bubbles-data-v-46b20d22 linear .75s infinite}.loading-bubbles[data-v-46b20d22] .bubble-item:first-child{margin-top:-12.5px;margin-left:-.5px}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(2){margin-top:-9.26px;margin-left:8.26px}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(3){margin-top:-.5px;margin-left:11.5px}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(4){margin-top:8.26px;margin-left:8.26px}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(5){margin-top:11.5px;margin-left:-.5px}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(6){margin-top:8.26px;margin-left:-9.26px}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(7){margin-top:-.5px;margin-left:-12.5px}.loading-bubbles[data-v-46b20d22] .bubble-item:last-child{margin-top:-9.26px;margin-left:-9.26px}@-webkit-keyframes loading-bubbles-data-v-46b20d22{0%{width:1px;height:1px;box-shadow:0 0 0 3px #666}90%{width:1px;height:1px;box-shadow:0 0 0 0 #666}to{width:1px;height:1px;box-shadow:0 0 0 3px #666}}@keyframes loading-bubbles-data-v-46b20d22{0%{width:1px;height:1px;box-shadow:0 0 0 3px #666}90%{width:1px;height:1px;box-shadow:0 0 0 0 #666}to{width:1px;height:1px;box-shadow:0 0 0 3px #666}}.loading-default[data-v-46b20d22]{position:relative;border:1px solid #999;-webkit-animation:loading-rotating-data-v-46b20d22 ease 1.5s infinite;animation:loading-rotating-data-v-46b20d22 ease 1.5s infinite}.loading-default[data-v-46b20d22]:before{content:"";position:absolute;display:block;top:0;left:50%;margin-top:-3px;margin-left:-3px;width:6px;height:6px;background-color:#999;border-radius:50%}.loading-spiral[data-v-46b20d22]{border:2px solid #777;border-right-color:transparent;-webkit-animation:loading-rotating-data-v-46b20d22 linear .85s infinite;animation:loading-rotating-data-v-46b20d22 linear .85s infinite}@-webkit-keyframes loading-rotating-data-v-46b20d22{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-rotating-data-v-46b20d22{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-bubbles[data-v-46b20d22],.loading-circles[data-v-46b20d22]{position:relative}.loading-bubbles[data-v-46b20d22] .bubble-item,.loading-circles[data-v-46b20d22] .circle-item{position:absolute;top:50%;left:50%;display:inline-block;border-radius:50%}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(2),.loading-circles[data-v-46b20d22] .circle-item:nth-child(2){-webkit-animation-delay:93ms;animation-delay:93ms}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(3),.loading-circles[data-v-46b20d22] .circle-item:nth-child(3){-webkit-animation-delay:.186s;animation-delay:.186s}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(4),.loading-circles[data-v-46b20d22] .circle-item:nth-child(4){-webkit-animation-delay:.279s;animation-delay:.279s}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(5),.loading-circles[data-v-46b20d22] .circle-item:nth-child(5){-webkit-animation-delay:.372s;animation-delay:.372s}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(6),.loading-circles[data-v-46b20d22] .circle-item:nth-child(6){-webkit-animation-delay:.465s;animation-delay:.465s}.loading-bubbles[data-v-46b20d22] .bubble-item:nth-child(7),.loading-circles[data-v-46b20d22] .circle-item:nth-child(7){-webkit-animation-delay:.558s;animation-delay:.558s}.loading-bubbles[data-v-46b20d22] .bubble-item:last-child,.loading-circles[data-v-46b20d22] .circle-item:last-child{-webkit-animation-delay:.651s;animation-delay:.651s}', '']) }, function(t, e, n) { 'use strict';n.r(e);var i = n(1);var r = n.n(i);for (var a in i)a !== 'default' && (function(t) { n.d(e, t, function() { return i[t] }) }(a));e.default = r.a }, function(t, e, n) { (t.exports = n(2)(!1)).push([t.i, '.infinite-loading-container[data-v-46b21138]{clear:both;text-align:center}.infinite-loading-container[data-v-46b21138] [class^=loading-]{display:inline-block;margin:5px 0;width:28px;height:28px;font-size:28px;line-height:28px;border-radius:50%}.btn-try-infinite[data-v-46b21138]{margin-top:5px;padding:5px 10px;color:#999;font-size:14px;line-height:1;background:transparent;border:1px solid #ccc;border-radius:3px;outline:none;cursor:pointer}.btn-try-infinite[data-v-46b21138]:not(:active):hover{opacity:.8}', '']) }, function(t, e, n) { 'use strict';n.r(e);var i = { throttleLimit: 50, loopCheckTimeout: 1e3, loopCheckMaxCalls: 10 };var r = (function() { var t = !1;try { var e = Object.defineProperty({}, 'passive', { get: function() { return t = { passive: !0 }, !0 } });window.addEventListener('testpassive', e, e), window.remove('testpassive', e, e) } catch (t) {} return t }());var a = { STATE_CHANGER: ['emit `loaded` and `complete` event through component instance of `$refs` may cause error, so it will be deprecated soon, please use the `$state` argument instead (`$state` just the special `$event` variable):', '\ntemplate:', '<infinite-loading @infinite="infiniteHandler"></infinite-loading>', "\nscript:\n...\ninfiniteHandler($state) {\n  ajax('https://www.example.com/api/news')\n    .then((res) => {\n      if (res.data.length) {\n        $state.loaded();\n      } else {\n        $state.complete();\n      }\n    });\n}\n...", '', 'more details: https://github.com/PeachScript/vue-infinite-loading/issues/57#issuecomment-324370549'].join('\n'), INFINITE_EVENT: '`:on-infinite` property will be deprecated soon, please use `@infinite` event instead.', IDENTIFIER: 'the `reset` event will be deprecated soon, please reset this component by change the `identifier` property.' };var o = { INFINITE_LOOP: ['executed the callback function more than '.concat(i.loopCheckMaxCalls, ' times for a short time, it looks like searched a wrong scroll wrapper that doest not has fixed height or maximum height, please check it. If you want to force to set a element as scroll wrapper ranther than automatic searching, you can do this:'), '\n\x3c!-- add a special attribute for the real scroll wrapper --\x3e\n<div infinite-wrapper>\n  ...\n  \x3c!-- set force-use-infinite-wrapper --\x3e\n  <infinite-loading force-use-infinite-wrapper></infinite-loading>\n</div>\nor\n<div class="infinite-wrapper">\n  ...\n  \x3c!-- set force-use-infinite-wrapper as css selector of the real scroll wrapper --\x3e\n  <infinite-loading force-use-infinite-wrapper=".infinite-wrapper"></infinite-loading>\n</div>\n    ', 'more details: https://github.com/PeachScript/vue-infinite-loading/issues/55#issuecomment-316934169'].join('\n') };var s = { READY: 0, LOADING: 1, COMPLETE: 2, ERROR: 3 };var l = { color: '#666', fontSize: '14px', padding: '10px 0' };var d = { mode: 'development', props: { spinner: 'default', distance: 100, forceUseInfiniteWrapper: !1 }, system: i, slots: { noResults: 'No results :(', noMore: 'No more data :)', error: 'Opps, something went wrong :(', errorBtnText: 'Retry', spinner: '' }, WARNINGS: a, ERRORS: o, STATUS: s };var c = n(4);var u = n.n(c);var p = { BUBBLES: { render: function(t) { return t('span', { attrs: { class: 'loading-bubbles' }}, Array.apply(Array, Array(8)).map(function() { return t('span', { attrs: { class: 'bubble-item' }}) })) } }, CIRCLES: { render: function(t) { return t('span', { attrs: { class: 'loading-circles' }}, Array.apply(Array, Array(8)).map(function() { return t('span', { attrs: { class: 'circle-item' }}) })) } }, DEFAULT: { render: function(t) { return t('i', { attrs: { class: 'loading-default' }}) } }, SPIRAL: { render: function(t) { return t('i', { attrs: { class: 'loading-spiral' }}) } }, WAVEDOTS: { render: function(t) { return t('span', { attrs: { class: 'loading-wave-dots' }}, Array.apply(Array, Array(5)).map(function() { return t('span', { attrs: { class: 'wave-item' }}) })) } }};function f(t, e, n, i, r, a, o, s) { var l;var d = typeof t === 'function' ? t.options : t;if (e && (d.render = e, d.staticRenderFns = n, d._compiled = !0), i && (d.functional = !0), a && (d._scopeId = 'data-v-' + a), o ? (l = function(t) { (t = t || this.$vnode && this.$vnode.ssrContext || this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) || typeof __VUE_SSR_CONTEXT__ === 'undefined' || (t = __VUE_SSR_CONTEXT__), r && r.call(this, t), t && t._registeredComponents && t._registeredComponents.add(o) }, d._ssrRegister = l) : r && (l = s ? function() { r.call(this, this.$root.$options.shadowRoot) } : r), l) if (d.functional) { d._injectStyles = l;var c = d.render;d.render = function(t, e) { return l.call(e), c(t, e) } } else { var u = d.beforeCreate;d.beforeCreate = u ? [].concat(u, l) : [l] } return { exports: t, options: d } } var b = f({ name: 'Spinner', computed: { spinnerView: function() { return p[(this.$attrs.spinner || '').toUpperCase()] || this.spinnerInConfig }, spinnerInConfig: function() { return d.slots.spinner && typeof d.slots.spinner === 'string' ? { render: function() { return this._v(d.slots.spinner) } } : u()(d.slots.spinner) === 'object' ? d.slots.spinner : p[d.props.spinner.toUpperCase()] || p.DEFAULT } }}, function() { var t = this.$createElement;return (this._self._c || t)(this.spinnerView, { tag: 'component' }) }, [], !1, function(t) { var e = n(5);e.__inject__ && e.__inject__(t) }, '46b20d22', null);b.options.__file = 'Spinner.vue';var h = b.exports;function m(t) { d.mode !== 'production' && console.warn('[Vue-infinite-loading warn]: '.concat(t)) } function g(t) { console.error('[Vue-infinite-loading error]: '.concat(t)) } var v = { timers: [], caches: [], throttle: function(t) { var e = this;this.caches.indexOf(t) === -1 && (this.caches.push(t), this.timers.push(setTimeout(function() { t(), e.caches.splice(e.caches.indexOf(t), 1), e.timers.shift() }, d.system.throttleLimit))) }, reset: function() { this.timers.forEach(function(t) { clearTimeout(t) }), this.timers.length = 0, this.caches = [] } };var y = { isChecked: !1, timer: null, times: 0, track: function() { var t = this;this.times += 1, clearTimeout(this.timer), this.timer = setTimeout(function() { t.isChecked = !0 }, d.system.loopCheckTimeout), this.times > d.system.loopCheckMaxCalls && (g(o.INFINITE_LOOP), this.isChecked = !0) } };var w = { key: '_infiniteScrollHeight', getScrollElm: function(t) { return t === window ? document.documentElement : t }, save: function(t) { var e = this.getScrollElm(t);e[this.key] = e.scrollHeight }, restore: function(t) { var e = this.getScrollElm(t);typeof e[this.key] === 'number' && (e.scrollTop = e.scrollHeight - e[this.key] + e.scrollTop), this.remove(e) }, remove: function(t) { void 0 !== t[this.key] && delete t[this.key] } };function x(t) { return t.replace(/[A-Z]/g, function(t) { return '-'.concat(t.toLowerCase()) }) } function k(t) { return t.offsetWidth + t.offsetHeight > 0 } var S = f({ name: 'InfiniteLoading', data: function() { return { scrollParent: null, scrollHandler: null, isFirstLoad: !0, status: s.READY, slots: d.slots } }, components: { Spinner: h }, computed: { isShowSpinner: function() { return this.status === s.LOADING }, isShowError: function() { return this.status === s.ERROR }, isShowNoResults: function() { return this.status === s.COMPLETE && this.isFirstLoad }, isShowNoMore: function() { return this.status === s.COMPLETE && !this.isFirstLoad }, slotStyles: function() { var t = this;var e = {};return Object.keys(d.slots).forEach(function(n) { var i = x(n);(!t.$slots[i] && !d.slots[n].render || t.$slots[i] && !t.$slots[i][0].tag) && (e[n] = l) }), e } }, props: { distance: { type: Number, default: d.props.distance }, spinner: String, direction: { type: String, default: 'bottom' }, forceUseInfiniteWrapper: { type: [Boolean, String], default: d.props.forceUseInfiniteWrapper }, identifier: { default: +new Date() }, onInfinite: Function }, watch: { identifier: function() { this.stateChanger.reset() } }, mounted: function() { var t = this;this.$watch('forceUseInfiniteWrapper', function() { t.scrollParent = t.getScrollParent() }, { immediate: !0 }), this.scrollHandler = function(e) { t.status === s.READY && (e && e.constructor === Event && k(t.$el) ? v.throttle(t.attemptLoad) : t.attemptLoad()) }, setTimeout(function() { t.scrollHandler(), t.scrollParent.addEventListener('scroll', t.scrollHandler, r) }, 1), this.$on('$InfiniteLoading:loaded', function(e) { t.isFirstLoad = !1, t.direction === 'top' && t.$nextTick(function() { w.restore(t.scrollParent) }), t.status === s.LOADING && t.$nextTick(t.attemptLoad.bind(null, !0)), e && e.target === t || m(a.STATE_CHANGER) }), this.$on('$InfiniteLoading:complete', function(e) { t.status = s.COMPLETE, t.$nextTick(function() { t.$forceUpdate() }), t.scrollParent.removeEventListener('scroll', t.scrollHandler, r), e && e.target === t || m(a.STATE_CHANGER) }), this.$on('$InfiniteLoading:reset', function(e) { t.status = s.READY, t.isFirstLoad = !0, w.remove(t.scrollParent), t.scrollParent.addEventListener('scroll', t.scrollHandler, r), setTimeout(function() { v.reset(), t.scrollHandler() }, 1), e && e.target === t || m(a.IDENTIFIER) }), this.stateChanger = { loaded: function() { t.$emit('$InfiniteLoading:loaded', { target: t }) }, complete: function() { t.$emit('$InfiniteLoading:complete', { target: t }) }, reset: function() { t.$emit('$InfiniteLoading:reset', { target: t }) }, error: function() { t.status = s.ERROR, v.reset() } }, this.onInfinite && m(a.INFINITE_EVENT) }, deactivated: function() { this.status === s.LOADING && (this.status = s.READY), this.scrollParent.removeEventListener('scroll', this.scrollHandler, r) }, activated: function() { this.scrollParent.addEventListener('scroll', this.scrollHandler, r) }, methods: { attemptLoad: function(t) { var e = this;this.status !== s.COMPLETE && k(this.$el) && this.getCurrentDistance() <= this.distance ? (this.status = s.LOADING, this.direction === 'top' && this.$nextTick(function() { w.save(e.scrollParent) }), typeof this.onInfinite === 'function' ? this.onInfinite.call(null, this.stateChanger) : this.$emit('infinite', this.stateChanger), !t || this.forceUseInfiniteWrapper || y.isChecked || y.track()) : this.status === s.LOADING && (this.status = s.READY) }, getCurrentDistance: function() { var t;this.direction === 'top' ? t = typeof this.scrollParent.scrollTop === 'number' ? this.scrollParent.scrollTop : this.scrollParent.pageYOffset : t = this.$el.getBoundingClientRect().top - (this.scrollParent === window ? window.innerHeight : this.scrollParent.getBoundingClientRect().bottom);return t }, getScrollParent: function() { var t;var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.$el;return typeof this.forceUseInfiniteWrapper === 'string' && (t = e.querySelector(this.forceUseInfiniteWrapper)), t || (e.tagName === 'BODY' ? t = window : !this.forceUseInfiniteWrapper && ['scroll', 'auto'].indexOf(getComputedStyle(e).overflowY) > -1 ? t = e : (e.hasAttribute('infinite-wrapper') || e.hasAttribute('data-infinite-wrapper')) && (t = e)), t || this.getScrollParent(e.parentNode) } }, destroyed: function() { !this.status !== s.COMPLETE && (v.reset(), w.remove(this.scrollParent), this.scrollParent.removeEventListener('scroll', this.scrollHandler, r)) } }, function() { var t = this;var e = t.$createElement;var n = t._self._c || e;return n('div', { staticClass: 'infinite-loading-container' }, [n('div', { directives: [{ name: 'show', rawName: 'v-show', value: t.isShowSpinner, expression: 'isShowSpinner' }], staticClass: 'infinite-status-prompt', style: t.slotStyles.spinner }, [t._t('spinner', [n('spinner', { attrs: { spinner: t.spinner }})])], 2), t._v(' '), n('div', { directives: [{ name: 'show', rawName: 'v-show', value: t.isShowNoResults, expression: 'isShowNoResults' }], staticClass: 'infinite-status-prompt', style: t.slotStyles.noResults }, [t._t('no-results', [t.slots.noResults.render ? n(t.slots.noResults, { tag: 'component' }) : [t._v(t._s(t.slots.noResults))]])], 2), t._v(' '), n('div', { directives: [{ name: 'show', rawName: 'v-show', value: t.isShowNoMore, expression: 'isShowNoMore' }], staticClass: 'infinite-status-prompt', style: t.slotStyles.noMore }, [t._t('no-more', [t.slots.noMore.render ? n(t.slots.noMore, { tag: 'component' }) : [t._v(t._s(t.slots.noMore))]])], 2), t._v(' '), n('div', { directives: [{ name: 'show', rawName: 'v-show', value: t.isShowError, expression: 'isShowError' }], staticClass: 'infinite-status-prompt', style: t.slotStyles.error }, [t._t('error', [t.slots.error.render ? n(t.slots.error, { tag: 'component', attrs: { trigger: t.attemptLoad }}) : [t._v('\n        ' + t._s(t.slots.error) + '\n        '), n('br'), t._v(' '), n('button', { staticClass: 'btn-try-infinite', domProps: { textContent: t._s(t.slots.errorBtnText) }, on: { click: t.attemptLoad }})]], { trigger: t.attemptLoad })], 2)]) }, [], !1, function(t) { var e = n(7);e.__inject__ && e.__inject__(t) }, '46b21138', null);S.options.__file = 'InfiniteLoading.vue';var E = S.exports;function _(t) { d.mode = t.config.productionTip ? 'development' : 'production' }Object.defineProperty(E, 'install', { configurable: !1, enumerable: !1, value: function(t, e) { Object.assign(d.props, e && e.props), Object.assign(d.slots, e && e.slots), Object.assign(d.system, e && e.system), t.component('infinite-loading', E), _(t) } }), typeof window !== 'undefined' && window.Vue && (window.Vue.component('infinite-loading', E), _(window.Vue));e.default = E }])) }));
