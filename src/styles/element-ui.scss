// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// 统一化el-dialog弹出框的样式包括header，footer，button，close button等
.el-dialog__header{
  background-color:#2477AD;   // #85C1E9; rgb(36, 119, 173)
  border-bottom: #FDFEFE 0.1px solid;
  border-bottom-width: 95%;
  // line-height: 30px;
}

.el-dialog__footer{
  background-color:#F8F8F8;
  // border-top: #BEBEBE 1px solid
}

.el-dialog__headerbtn .el-dialog__close{
  color: #F8F8F8
}

.el-dialog__headerbtn .el-dialog__close :hover{
  color: #ffffff
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}
