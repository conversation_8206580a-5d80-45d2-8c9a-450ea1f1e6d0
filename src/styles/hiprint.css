/* hiprint 编辑器增强样式 */
.hiprint-printTemplate {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  position: relative;
  overflow: visible;
}

.hiprint-printTemplate .hiprint-printPanel {
  background: white;
  margin: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  border: 1px solid #e0e0e0;
  min-height: 400px;
  min-width: 300px;
}

/* 确保空白面板也能显示 */
.hiprint-printTemplate .hiprint-printPanel:empty::before {
  content: '拖拽组件到此处开始设计';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
  pointer-events: none;
  z-index: 1;
}

.hiprint-printTemplate .hiprint-printElement {
  position: absolute;
  border: 1px dashed #ccc;
  cursor: move;
  user-select: none;
  transition: all 0.2s ease;
}

.hiprint-printTemplate .hiprint-printElement:hover {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
}

.hiprint-printTemplate .hiprint-printElement.hiprint-printElement-selected {
  border-color: #409eff;
  border-style: solid;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.4);
  z-index: 1000;
}

.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-text {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  overflow: hidden;
}

.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-barcode,
.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-qrcode {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #666;
  font-size: 10px;
}

.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-table {
  width: 100%;
  height: 100%;
  border-collapse: collapse;
}

.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-table td,
.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-table th {
  border: 1px solid #ddd;
  padding: 4px;
  font-size: 10px;
  text-align: center;
}

.hiprint-printTemplate .hiprint-printElement .hiprint-printElement-line {
  background: #333;
}

/* 拖拽辅助线 */
.hiprint-printTemplate .hiprint-auxiliary-line {
  position: absolute;
  background: #409eff;
  z-index: 1000;
}

.hiprint-printTemplate .hiprint-auxiliary-line-horizontal {
  height: 1px;
  width: 100%;
}

.hiprint-printTemplate .hiprint-auxiliary-line-vertical {
  width: 1px;
  height: 100%;
}

/* 元素控制点 */
.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 1px solid white;
  border-radius: 50%;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-nw {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-ne {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-sw {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-se {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-n {
  top: -4px;
  left: 50%;
  margin-left: -4px;
  cursor: n-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-s {
  bottom: -4px;
  left: 50%;
  margin-left: -4px;
  cursor: s-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-w {
  top: 50%;
  left: -4px;
  margin-top: -4px;
  cursor: w-resize;
}

.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle-e {
  top: 50%;
  right: -4px;
  margin-top: -4px;
  cursor: e-resize;
}

/* 右键菜单 */
.hiprint-context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  min-width: 120px;
}

.hiprint-context-menu .menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 12px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.hiprint-context-menu .menu-item:hover {
  background: #f5f5f5;
}

.hiprint-context-menu .menu-item:last-child {
  border-bottom: none;
}

.hiprint-context-menu .menu-item.disabled {
  color: #ccc;
  cursor: not-allowed;
}

.hiprint-context-menu .menu-item.disabled:hover {
  background: transparent;
}

/* 标尺 */
.hiprint-ruler {
  background: #f8f9fa;
  border: 1px solid #e6e6e6;
  font-size: 10px;
  color: #666;
}

.hiprint-ruler-horizontal {
  height: 20px;
  width: 100%;
  position: relative;
}

.hiprint-ruler-vertical {
  width: 20px;
  height: 100%;
  position: relative;
}

/* 网格 */
.hiprint-grid {
  background-image:
    linear-gradient(to right, #e6e6e6 1px, transparent 1px),
    linear-gradient(to bottom, #e6e6e6 1px, transparent 1px);
  background-size: 10px 10px;
}

/* 打印预览样式 */
.hiprint-preview {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
}

.hiprint-preview .hiprint-printElement {
  border: none !important;
  box-shadow: none !important;
}

/* 网格背景增强 */
.hiprint-printTemplate.show-grid {
  background-image:
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
  background-size: 10px 10px;
  background-position: 40px 40px;
}

/* 标尺样式增强 */
.hiprint-ruler {
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  font-size: 10px;
  color: #6c757d;
  user-select: none;
}

.hiprint-ruler-horizontal {
  height: 20px;
  width: 100%;
  position: relative;
  border-bottom: 1px solid #dee2e6;
}

.hiprint-ruler-vertical {
  width: 20px;
  height: 100%;
  position: relative;
  border-right: 1px solid #dee2e6;
}

/* 辅助线样式 */
.hiprint-auxiliary-line {
  position: absolute;
  background: #409eff;
  z-index: 1001;
  pointer-events: none;
}

.hiprint-auxiliary-line-horizontal {
  height: 1px;
  width: 100%;
}

.hiprint-auxiliary-line-vertical {
  width: 1px;
  height: 100%;
}

/* 选择框样式 */
.hiprint-selection-box {
  position: absolute;
  border: 1px dashed #409eff;
  background: rgba(64, 158, 255, 0.1);
  pointer-events: none;
  z-index: 999;
}

/* 元素控制点增强 */
.hiprint-printTemplate .hiprint-printElement-selected .hiprint-resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 右键菜单增强 */
.hiprint-context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  min-width: 140px;
  padding: 4px 0;
}

.hiprint-context-menu .menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 12px;
  color: #333;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.hiprint-context-menu .menu-item:hover {
  background: #f5f5f5;
}

.hiprint-context-menu .menu-item i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.hiprint-context-menu .menu-divider {
  height: 1px;
  background: #e0e0e0;
  margin: 4px 0;
}

/* 工具提示样式 */
.hiprint-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  z-index: 1002;
  pointer-events: none;
  white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hiprint-printTemplate {
    margin: 10px;
  }

  .hiprint-printTemplate .hiprint-printPanel {
    margin: 20px;
  }

  .hiprint-printTemplate .hiprint-printElement {
    min-width: 20px;
    min-height: 20px;
  }

  .hiprint-resize-handle {
    width: 12px !important;
    height: 12px !important;
  }
}

/* 打印预览样式 */
.hiprint-preview {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
}

.hiprint-preview .hiprint-printElement {
  border: none !important;
  box-shadow: none !important;
}

/* 动画效果 */
@keyframes hiprint-element-add {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.hiprint-printElement.hiprint-element-new {
  animation: hiprint-element-add 0.3s ease-out;
}

/* 加载状态 */
.hiprint-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
}

.hiprint-loading::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top-color: #409eff;
  border-radius: 50%;
  animation: hiprint-spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes hiprint-spin {
  to {
    transform: rotate(360deg);
  }
}
