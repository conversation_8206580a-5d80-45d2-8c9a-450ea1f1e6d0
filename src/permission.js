import router from './router'
import store from './store'
import {
  Message
} from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import {
  getToken
} from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({
  showSpinner: false
}) // NProgress配置

const whiteList = ['/login', '/auth-redirect'] // 可以直接访问页面白名单

router.beforeEach(async(to, from, next) => {
  // 启动progress bar
  NProgress.start()

  // 设置页面标题
  document.title = getPageTitle(to.meta.title)

  // 确认用户是否已经登录（cookie，store在F5刷新时会重置，导致F5后全部重新导航到登录页）
  const hasToken = getToken()
  const hasTokenName = sessionStorage.getItem('name')
  // console.log('hasToken',hasToken)

  if (hasToken) {
    if (to.path === '/login') {
      // 如果已经登录过，直接重新导航到首页（DashBoards）
      next({
        path: '/'
      })
      NProgress.done()
    } else {
      // 判断是否已经获取过权限
      // console.log('user-routes-permission.js', store.getters.permission_routes)
      const hasPermission = store.getters.permission_routes && store.getters.permission_routes.length > 0
      console.log('hasPermission', hasPermission)
      if (hasPermission) { // 页面跳墙处理，防止死循环
        next()
      } else {
        // store中未记录权限，则重新获取
        if (hasTokenName) {
          try {
            // generate accessible routes map based on roles
            const accessRoutes = await store.dispatch('permission/generateRoutes')
            router.addRoutes(accessRoutes)
            // hack method to ensure that addRoutes is complete
            // set the replace: true, so the navigation will not leave a history record
            // next({ ...to, replace: true })
            next({
              ...to,
              replace: true
            }) // 本身会再次进入路由钩子（beforeEach），考虑跳墙处理，否则会死循环
          } catch (error) {
            // 移除Token，并导航到Login页面以重新登录
            await store.dispatch('user/resetToken')
            Message.error(error || 'Has Error')
            // next(`/login?redirect=${to.path}`)
            next('/login')
            NProgress.done()
          }
        } else {
          await store.dispatch('user/resetToken')
          Message.error('登录失效，请重新登录')
          // next(`/login?redirect=${to.path}`)
          next('/login')
          NProgress.done()
        }
      }
    }
  } else {
    // 还未获取Token
    if (whiteList.indexOf(to.path) !== -1) {
      // 重定向白名单的路由，直接跳转
      next()
    } else {
      // 需要鉴权页面则重新导航到登录页面()
      // next(`/login?redirect=${to.path}`)
      next('/login')
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
