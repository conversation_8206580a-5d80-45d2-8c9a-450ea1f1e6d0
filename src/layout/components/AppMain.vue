<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import { fetchUnreadMessageList, readMessage } from '../../api/Sys/Sys_UserMessage'

export default {
  name: 'AppMain',
  data() {
    return {
      UnreadMessageList: []
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  created() {
    // var UnreadMessageList = []
    this.getMessage()
  },
  methods: {
    getMessage() {
      let messagesID
      if (this.UnreadMessageList) {
        messagesID = this.UnreadMessageList.map(v => v.UserMessageID)
      } else {
        messagesID = []
      }
      fetchUnreadMessageList({ unReadList: messagesID }).then(res => {
        const tempList = this.removeDuplicate(this.UnreadMessageList, res.Data)
        if (tempList) {
          tempList.forEach(item => {
            const notify = this.$notify({
              position: 'bottom-right',
              title: item.MessageTitle,
              message: item.MessageContent,
              customClass: item.UserMessageID, // 这里写入查回来的UserMessageID
              duration: 0,
              onClose: () => {
                this.readMessage(notify.customClass)
              }
            })
          })
        }
        setTimeout(() => {
          this.getMessage()
        }, 2000000)
      })
    },
    // 对象数组去重，若现有UnreadMessageList和查询回来的res.Data一模一样，则返回false，页面内notify不弹出消息
    // 若查询的数据有新消息，则把新条目添加进UnreadMessageList并返回新条目，然后notify弹出新的消息
    removeDuplicate(resourceArray, aimArray) {
      const newMessagesID = aimArray.map(v => v.UserMessageID)
      for (let i = 0;i < newMessagesID.length;i++) {
        const obj = resourceArray.find(v => v.UserMessageID === newMessagesID[i])
        if (obj) {
          aimArray.splice(obj, 1)
        }
      }
      for (let i = 0;i < aimArray.length;i++) {
        this.UnreadMessageList.push(aimArray[i])
      }
      return aimArray
    },
    // 该方法用于关闭消息通知表示已阅时，将关闭的这条通知从UnreadMessageList中移除
    readMessage(id) {
      readMessage({ userMessageID: id }).then(() => {
      })
      const obj = this.UnreadMessageList.find(v => v.UserMessageID === id)
      const index = this.UnreadMessageList.indexOf(obj)
      this.UnreadMessageList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
  height: 100vh;
  overflow: auto;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
