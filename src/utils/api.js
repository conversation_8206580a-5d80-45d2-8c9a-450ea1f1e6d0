let BaseURL, ProURL
if (window.location.host.indexOf('9527') > 0) {
  BaseURL = 'http://localhost:9527/'
  ProURL = 'http://localhost:9527'
} else if (window.location.host.indexOf('86') > 0) {
  BaseURL = 'http://**********:8001/'
  ProURL = 'http://**********:8001'
} else if (window.location.host.indexOf('87') > 0) {
  BaseURL = 'http://**********:8001/'
  ProURL = 'http://**********:8001'
} else if (window.location.host.indexOf('94') > 0) {
  BaseURL = 'http://**********:8001/'
  ProURL = 'http://**********:8001'
}
console.log('BaseURL:' + BaseURL)
export default {
  BaseURL,
  ProURL
}
