import { fetchList as fetchLabelTemplateList } from '../api/MD/MD_LabelTemplate'

// 格式化模板显示
export async function formatLabelTemplate(row, column, currentValue) {
  let findObj = null
  let labelTemplateList = []
  const res = await fetchLabelTemplateList()
  labelTemplateList = res.Data

  findObj = labelTemplateList.find(element => {
    return element.TempleteID === currentValue
  })
  console.log('formatLabelTemplate', findObj)
  return findObj != null ? findObj.TempleteDesc : ''
}
