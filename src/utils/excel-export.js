import XLSX from 'xlsx'

/**
 * 解析Excel中的日期值，修复时区问题
 * @param {string|number} value - Excel单元格的值
 * @returns {Date|null} - 解析后的日期对象，如果不是日期则返回null
 */
function parseExcelDate(value) {
  if (!value) return null;

  // 如果是数字，可能是Excel的日期序列号
  if (typeof value === 'number') {
    // Excel日期序列号转换
    if (value >= 1 && value < 2958466) { // 合理的Excel日期范围
      // 使用XLSX库的内置日期转换
      try {
        // XLSX.SSF.parse_date_code 可以正确转换Excel序列号
        const excelDate = XLSX.SSF.parse_date_code(value);
        if (excelDate && excelDate.y && excelDate.m && excelDate.d) {
          // 创建UTC日期，但使用Excel显示的日期值
          // 这样可以确保日期与Excel显示的完全一致
          const utcDate = new Date(Date.UTC(excelDate.y, excelDate.m - 1, excelDate.d));
          return utcDate;
        }
      } catch (error) {
        console.warn('Excel日期序列号转换失败:', error);
      }

      // 如果XLSX转换失败，使用备用算法
      // Excel基准：1900年1月1日 = 1，但需要处理1900年闰年bug
      let days = Math.floor(value);
      if (days >= 60) {
        days = days - 1; // 修正Excel的1900年2月29日bug
      }

      // 从1899年12月31日开始计算，使用UTC时间
      const baseDate = new Date(Date.UTC(1899, 11, 31)); // 1899年12月31日 UTC
      const targetDate = new Date(baseDate.getTime() + (days - 1) * 24 * 60 * 60 * 1000);

      return targetDate;
    }
  }

  // 如果是字符串，尝试解析常见的日期格式
  if (typeof value === 'string') {
    const trimmedValue = value.trim();

    // YYYY-MM-DD 格式
    if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(trimmedValue)) {
      const [year, month, day] = trimmedValue.split('-').map(Number);
      // 使用UTC时间创建日期，确保与Excel显示一致
      return new Date(Date.UTC(year, month - 1, day));
    }

    // YYYY/MM/DD 格式
    if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(trimmedValue)) {
      const [year, month, day] = trimmedValue.split('/').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
    }

    // MM/DD/YYYY 格式
    if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(trimmedValue)) {
      const [month, day, year] = trimmedValue.split('/').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
    }

    // MM-DD-YYYY 格式
    if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(trimmedValue)) {
      const [month, day, year] = trimmedValue.split('-').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
    }

    // YYYY-MM-DD HH:MM:SS 格式
    if (/^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(trimmedValue)) {
      // 对于包含时间的日期，使用原生解析
      const parsedDate = new Date(trimmedValue);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate;
      }
    }
  }

  return null;
}

// 导出Excel文件
export function exportToExcel(response, defaultFilename) {
  // 处理响应数据
  let data = response
  let filename = defaultFilename

  // 如果响应是一个对象，包含 data 和 fileName
  if (response && response.data && response.fileName) {
    data = response.data
    filename = response.fileName
  }

  // 确保文件名有正确的扩展名
  if (!filename.endsWith('.xlsx')) {
    filename = filename + '.xlsx'
  }

  const blob = new Blob([data], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })

  // 针对于IE浏览器的处理, 因部分IE浏览器不支持createObjectURL
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(blob, filename)
  } else {
    const downloadElement = document.createElement('a')
    const href = window.URL.createObjectURL(blob) // 创建下载的链接
    downloadElement.href = href
    downloadElement.download = filename // 下载后文件名
    document.body.appendChild(downloadElement)
    downloadElement.click() // 点击下载
    document.body.removeChild(downloadElement) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blob对象
  }
}

// 导入
export function importExcel(_this, SuccessCallBackData) {
  const inputDOM = _this.$refs.inputer;
  // 通过DOM取文件数据
  _this.file = event.currentTarget.files[0];
  var rABS = false; // 是否将文件读取为二进制字符串
  var f = _this.file;
  var reader = new FileReader();
  // if (!FileReader.prototype.readAsBinaryString) {
  FileReader.prototype.readAsBinaryString = function(f) {
    var binary = '';
    var rABS = false; // 是否将文件读取为二进制字符串
    var pt = _this;
    var wb; // 读取完成的数据
    var outdata;
    var reader = new FileReader();
    reader.onload = function(e) {
      var bytes = new Uint8Array(reader.result);
      var length = bytes.byteLength;
      for (var i = 0;i < length;i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      var XLSX = require('xlsx');
      if (rABS) {
        // eslint-disable-next-line no-undef
        wb = XLSX.read(btoa(fixdata(binary)), {
          // 手动转化
          type: 'base64'
        });
      } else {
        wb = XLSX.read(binary, {
          type: 'binary',
          cellDates: false // 禁用自动日期解析，避免时区问题
        });
      }
      console.log(wb)
      // outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]); // 生成json表格内容
      outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], {
        defval: '',
        raw: false, // 不使用原始值，使用格式化后的值
        dateNF: 'yyyy-mm-dd' // 日期格式
      });

      // 手动处理日期字段，修复时区问题
      outdata = outdata.map(row => {
        const processedRow = { ...row };
        Object.keys(processedRow).forEach(key => {
          const value = processedRow[key];
          // 检查是否为日期格式的字符串或数字
          if (value && (typeof value === 'string' || typeof value === 'number')) {
            // 尝试解析为日期
            const dateValue = parseExcelDate(value);
            if (dateValue) {
              processedRow[key] = dateValue;
            }
          }
        });
        return processedRow;
      });

      console.log(outdata)
      SuccessCallBackData(outdata);
      // 此处可对数据进行处理
      // return outdata;
    };
    reader.readAsArrayBuffer(f);
  };
  if (rABS) {
    reader.readAsArrayBuffer(f);
  } else {
    reader.readAsBinaryString(f);
  }
}
