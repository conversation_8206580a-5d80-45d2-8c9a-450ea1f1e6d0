import XLSX from 'xlsx'

/**
 * 解析Excel中的日期值，修复时区问题
 * @param {string|number} value - Excel单元格的值
 * @returns {Date|null} - 解析后的日期对象，如果不是日期则返回null
 */
function parseExcelDate(value) {
  if (!value) return null;

  // 如果是数字，可能是Excel的日期序列号
  if (typeof value === 'number') {
    // Excel日期序列号转换
    if (value >= 1 && value < 2958466) { // 合理的Excel日期范围
      // 使用XLSX库的内置日期转换
      try {
        // XLSX.SSF.parse_date_code 可以正确转换Excel序列号
        const excelDate = XLSX.SSF.parse_date_code(value);
        if (excelDate && excelDate.y && excelDate.m && excelDate.d) {
          // 创建UTC日期，但使用Excel显示的日期值
          // 这样可以确保日期与Excel显示的完全一致
          const utcDate = new Date(Date.UTC(excelDate.y, excelDate.m - 1, excelDate.d));
          return utcDate;
        }
      } catch (error) {
        console.warn('Excel日期序列号转换失败:', error);
      }

      // 如果XLSX转换失败，使用备用算法
      // Excel基准：1900年1月1日 = 1，但需要处理1900年闰年bug
      let days = Math.floor(value);
      if (days >= 60) {
        days = days - 1; // 修正Excel的1900年2月29日bug
      }

      // 从1899年12月31日开始计算，使用UTC时间
      const baseDate = new Date(Date.UTC(1899, 11, 31)); // 1899年12月31日 UTC
      const targetDate = new Date(baseDate.getTime() + (days - 1) * 24 * 60 * 60 * 1000);

      return targetDate;
    }
  }

  // 如果是字符串，尝试解析常见的日期格式
  if (typeof value === 'string') {
    const trimmedValue = value.trim();

    // YYYY-MM-DD 格式
    if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(trimmedValue)) {
      const [year, month, day] = trimmedValue.split('-').map(Number);
      // 使用UTC时间创建日期，确保与Excel显示一致
      return new Date(Date.UTC(year, month - 1, day));
    }

    // YYYY/MM/DD 格式
    if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(trimmedValue)) {
      const [year, month, day] = trimmedValue.split('/').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
    }

    // MM/DD/YYYY 格式
    if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(trimmedValue)) {
      const [month, day, year] = trimmedValue.split('/').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
    }

    // MM-DD-YYYY 格式
    if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(trimmedValue)) {
      const [month, day, year] = trimmedValue.split('-').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
    }

    // YYYY-MM-DD HH:MM:SS 格式
    if (/^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(trimmedValue)) {
      // 对于包含时间的日期，使用原生解析
      const parsedDate = new Date(trimmedValue);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate;
      }
    }
  }

  return null;
}

/**
 * 解析Excel文件
 * @param {File} file - Excel文件
 * @param {Number} headerRowIndex - 表头行索引（从0开始）
 * @returns {Promise<Array>} - 解析后的数据数组
 */
export function parseExcelFile(file, headerRowIndex = 0) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = e.target.result
        const workbook = XLSX.read(data, {
          type: 'binary',
          cellDates: false // 禁用自动日期解析，避免时区问题
        })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 获取表格范围
        const range = XLSX.utils.decode_range(worksheet['!ref'])

        // 如果表头行索引超出范围，则使用第一行作为表头
        if (headerRowIndex > range.e.r) {
          headerRowIndex = 0
        }

        // 获取表头
        const headers = []
        for (let col = range.s.c;col <= range.e.c;col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col })
          const cell = worksheet[cellAddress]
          if (cell && cell.v) {
            headers[col] = cell.v
          } else {
            headers[col] = `Column${col}`
          }
        }

        // 解析数据
        const result = []
        for (let row = headerRowIndex + 1;row <= range.e.r;row++) {
          const rowData = {}
          let hasData = false

          for (let col = range.s.c;col <= range.e.c;col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
            const cell = worksheet[cellAddress]
            const header = headers[col]

            if (cell && cell.v !== undefined) {
              // 尝试解析日期，如果不是日期则使用原值
              const dateValue = parseExcelDate(cell.v);
              rowData[header] = dateValue || cell.v;
              hasData = true
            } else {
              rowData[header] = null
            }
          }

          if (hasData) {
            result.push(rowData)
          }
        }

        resolve(result)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = (error) => {
      reject(error)
    }
    reader.readAsBinaryString(file)
  })
}

/**
 * 将解析后的数据映射到指定的实体类型
 * @param {Array} data - 解析后的数据数组
 * @param {Object} mapping - 字段映射关系，key为Excel表头，value为实体属性名
 * @returns {Array} - 映射后的实体数组
 */
export function mapToEntity(data, mapping) {
  return data.map(item => {
    const entity = {}
    Object.keys(mapping).forEach(key => {
      if (item[key] !== undefined) {
        entity[mapping[key]] = item[key]
      }
    })
    return entity
  })
}

/**
 * 解析Excel文件并映射到指定的实体类型
 * @param {File} file - Excel文件
 * @param {Object} mapping - 字段映射关系，key为Excel表头，value为实体属性名
 * @param {Number} headerRowIndex - 表头行索引（从0开始）
 * @returns {Promise<Array>} - 映射后的实体数组
 */
export function parseExcelAndMapToEntity(file, mapping, headerRowIndex = 0) {
  return parseExcelFile(file, headerRowIndex)
    .then(data => mapToEntity(data, mapping))
}
