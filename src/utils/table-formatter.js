import i18n from '@/lang'

/**
 * 将一维的扁平数组转换为多层级对象
 * @param  {[type]} list 一维数组
 * @param  {[type]} idField key字段名
 * @param  {[type]} pidField 父key字段名
 * @return {[type]} tree 多层级树状结构
 */

// 枚举字段 1：男 2：女
export function formatGender(row, column, currentValue) {
  const genderMap = this.$t('Dictionary.GenderMap')
  const genderMapObject = eval('(' + genderMap + ')') // 暂定方案，后期优化

  const opt = genderMapObject.find((element) => {
    return element.value === currentValue
  })
  return opt === undefined ? '' : opt.label
}
