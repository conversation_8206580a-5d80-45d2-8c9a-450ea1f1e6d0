import axios from 'axios'
import {
  MessageBox,
  Message
} from 'element-ui'
import store from '@/store'
import {
  getToken
} from '@/utils/auth'
console.log('VUE_APP_BASE_API:' + process.env.VUE_APP_BASE_API)
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // baseURL: '/api',
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 1500000 // 默认5分钟超时
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    // console.log(getToken());
    // console.log(service.interceptors.request)
    // console.log(config.headers)
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['X-Token'] = getToken()
      config.headers['AppID'] = 'P0001'
    }

    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data

    // console.log('http响应：',res)
    // console.log('Http的响应类型：responseType=',response.config.responseType)

    // 自定义返回代码2000 为正常，否则异常
    if (response.config.responseType === 'blob') {
      console.log('blob.response', response)
      console.log('response.content-disposition', response.headers['content-disposition']) // ['content-disposition']
      // return Promise.resolve({ data: response.data, fileName: "fileName001.xls" })
      const fileName = decodeURI(response.headers['content-disposition'].split('filename=')[1])

      // 返回文件流内容，以及获取文件名, response.headers['content-disposition']的获取, 默认是获取不到的,需要对服务端webapi进行配置
      return Promise.resolve({
        data: response.data,
        fileName: fileName
      })
    } else {
      console.log('axios-request', res)
      if (res.Code !== 2000) {
        console.log('请求API:', response.request.responseURL + '--执行异常!', res)

        // 无效Token或者Token过期
        if (res.Code === 50008 || res.Code === 50012 || res.Code === 50014) {
          // to re-login
          MessageBox.confirm('您已经退出系统，您可以重新登录或者继续停留在本页面', '退出确认', {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            store.dispatch('user/resetToken').then(() => {
              location.reload()
            })
          })
        } else {
          Message({
            message: i18n.t(res.Message) || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
        }
        console.log('请求API异常消息：', i18n.t(res.Message))
        // 业务异常统一处理
        return Promise.reject(new Error(i18n.t(res.Message) || 'API响应异常'))
      }
    }

    return res
  },
  error => {
    // 响应异常
    console.log('请求响应异常(error):', error, error.response, error.response.data.Message) // 调试用
    // console.log(typeof error)
    // 当响应异常时做一些处理
    let errormessage = '网络异常,请联系网络管理员!' // error.response.data.Message
    if (error && error.response) {
      if (error.code === 'ECONNABORTED') {
        errormessage = '请求网络超时，请稍候再试!'
      } else {
        switch (error.response.status) {
          case 400:
            errormessage = '请求错误(400)'
            break
          case 401:
            errormessage = '未授权，请重新登录(401)'
            break
          case 403:
            errormessage = '拒绝访问(403)'
            break
          case 404:
            errormessage = '请求出错(404),请求的API资源不存在!'
            break
          case 408:
            errormessage = '请求超时(408)'
            break
          case 500:
            if (error.response.data.Message) {
              errormessage = error.response.data.Message
            } else {
              errormessage = '服务器错误(500)'
            }
            break
          case 501:
            errormessage = '服务未实现(501)'
            break
          case 502:
            errormessage = '网络错误(502)'
            break
          case 503:
            errormessage = '服务不可用(503)'
            break
          case 504:
            errormessage = '网络超时(504)'
            break
          case 505:
            errormessage = 'HTTP版本不受支持(505)'
            break
          default:
            errormessage = `服务器连接出错(${err.response.status})!`
        }
      }
    }

    Message({
      message: errormessage,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
