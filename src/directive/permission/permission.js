import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const {
      value
    } = binding
    const permission_buttons = store.getters && store.getters.permission_buttons

    // console.log('permission_buttons',value,permission_buttons)
    // && value instanceof Array && value.length > 0 数组类型校验
    if (permission_buttons != null && value.name !== undefined) {
      // const hasPermission = permission_buttons.some(role => {
      //   return permissionRoles.includes(role)
      // })
      const hasPermission = permission_buttons.includes(value.name)

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
    // else {
    //   throw new Error(`你必须设定相关按钮的资源名称以进行权限校验！`)
    // }
  }
}
