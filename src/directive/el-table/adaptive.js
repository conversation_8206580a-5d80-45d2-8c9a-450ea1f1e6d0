import TableLayout from 'element-ui/packages/table/src/table-layout'
/**
 * 使用
 * 期望使用该指令的表格实现el-table的高度自适应
 * 所有的el-table只需要简单的配置而不用重复的代码
 */

/*
  * 修改el-table表格的height需要触发官方提供的监听器去set高度值height
  * 暂时没能找到合适的方法和流程触发监听器
  * 监听器定义在TableLayout中，TableLayout是一个类，未能学会使用
  * 在页面内部将height设为变量进行修改的时候会出现style未定义的错误，但能够修改成功height
  * 猜测两边的机制有互通之处，研究懂了就能够封装在自定义指令里面
* */
export default {
  bind(el, binding, vnode) {
    console.log('09089797867::::', el.setHeight)
    vnode.context.$nextTick(function() {
      el.style.cssText = `width:100%;height:${window.innerHeight - el.offsetTop - 110};` // window.innerHeight - el.offsetTop - 110

      // 监听窗口大小变化
      // const self = that
      window.onresize = function() {
        // vnode.context.$set(el, 'style.height', window.innerHeight - el.offsetTop - 110)
        el.style.cssText = `width:100%;max-height:${window.innerHeight - el.offsetTop - 110};` // window.innerHeight - el.offsetTop - 110
        console.log('22222222222222', el.style)
      }
    })
  },
  inserted(el, binding, vnode) {
  },
  update(el, binding, vnode) {
    /* console.log('09089797867::::', el)
    const tableDom = el.querySelector('.el-table')
    console.log('qqqqqqqq+++', tableDom)
    console.log('333333333333', el.style.cssText)
    vnode.context.$nextTick(function() {
      tableDom.style.height = window.innerHeight - el.offsetTop - 110 // `height:${window.innerHeight - el.offsetTop - 110};`

      // 监听窗口大小变化
      // const self = that
      window.onresize = function() {
        // vnode.context.$set(el, 'style.height', window.innerHeight - el.offsetTop - 110)
        tableDom.style.height = window.innerHeight - el.offsetTop - 110 // `height:${window.innerHeight - el.offsetTop - 110};`
        console.log('22222222222222', tableDom.style.cssText)
      }
    })*/
  }
}
