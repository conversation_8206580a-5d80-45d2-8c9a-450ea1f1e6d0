import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css
import './styles/style.css'

import App from './App'
import store from './store'
import router from './router'

import api from './utils/api.js' // 先引入文件
Vue.prototype.API = api; // 将引入的文件挂载到vue的原型中

// 全局引入(保证没有实例的情况下也可以引入)
window.i18n = i18n
export default i18n

import i18n from './lang' // internationalization
import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */
import {
  mockXHR
} from '../mock'
if (process.env.NODE_ENV === 'production') {
  mockXHR()
}

import {
  convertToKeyValue
} from '@/utils'

// 引入日期格式化库
import moment from 'moment'

// 优雅的全局引入第三方库 this.$moment().format('HH:mm')
Object.defineProperty(Vue.prototype, '$moment', {
  value: moment
})

// 定义一个全局过滤器实现日期格式化
Vue.filter(
  'datetime',
  function(input, fmtstring) {
    if (input) {
      const fmtstr = fmtstring || 'YYYY-MM-DD HH:mm:ss'
      return moment(input).format(fmtstr)
    }
  }
)
Vue.filter(
  'date',
  function(input, fmtstring) {
    if (input) {
      const fmtstr = fmtstring || 'YYYY-MM-DD'
      return moment(input).format(fmtstr)
    }
  }
)

Vue.filter('yesnoFilter', function(input) {
  const statusMapArr = [{
    value: true,
    label: i18n.t('Dictionary.YesNoMap.YesValue')
  }, // 正常
  {
    value: false,
    label: i18n.t('Dictionary.YesNoMap.NoValue')
  } // 冻结
  ]

  // const statusMap = [
  //   { true: i18n.t('Dictionary.YesNoMap.YesValue') }, // 正常
  //   { false: i18n.t('Dictionary.YesNoMap.NoValue') } // 冻结
  // ]

  const statusMap = convertToKeyValue(statusMapArr)
  return statusMap[input]
})

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false
Vue.prototype.appName = '‘My App’'
Vue.filter('i18n', i18n)

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})

// /////////////////////////法拉鼎

// ///////////全局函数
Vue.prototype.showNotify = function(tp, msg) {
  this.$notify({
    title: i18n.t('Common.' + tp),
    message: i18n.t(msg),
    type: tp,
    duration: 5000
  })
}
let loading
Vue.prototype.startLoading = function() {
  loading = this.$loading({
    lock: true,
    text: 'Loading',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
}
Vue.prototype.endLoading = function() {
  loading.close()
}
// 路由跳转
Vue.prototype.routeTo = function(routeName, pms) {
  this.$store.dispatch('tagsView/addCachedView', this.$route)
  this.$router.push({
    name: routeName,
    params: pms
  })
}

// 返回路由，并关闭当前页面
Vue.prototype.backTo = function(routeName, pms) {
  this.$store.dispatch('tagsView/delView', this.$route)
  this.$router.push({
    name: routeName,
    params: pms
  })
}

// 检查单选
Vue.prototype.checkSingleSelection = function(rows) {
  if (rows.length > 1) {
    this.showNotify('error', 'Common.justSingleSelection')
    return false
  } else if (rows.length === 1) {
    return true
  } else {
    this.showNotify('error', 'Common.noSelection')
    return false
  }
}

// 检查多选
Vue.prototype.checkMultiSelection = function(rows) {
  if (rows.length < 1) {
    this.showNotify('warning', 'Common.noSelection')
    return false
  } else {
    return true
  }
}

// 获取数据字典实体列表
Vue.prototype.getDict = function(typeCode) {
  return new Promise((resolve, reject) => {
    if (store.getters.dataDict.length > 0) {
      resolve(store.getters.dataDict.filter(dict => dict.TypeCode === typeCode))
    } else {
      store.dispatch('dataDict/getDataDict').then((data) => {
        resolve(data.filter(dict => dict.TypeCode === typeCode))
      }).catch(error => {
        reject(error)
      })
    }
  })
}

Vue.prototype.QtyValidator = function(rule, value, callback) {
  // 自定义数量验证器
  if (value > 0) {
    callback()
  } else if (value <= 0) {
    callback(new Error(i18n.t('Common.mustBeGreaterThanZero')))
  } else {
    callback(new Error(i18n.t('Common.numberRequired')))
  }
}
// ///////////全局函数

// ///////////全局过滤器

// 过账状态
Vue.filter(
  'posting',
  function(input) {
    var data = store.getters.dataDict.filter(dict => dict.TypeCode === 'SYS001')
    if (input === true) return data.find(val => val.EnumKey == 2).EnumValue
    else return data.find(val => val.EnumKey == 1).EnumValue
  }
)
// 生产订单管理状态
Vue.filter(
  'OrderStates',
  function(input) {
    const statusMapArr = [{
      value: 1,
      label: i18n.t('Dictionary.PP_Order_Status.One')
    }, // 准备中
    {
      value: 2,
      label: i18n.t('Dictionary.PP_Order_Status.Two')
    }, // 下达
    {
      value: 3,
      label: i18n.t('Dictionary.PP_Order_Status.Three')
    }, // 开始
    {
      value: 4,
      label: i18n.t('Dictionary.PP_Order_Status.Four')
    }, // 完成
    {
      value: 5,
      label: i18n.t('Dictionary.PP_Order_Status.Five')
    }, // 关闭
    {
      value: 6,
      label: i18n.t('Dictionary.PP_Order_Status.Six')
    } // 取消
    ]
    const statusMap = convertToKeyValue(statusMapArr)
    return statusMap[input]
  }
)
// 销售订单管理状态
Vue.filter(
  'SDOrderStates',
  function(input) {
    const statusMapArr = [{
      value: 1,
      label: i18n.t('Dictionary.SD_Order_Status.One')
    }, //
    {
      value: 2,
      label: i18n.t('Dictionary.SD_Order_Status.Two')
    }, //
    {
      value: 3,
      label: i18n.t('Dictionary.SD_Order_Status.Three')
    }, //
    {
      value: 4,
      label: i18n.t('Dictionary.SD_Order_Status.Four')
    } //
    ]
    const statusMap = convertToKeyValue(statusMapArr)
    return statusMap[input]
  }
)
// 处理方案
Vue.filter(
  'dealWithFormatter',
  function(input) {
    var data = store.getters.dataDict.filter(dict => dict.TypeCode === 'PP003')
    return data.find(val => val.EnumKey == input).EnumValue
  }
)
// 质检状态
Vue.filter(
  'qi',
  function(input) {
    var data = store.getters.dataDict.filter(dict => dict.TypeCode === 'SYS003')
    if (input === 2) return data.find(val => val.EnumKey == 2).EnumValue
    else return data.find(val => val.EnumKey == 1).EnumValue
  }
)
// ///////////全局过滤器
