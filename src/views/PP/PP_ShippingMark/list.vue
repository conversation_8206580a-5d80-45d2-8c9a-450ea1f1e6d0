<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.AssemblyLine"
        size="mini"
        placeholder="请输入装配线号"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.OrderNo"
        size="mini"
        placeholder="请输入定单号"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ContractNo"
        size="mini"
        placeholder="请输入合同号"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.FactoryNo"
        size="mini"
        placeholder="请输入出厂编号"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.DeliveryCustomer"
        size="mini"
        placeholder="请输入发货单位"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.IsExport"
        size="mini"
        clearable
        placeholder="是否出口"
        style="width: 180px"
        class="filter-item"
      >
        <el-option label="是" value="是" />
        <el-option label="否" value="否" />
      </el-select>
      <el-input
        v-model="listQuery.KeyWord"
        size="mini"
        placeholder="请输入关键字"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.PrintStatus"
        size="mini"
        clearable
        placeholder="打印状态"
        style="width: 180px"
        class="filter-item"
      >
        <el-option v-for="item in printOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.ShippingMarkPrintSystem"
        size="mini"
        placeholder="请输入唛头打印系统"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.ParameterCheck"
        size="mini"
        clearable
        placeholder="参数缺失"
        style="width: 180px"
        class="filter-item"
      >
        <el-option label="是" value="true" />
        <el-option label="否" value="false" />
      </el-select>
      <el-select
        v-model="listQuery.PackageType"
        size="mini"
        clearable
        placeholder="塑封/套袋"
        style="width: 180px"
        class="filter-item"
      >
        <el-option v-for="item in packOptions" :key="item.label" :label="item.label" :value="item.label" />
      </el-select>
      <el-input
        v-model="listQuery.PrintDirection"
        size="mini"
        placeholder="请输入打印方向"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        align="right"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        style="width: 350px"
        class="filter-item"
        size="mini"
      />
      <el-button
        v-waves
        size="mini"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="mini"
        :disabled="multipleSelection.length !== 1"
        @click="handleEdit"
      >
        编辑
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="mini"
        :disabled="multipleSelection.length === 0"
        @click="handleDelete"
      >
        删除
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="mini"
        @click="handleExport"
      >
        导出
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-download"
        size="mini"
        @click="handleExportTemplate"
      >
        导出模板
      </el-button>
      <el-upload
        v-waves
        style="margin-left: 10px"
        class="filter-item"
        :action="'#'"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button v-waves type="primary" icon="el-icon-upload" size="mini">导入</el-button>
      </el-upload>
      <el-button
        v-waves
        style="margin-left: 10px"
        class="filter-item"
        type="info"
        size="mini"
        :disabled="multipleSelection.length === 0"
        @click="handlePrint"
      >
        打印
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      size="mini"
      height="calc(100vh - 320px)"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <!-- 固定列 -->
      <el-table-column type="selection" width="55" align="center" fixed />

      <el-table-column label="打印状态" prop="PrintStatus" align="center" width="80" fixed show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="print-status-indicator">
            <span
              :class="['status-circle', scope.row.PrintStatus ? 'status-printed' : 'status-unprinted']"
              :title="scope.row.PrintStatus ? '已打印' : '未打印'"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="打印系统" prop="PrintSystem" align="center" width="80" fixed show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PrintSystem }}</span>
        </template>
      </el-table-column>

      <el-table-column label="纸张选项" prop="PaperSize" align="center" width="70" fixed show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PaperSize + scope.row.PaperType }}</span>
        </template>
      </el-table-column>

      <el-table-column label="模板名称" prop="TemplateName" align="center" width="80" fixed show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TemplateName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="打印方向" prop="PrintDirection" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PrintDirection }}</span>
        </template>
      </el-table-column>

      <el-table-column label="封装方式" prop="EncapsulationMethod" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.EncapsulationMethod }}</span>
        </template>
      </el-table-column>

      <el-table-column label="参数缺失" prop="ParameterCheck" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="parameter-status-indicator">
            <i
              :class="['el-icon-star-on', 'parameter-star', scope.row.ParameterCheck ? 'parameter-missing' : 'parameter-normal']"
              :title="scope.row.ParameterCheck ? '参数缺失' : '参数正常'"
            />
          </div>
        </template>
      </el-table-column>

      <!-- 其他列 -->
      <el-table-column label="SAP生产订单号" prop="SapProduceNo" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapProduceNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="出厂编号" prop="FactoryNo" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.FactoryNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="订单号" prop="OrderNo" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="发货单位" prop="DeliveryCustomer" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryCustomer }}</span>
        </template>
      </el-table-column>

      <el-table-column label="产品型号" prop="ProduceModel" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProduceModel }}</span>
        </template>
      </el-table-column>

      <el-table-column label="产品件号" prop="ProducePart" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProducePart }}</span>
        </template>
      </el-table-column>

      <el-table-column label="装配线号" prop="AssemblyLine" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.AssemblyLine }}</span>
        </template>
      </el-table-column>

      <el-table-column label="装配日期" prop="AssemblyDate" align="center" width="120" :formatter="formatDateTime" show-overflow-tooltip />

      <el-table-column label="额定载重" prop="RatedLoad" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedLoad }}</span>
        </template>
      </el-table-column>

      <el-table-column label="额定速度" prop="RatedSpeed" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedSpeed }}</span>
        </template>
      </el-table-column>

      <el-table-column label="额定电压" prop="RatedVoltage" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedVoltage }}</span>
        </template>
      </el-table-column>

      <el-table-column label="铭牌要求" prop="NameplateRequirements" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.NameplateRequirements }}</span>
        </template>
      </el-table-column>

      <el-table-column label="曳引比" prop="TractionRatio" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TractionRatio }}</span>
        </template>
      </el-table-column>

      <el-table-column label="客户型号" prop="CustomerModel" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerModel }}</span>
        </template>
      </el-table-column>

      <el-table-column label="箱板刷字" prop="BoxBoardBrushing" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBoardBrushing }}</span>
        </template>
      </el-table-column>

      <el-table-column label="是否出口" prop="IsExport" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsExport }}</span>
        </template>
      </el-table-column>

      <el-table-column label="项目名称" prop="ProjectName" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProjectName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="备注" prop="Remark" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>

      <el-table-column label="接受编号" prop="AcceptNo" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.AcceptNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="数量" prop="Quantity" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Quantity }}</span>
        </template>
      </el-table-column>

      <el-table-column label="制动器电压" prop="BrakeVoltage" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BrakeVoltage }}</span>
        </template>
      </el-table-column>

      <el-table-column label="SAP销售订单号" prop="SapSalesOrderNo" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapSalesOrderNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="SAP销售订单行号" prop="SapSalesOrderLineNo" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapSalesOrderLineNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="生产管理员" prop="ProductionManager" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionManager }}</span>
        </template>
      </el-table-column>

      <el-table-column label="装箱尺寸" prop="PackingSize" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PackingSize }}</span>
        </template>
      </el-table-column>

      <el-table-column label="装箱净重" prop="PackingNetWeight" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PackingNetWeight }}</span>
        </template>
      </el-table-column>

      <el-table-column label="装箱毛重" prop="PackingGrossWeight" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PackingGrossWeight }}</span>
        </template>
      </el-table-column>

      <el-table-column label="额定功率" prop="RatedPower" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedPower }}</span>
        </template>
      </el-table-column>

      <el-table-column label="节径" prop="PitchDiameter" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PitchDiameter }}</span>
        </template>
      </el-table-column>

      <el-table-column label="绳槽" prop="RopeGroove" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RopeGroove }}</span>
        </template>
      </el-table-column>

      <el-table-column label="槽距" prop="GrooveDistance" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.GrooveDistance }}</span>
        </template>
      </el-table-column>

      <el-table-column label="唛头打印时间" prop="ShippingMarkPrintTime" align="center" width="100" show-overflow-tooltip :formatter="formatDateTime" />

      <!-- 操作列 -->
      <el-table-column label="操作" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleViewPrintRecords(scope.row)"
          >
            打印明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 表单对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="800px">
      <el-form ref="form" :model="temp" :rules="rules" label-position="right" label-width="140px">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="打印状态" prop="PrintStatus">
              <el-select v-model="temp.PrintStatus" placeholder="请选择打印状态" style="width: 100%" size="mini">
                <el-option label="未打印" :value="false" />
                <el-option label="已打印" :value="true" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="打印系统" prop="PrintSystem">
              <el-input v-model="temp.PrintSystem" placeholder="请输入打印系统" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="纸张大小" prop="PaperSize">
              <el-input v-model="temp.PaperSize" placeholder="请输入纸张大小" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纸张类别" prop="PaperType">
              <el-input v-model="temp.PaperType" placeholder="请输入纸张类别" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- SAP和订单信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SAP生产订单号" prop="SapProduceNo">
              <el-input v-model="temp.SapProduceNo" placeholder="请输入SAP生产订单号" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出厂编号" prop="FactoryNo">
              <el-input v-model="temp.FactoryNo" placeholder="请输入出厂编号" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单号" prop="OrderNo">
              <el-input v-model="temp.OrderNo" placeholder="请输入订单号" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同号" prop="ContractNo">
              <el-input v-model="temp.ContractNo" placeholder="请输入合同号" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发货单位" prop="DeliveryCustomer">
              <el-input v-model="temp.DeliveryCustomer" placeholder="请输入发货单位" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装配线号" prop="AssemblyLine">
              <el-input v-model="temp.AssemblyLine" placeholder="请输入装配线号" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 产品信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品型号" prop="ProduceModel">
              <el-input v-model="temp.ProduceModel" placeholder="请输入产品型号" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品件号" prop="ProducePart">
              <el-input v-model="temp.ProducePart" placeholder="请输入产品件号" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="装配日期" prop="AssemblyDate">
              <el-date-picker
                v-model="temp.AssemblyDate"
                type="datetime"
                placeholder="选择装配日期"
                style="width: 100%"
                size="mini"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户型号" prop="CustomerModel">
              <el-input v-model="temp.CustomerModel" placeholder="请输入客户型号" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 技术参数 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="额定载重" prop="RatedLoad">
              <el-input v-model="temp.RatedLoad" placeholder="请输入额定载重" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="额定速度" prop="RatedSpeed">
              <el-input v-model="temp.RatedSpeed" placeholder="请输入额定速度" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="额定电压" prop="RatedVoltage">
              <el-input v-model="temp.RatedVoltage" placeholder="请输入额定电压" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="功率" prop="RatedPower">
              <el-input v-model="temp.RatedPower" placeholder="请输入功率" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="曳引比" prop="TractionRatio">
              <el-input v-model="temp.TractionRatio" placeholder="请输入曳引比" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节径" prop="PitchDiameter">
              <el-input v-model="temp.PitchDiameter" placeholder="请输入节径" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="绳槽" prop="RopeGroove">
              <el-input v-model="temp.RopeGroove" placeholder="请输入绳槽" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="槽距" prop="GrooveDistance">
              <el-input v-model="temp.GrooveDistance" placeholder="请输入槽距" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 铭牌和项目信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="铭牌要求" prop="NameplateRequirements">
              <el-input v-model="temp.NameplateRequirements" placeholder="请输入铭牌要求" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="箱板刷字" prop="BoxBoardBrushing">
              <el-input v-model="temp.BoxBoardBrushing" placeholder="请输入箱板刷字" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否出口" prop="IsExport">
              <el-select v-model="temp.IsExport" placeholder="请选择是否出口" style="width: 100%" size="mini">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数校验" prop="ParameterCheck">
              <el-input v-model="temp.ParameterCheck" placeholder="请输入参数校验" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="ProjectName">
              <el-input v-model="temp.ProjectName" placeholder="请输入项目名称" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 空列，保持布局平衡 -->
          </el-col>
        </el-row>
        <!-- SAP相关信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接受编号" prop="AcceptNo">
              <el-input v-model="temp.AcceptNo" placeholder="请输入接受编号" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="Quantity">
              <el-input-number v-model="temp.Quantity" :min="0" style="width: 100%" placeholder="请输入数量" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="制动器电压" prop="BrakeVoltage">
              <el-input v-model="temp.BrakeVoltage" placeholder="请输入制动器电压" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="SAP销售订单号" prop="SapSalesOrderNo">
              <el-input v-model="temp.SapSalesOrderNo" placeholder="请输入SAP销售订单号" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SAP销售订单行号" prop="SapSalesOrderLineNo">
              <el-input v-model="temp.SapSalesOrderLineNo" placeholder="请输入SAP销售订单行号" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产管理员" prop="ProductionManager">
              <el-input v-model="temp.ProductionManager" placeholder="请输入生产管理员" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 包装信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="装箱尺寸" prop="PackingSize">
              <el-input v-model="temp.PackingSize" placeholder="请输入装箱尺寸" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装箱净重" prop="PackingNetWeight">
              <el-input v-model="temp.PackingNetWeight" placeholder="请输入装箱净重" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="装箱毛重" prop="PackingGrossWeight">
              <el-input v-model="temp.PackingGrossWeight" placeholder="请输入装箱毛重" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="打印方向" prop="PrintDirection">
              <el-input v-model="temp.PrintDirection" placeholder="请输入打印方向" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 打印相关信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="唛头打印时间" prop="ShippingMarkPrintTime">
              <el-date-picker
                v-model="temp.ShippingMarkPrintTime"
                type="datetime"
                placeholder="选择唛头打印时间"
                style="width: 100%"
                size="mini"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="清单打印状态" prop="ListPrintStatus">
              <el-select v-model="temp.ListPrintStatus" placeholder="请选择清单打印状态" style="width: 100%" size="mini">
                <el-option label="未打印" :value="false" />
                <el-option label="已打印" :value="true" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="清单打印时间" prop="ListPrintTime">
              <el-date-picker
                v-model="temp.ListPrintTime"
                type="datetime"
                placeholder="选择清单打印时间"
                style="width: 100%"
                size="mini"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="打印模板" prop="PrintTemplateId">
              <el-select
                v-model="temp.PrintTemplateId"
                placeholder="请选择打印模板"
                filterable
                clearable
                style="width: 100%"
                size="mini"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in printTemplateList"
                  :key="template.Id"
                  :label="template.TemplateName"
                  :value="template.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="Remark">
              <el-input v-model="temp.Remark" type="textarea" :rows="3" size="mini" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus === 'create'" type="primary" size="mini" @click="handleCreateConfirm">确定</el-button>
        <el-button v-else type="primary" size="mini" @click="handleEditConfirm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 打印记录对话框 -->
    <el-dialog title="打印记录明细" :visible.sync="printRecordsDialogVisible" width="1200px">
      <el-table
        v-loading="printRecordsLoading"
        :data="printRecordsList"
        border
        fit
        highlight-current-row
        style="width: 100%"
        max-height="500"
      >
        <el-table-column label="模板名称" prop="TemplateName" align="center" width="120" show-overflow-tooltip />
        <el-table-column label="纸张方向" prop="PrintDirection" align="center" width="100" show-overflow-tooltip />
        <el-table-column label="纸张大小" prop="PaperSize" align="center" width="100" show-overflow-tooltip />
        <el-table-column label="纸张类别" prop="PaperType" align="center" width="100" show-overflow-tooltip />
        <el-table-column label="出厂编号" prop="FactoryNo" align="center" width="120" show-overflow-tooltip />
        <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
        <el-table-column label="订单号" prop="OrderNo" align="center" width="120" show-overflow-tooltip />
        <el-table-column label="发货单位" prop="DeliveryCustomer" align="center" width="140" show-overflow-tooltip />
        <el-table-column label="生产型号" prop="ProduceModel" align="center" width="140" show-overflow-tooltip />
        <el-table-column label="装配线号" prop="AssemblyLine" align="center" width="100" show-overflow-tooltip />
        <el-table-column label="装配日期" prop="AssemblyDate" align="center" width="120" :formatter="formatDateTime" show-overflow-tooltip />
        <el-table-column label="SAP生产订单号" prop="SapProduceNo" align="center" width="140" show-overflow-tooltip />
        <el-table-column label="创建用户" prop="CUser" align="center" width="100" show-overflow-tooltip />
        <el-table-column label="创建时间" prop="CTime" align="center" width="150" :formatter="formatDateTime" show-overflow-tooltip />
        <el-table-column label="备注" prop="Remark" align="center" width="140" show-overflow-tooltip />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="printRecordsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { formatDateTime } from '@/utils'
import {
  fetchList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  exportExcelTemplate,
  importExcelData,
  createPrintRecords,
  getPrintRecordsByShippingMarkId
} from '@/api/PP/PP_ShippingMark'

import { exportToExcel } from '@/utils/excel-export'
import { parseExcelFile } from '@/utils/excel-import'
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import { getByTemplateKey } from '@/api/MD/MD_PrintTemplate'
import XLSX from 'xlsx'
import { datetime } from 'mockjs/src/mock/random/date';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

// 纸张尺寸配置（单位：mm）
const PAPER_SIZES = {
  'A4': {
    portrait: { width: 210, height: 297, name: 'A4纵向' },
    landscape: { width: 297, height: 210, name: 'A4横向' }
  },
  'A5': {
    portrait: { width: 148, height: 210, name: 'A5纵向' },
    landscape: { width: 210, height: 148, name: 'A5横向' }
  },
  'A6': {
    portrait: { width: 105, height: 148, name: 'A6纵向' },
    landscape: { width: 148, height: 105, name: 'A6横向' }
  }
};

// 纸张尺寸工具函数
const PaperSizeUtils = {
  // 识别纸张尺寸和方向
  identifyPaperSize(width, height) {
    const tolerance = 5; // 允许5mm的误差

    for (const [size, orientations] of Object.entries(PAPER_SIZES)) {
      // 检查纵向
      if (Math.abs(width - orientations.portrait.width) <= tolerance &&
          Math.abs(height - orientations.portrait.height) <= tolerance) {
        return {
          size: size,
          orientation: 'portrait',
          name: orientations.portrait.name,
          width: orientations.portrait.width,
          height: orientations.portrait.height
        };
      }

      // 检查横向
      if (Math.abs(width - orientations.landscape.width) <= tolerance &&
          Math.abs(height - orientations.landscape.height) <= tolerance) {
        return {
          size: size,
          orientation: 'landscape',
          name: orientations.landscape.name,
          width: orientations.landscape.width,
          height: orientations.landscape.height
        };
      }
    }

    return null; // 未识别的尺寸
  },

  // 根据记录的纸张尺寸字段解析尺寸
  parsePaperSizeFromRecord(record) {
    // 优先使用ShippingMarkPaperSize，其次使用PaperSize
    const paperSizeStr = record.ShippingMarkPaperSize || record.PaperSize;
    if (!paperSizeStr) return null;

    // 获取打印方向
    const printDirection = record.PrintDirection || '';

    // 尝试解析常见的纸张尺寸格式
    const sizeStr = paperSizeStr.toString().toUpperCase();
    const directionStr = printDirection.toString().toUpperCase();

    // 判断是否为横向打印
    const isLandscape = sizeStr.includes('横') || sizeStr.includes('LANDSCAPE') ||
                       directionStr.includes('横') || directionStr.includes('LANDSCAPE') ||
                       directionStr.includes('HORIZONTAL');

    // 直接匹配标准尺寸
    if (sizeStr.includes('A4')) {
      return isLandscape
        ? { width: 297, height: 210 } : { width: 210, height: 297 };
    }
    if (sizeStr.includes('A5')) {
      return isLandscape
        ? { width: 210, height: 148 } : { width: 148, height: 210 };
    }
    if (sizeStr.includes('A6')) {
      return isLandscape
        ? { width: 148, height: 105 } : { width: 105, height: 148 };
    }

    return null;
  },

  // 比较两个纸张尺寸是否一致
  comparePaperSizes(size1, size2) {
    if (!size1 || !size2) return false;
    return size1.size === size2.size && size1.orientation === size2.orientation;
  },

  // 格式化纸张尺寸信息
  formatPaperSizeInfo(paperSize) {
    if (!paperSize) return '未知尺寸';
    return `${paperSize.name} (${paperSize.width}×${paperSize.height}mm)`;
  }
};

export default {
  name: 'PP.PP_ShippingMark',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      printDialogVisible: false,
      printData: null,
      printLoading: false,
      printTemplates: [], // 打印模板列表
      printTemplateList: [], // 表单中的打印模板列表
      printOptions: [
        {
          label: '是',
          key: true
        }, {
          label: '否',
          key: false
        }
      ],
      packOptions: [
        {
          label: '塑封'
        }, {
          label: '套袋'
        }
      ],
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        KeyWord: '', // 关键字
        ContractNo: '', // 合同号
        FactoryNo: '', // 出厂编号
        AssemblyLine: '', // 装配线
        DeliveryCustomer: '', // 发货单位
        ParameterCheck: undefined, // 参数校验
        OrderNo: '', // 订单号
        PrintStatus: '', // 打印状态
        PackageType: '', // 套袋/塑封
        ShippingMarkPrintSystem: '', // 唛头打印系统
        IsExport: '', // 是否出口
        PrintDirection: '', // 打印方向
        EncapsulationMethod: '', // 封装方式
        StartTime: undefined, // 装配日期开始
        EndTime: undefined // 装配日期结束
      },
      multipleSelection: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑唛头打印',
        create: '新增唛头打印'
      },
      temp: {
        Id: '',
        PrintStatus: null,
        PrintSystem: '',
        PaperSize: '',
        PaperType: '',
        SapProduceNo: '',
        FactoryNo: '',
        OrderNo: '',
        ContractNo: '',
        DeliveryCustomer: '',
        ProduceModel: '',
        ProducePart: '',
        AssemblyLine: '',
        AssemblyDate: null,
        RatedLoad: '',
        RatedSpeed: '',
        RatedVoltage: '',
        NameplateRequirements: '',
        TractionRatio: '',
        CustomerModel: '',
        BoxBoardBrushing: '',
        IsExport: '',
        ParameterCheck: null,
        ProjectName: '',
        AcceptNo: '',
        Quantity: null,
        BrakeVoltage: '',
        SapSalesOrderNo: '',
        SapSalesOrderLineNo: '',
        ProductionManager: '',
        PackingSize: '',
        PackingNetWeight: '',
        PackingGrossWeight: '',
        RatedPower: '',
        PitchDiameter: '',
        RopeGroove: '',
        GrooveDistance: '',
        ShippingMarkPrintTime: null,
        ListPrintStatus: null,
        ListPrintTime: null,
        PrintTemplateId: '',
        PrintDirection: '',
        EncapsulationMethod: ''
      },
      rules: {
        PrintSystem: [{ required: true, message: '请输入打印系统', trigger: 'blur' }],
        FactoryNo: [{ required: true, message: '请输入出厂编号', trigger: 'blur' }],
        OrderNo: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
        ContractNo: [{ required: true, message: '请输入合同号', trigger: 'blur' }],
        AssemblyLine: [{ required: true, message: '请输入装配线号', trigger: 'blur' }],
        ProduceModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
        ProducePart: [{ required: true, message: '请输入产品件号', trigger: 'blur' }]
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 打印记录相关
      printRecordsDialogVisible: false,
      printRecordsLoading: false,
      printRecordsList: [],
      currentShippingMarkId: ''
    }
  },
  watch: {
    dateRange(val) {
      if (val && val.length === 2) {
        this.listQuery.AssemblyStartDate = val[0]
        this.listQuery.AssemblyEndDate = val[1]
      } else {
        this.listQuery.AssemblyStartDate = undefined
        this.listQuery.AssemblyEndDate = undefined
      }
    }
  },
  created() {
    this.getList()
    this.loadPrintTemplates()
    this.loadPrintTemplateList()
  },
  methods: {
    datetime,
    formatDateTime,
    getList() {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items
          this.total = response.Data.total
        } else {
          this.$notify({
            title: '错误',
            message: response.Message,
            type: 'error',
            duration: 2000
          })
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取列表失败:', error)
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === null || order === null) return
      this.listQuery.sort = (order === 'ascending' ? '+' : '-') + prop
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        Id: '',
        PrintStatus: null,
        PrintSystem: '',
        PaperSize: '',
        PaperType: '',
        SapProduceNo: '',
        FactoryNo: '',
        OrderNo: '',
        ContractNo: '',
        DeliveryCustomer: '',
        ProduceModel: '',
        ProducePart: '',
        AssemblyLine: '',
        AssemblyDate: null,
        RatedLoad: '',
        RatedSpeed: '',
        RatedVoltage: '',
        NameplateRequirements: '',
        TractionRatio: '',
        CustomerModel: '',
        BoxBoardBrushing: '',
        IsExport: '',
        ParameterCheck: null,
        ProjectName: '',
        AcceptNo: '',
        Quantity: null,
        BrakeVoltage: '',
        SapSalesOrderNo: '',
        SapSalesOrderLineNo: '',
        ProductionManager: '',
        PackingSize: '',
        PackingNetWeight: '',
        PackingGrossWeight: '',
        RatedPower: '',
        PitchDiameter: '',
        RopeGroove: '',
        GrooveDistance: '',
        ShippingMarkPrintTime: null,
        ListPrintStatus: null,
        ListPrintTime: null,
        PrintTemplateId: '',
        PrintDirection: '',
        EncapsulationMethod: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleEdit() {
      this.resetTemp()
      this.temp = Object.assign({}, this.multipleSelection[0])
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleDelete() {
      this.$confirm('确定要删除选中的数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.Id)
        batchDelete(ids).then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message,
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    handleCreateConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          add(this.temp).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleEditConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          update(tempData).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '编辑成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleExport() {
      this.isProcessing = true
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res, '唛头打印')
        this.isProcessing = false
      }).catch(error => {
        console.error('导出失败:', error)
        this.$notify({
          title: '错误',
          message: '导出失败',
          type: 'error',
          duration: 2000
        })
        this.isProcessing = false
      })
    },
    handleExportTemplate() {
      this.isProcessing = true
      exportExcelTemplate().then(res => {
        exportToExcel(res, '唛头打印模板')
        this.isProcessing = false
      }).catch(error => {
        console.error('导出模板失败:', error)
        this.$notify({
          title: '错误',
          message: '导出模板失败',
          type: 'error',
          duration: 2000
        })
        this.isProcessing = false
      })
    },
    beforeUpload(file) {
      this.isProcessing = true

      // 表头在第43行，所以索引是42（从0开始计数）
      const headerRowIndex = 2

      // 首先读取Excel文件以提取第二行D列的装配日期
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'binary' })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]

          // 读取第二行D列的内容（行索引1，列索引3，因为从0开始计数）
          const cellAddress = XLSX.utils.encode_cell({ r: 1, c: 3 }) // 第二行D列
          const cell = worksheet[cellAddress]
          let extractedAssemblyDate = null

          if (cell && cell.v) {
            const cellContent = cell.v.toString()
            console.log('第二行D列内容:', cellContent)

            // 优先提取装配日期（车间YYYY-MM-DD装配格式）
            const assemblyDateRegex = /车间(\d{4}-\d{1,2}-\d{1,2})装配/
            const assemblyMatch = cellContent.match(assemblyDateRegex)

            if (assemblyMatch) {
              const dateStr = assemblyMatch[1]
              const parsedDate = new Date(dateStr)
              if (!isNaN(parsedDate.getTime())) {
                extractedAssemblyDate = parsedDate.toISOString()
                console.log('提取到的装配日期（车间装配）:', extractedAssemblyDate)
              }
            } else {
              // 如果没有找到装配日期格式，则提取第一个日期
              const dateRegex = /(\d{4}-\d{1,2}-\d{1,2})/
              const match = cellContent.match(dateRegex)

              if (match) {
                const dateStr = match[1]
                const parsedDate = new Date(dateStr)
                if (!isNaN(parsedDate.getTime())) {
                  extractedAssemblyDate = parsedDate.toISOString()
                  console.log('提取到的日期（通用格式）:', extractedAssemblyDate)
                }
              }
            }
          }

          // 继续执行原有的导入逻辑
          this.processExcelImport(file, headerRowIndex, extractedAssemblyDate)
        } catch (error) {
          console.error('读取Excel文件失败:', error)
          this.$notify({
            title: '错误',
            message: '读取Excel文件失败',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        }
      }
      reader.onerror = (error) => {
        console.error('文件读取错误:', error)
        this.isProcessing = false
      }
      reader.readAsBinaryString(file)

      return false
    },

    // 处理Excel导入的主要逻辑
    processExcelImport(file, headerRowIndex, extractedAssemblyDate) {
      // Excel表头（中文描述）到实体属性名的映射
      const headerMapping = {
        '装配线号': 'AssemblyLine',
        '定单号': 'OrderNo',
        '合同号': 'ContractNo',
        '发货单位': 'DeliveryCustomer',
        '交货时间': 'DeliveryTime',
        '产品型号': 'ProduceModel',
        '产品件号': 'ProducePart',
        '盘车齿轮': 'TurningGear',
        '制动器电压': 'BrakeVoltage',
        '连接方式': 'ConnectMethod',
        '编码器型号': 'EncoderModel',
        '编码器线长': 'EncoderLineLength',
        '软管': 'Hose',
        '变频器': 'FrequencyConverter',
        '护罩': 'Shield',
        '远程松闸': 'RemoteGateRelease',
        '松闸线长': 'LooseGateLineLength',
        '额定载重': 'RatedLoad',
        '额定速度': 'RatedSpeed',
        '额定电压': 'RatedVoltage',
        '铭牌要求': 'NameplateRequirements',
        '曳引比': 'tractionRatio',
        '客户型号': 'CustomerModel',
        '箱板刷字': 'BoxBoardBrushing',
        '是否是出口梯': 'IsExport',
        '项目名称': 'ProjectName',
        '接受编号': 'AcceptNo',
        '入库时间': 'InStoreTime',
        '出厂编号': 'FactoryNo',
        '批次': 'BatchNo',
        '主机未完成原因': 'HostUnCompleteReason',
        '部件代码': 'PartCode',
        '上行超速保护代码': 'UpOverSpeedProtectCode',
        '意外移动保护代码': 'AccidentMoveProtectCode',
        '制动器编号1': 'BreakNoOne',
        '制动器编号2': 'BreakNoTwo',
        '唛头纸尺寸': 'ShippingMarkPaperSize',
        '参数校验': 'ParameterCheck',
        '备注': 'Remark'
      }

      // 解析Excel文件
      parseExcelFile(file, headerRowIndex)
        .then(data => {
          // 将解析后的数据映射到实体
          const entities = data.map(row => {
            const entity = {}

            // 遍历字段，将Excel数据映射到实体属性
            Object.keys(row).forEach(excelHeader => {
              // 根据Excel表头找到对应的实体属性名
              const entityProperty = headerMapping[excelHeader]
              if (entityProperty && row[excelHeader] !== undefined && row[excelHeader] !== null && row[excelHeader] !== '') {
                // 处理日期字段
                if (entityProperty === 'DeliveryTime' || entityProperty === 'InStoreTime') {
                  // 如果是日期字段，确保格式正确
                  const dateValue = row[excelHeader]
                  if (dateValue instanceof Date) {
                    entity[entityProperty] = dateValue.toISOString()
                  } else if (typeof dateValue === 'string' && dateValue.trim() !== '') {
                    // 尝试解析字符串日期
                    const parsedDate = new Date(dateValue)
                    if (!isNaN(parsedDate.getTime())) {
                      entity[entityProperty] = parsedDate.toISOString()
                    }
                  }
                } else {
                  // 其他字段直接赋值，转换为字符串
                  entity[entityProperty] = String(row[excelHeader]).trim()
                }
              }
            })

            // 如果从第二行D列提取到了装配日期，则为所有记录设置该日期
            if (extractedAssemblyDate) {
              entity.AssemblyDate = extractedAssemblyDate
            }

            return entity
          }).filter(entity => {
            // 过滤空行：检查实体是否有任何有效数据
            // 如果所有属性都为空或未定义，则认为是空行
            const hasValidData = Object.keys(entity).some(key => {
              const value = entity[key]
              return value !== undefined && value !== null && value !== ''
            })

            // 额外检查：至少要有一个关键字段有值才认为是有效行
            // 这里检查装配线号、定单号、合同号、产品型号、产品件号等关键字段
            const hasKeyField = entity.AssemblyLine || entity.OrderNo || entity.ContractNo ||
                               entity.ProduceModel || entity.ProducePart || entity.FactoryNo

            return hasValidData && hasKeyField
          })

          // 发送解析后的数据到后端
          return importExcelData(entities)
        })
        .then(response => {
          if (response.Code === 2000) {
            let successMessage = '导入成功'
            if (extractedAssemblyDate) {
              const assemblyDateStr = new Date(extractedAssemblyDate).toLocaleDateString('zh-CN')
              successMessage += `，已自动设置装配日期为：${assemblyDateStr}`
            }
            this.$notify({
              title: '成功',
              message: successMessage,
              type: 'success',
              duration: 3000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '导入失败',
              type: 'error',
              duration: 2000
            })
          }
          this.isProcessing = false
        })
        .catch(error => {
          console.error('导入失败:', error)
          this.$notify({
            title: '错误',
            message: '解析Excel文件失败，请检查文件格式',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        })
    },
    // 加载打印模板
    loadPrintTemplates() {
      getByTemplateKey('HostShippingMark').then(response => {
        if (response.Code === 2000 && response.Data) {
          this.printTemplates = response.Data
        } else {
          console.warn('未找到HostShippingMark模板')
          this.printTemplates = []
        }
      }).catch(error => {
        console.error('加载打印模板失败:', error)
        this.printTemplates = []
      })
    },
    // 加载表单中的打印模板列表
    loadPrintTemplateList() {
      getByTemplateKey('HostShippingMark').then(response => {
        if (response.Code === 2000 && response.Data) {
          this.printTemplateList = response.Data
        } else {
          console.warn('未找到HostShippingMark模板')
          this.printTemplateList = []
        }
      }).catch(error => {
        console.error('加载打印模板列表失败:', error)
        this.printTemplateList = []
      })
    },
    // 处理模板变更
    handleTemplateChange(templateId) {
      if (templateId) {
        const template = this.printTemplateList.find(t => t.Id === templateId)
        if (template) {
          // 可以在这里添加模板变更后的逻辑
          console.log('选择的模板:', template.TemplateName)
        }
      }
    },
    // 处理打印
    handlePrint() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要打印的记录')
        return
      }

      console.log('选中的记录:', this.multipleSelection)
      console.log('可用模板列表:', this.printTemplateList)

      // 检查选中记录是否都有模板ID
      const recordsWithoutTemplate = this.multipleSelection.filter(item => !item.PrintTemplateId)
      if (recordsWithoutTemplate.length > 0) {
        this.$message.error('选中的记录中有未设置打印模板的，请先设置打印模板')
        return
      }

      // 准备打印数据，为每条记录找到对应的模板
      const printItems = []
      const paperSizeValidationErrors = []

      this.multipleSelection.forEach(item => {
        const template = this.printTemplateList.find(t => t.Id === item.PrintTemplateId)
        if (template) {
          // 从记录的纸张尺寸字段解析尺寸
          const recordPaperSize = PaperSizeUtils.parsePaperSizeFromRecord(item)
          if (recordPaperSize) {
            const identifiedSize = PaperSizeUtils.identifyPaperSize(recordPaperSize.width, recordPaperSize.height)

            if (identifiedSize) {
              printItems.push({
                record: item,
                template: template,
                paperSize: identifiedSize
              })
              console.log(`记录 ${item.FactoryNo} 使用模板: ${template.TemplateName}, 纸张: ${PaperSizeUtils.formatPaperSizeInfo(identifiedSize)}`)
            } else {
              paperSizeValidationErrors.push(`记录 ${item.FactoryNo} 包含不支持的纸张尺寸 (${recordPaperSize.width}×${recordPaperSize.height}mm)`)
            }
          } else {
            paperSizeValidationErrors.push(`记录 ${item.FactoryNo} 的纸张尺寸字段为空或格式不正确: ${item.ShippingMarkPaperSize || item.PaperSize || '无'}`)
          }
        } else {
          console.warn('未找到记录 ' + item.FactoryNo + ' 的模板 ID: ' + item.PrintTemplateId)
        }
      })

      // 检查纸张尺寸解析错误
      if (paperSizeValidationErrors.length > 0) {
        this.$message.error('纸张尺寸校验失败：\n' + paperSizeValidationErrors.join('\n'))
        return
      }

      if (printItems.length === 0) {
        this.$message.error('未找到有效的打印模板')
        return
      }

      // 按纸张尺寸、方向和PrintDirection字段分组进行校验
      const groupedByPaperSize = {}
      printItems.forEach(item => {
        const printDirection = item.record.PrintDirection || ''
        const sizeKey = `${item.paperSize.size}_${item.paperSize.orientation}_${printDirection}`
        if (!groupedByPaperSize[sizeKey]) {
          groupedByPaperSize[sizeKey] = {
            paperSize: item.paperSize,
            printDirection: printDirection,
            items: []
          }
        }
        groupedByPaperSize[sizeKey].items.push(item)
      })

      const paperSizeGroups = Object.keys(groupedByPaperSize)
      console.log('纸张尺寸和打印方向分组:', groupedByPaperSize)

      // 严格校验：同一次打印只能选择相同纸张尺寸、方向和打印方向的模板
      if (paperSizeGroups.length > 1) {
        const sizeNames = Object.values(groupedByPaperSize).map(group => {
          const directionInfo = group.printDirection ? ` (打印方向: ${group.printDirection})` : ''
          return PaperSizeUtils.formatPaperSizeInfo(group.paperSize) + directionInfo
        })
        this.$message.error(`选中的记录包含多种纸张尺寸和打印方向，同一次打印只能选择相同纸张尺寸和打印方向的模板：\n${sizeNames.join('\n')}`)
        return
      }

      // 执行打印 - 传递所有打印项
      this.printWithDynamicTemplate(printItems)
    },
    // 使用动态模板打印
    printWithDynamicTemplate(printItems) {
      this.printLoading = true

      try {
        console.log('开始动态模板打印，打印项数量:', printItems.length)

        // 使用第一行记录的纸张尺寸作为统一标准
        const firstRecordPaperSize = printItems[0].paperSize
        console.log('使用第一行记录的纸张尺寸:', PaperSizeUtils.formatPaperSizeInfo(firstRecordPaperSize))

        // 构建多模板打印数据
        const templates = []

        printItems.forEach((item, index) => {
          try {
            console.log(`处理第 ${index + 1}/${printItems.length} 项: ${item.template.TemplateName}`)

            const templateJson = JSON.parse(item.template.TemplateJson)

            // 使用第一行记录的纸张尺寸设置所有模板
            if (templateJson.panels && templateJson.panels.length > 0) {
              templateJson.panels[0].width = firstRecordPaperSize.width
              templateJson.panels[0].height = firstRecordPaperSize.height
              console.log(`模板 ${item.template.TemplateName} 纸张尺寸已统一设置为: ${firstRecordPaperSize.width}×${firstRecordPaperSize.height}mm`)
            }

            const printData = {
              ContractNo: item.record.ContractNo,
              FactoryNo: item.record.FactoryNo,
              ProduceModel: item.record.ProduceModel,
              DeliveryCustomer: item.record.DeliveryCustomer,
              ProjectName: item.record.ProjectName,
              BatchNo: item.record.BatchNo,
              PackingSize: item.record.PackingSize,
              RatedPower: item.record.RatedPower == null ? '' : item.record.RatedPower + ' KW',
              PackingNetWeight: item.record.PackingNetWeight == null ? '' : item.record.PackingNetWeight + ' KG',
              PackingGrossWeight: item.record.PackingGrossWeight == null ? '' : item.record.PackingGrossWeight + ' KG',
              AssemblyLine: item.record.AssemblyLine,
              OrderNo: item.record.OrderNo,
              ProducePart: item.record.ProducePart,
              DeliveryTime: item.record.DeliveryTime,
              InStoreTime: item.record.InStoreTime,
              ShippingMarkPaperSize: item.record.ShippingMarkPaperSize,
              TemplateName: item.template.TemplateName
            }

            console.log('模板 ' + item.template.TemplateName + ' 的打印数据:', printData)

            // 创建模板实例
            const hiprintTemplate = new hiprint.PrintTemplate({ template: templateJson })

            // 添加到模板数组
            templates.push({
              template: hiprintTemplate,
              data: printData,
              options: {
                // 使用第一行记录的纸张尺寸设置打印选项
                leftOffset: 0,
                topOffset: 0,
                paperWidth: firstRecordPaperSize.width,
                paperHeight: firstRecordPaperSize.height
              }
            })
          } catch (error) {
            console.error('处理记录失败:', error)
            this.$message.error('处理模板 ' + item.template.TemplateName + ' 失败: ' + error.message)
          }
        })

        if (templates.length === 0) {
          this.$message.error('没有可打印的模板')
          this.printLoading = false
          return
        }

        console.log('准备打印，模板数量:', templates.length)

        // 使用hiprint多模板打印功能
        hiprint.print({
          templates: templates
        })

        // 打印成功后更新打印状态
        this.updatePrintStatus(printItems)

        this.printLoading = false
        const paperSizeInfo = PaperSizeUtils.formatPaperSizeInfo(firstRecordPaperSize)
        this.$message.success(`打印任务已发送，共 ${templates.length} 页，纸张规格：${paperSizeInfo}`)
      } catch (error) {
        console.error('打印失败:', error)
        this.$message.error('打印失败: ' + error.message)
        this.printLoading = false
      }
    },
    // 创建打印记录
    updatePrintStatus(printItems) {
      try {
        // 提取所有记录的ID
        const shippingMarkIds = printItems.map(item => item.record.Id)

        console.log('创建打印记录请求参数:', shippingMarkIds)

        // 调用批量创建打印记录接口
        createPrintRecords(shippingMarkIds).then(response => {
          if (response.Code === 2000) {
            console.log('打印记录创建成功:', response.Message)
            // 刷新列表以显示最新状态
            this.getList()
          } else {
            console.warn('打印记录创建失败:', response.Message)
            this.$message.warning('打印成功，但记录创建失败: ' + response.Message)
          }
        }).catch(error => {
          console.error('打印记录创建异常:', error)
          this.$message.warning('打印成功，但记录创建异常: ' + error.message)
        })
      } catch (error) {
        console.error('构建打印记录创建请求失败:', error)
        this.$message.warning('打印成功，但记录创建失败: ' + error.message)
      }
    },
    // 查看打印记录明细
    handleViewPrintRecords(row) {
      this.currentShippingMarkId = row.Id
      this.printRecordsDialogVisible = true
      this.loadPrintRecords(row.Id)
    },
    // 加载打印记录
    loadPrintRecords(shippingMarkId) {
      this.printRecordsLoading = true
      getPrintRecordsByShippingMarkId(shippingMarkId).then(response => {
        if (response.Code === 2000) {
          this.printRecordsList = response.Data || []
        } else {
          this.$message.error('获取打印记录失败: ' + response.Message)
          this.printRecordsList = []
        }
        this.printRecordsLoading = false
      }).catch(error => {
        console.error('获取打印记录失败:', error)
        this.$message.error('获取打印记录失败: ' + error.message)
        this.printRecordsList = []
        this.printRecordsLoading = false
      })
    }
  }
}
</script>

<style scoped>

.print-container {
  padding: 20px;
  font-family: Arial, sans-serif;
  width: 297mm; /* A4 横向宽度 */
  height: 210mm; /* A4 横向高度 */
  margin: 0 auto;
}

.print-header {
  text-align: center;
  margin-bottom: 20px;
}

.print-title {
  font-size: 24px;
  font-weight: bold;
}

.print-content {
  margin-bottom: 20px;
}

.print-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ddd;
  table-layout: fixed; /* 固定表格布局 */
}

.print-table td {
  padding: 8px;
  border: 1px solid #ddd;
}

.print-label {
  font-weight: bold;
  width: 20%;
  background-color: #f5f7fa;
}

.print-value {
  width: 30%;
}

@media print {
  body * {
    visibility: hidden;
  }
  .print-container, .print-container * {
    visibility: visible;
  }
  .print-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  @page {
    size: A4 landscape; /* A4 横向 */
    margin: 0;
  }
  .el-dialog__wrapper {
    display: none !important;
  }
  .el-dialog__footer {
    display: none !important;
  }
  @page {
    size: A4 landscape; /* A4 横向 */
    margin: 0;
  }
}

/* 参数缺失行的样式 */
::v-deep .parameter-missing-row {
  background-color: #fef0f0 !important;
}

::v-deep .parameter-missing-row:hover {
  background-color: #fde2e2 !important;
}

/* 打印状态圆形指示器样式 */
.print-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.status-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;
}

.status-printed {
  background-color: #67c23a; /* 绿色 - 已打印 */
}

.status-unprinted {
  background-color: #f56c6c; /* 红色 - 未打印 */
}

.status-text {
  font-size: 12px;
  color: #606266;
}

/* 参数状态五角星指示器样式 */
.parameter-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.parameter-star {
  font-size: 16px;
  flex-shrink: 0;
}

.parameter-normal {
  color: #67c23a; /* 绿色 - 参数正常 */
}

.parameter-missing {
  color: #f56c6c; /* 红色 - 参数缺失 */
}

.parameter-text {
  font-size: 12px;
  color: #606266;
}
</style>
