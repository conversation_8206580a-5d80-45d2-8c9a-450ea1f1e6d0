<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />

      <el-select
        v-model="isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.EnumKey"
          :label="item.EnumValue"
          :value="item.EnumKey"
        />
      </el-select>

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-permission="{ name: 'PP.PP_StockingScan.Delete' }"
        v-waves
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}</el-button>

      <el-button
        v-permission="{ name: 'PP.PP_StockingScan.Posting' }"
        v-waves
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="deletable"
        @click="handlePosting"
      >{{ $t("Common.posting") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_StockingScan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <!-- <el-button v-waves class="filter-item" type="primary" icon="el-icon-document" @click="handleExport">{{ $t('Common.export') }}</el-button> -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingScan.ScanID')"
        prop="ScanID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.DocNum')"
        prop="DocNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.WaveNum')"
        prop="WaveNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.StockingScan.BaseEntry')" prop="BaseEntry"   align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.BaseEntry }}</span>
            </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.StockingScan.BaseNum')"
        prop="BaseNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.StockingScan.BaseLine')" prop="BaseLine"   align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.BaseLine }}</span>
            </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.StockingScan.BarCode')"
        prop="BarCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.ItemCode')"
        prop="ItemCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.ItemName')"
        prop="ItemName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PP.StockingScan.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.StockingScan.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.Unit')"
        prop="Unit"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.PLine')"
        prop="PLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.BatchNum')"
        prop="BatchNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PP.StockingScan.OutWhsCode')"
        prop="OutWhsCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutWhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.OutWhsName')"
        prop="OutWhsName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingScan.OutRegionCode')"
        prop="OutRegionCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.OutRegionName')"
        prop="OutRegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingScan.OutBinLocationCode')"
        prop="OutBinLocationCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.OutBinLocationName')"
        prop="OutBinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PP.StockingScan.InWhsCode')"
        prop="InWhsCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.InWhsName')"
        prop="InWhsName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingScan.InRegionCode')"
        prop="InRegionCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.InRegionName')"
        prop="InRegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingScan.InBinLocationCode')"
        prop="InBinLocationCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.InBinLocationName')"
        prop="InBinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.IsPosted')"
        prop="IsPosted"

        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | posting }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.ERPDocNum')"
        prop="ERPDocNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.PostUser')"
        prop="PostUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingScan.PostTime')"
        prop="PostTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime | datetime }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  fetchPage,
  add,
  update,
  batchDelete,
  postToSAP,
  exportExcelFile,
  delList
} from '@/api/PP/PP_StockingScan';
import { exportToExcel } from '@/utils/excel-export';
import { convertToKeyValue } from '@/utils';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'PP.PP_StockingScan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        isPosted: false
      },
      isPosted: 1,
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      multipleSelection: [],
      isPostedOptions: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) {
        return true;
      }
      while (i--) {
        if (this.multipleSelection[i].IsPosted) {
          return true;
        }
      }
    }
  },
  created() {
    this.getDict('SYS001').then(data => {
      this.isPostedOptions = data;
      this.getList();
    });
  },
  methods: {
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';

      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }

      if (this.isPosted == 1) this.listQuery.isPosted = false;
      else if (this.isPosted == 2) this.listQuery.isPosted = true;
      else this.listQuery.isPosted = '';
      var newListQuery = {
        keyword: this.listQuery.keyword, // 用户姓名 | 登录账号
        PageNumber: this.listQuery.PageNumber,
        PageSize: this.listQuery.PageSize,
        dateTimes: this.dateRangeValue,
        isPosted: this.listQuery.isPosted
      };
      fetchPage(newListQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          var tmpRow = selectRows.find(val => val.IsPosted);
          if (tmpRow) {
            this.showNotify('error', 'Common.operationNotPermitted');
            return;
          }
          this.isProcessing = true;

          var arrRowsID = selectRows.map(function(v) {
            return v.ScanID;
          });

          // 删除逻辑处理
          delList({
            Data: selectRows
          })
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handlePosting() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('ui.PP.StockingScan.postToSAPConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          var waveNums = selectRows.map(x => x.DocNum);
          postToSAP(waveNums)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                if (response.Data == false) {
                  this.showNotify('error', 'Common.operationFailed');
                } else {
                  this.showNotify('success', 'Common.operationSuccess');
                }
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword, // 用户姓名 | 登录账号
        dateTimes: this.dateRangeValue,
        state: this.listQuery.isPosted
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '生产备货管理')
      );
    }
  }
};
</script>
