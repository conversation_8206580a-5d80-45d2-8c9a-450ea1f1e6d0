<template>
  <div class="app-container">
    <!-- <div slot="header" class="clearfix">
        <span style="font-size:24px;font-weight:bold;color:#FF7400">
            法拉鼎WMS看板-生产备料看板
        </span>
    </div> -->
    <div class="filter-container">
      <el-select v-model="listQuery.productionLine" filterable placeholder="生产线" @change="handleChangePLine">
        <el-option v-for="item in pLineOptions" :key="item.EnumValue" :value="item.EnumValue" :label="item.Remark" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit :header-cell-style="{background:'#eef1f6',color:'#606266'}" highlight-current-row style="width: 100%">
      <el-table-column label="生产订单号" prop="ProductionOrderID" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ProductionOrderID }}</span> </template> </el-table-column>
      <el-table-column label="产品编号" prop="ProductID" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ProductID }}</span> </template> </el-table-column>
      <el-table-column label="产品名称" prop="ProductDescription" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ProductDescription }}</span> </template> </el-table-column>
      <el-table-column label="生产线" prop="ProductionLine" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ProductionLine }}</span> </template> </el-table-column>
      <el-table-column label="线边仓" prop="BinLocationName" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BinLocationName }}</span> </template> </el-table-column>
      <el-table-column label="原料编号" prop="MaterialID" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.MaterialID }}</span> </template> </el-table-column>
      <el-table-column label="原料基本量" prop="BOMQty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BOMQty }}</span> </template> </el-table-column>
      <el-table-column label="原料计划用量" prop="PlanQty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.PlanQty }}</span> </template> </el-table-column>
      <el-table-column label="看板备料数量" prop="StockingQty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.StockingQty }}</span> </template> </el-table-column>
      <el-table-column label="线边仓库存" prop="BufferQty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BufferQty }}</span> </template> </el-table-column>
      <el-table-column label="已备料" prop="PreparedQty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.PreparedQty }}</span> </template> </el-table-column>
      <el-table-column label="已投料" prop="FeededQty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.FeededQty }}</span> </template> </el-table-column>
      <el-table-column label="预警状态" prop="WarningStatus" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.WarningStatus }}</span> </template> </el-table-column>
      <el-table-column label="数据更新时间" prop="CTime" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.CTime|datetime }}</span> </template> </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  fetchAllList,
  generateData
} from '@/api/PP/PP_MaterialReqKanban'
// import { exportToExcel } from '@/utils/excel-export'
import waves from '@/directive/waves'; // waves directive 特效
import {
  setInterval,
  clearInterval
} from 'timers';

export default {
  name: 'PP.PP_MaterialReqKanban',
  components: {},
  directives: {
    waves
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        productionLine: ''
      },
      downloadLoading: false,
      pLineOptions: [],
      timerHandlerr: undefined, // 定时器相关,
      refreshInterval: 30 // 刷新时间间隔
    };
  },
  created() {
    this.getDict('PP005').then(data => {
      data = _.uniqBy(data, x => x.EnumValue);
      data = _.sortBy(data, x => x.EnumValue);
      this.pLineOptions = data;
      if (this.listQuery.productionLine) {} else {
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        if (this.pLineOptions && this.pLineOptions.length > 0) {
          this.listQuery.productionLine = this.pLineOptions[0].EnumValue
        }
      }
      this.getList();
    });
    this.startTimer();
  },
  methods: {
    clearTimer() {
      if (this.timerHandler) {
        clearInterval(timerHandler)
      }
    },
    startTimer() {
      this.generateKanbanData();
      this.clearTimer();
      this.timerHandler = setInterval(() => {
        this.generateKanbanData()
      }, this.refreshInterval * 1000 * 60 * 60)
    },
    getRefreshInterval() {
      this.getDict('PP007').then(data => {
        if (data && data.length > 0) {
          this.refreshInterval = data[0].EnumValue
        }
      })
    },
    getList() {
      // this.listLoading = true
      fetchAllList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data
        } else {
          this.showNotify('error', response.Message)
        }
        // this.listLoading = false
      });
    },
    generateKanbanData() {
      var startDate = new Date();
      generateData().then(response => {
        var endDate = new Date();
        console.log('生成看板数据结束:开始时间[' + startDate + ']...结束时间[' + endDate + ']')
      })
    },
    handleChangePLine() {
      this.handleFilter();
    },
    handleFilter() {
      this.getList();
    }
  }
};
</script>
