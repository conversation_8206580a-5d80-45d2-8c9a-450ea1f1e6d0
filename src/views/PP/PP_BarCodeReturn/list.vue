<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCodeReturn.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCodeReturn.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCodeReturn.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCodeReturn.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCodeReturn.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="deltailSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.BarCodeReturn.BarID')"
        prop="BarID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.BarCode')"
        prop="BarCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.Unit')"
        prop="Unit"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.BatchNum')"
        prop="BatchNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.ItemCode')"
        prop="ItemCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.ItemName')"
        prop="ItemName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column :label="$t('ui.PP.BarCodeReturn.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="140">
            <template slot-scope="scope">
                <span>{{ scope.row.ItmsGrpCode }}</span>
            </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PP.BarCodeReturn.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="220">
            <template slot-scope="scope">
                <span>{{ scope.row.ItmsGrpName }}</span>
            </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.PTime')"
        prop="PTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.PrintTemplate')"
        prop="PrintTemplate"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PrintTemplate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.BarCodeReturn.RegionCode')"
        prop="RegionCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.RegionName')"
        prop="RegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.BarCodeReturn.BinLocationCode')"
        prop="BinLocationCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCodeReturn.BinLocationName')"
        prop="BinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.BarCodeReturn.IsUsed')"
        prop="IsUsed"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsUsed }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime|datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  fetchPage,
  add,
  update,
  batchDelete,
  exportExcelFile
} from '@/api/PP/PP_BarCodeReturn';
import { exportToExcel } from '@/utils/excel-export';
import { printBarCodeToPDF } from '@/api/PP/PP_Print';

// import {
//     aa as fetchPrintTemplateList
// } from '@/api/MD/MD_PrintTemplate'
import { convertToKeyValue } from '@/utils';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'PP.PP_BarCodeReturn',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        dateRangeValue: [
          new Date(),
          new Date()
        ]
      },
      formData: {
        // 表单数据
        BarID: '',
        BarCode: '',
        BatchNum: '',
        PTime: '',
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: '',
        Unit: '',
        PrintTemplate: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: '',
        Remark: ''
      },
      formTitle: '', // 弹窗标题
      formMode: '',
      dialogFormVisible: false, // 表单窗口是否显示标志
      rules: {
        // 表单校验逻辑
        UserName: [
          {
            required: true,
            message: '用户名必须输入',
            trigger: 'change'
          }
        ]
      },
      downloadLoading: false,
      textMap: {
        create: this.$i18n.t('Common.add'),
        update: this.$i18n.t('Common.edit')
      },
      temp: {},
      multipleSelection: [],
      printTemplateOptions: [],
      dialogStatus: 'create',
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      isProcessing: false
    };
  },
  computed: {
    selective() {
      return (
        this.multipleSelection.length > 1 || this.multipleSelection.length === 0
      );
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    // this.getPrintTemplateList()
    this.getList();
  },
  methods: {
    // fetchPrintTemplateList,
    deltailSortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.listQuery.dateRangeValue) {
        this.listQuery.fromTime = this.listQuery.dateRangeValue[0];
        this.listQuery.toTime = this.listQuery.dateRangeValue[1];
      }

      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    // getPrintTemplateList() {
    //     fetchPrintTemplateList().then(response => {
    //         if (response.Code === 2000) {
    //             this.printTemplateOptions = response.Data.items;
    //         }
    //     })
    // },
    clearTables() {
      this.list = [];
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleCreate() {
      this.routeTo('PP.PP_BarCodeReturnEdit');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        var currentRow = Object.assign({}, selectRows[0]);
        if (currentRow.IsUsed) {
          this.showNotify('error', 'Common.operationNotPermitted');
        } else {
          console.log(currentRow);
          this.routeTo('PP.PP_BarCodeReturnEdit', currentRow);
        }
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          var tmpRow = selectRows.find(val => val.IsUsed);
          if (tmpRow) {
            this.showNotify('error', 'Common.operationNotPermitted');
            return;
          }
          this.isProcessing = true;

          var arrRowsID = selectRows.map(function(v) {
            return v.BarID;
          });

          batchDelete(arrRowsID)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                if (response.Data > 0) {
                  this.showNotify('success', 'Common.deleteSuccess');
                  this.handleFilter();
                } else {
                  this.showNotify('error', 'Common.operationFailed');
                }
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleExport() {
      exportExcelFile({ Keyword: this.listQuery.keyword }).then(res =>
        exportToExcel(res.data, '生产退料标签管理')
      );
    },
    handlePrint() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        printBarCodeToPDF({
          barcodes: selectRows.map(x => x.BarCode),
          templateCode: selectRows[0].PrintTemplate
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
        });
      }
    },
    handleRowClick(row, col, evt) {
      Object.assign(this.formData, row);
    }
  }
};
</script>
