<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('ui.PP.BarCodeReturn.title') }}</label>
    </p>
    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-delete"
        @click="handleClear"
      >{{ $t('Common.empty') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleConfirm"
      >{{ $t('Common.confirm') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-back"
        @click="handleCancel"
      >{{ $t('Common.cancel') }}</el-button>
    </div>
    <el-form
      ref="dataForm"
      :inline="true"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <el-form-item :label="$t('Base.BinLocation.BinLocationName')" prop="BinLocationCode">
        <el-select
          v-model="temp.BinLocationCode"
          filterable
          style="width: 200px"
          :disabled="editStatus=='edit'"
          @change="handleBinLocationChange"
        >
          <el-option
            v-for="item in binLocationOptions"
            :key="item.BinLocationCode"
            :label="item.BinLocationName"
            :value="item.BinLocationCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCodeReturn.ItemCode')" prop="ItemCode">
        <el-input
          v-model="temp.ItemCode"
          readonly
          style="width: 200px"
          :disabled="editStatus=='edit'"
        >
          <el-button
            slot="append"
            icon="el-icon-more"
            :disabled="editStatus=='edit'"
            @click="handleStockBtnClick"
          />
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCodeReturn.ItemName')" prop="ItemName">
        <el-input v-model="temp.ItemName" style="width: 200px" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCodeReturn.BarCode')" prop="BarCode">
        <el-input v-model="temp.BarCode" style="width: 200px" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.PTime')" prop="PTime">
        <el-date-picker v-model="temp.PTime" style="width: 200px" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCodeReturn.BatchNum')" prop="BatchNum">
        <el-input v-model="temp.BatchNum" readonly style="width: 200px" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCodeReturn.Qty')" prop="Qty">
        <el-input-number
          v-model="temp.Qty"
          controls-position="right"
          style="width: 200px"
          :max="maxQty"
          :min="0"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCodeReturn.PrintTemplate')" prop="PrintTemplate">
        <el-select v-model="temp.PrintTemplate" filterable style="width: 200px">
          <el-option
            v-for="item in printTemplateOptions"
            :key="item.TempleteDesc"
            :label="item.TempleteDesc"
            :value="item.TempleteFile"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <el-dialog :title="$t('Common.selectMaterial')" :visible.sync="dialogFormVisible">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          :placeholder="$t('Common.keyword')"
          style="width: 200px"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >{{ $t('Common.search') }}</el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :title="$t('ui.MD.Stock.title')"
        :data="list"
        border
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        height="350px"
        @sort-change="sortChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          v-if="false"
          :label="$t('ui.MD.Stock.StockID')"
          prop="StockID"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.StockID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.MD.Stock.SupplierCode')"
          prop="SupplierCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.MD.Stock.SupplierName')"
          prop="SupplierName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.BarCode')"
          prop="BarCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BarCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.ItemCode')"
          prop="ItemCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.ItemName')"
          prop="ItemName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column
          :label="$t('ui.MD.Stock.ItmsGrpCode')"
          prop="ItmsGrpCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItmsGrpCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.ItmsGrpName')"
          prop="ItmsGrpName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItmsGrpName }}</span>
          </template>
        </el-table-column>-->
        <el-table-column
          :label="$t('ui.MD.Stock.Qty')"
          prop="Qty"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Qty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.Unit')"
          prop="Unit"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Unit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.BatchNum')"
          prop="BatchNum"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BatchNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.SupplierBatch')"
          prop="SupplierBatch"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierBatch }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.PTime')"
          prop="PTime"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.PTime|date }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.RegionCode')"
          prop="RegionCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.RegionCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.RegionName')"
          prop="RegionName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.RegionName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.BinLocationCode')"
          prop="BinLocationCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BinLocationCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.BinLocationName')"
          prop="BinLocationName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BinLocationName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.IsDelete')"
          prop="IsDelete"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.IsDelete }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.CUser')"
          prop="CUser"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.CUser }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.CTime')"
          prop="CTime"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.CTime|datetime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.MUser')"
          prop="MUser"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.MUser }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.MTime')"
          prop="MTime"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.MTime|datetime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.DUser')"
          prop="DUser"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.DUser }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('Common.DTime')"
          prop="DTime"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.DTime|datetime }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.PageNumber"
        :limit.sync="listQuery.PageSize"
        @pagination="getList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button
          v-waves
          type="primary"
          icon="el-icon-plus"
          @click="handleSelectBtnClick"
        >{{ $t('Common.select') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { add, update } from '@/api/PP/PP_BarCodeReturn';
import { fetchListByRegionCode as fetchBinLocationList } from '@/api/MD/MD_BinLocation';
import { fetchPage as fetchStockPage } from '@/api/MD/MD_Stock';
import { fetchNewBarcode } from '@/api/MM/MM_BarCode';
import { fetchTemplate as fetchPrintTemplate } from '@/api/MD/MD_LabelTemplate';
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination

_ = require('lodash');

export default {
  name: 'PP.PP_BarCodeReturnEdit',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    var validateQty = (rule, value, callback) => {
      // 自定义验证器
      if (value > 0) {
        callback();
      } else if (value <= 0) {
        callback(new Error(this.$i18n.t('Common.mustBeGreaterThanZero')));
      } else {
        callback(new Error(this.$i18n.t('Common.numberRequired')));
      }
    };
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        keyword: '',
        regionCode: ''
      },
      temp: {
        BarID: '',
        BarCode: '',
        BatchNum: '',
        PTime: new Date(0),
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: 0,
        Unit: '',
        PrintTemplate: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: '',
        Remark: ''
      },
      maxQty: Infinity,
      binLocationOptions: [],
      printTemplateOptions: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        selectMaterial: this.$i18n.t('Common.selectMaterial')
      },
      editStatus: 'create',

      rules: {
        // 表单校验逻辑
        PrintTemplate: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'blur'
          }
        ],
        Qty: [
          {
            required: true,
            trigger: 'change'
          },
          {
            validator: validateQty,
            trigger: 'change'
          }
        ],
        ItemCode: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'blur'
          }
        ],
        BinLocationCode: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ],
        BarCode: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'blur'
          }
        ]
      }
    };
  },
  created() {
    this.getPrintTemplateList();
    this.getBinLocationList();
    this.temp = this.$route.params;
    if (this.temp.BarID) {
      this.editStatus = 'edit';
    } else {
      this.editStatus = 'create';
    }
  },
  methods: {
    getPrintTemplateList() {
      fetchPrintTemplate({ templateType: 41 }).then(response => {
        if (response.Code === 2000) {
          this.printTemplateOptions = response.Data;
          this.temp.PrintTemplate = this.printTemplateOptions[0].TempleteDesc;
        }
      });
    },
    getBinLocationList() {
      fetchBinLocationList({
        regionCode: 'BK1'
      })
        .then(response => {
          if (response.Code === 2000) {
            this.binLocationOptions = response.Data;
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    getList() {
      this.listLoading = true;
      this.listQuery.regionCode = 'BK1';
      this.listQuery.binLocationCode = this.temp.BinLocationCode;
      fetchStockPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    resetTemp() {
      this.temp = {
        BarID: '',
        BarCode: '',
        BatchNum: '',
        PTime: new Date(0),
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: 0,
        Unit: '',
        PrintTemplate: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: '',
        Remark: ''
      };
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleBinLocationChange() {
      this.temp.PTime = '';
      this.temp.ItemCode = '';
      this.temp.ItemName = '';
      this.temp.BarCode = '';
      this.temp.BatchNum = '';
      this.temp.Qty = 0;
    },
    handleStockBtnClick() {
      this.dialogStatus = 'select';
      this.dialogFormVisible = true;
      this.getList();
    },
    handleRowClick(row, col, evt) {
      const newRow = row;
      this.maxQty = row.Qty;
      // newRow.Qty = 0
      Object.assign(this.temp, newRow);
      this.dialogFormVisible = false;
      // this.getNewBarcode()
    },
    handleSelectBtnClick() {
      this.dialogFormVisible = false;
    },
    handleClear() {
      this.resetTemp();
    },
    handleConfirm() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          var isChoose = this.printTemplateOptions.find(
            x => x.TempleteDesc == this.temp.PrintTemplate
          );
          if (isChoose) {
            // 判断是否是key值，如果是key值换成value值
            this.temp.PrintTemplate = isChoose.TempleteFile;
          }
          if (this.editStatus == 'edit') {
            update(this.temp).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.backTo('PP.PP_BarCodeReturn');
              } else {
                this.showNotify('error', response.Message);
              }
            });
          } else {
            add(this.temp).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.backTo('PP.PP_BarCodeReturn');
              } else {
                this.showNotify('error', response.Message);
              }
            });
          }
        }
      });
    },
    handleCancel() {
      this.backTo('PP.PP_BarCodeReturn');
    }
  }
};
</script>
