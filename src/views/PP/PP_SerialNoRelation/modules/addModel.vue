<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form :model="model" label-width="100px">
        <!-- <el-form-item label="移动类型" label-width="100px">
          <el-select v-model="model.MovementType" filterable placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="评估类型">
          <el-select
            v-model="model.AssessmentType"
            style="width: 100%;"
            filterable
            placeholder="请选择"
            :disabled="disabled"
            @change="changeType"
          >
            <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单数量">
          <el-input v-model="model.DemandQty" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZ_SAP
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        DemandQty: '',
        AssessmentType: ''
      },
      options: [{
        value: '261',
        label: '工废'
      }, {
        value: '311',
        label: '料废'
      }],
      disabled: true,
      types: [{
        value: '01',
        label: '自制'
      },
      {
        value: '02',
        label: '外购'
      }
      ]
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.model = Object.assign({}, record);
      if (record.AssessmentCategory === 'B') {
        this.disabled = false;
        this.model.AssessmentType = '01';
      }
      this.drawer = true;
    },
    handleSave() {
      this.$emit('ok', this.model);
      this.drawer = false;
    },
    changeType(e) {
      this.model.AssessmentTypeName = this.types.filter(item => item.value === e)[0].label;
      console.log(this.model);
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
