<template>
  <div class="app-container">
    <p>
      <label style="width:100%">超额领料申请登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox "
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="超额领料单号">
            <el-input v-model="searchQuery.OverReceiveNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生产订单">
            <el-input v-model="searchQuery.purchase" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型">
            <el-select v-model="searchQuery.MovementType" filterable placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="选择过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="转入仓库">
            <el-select v-model="searchQuery.DeliverLocation" filterable placeholder="请选择" @change="changeDeliverLocation">
              <el-option
                v-for="item in DeliverLocationOptions"
                :key="item.value"
                :label="item.value+'-'+item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上传附件">
            <el-upload
              class="upload-demo"
              :on-success="handleAvatarSuccess"
              :limit="1"
              action="customize"
              :file-list="fileList"
              :before-upload="beforeAvatarUpload"
              :accept="acceptFileType"
              :on-exceed="handleExceed"
              :on-preview="handlePreview"
              :on-change="handleChange"
              :http-request="uploadFile"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">上传附件只能是 JPG、PNG、BMP、PDF 格式!</div>
            </el-upload>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />

      <el-table-column label="移动类型" prop="MovementType" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MovementType === '261'">工废</span>
          <span v-if="scope.row.MovementType === '311'">料废</span>
        </template>
      </el-table-column>
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="SerialNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="客户订单号" prop="SalesOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="装配线" prop="ProductionLineDes" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="领料数量" prop="DemandQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="ComponentUnit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料组" prop="MaterialGroupDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="转出仓库" prop="OutWarehouse" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="转入仓库" prop="DeliverLocation" align="center" width="120" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <add-select-model ref="modalForm" @ok="modalFormOk" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  update,
  Upload
} from '@/api/PP/PP_OverPickingApplication';
import {
  GetXZ_SAP
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'PP.PP_OverPickingApplicationDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        OverReceiveNo: '',
        MovementType: '',
        FilePath: '',
        ManualPostTime: new Date(),
        Remark: '',
        DeliverLocation: '',
        DeliverLocationName: ''
      },
      multipleSelection: [],
      rules: {

      },
      editStatus: 'create',
      delList: [],
      action: '/PP/PP_OverReceive/Upload',
      options: [{
        value: '261',
        label: '工废'
      }, {
        value: '311',
        label: '料废'
      }],
      acceptFileType: '.jpeg,.png,.bmp,.pdf,.jpg',
      uploadTemplateFile: null,
      TempleteFile: null,
      fileList: [],
      DeliverLocationOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetXZ_SAP1();
  },
  activated() {
    if (Object.keys(this.$route.params).length > 0) {
      console.log('activated调用了', this.$route.params);
      if (this.$route.params.status === 'add') {
        this.searchQuery = {
          OverReceiveNo: '',
          purchase: '',
          MovementType: '',
          Remark: '',
          ManualPostTime: new Date(),
          DeliverLocation: ''
        };
        this.list = [];
        this.fileList = [];
      }
      this.getPageParams();
    }
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ID) {
            v.IsDelete = true;
            this.delList.push(v.ID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择生产订单信息');
        return;
      }
      if (this.searchQuery.MovementType === '') {
        this.showNotify('warning', '移动类型不能为空');
        return;
      }
      if (!this.searchQuery.ManualPostTime) {
        this.showNotify('warning', '过账时间不能为空');
        return;
      }
      let switchBtn = true;
      this.list.some(res => {
        if (res.DemandQty === null || res.DemandQty === 0 || res.DemandQty === '0' || res.DemandQty === '') {
          this.showNotify('warning', '需求数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.AssessmentCategory === 'B') {
          if (res.AssessmentType === null || res.AssessmentType === '') {
            this.showNotify('warning', '评估类型不能为空');
            switchBtn = false;
            return true;
          }
        }
      });

      if (switchBtn) {
        this.startLoading();
        const query = {
          OverReceiveNo: this.searchQuery.OverReceiveNo,
          FilePath: this.searchQuery.FilePath,
          FileName: this.TempleteFile,
          Remark: this.searchQuery.Remark,
          MovementType: this.searchQuery.MovementType,
          DetailList: this.list,
          DelDetailIds: this.delList,
          ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
          ID: this.searchQuery.ID
        };
        if (this.editStatus === 'create') {
          SubmitScanInfo(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PP.PP_OverPickingApplication');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        } else {
          update(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PP.PP_OverPickingApplication');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ProductionOrderNo + v.ComponentLineNo === record.ProductionOrderNo + record.ComponentLineNo) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.OverReceiveNo = response.Data;
          console.log(this.searchQuery);
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        v.DeliverLocation = this.searchQuery.DeliverLocation; // 转入仓库
        v.DeliverLocationName = this.searchQuery.DeliverLocationName;
        v.DemandQty = v.OutQty;
      });
      const obj = {};
      this.list = this.list.concat(record).reduce((cur, next) => {
        obj[next.ProductionOrderNo + next.ComponentLineNo] ? '' : obj[next.ProductionOrderNo + next.ComponentLineNo] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        overReceiveNo: this.searchQuery.OverReceiveNo
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        if (this.searchQuery.FilePath) {
          this.fileList = [{
            name: this.searchQuery.FileName,
            url: this.searchQuery.FilePath
          }]
        }
        this.getDetailList()
      } else {
        this.fetchDocNum();
        this.fileList = [];
        this.editStatus = 'create';
      }
    },
    handleAvatarSuccess(res, file) {
      this.searchQuery.FilePath = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      console.log(file);
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isBMP = file.type === 'image/bmp';
      const isPDF = file.type === 'application/pdf';
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isJPG && !isPNG && !isBMP && !isPDF) {
        this.$message.error('上传附件只能是 JPG、PNG、BMP、PDF 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 5MB!');
      }
      return isJPG && isLt2M;
    },
    uploadFile() {
      this.uploadLoading = this.$loading({
        lock: true,
        text: this.$t('ui.MD.MD_LabelTemplate.Uploading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      const fd = new FormData();
      fd.append('file', this.uploadTemplateFile);
      fd.append('TempleteFile', this.TempleteFile);
      Upload(fd).then(rsp => {
        this.uploadLoading.close();
        this.searchQuery.FilePath = rsp.Data.FilePath;
        this.fileList = [{
          name: this.TempleteFile,
          url: this.searchQuery.FilePath
        }];
        this.$notify({
          title: this.$t('Common.success'),
          message: this.$t('Common.operationSuccess'),
          type: 'success',
          duration: 2000
        });
        this.getList();
      })
        .catch(() => {
          this.uploadLoading.close();
        });
    },
    handlePreview(file) {
      window.open(his.API.BaseURL + this.searchQuery.FilePath);
    },
    beforeRemove(file, fileList) {
      console.log(this.fileList);
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    handleChange(file, fileList) {
      this.uploadTemplateFile = file.raw;
      this.TempleteFile = file.name;
      this.uploadFile();
    },
    GetXZ_SAP1() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.DeliverLocationOptions = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.DeliverLocationOptions.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeDeliverLocation(e) {
      const obj = this.DeliverLocationOptions.find(v => v.value === e);
      this.searchQuery.DeliverLocationName = obj.label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'DeliverLocationName', this.searchQuery.DeliverLocationName);
          this.$set(res, 'DeliverLocation', this.searchQuery.DeliverLocation);
        })
      }
      this.$forceUpdate();
    }
  }
}
</script>
