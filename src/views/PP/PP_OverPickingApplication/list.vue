<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.ExamineStatus"
        size="small"
        filterable
        placeholder="审核状态"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in ExamineStatusOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_OverPickingApplication.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.PlanReview' }"
        class="filter-item"
        type="primary"
        size="small"
        :disabled="deletable"
        icon="el-icon-document"
        @click="handleReview(1)"
      >计划审核
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.DepotReview' }"
        class="filter-item"
        type="primary"
        size="small"
        :disabled="deletable"
        icon="el-icon-document"
        @click="handleReview(2)"
      >仓库审核
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.CancelReview' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        :disabled="deletable"
        @click="handleCancelReview"
      >取消审核
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_OverPickingApplication.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-permission="{ name: 'PP.PP_OverPickingApplication.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-printer"
        :disabled="canNotUpdate"
        @click="handlePrint"
      >
        {{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_OverPickingApplication.Download' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-download"
        :disabled="canNotUpdate"
        @click="handleDownload"
      >下载附件
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="领料单号" prop="OverReceiveNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="移动类型" prop="MovementType" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MovementType === '261'">工废</span>
          <span v-if="scope.row.MovementType === '311'">料废</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="责任单位" prop="" align="center" width="120" show-overflow-tooltip /> -->
      <el-table-column label="附件" align="center" width="60" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.FilePath !== '' && scope.row.FilePath !== null">有</span>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" prop="ExamineStatus" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ExamineStatus === 0">未审核</span>
          <span v-if="scope.row.ExamineStatus === 1">计划审核</span>
          <span v-if="scope.row.ExamineStatus === 2">仓库审核</span>
        </template>
      </el-table-column>
      <el-table-column label="计划审核人" prop="PlanReviewer" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="计划审核时间"
        prop="PlannedTime"
        align="center"
        width="100"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="仓库审核人" prop="WarehouseReviewer" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="仓库审核时间"
        prop="WarehouseTime"
        align="center"
        width="100"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" show-overflow-tooltip />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="SAP过账时间"
        prop="ManualPostTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>超额领料明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="领料单号" prop="OverReceiveNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="移动类型" prop="MovementType" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MovementType === '261'">工废</span>
          <span v-if="scope.row.MovementType === '311'">料废</span>
        </template>
      </el-table-column>
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="SerialNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="客户订单号" prop="SalesOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="装配线" prop="ProductionLineDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="领料数量" prop="DemandQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="ComponentUnit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料组" prop="MaterialGroupDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="转出仓库" prop="OutWarehouse" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="转入仓库" prop="DeliverLocation" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="过账人" prop="PostUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="SAP过账时间"
        prop="ManualPostTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="SAP物料凭证号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="SAP物料凭证行号" prop="SapLine" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  GetDetailedPageList,
  batchDelete,
  exportExcelFile,
  Download,
  Examine,
  DoPost,
  printToPDF,
  CancelExamine
} from '@/api/PP/PP_OverPickingApplication';

export default {
  name: 'PP.PP_OverPickingApplication',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        ExamineStatus: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      ExamineStatusOptions: [{
        label: '全部审核',
        key: ''
      },
      {
        label: '未审核',
        key: 0
      },
      {
        label: '计划审核',
        key: 1
      }, {
        label: '仓库审核',
        key: 2
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/PP/PP_OverPickingApplication') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        ExamineStatus: this.listQuery.ExamineStatus
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '超领料申请');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
        if (v.ExamineStatus !== 0) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息已审核，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          const arrRowsID = selectRows.map(v => v.OverReceiveNo);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    // 取消审核
    handleCancelReview() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        // if (this.$store.getters.userRole !== 1) {
        //   if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
        //     this.showNotify("warning", '领料单号为：' + v.OverReceiveNo + '信息，您无权操作');
        //     switchBtn = false;
        //     this.isProcessing = false;
        //     return true;
        //   }
        // }
        if (v.IsPosted === true) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息已过账，禁止取消审核');
          switchBtn = false;
          return true;
        }
        if (v.ExamineStatus === 0) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息未审核，无法取消');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.isProcessing = true;
        const query = {
          status: 0,
          entities: this.multipleSelection
        };
        CancelExamine(query).then(res => {
          if (res.Code === 2000) {
            this.handleFilter();
            this.isProcessing = false;
            this.showNotify('success', '审核成功！');
          } else {
            this.isProcessing = false;
            this.showNotify('success', res.Message);
          }
        }).catch(err => {
          this.isProcessing = false;
        })
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = {
        overReceiveNo: this.currentRow.OverReceiveNo,
        PageNumber: this.listDetailQuery.PageNumber,
        PageSize: this.listDetailQuery.PageSize
      };
      GetDetailedPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('PP.PP_OverPickingApplicationDetail', {
        status: 'add'
      });
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.ExamineStatus === 1) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息已计划审核,请勿重复操作');
          switchBtn = false;
          return true;
        }
        if (v.ExamineStatus === 2) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息已仓库审核,请勿重复操作');
          switchBtn = false;
          return true;
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '领料单号为：' + v.OverReceiveNo + '信息已过账，禁止编辑');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('PP.PP_OverPickingApplicationDetail', this.multipleSelection[0]);
      }
    },
    // 审核
    handleReview(type) {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      if (type === 2) {
        // 仓库审核
        selectRows.some(res => {
          if (res.ExamineStatus === 0) {
            this.showNotify('warning', '领料单号为：' + res.OverReceiveNo + '信息未进行计划审核,请先计划审核操作');
            switchBtn = false;
            return true;
          }
          if (res.ExamineStatus === 2) {
            this.showNotify('warning', '领料单号为：' + res.OverReceiveNo + '信息已仓库审核,请勿重复操作');
            switchBtn = false;
            return true;
          }
        })
      } else if (type === 1) {
        // 计划审核
        selectRows.some(res => {
          if (res.ExamineStatus === 1) {
            this.showNotify('warning', '领料单号为：' + res.OverReceiveNo + '信息已计划审核,请勿重复操作');
            switchBtn = false;
            return true;
          }
          if (res.ExamineStatus === 2) {
            this.showNotify('warning', '领料单号为：' + res.OverReceiveNo + '信息已仓库审核,请勿重复操作');
            switchBtn = false;
            return true;
          }
        })
      } else {
        switchBtn = true;
      }

      if (switchBtn) {
        this.isProcessing = true;
        const query = {
          status: type,
          entities: this.multipleSelection
        };
        Examine(query).then(res => {
          if (res.Code === 2000) {
            this.handleFilter();
            this.isProcessing = false;
            this.showNotify('success', '审核成功！');
          } else {
            this.isProcessing = false;
            this.showNotify('success', res.Message);
          }
        }).catch(err => {
          this.isProcessing = false;
        })
      }
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      const DocNums = selectRows.map(v => v.OverReceiveNo);
      const query = {
        docNums: DocNums
      };
      printToPDF(query).then(response => {
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    // 过账
    handlePosting() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(res => {
        if (res.ExamineStatus !== 2) {
          this.showNotify('warning', '领料单号为：' + res.OverReceiveNo + '信息未审核,请先审核操作');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn === true) {
        this.isProcessing = true;
        DoPost(this.multipleSelection).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', res.Message || 'Common.postSuccess');
          } else {
            this.showNotify('error', res.Message || 'Common.operationFailed');
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(err => {
          this.isProcessing = false;
        });
      }
    },
    // 下载
    handleDownload() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (selectRows.length > 0) {
        selectRows.some(res => {
          if (res.FilePath) {
            // window.open();
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = this.API.BaseURL + res.FilePath;
            link.setAttribute('download', res.FileName);
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link); // 下载完成移除元素

            // let FilePath = this.API.BaseURL + res.FilePath
            // let fileName = res.FileName
            // let x = new XMLHttpRequest();
            // x.open("GET", FilePath, true);
            // x.responseType = 'blob';
            // x.onload = function (e) {
            //   let url = window.URL.createObjectURL(x.response)
            //   let a = document.createElement('a');
            //   a.href = url
            //   a.download = fileName;
            //   a.click()
            // }
            // x.send();

            this.isProcessing = false;
          } else {
            this.showNotify('warning', '领料单号为：' + res.OverReceiveNo + '信息暂无附件，无法下载');
            this.isProcessing = false;
          }
        })
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    }
  }
};
</script>
