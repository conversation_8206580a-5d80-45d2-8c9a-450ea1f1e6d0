<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="ruleForm" :model="model" :rules="rules" label-width="100px">
        <!-- <el-form-item label="移动类型" label-width="100px">
          <el-select v-model="model.MovementType" filterable placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="评估类型">
          <el-select
            v-model="model.AssessmentType"
            filterable
            placeholder="请选择"
            :disabled="disabled"
            @change="changeType"
          >
            <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="转入仓库" prop="DeliverLocation">
          <el-select v-model="model.DeliverLocation" filterable placeholder="请选择" @change="changeDeliverLocation">
            <el-option
              v-for="item in DeliverLocationOptions"
              :key="item.value"
              :label="item.value+'-'+item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="转出仓库" prop="OutWarehouse">
          <el-select v-model="model.OutWarehouse" filterable placeholder="请选择" @change="changeWhsName">
            <el-option
              v-for="item in OutWarehouseOptions"
              :key="item.value"
              :label="item.value+'-'+item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="DemandQty">
          <el-input v-model="model.DemandQty" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZ_SAP
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      disabled: true,
      direction: 'rtl',
      model: {
        DemandQty: '',
        OutWarehouse: '', // 转出仓库
        OutWarehouseName: '', // 转出仓库
        DeliverLocation: '', // 转入仓库
        DeliverLocationName: '',
        AssessmentType: '', // 评估类型
        AssessmentTypeName: '', // 评估类型
        AssessmentCategory: '' // 评估类别
      },
      options: [{
        value: '261',
        label: '工废'
      }, {
        value: '311',
        label: '料废'
      }],
      types: [{
        value: '01',
        label: '自制'
      },
      {
        value: '02',
        label: '外购'
      }
      ],
      OutWarehouseOptions: [],
      DeliverLocationOptions: [],
      rules: {
        OutWarehouse: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        DemandQty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        DeliverLocation: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      }
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.GetXZ_SAP();
      this.GetXZ_SAP1();
      this.drawer = true;
      this.model = Object.assign({}, record);
      console.log(this.model);
      if (this.model.OutWarehouse === ' ') {
        this.model.OutWarehouse = '';
      }
      if (record.AssessmentCategory === 'B') {
        this.disabled = false;
        this.model.AssessmentType = '01';
      }
    },
    handleSave() {
      console.log(this.$refs.ruleForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.OutWarehouseOptions = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.OutWarehouseOptions.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      // console.log(e,this.options)
      this.model.OutWarehouseName = this.OutWarehouseOptions.filter(item => item.value === e)[0].label;
      console.log(this.model);
    },
    changeType(e) {
      this.model.AssessmentTypeName = this.types.filter(item => item.value === e)[0].label;
      console.log(this.model);
    },
    GetXZ_SAP1() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.DeliverLocationOptions = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.DeliverLocationOptions.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeDeliverLocation(e) {
      this.model.DeliverLocationName = this.DeliverLocationOptions.filter(item => item.value === e)[0].label;
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }
</style>
