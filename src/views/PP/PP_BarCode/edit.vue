<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('ui.PP.BarCode.title') }}</label>
    </p>
    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-delete"
        @click="handleClear"
      >{{ $t('Common.empty') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleConfirm"
      >{{ $t('Common.confirm') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-back"
        @click="handleCancel"
      >{{ $t('Common.cancel') }}</el-button>
    </div>
    <el-form
      ref="dataForm"
      :inline="true"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <el-form-item v-show="false" :label="$t('ui.PP.BarCode.BaseEntry')" prop="BaseEntry">
        <el-input v-model="temp.BaseEntry" readonly />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.BaseNum')">
        <el-input v-model="temp.BaseNum" style="width: 200px" :disabled="editStatus=='edit'">
          <el-button slot="append" icon="el-icon-more" @click="handleOrderBtnClick" />
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.PLine')" prop="PLine">
        <el-input v-model="temp.PLine" disabled style="width: 200px" />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.PTime')" prop="PTime">
        <el-date-picker v-model="temp.PTime" style="width: 200px" :clearable="false" />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.ItemCode')" prop="ItemCode">
        <el-input v-model="temp.ItemCode" suffix-icon="xxxx" disabled style="width: 200px" />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.ItemName')" prop="ItemName">
        <el-input v-model="temp.ItemName" disabled style="width: 200px" />
      </el-form-item>
      <!-- <el-form-item :label="$t('ui.PP.BarCode.ItmsGrpCode')" prop="ItmsGrpCode">
            <el-input v-model="temp.ItmsGrpCode" suffix-icon="xxxx" disabled style="width: 200px" />
        </el-form-item>
        <el-form-item :label="$t('ui.PP.BarCode.ItmsGrpName')" prop="ItmsGrpName">
            <el-input v-model="temp.ItmsGrpName" disabled style="width: 200px" />
      </el-form-item>-->
      <el-form-item :label="$t('ui.PP.BarCode.Unit')" prop="Unit">
        <el-input v-model="temp.Unit" disabled style="width: 200px" />
      </el-form-item>
      <!-- <el-form-item :label="$t('ui.PP.BarCode.BatchNum')" prop="BatchNum">
        <el-input v-model="BatchNum" disabled style="width: 200px" />
      </el-form-item> -->
      <el-form-item :label="$t('ui.PP.BarCode.Qty')" prop="Qty">
        <el-input-number v-model="temp.Qty" controls-position="right" style="width: 200px" />
      </el-form-item>
      <el-form-item :label="$t('Common.printQty')" prop="printQty">
        <el-input-number
          v-model="temp.printQty"
          controls-position="right"
          style="width: 200px"
          :disabled="editStatus=='edit'"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.BarCode.PrintTemplate')" prop="PrintTemplate">
        <el-select v-model="temp.PrintTemplate" filterable style="width: 200px">
          <el-option
            v-for="item in printTemplateOptions"
            :key="item.TempleteDesc"
            :label="item.TempleteDesc"
            :value="item.TempleteFile"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <ProductionOrderDialog
      ref="productionOrderDlg"
      :show.sync="dialogProductionOrderVisible"
      :is-multiple="false"
      @close="productionOrderSelected"
    />
  </div>
</template>

<script>
import { add, update, fetchBatchNum, batchAdd } from '@/api/PP/PP_BarCode';
import { fetchTemplate as fetchPrintTemplate } from '@/api/MD/MD_LabelTemplate';
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import ProductionOrderDialog from '@/components/FLD/ProductionOrderDialog';

_ = require('lodash');

export default {
  name: 'PP.PP_BarCodeEdit',
  components: {
    Pagination,
    ProductionOrderDialog
  },
  directives: {
    waves
  },
  data() {
    return {
      BatchNum: undefined,
      temp: {
        BarID: undefined,
        BarCode: undefined,
        BatchNum: undefined,
        BaseEntry: undefined,
        BaseNum: undefined,
        PLine: undefined,
        PTime: new Date(),
        ItemCode: undefined,
        ItemName: undefined,
        ItmsGrpCode: undefined,
        ItmsGrpName: undefined,
        Qty: 1,
        Unit: undefined,
        PrintTemplate: undefined,
        Remark: undefined,
        BoxBarCode: undefined,
        printQty: 1
      },
      rules: {
        // 表单校验逻辑
        // PrintTemplate: [{
        //     required: true,
        //     message: this.$i18n.t('Common.IsRequired'),
        //     trigger: 'change'
        // }],
        Qty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ],
        // BatchNum: [{
        //     required: true,
        //     message: this.$i18n.t('Common.IsRequired'),
        //     trigger: 'change'
        // }],
        printQty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ],
        PTime: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ]
      },
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        keyword: ''
      },
      printTemplateOptions: [],
      dialogFormVisible: false,
      dialogStatus: 'select',
      textMap: {
        select: this.$i18n.t('Common.select')
      },
      editStatus: 'create',
      dialogProductionOrderVisible: false
    };
  },
  created() {
    this.getPrintTemplateList();
    this.getPageParams();
    this.BatchNum = this.temp.BatchNum;
    if (this.editStatus == 'create') {
      // this.getBatchNum().then(batchNum => {
      //   this.BatchNum = batchNum;
      // });
    } else {
      this.temp.printQty = 1;
    }
  },
  methods: {
    getPrintTemplateList() {
      fetchPrintTemplate({
        templateType: 40
      }).then(response => {
        if (response.Code === 2000) {
          this.printTemplateOptions = response.Data;
          this.temp.PrintTemplate = this.printTemplateOptions[0].TempleteDesc;
        }
      });
    },
    getBatchNum() {
      return new Promise((resolve, reject) => {
        fetchBatchNum()
          .then(response => {
            if (response.Code === 2000) {
              resolve(response.Data);
            } else {
              this.showNotify('error', response.Message);
            }
          })
          .catch(err => {
            console.log(err);
            reject(false);
          });
      });
    },
    getPageParams() {
      this.temp = Object.assign({}, this.$route.params);
      if (this.temp.BarID) {
        // 编辑
        this.editStatus = 'edit';
      } else {
        // 新增
        this.editStatus = 'create';
      }
      if (this.temp.PTime) {
      } else {
        this.temp.PTime = new Date();
      }
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    resetTemp() {
      this.temp = {
        BarID: undefined,
        BarCode: undefined,
        BatchNum: undefined,
        BaseEntry: undefined,
        BaseNum: undefined,
        PLine: undefined,
        PTime: new Date(),
        ItemCode: undefined,
        ItemName: undefined,
        ItmsGrpCode: undefined,
        ItmsGrpName: undefined,
        Qty: 1,
        Unit: undefined,
        PrintTemplate: undefined,
        Remark: undefined,
        BoxBarCode: undefined,
        printQty: 1
      };
      this.listQuery.keyword = '';
    },
    productionOrderSelected(productionOrder) {
      this.listLoading = true;
      if (productionOrder) {
        if (this.editStatus == 'edit') {
          if (this.temp.ItemCode == productionOrder.ItemCode) {
            Object.assign(this.temp, productionOrder);
            this.temp.Remark = '';
            this.temp.PLine = productionOrder.ProductionLine;
            this.temp.Unit = productionOrder.ProductionOrderPlannedQuantityUnit;
          } else {
            this.showNotify('warning', 'ui.PP.BarCode.editSameOrder');
            return;
          }
        } else {
          Object.assign(this.temp, productionOrder);
          this.temp.Remark = '';
          this.temp.PLine = productionOrder.ProductionLine;
          this.temp.Unit = productionOrder.ProductionOrderPlannedQuantityUnit;
        }
      }
    },
    handleOrderBtnClick() {
      this.dialogProductionOrderVisible = true;
    },
    handleRowClick(row, col, evt) {
      Object.assign(this.temp, row);
      this.dialogFormVisible = false;
    },
    handleClear() {
      this.resetTemp();
    },
    handleConfirm() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          var isChoose = this.printTemplateOptions.find(
            x => x.TempleteDesc == this.temp.PrintTemplate
          );
          if (isChoose) {
            // 判断是否是key值，如果是key值换成value值
            this.temp.PrintTemplate = isChoose.TempleteFile;
          }
          if (this.editStatus == 'edit') {
            update(this.temp).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.backTo('PP.PP_BarCode');
              } else {
                this.showNotify('error', response.Message);
              }
            });
          } else {
            this.temp.BatchNum = this.BatchNum;
            batchAdd({
              Entity: this.temp,
              Count: this.temp.printQty
            }).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.backTo('PP.PP_BarCode');
              } else {
                this.showNotify('error', response.Message);
              }
            });
          }
        }
      });
    },
    handleCancel() {
      this.backTo('PP.PP_BarCode');
    },
    handleSelectBtnClick() {
      this.dialogFormVisible = false;
    }
  }
};
</script>
