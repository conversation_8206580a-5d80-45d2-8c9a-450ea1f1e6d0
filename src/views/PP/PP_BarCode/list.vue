<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />

      <el-select
        v-model="listQuery.iStatus"
        filterable
        style="width: 140px"
        :placeholder="$t('ui.PP.BarCode.IStatus')"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.EnumKey"
          :label="item.EnumValue"
          :value="item.EnumKey"
        />
      </el-select>
      <el-select
        v-model="listQuery.productType"
        filterable
        style="width: 140px"
        :placeholder="$t('ui.PP.BarCode.productType')"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isProductTypeOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCode.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCode.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCode.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCode.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_BarCode.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.BarCode.BarID')"
        prop="BarID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.BoxBarCode')"
        prop="BoxBarCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.BarCode')"
        prop="BarCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.BarCode.BaseEntry')"
        prop="BaseEntry"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.BaseNum')"
        prop="BaseNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.PTime')"
        prop="PTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.ItemCode')"
        prop="ItemCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.ItemName')"
        prop="ItemName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.BarCode.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.PP.BarCode.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.BarCode.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.Unit')"
        prop="Unit"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.PrintTemplate')"
        prop="PrintTemplate"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PrintTemplate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.BatchNum')"
        prop="BatchNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.BarCode.PLine')"
        prop="PLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.BarCode.IStatus')" prop="IStatus" align="center" width="120" :formatter="statusFormatter" fixed="right" />

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { fetchPage, batchDelete, exportExcelFile } from '@/api/PP/PP_BarCode';
import { exportToExcel } from '@/utils/excel-export';
import { printBarCodeToPDF } from '@/api/PP/PP_Print';
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import permission from '@/directive/permission/index.js'; // 权限判断指令

_ = require('lodash');

export default {
  name: 'PP.PP_BarCode',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  rules: [],
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: null,
        toTime: null,
        iStatus: '',
        productType: 1
      },
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      multipleSelection: [],
      isProductTypeOptions: [
        {
          label: this.$i18n.t('ui.PP.BarCode.product.half'),
          key: 1
        },
        {
          label: this.$i18n.t('ui.PP.BarCode.product.whole'),
          key: 2
        }
      ],
      statusOptions: [],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      const i = this.multipleSelection.length;
      if (i === 0) return true;
      if (i > 1) return true;
      if (i === 1) {
        if (this.multipleSelection[0].IStatus === 2) return true;
      }
      return false;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IStatus === 2) return true;
      }
      return false;
    }
  },
  created() {
    this.getList();
    this.getDict('SYS003').then(data => {
      this.statusOptions = data;
    });
  },
  methods: {
    statusFormatter(row, column) {
      var a = this.statusOptions.find(x => x.EnumKey == row.IStatus);
      if (a) return a.EnumValue;
    },
    fetchPage,
    getList() {
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      console.log('listQuery', this.listQuery);
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getOrderList() {},
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter(1);
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = 'BarID asc';
      } else {
        this.listQuery.sort = 'BarID desc';
      }
      this.handleFilter();
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleCreate() {
      this.routeTo('PP.PP_BarCodeEdit');
    },
    handleEdit() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        this.routeTo('PP.PP_BarCodeEdit', Object.assign(selectRows[0]));
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          var arrRowsID = selectRows.map(function(v) {
            return v.BarID;
          });

          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      // if (this.dateRangeValue) {
      //     this.listQuery.fromTime = this.dateRangeValue[0]
      //     this.listQuery.toTime = this.dateRangeValue[1]
      // }
      exportExcelFile({
        Keyword: this.listQuery.keyword,
        dateTimes: this.dateRangeValue,
        iStatus: this.listQuery.iStatus,
        productType: this.listQuery.productType
      }).then(res => exportToExcel(res.data, '生产自制品标签管理'));
    },
    handlePrint() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        printBarCodeToPDF({
          barcodes: selectRows.map(x => x.BarCode),
          templateCode: selectRows[0].PrintTemplate
        }).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
        });
      }
    }
  }
};
</script>
