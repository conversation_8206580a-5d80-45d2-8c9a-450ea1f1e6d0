<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-permission="{ name: 'PP.PP_FTTM.Add' }"
        v-waves
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >{{ $t('Common.log') }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_FTTM.Edit' }"
        v-waves
        :disabled="!editable"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_FTTM.Delete' }"
        v-waves
        :disabled="!deletable"
        size="small"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_FTTM.Export' }"
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_FTTM.Delete' }"
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTM.FTTMID')"
        prop="FTTMID"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.FTTMID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTM.DocNum')"
        prop="DocNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTM.BaseEntry')"
        prop="BaseEntry"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTM.BaseNum')"
        prop="BaseNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTM.PLine')"
        prop="PLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>{{ $t('ui.PP.FTTMDetailed.title') }}</p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTMDetailed.DetailedID')"
        prop="DetailedID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.DocNum')"
        prop="DocNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.FTTMDetailed.BaseLine')" prop="BaseLine"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BaseLine }}</span> </template> </el-table-column> -->
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.ItemCode')"
        prop="ItemCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.ItemName')"
        prop="ItemName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.FTTMDetailed.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode }}</span> </template> </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.PP.FTTMDetailed.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.PP.FTTMDetailed.ReasonsCode')" prop="ReasonsCode"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ReasonsCode }}</span> </template> </el-table-column> -->
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.ReasonsDesc')"
        prop="ReasonsDesc"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.TreatmentPlan')"
        prop="TreatmentPlan"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.TreatmentPlan|dealWithFormatter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.IConfirm')"
        prop="IConfirm"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTMDetailed.WConfirm')"
        prop="WConfirm"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
  </div>
</template>

<script>
import {
  fetchPage,
  add,
  update,
  batchDelete,
  fetchDocNum,
  exportExcelFile
} from '@/api/PP/PP_FTTM';
import { exportToExcel } from '@/utils/excel-export';
import {
  fetchDetailPage,
  addDetail,
  updateDetail,
  batchDeleteDetail
} from '@/api/PP/PP_FTTMDetailed';
import { printOrderToPDF } from '@/api/PP/PP_Print';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import { convertToKeyValue } from '@/utils';
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'PP.PP_FTTM',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      listDetail: null,
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        dateRangeValue: [
          new Date(),
          new Date()
        ]
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 10,
        docNum: ''
      },
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      downloadLoading: false,
      multipleSelection: [],
      currentRow: {
        FTTMID: '',
        DocNum: '',
        BaseEntry: '',
        BaseNum: '',
        PLine: ''
      },
      isProcessing: false
    };
  },
  computed: {
    editable() {
      return this.multipleSelection.length === 1;
    },
    deletable() {
      return this.multipleSelection.length === 1;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.listQuery.dateRangeValue) {
        this.listQuery.fromTime = this.listQuery.dateRangeValue[0];
        this.listQuery.toTime = this.listQuery.dateRangeValue[1];
      }
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.currentRow.DocNum;

      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter(1);
    },
    detailSortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.handleFilter(2);
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    operationCheck() {
      var details = this.listDetail.filter(
        x => x.DocNum == this.multipleSelection[0].DocNum
      );
      console.log(details);
      if (details && details.length > 0) {
        var detailsTmp = details.filter(
          x => x.IConfirm === true || x.WConfirm === true
        );
        console.log(
          'operationCheck-detailsTmp',
          detailsTmp,
          detailsTmp && detailsTmp.length > 0,
          detailsTmp.length > 0
        );
        if (detailsTmp && detailsTmp.length > 0) {
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    handleFilter(flag) {
      if (flag == 2) {
        this.clearTables(2);
        if (this.currentRow) this.getDetailList();
      } else {
        this.clearTables(3);
        this.listQuery.PageNumber = 1;
        this.listQuery.PageSize = 10;
        this.getList();
      }
    },
    handleCreate() {
      this.routeTo('PP.PP_FTTMDetailed');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        if (this.operationCheck()) {
          this.routeTo('PP.PP_FTTMDetailed', Object.assign(selectRows[0]));
        } else {
          this.showNotify('error', 'Common.operationNotPermitted');
        }
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        if (this.operationCheck()) {
          this.$confirm(
            this.$i18n.t('Common.batchDeletingConfirm'),
            this.$i18n.t('Common.tip'),
            {
              confirmButtonText: this.$i18n.t('Common.affirm'),
              cancelButtonText: this.$i18n.t('Common.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.isProcessing = true;
            var arrRowsID = selectRows.map(function(v) {
              return v.FTTMID;
            });

            // 删除逻辑处理
            batchDelete(arrRowsID)
              .then(response => {
                this.isProcessing = false;
                if (response.Code === 2000) {
                  this.showNotify('success', 'Common.deleteSuccess');
                  this.handleFilter(1);
                } else {
                  this.showNotify('error', response.Message);
                }
              })
              .catch(error => {
                this.isProcessing = false;
              });
          });
        } else {
          this.showNotify('error', 'Common.operationNotPermitted');
        }
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
      if (val.length == 0) {
        return;
      }
      this.handleRowClick(val[0]);
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.handleFilter(2);
    },
    handleComplete() {},
    handleCommit() {
      // todo:
    },
    handlePrint() {
      var selectRows = this.multipleSelection;
      var plineSame = selectRows[0].PLine;
      var isSamePline = selectRows.find(x => x.PLine != plineSame);
      if (isSamePline) {
        this.showNotify('warning', 'Common.NotSamePline');
        return;
      }
      // if (this.checkSingleSelection(selectRows)) {

      var docNums = selectRows.map(v => v.DocNum);
      printOrderToPDF({
        docNums: docNums,
        templateCode: 'PP_FTTM'
      }).then(response => {
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
      // }
    },

    handleExport() {
      // eslint-disable-next-line no-undef
      exportExcelFile({
        Keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.ateRangeValue
      }).then(res => exportToExcel(res.data, '生产FTT原料管理'));
    }
  }
};
</script>
