<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('ui.PP.FTTM.title') }}</label>
    </p>
    <el-form ref="dataForm" :inline="true" :rules="rules" :model="primary" label-position="right" label-width="100px">
      <el-form-item :label="$t('ui.PP.FTTM.DocNum')" prop="DocNum">
        <el-input v-model="primary.DocNum" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTM.BaseNum')" prop="BaseNum">
        <!-- <el-autocomplete v-model="primary.BaseNum" class="inline-input" :fetch-suggestions="handleQueryBaseNum" :placeholder="$t('ui.PP.FTTM.BaseNum')" :trigger-on-focus="false" :disabled="editFlag!=0" @select="handleSelectOrder" @keyup.enter.native="handleEnterBaseNum">
                <template slot="append">
                    <el-button icon="el-icon-more" :disabled="editFlag!=0" @click="handleOrderBtnClick" />
                </template>
        </el-autocomplete>-->
        <el-input v-model="primary.BaseNum" @keyup.enter.native="handleEnterBaseNum">
          <el-button slot="append" icon="el-icon-more" :disabled="editFlag!=0" @click="handleOrderBtnClick" />
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTM.PLine')" prop="PLine">
        <el-input v-model="primary.PLine" disabled />
      </el-form-item>
    </el-form>
    <p>
      <label style="width:100%">{{ $t('ui.PP.FTTMDetailed.title') }}</label>
    </p>
    <el-form :inline="true" :model="temp" label-position="right" label-width="100px" :rules="rules">
      <el-form-item :label="$t('ui.PP.FTTMDetailed.ItemCode')" prop="ItemCode">
        <!-- <el-input v-model="temp.ItemCode" :placeholder="$t('ui.PP.FTTMDetailed.ItemCode')" /> -->
        <el-select v-model="temp.ItemCode" style="width: 200px" filterable @change="handleSelectMaterial">
          <el-option v-for="item in itemOptions" :key="item.ItemCode" :label="item.ItemCode" :value="item.ItemCode" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTMDetailed.ItemName')" prop="ItemName">
        <el-input v-model="temp.ItemName" :placeholder="$t('ui.PP.FTTMDetailed.ItemName')" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTMDetailed.Qty')" prop="Qty">
        <el-input-number
          v-model="temp.Qty"
          :min="0"
          controls-position="right"
          :placeholder="$t('ui.PP.FTTMDetailed.Qty')"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTMDetailed.ReasonsDesc')" prop="ReasonsDesc">
        <el-input
          v-model="temp.ReasonsDesc"
          controls-position="right"
          :placeholder="$t('ui.PP.FTTMDetailed.ReasonsDesc')"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTMDetailed.TreatmentPlan')" prop="TreatmentPlan">
        <el-select v-model="temp.TreatmentPlan" filterable>
          <el-option
            v-for="item in dealWithOptions"
            :key="item.EnumKey"
            :label="item.EnumValue"
            :value="item.EnumKey"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('ui.PP.FTTMDetailed.TreatmentPlan')" prop="TreatmentPlan">
        <el-input
          v-model="temp.TreatmentPlan"
          controls-position="right"
          :placeholder="$t('ui.PP.FTTMDetailed.TreatmentPlan')"
        />
      </el-form-item>-->
      <!-- <el-form-item :label="$t('ui.PP.FTTMDetailed.ReasonsDesc')" prop="ReasonsCode">
            <el-select v-model="temp.ReasonsCode" filterable @change="handleSelectReason">
                <el-option v-for="item in reasonOptions" :key="item.EnumKey" :label="item.EnumValue" :value="item.EnumKey" />
            </el-select>
      </el-form-item>-->
      <div class="filter-container">
        <el-button type="primary" icon="el-icon-edit" @click="handleAddDetail">{{ $t('Common.log') }}</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="handleDeleteDetail">{{ $t('Common.delete') }}</el-button>
        <el-button type="success" icon="el-icon-edit" @click="handleCommit">{{ $t('Common.confirm') }}</el-button>
      </div>
    </el-form>
    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      highlight-current-row
      height="270px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="100" />
      <el-table-column :label="$t('ui.PP.FTTMDetailed.DocNum')" prop="DocNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.ItemName')" prop="ItemName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTMDetailed.ReasonsCode')"
        prop="ReasonsCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.ReasonsDesc')" prop="ReasonsDesc" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.TreatmentPlan')" prop="TreatmentPlan" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.TreatmentPlan|dealWithFormatter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.IConfirm')" prop="IConfirm" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTMDetailed.WConfirm')" prop="WConfirm" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.WConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CTime')" prop="CTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
    <ProductionOrderDialog
      ref="productionOrderDlg"
      :show.sync="dialogProductionOrderVisible"
      :multi-selection="false"
      :show-detail="false"
      @close="handleSelectOrder"
    />
  </div>
</template>

<script>
import {
  fetchDetailPage,
  addDetails,
  updateDetails
} from '@/api/PP/PP_FTTMDetailed';
import {
  fetchDocNum
} from '@/api/PP/PP_FTTM';
import {
  fetchList as fetchProductionOrderList,
  fetchDetailList as fetchProductionOrderDetailList
} from '@/api/PP/PP_ProductionOrder';
import waves from '@/directive/waves'; // waves directive

import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import ProductionOrderDialog from '@/components/FLD/ProductionOrderDialog';

_ = require('lodash');

export default {
  name: 'PP.PP_FTTMDetailed',
  components: {
    Pagination,
    ProductionOrderDialog
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      editFlag: 0, // 0，表示页面为创建，1，表示页面为编辑
      reasonOptions: [],
      dealWithOptions: [],
      itemOptions: [],
      list: [],
      listDetail: [],
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        sort: 'ItemName asc',
        keyword: ''
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 20,
        docNum: undefined,
        sort: 'DetailedID asc',
        keyword: ''
      },
      primary: {
        FTTMID: undefined,
        DocNum: undefined,
        BaseEntry: undefined,
        BaseNum: undefined,
        PLine: undefined,
        Remark: ''
      },
      temp: {
        DetailedID: undefined,
        BaseLine: undefined,
        DocNum: undefined,
        ReasonsCode: undefined,
        ReasonsDesc: undefined,
        Qty: undefined,
        IConfirm: undefined,
        WConfirm: undefined,
        ItmsGrpCode: undefined,
        ItmsGrpName: undefined,
        ItemCode: undefined,
        ItemName: undefined
      },
      dialogProductionOrderVisible: false,
      dialogStatus: '',
      textMap: {
        select: this.$i18n.t('Common.select')
      },
      multipleSelection: [],
      rules: {
        BaseNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ItemCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        Qty: [{
          required: true,
          validator: this.QtyValidator,
          trigger: 'change'
        }]
      }
    };
  },
  created() {
    this.getPageParams(() => {
      if (this.editFlag != 0) {
        // this.getItemOptions()
        this.getDetailList();
      }
    });
    // 处理方式
    this.getDict('PP003').then(data => {
      this.dealWithOptions = data;
    });
    // 站点不良原因下拉框
    this.getDict('PP006').then(data => {
      this.reasonOptions = data;
    });
  },
  methods: {
    getDocNum(doneCallback) {
      fetchDocNum().then(response => {
        if (response.Code === 2000) {
          this.primary.DocNum = response.Data;
          if (doneCallback) doneCallback();
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getPageParams(doneCallback) {
      this.primary = Object.assign(this.primary, this.$route.params);
      if (this.primary.DocNum) {
        // 编辑
        this.editFlag = 1;
        if (doneCallback) doneCallback();
      } else {
        // 新增
        this.editFlag = 0;
        this.getDocNum(doneCallback);
      }
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.primary.DocNum;
      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    getList() {
      this.listLoading = true;
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getProductionOrderDetailList(productionOrderID) {},
    reset() {
      this.title = this.$i18n.t('Common.select'); // 对话框标题
      this.list = [];
      this.listQuery = {
        productionLine: this.pLine,
        PageNumber: 1,
        PageSize: 10
      };
      this.multipleSelection = undefined;
      this.listLoading = false;
    },
    commit() {
      if (this.listDetail && this.listDetail.length > 0) {
        this.startLoading();
        if (this.editFlag == 1) {
          updateDetails({
            primary: this.primary,
            list: this.listDetail
          }).then(response => {
            if (response.Code === 2000) {
              // 跳转回主单页面
              this.backTo('PP.PP_FTTM');
            } else {
              this.showNotify('error', response.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          });
        } else {
          addDetails({
            primary: this.primary,
            list: this.listDetail
          }).then(response => {
            if (response.Code === 2000) {
              // 跳转回主单页面
              this.backTo('PP.PP_FTTM');
            } else {
              this.showNotify('error', response.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          });
        }
      } else {
        this.showNotify('error', 'Common.detailIsNull');
      }
    },
    submitAddDetail() {
      console.log('123', this.dealWithOptions);

      console.log('123', this.temp);
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          var detail = this.listDetail.find(
            obj =>
              obj.ItemCode == this.temp.ItemCode &&
              obj.DocNum == this.primary.DocNum &&
              obj.TreatmentPlan == this.primary.TreatmentPlan &&
              obj.ReasonsDesc == this.primary.ReasonsDesc
          );
          if (detail) {
            // 如果找到已添加的同种物料只增加数量不新增条目
            // detail.Qty += this.temp.Qty;
            this.showNotify(
              'warning',
              'Common.correspondingDetailsAlreadyExist'
            );
          } else {
            this.listDetail.push(JSON.parse(JSON.stringify(this.temp)));
          }
          this.dialogProductionOrderVisible = false;
        } else {
          return false;
        }
      });
    },
    handleDetailFilter() {
      this.getDetailList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleAddDetail() {
      if (!this.temp.ItemCode) {
        this.showNotify('warning', 'Common.selectMaterial');
        return;
      }
      if (!this.temp.Qty) {
        this.showNotify('warning', 'Common.inCorrectQuantity');
        return;
      }
      this.temp.DocNum = this.primary.DocNum;
      this.submitAddDetail();
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.listDetail.indexOf(v);
          this.listDetail.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.$confirm(
        this.$i18n.t('Common.committingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.commit();
      });
    },
    handleSelectReason(val) {
      var reason = this.reasonOptions.find(x => x.EnumKey === val);
      this.temp.ReasonsDesc = reason ? reason.EnumValue : '';
    },
    handleQueryBaseNum(baseNum, callback) {
      if (this.primary.BaseNum && this.primary.BaseNum.trim() !== '') {
        fetchProductionOrderList({
          productionOrderStatus: '',
          productionLine: '',
          productionOrderID: this.primary.BaseNum
        })
          .then(response => {
            if (response && response.Code == 2000) {
              var orders = response.Data;
              if (orders && orders.length > 0) {
                this.primary = Object.assign(this.primary, order);
                this.primary.PLine = order.ProductionLine;
              }
            } else {
              callback([]);
            }
          })
          .catch(err => {
            callback([]);
          });
      }
    },
    handleOrderBtnClick() {
      this.dialogProductionOrderVisible = true;
    },
    handleEnterBaseNum() {
      if (this.primary.BaseNum && this.primary.BaseNum.trim() !== '') {
        fetchProductionOrderList({
          productionOrderStatus: '',
          productionLine: '',
          productionOrderID: this.primary.BaseNum
        })
          .then(response => {
            if (response && response.Code == 2000) {
              var orders = response.Data;
              if (orders && orders.length > 0) {
                orders.forEach(x => {
                  x.value = x.BaseNum;
                });
                this.handleSelectOrder(orders[0]);
              } else {
                this.showNotify('error', 'ui.PP.ProductionOrder.notFound');
              }
            } else {
              this.showNotify('error', 'ui.PP.ProductionOrder.notFound');
            }
          })
          .catch(err => {
            this.showNotify('error', 'ui.PP.ProductionOrder.notFound');
          });
      }
    },
    handleSelectOrder(order) {
      if (order) {
        this.primary = Object.assign(this.primary, order);
        this.primary.PLine = order.ProductionLine;
        console.log(this.primary);
        fetchProductionOrderDetailList({
          productionOrderID: this.primary.BaseNum
        }).then(response => {
          if (response.Code == 2000) {
            if (response.Data && response.Data.length > 0) {
              var data = response.Data.map(x => ({
                ItemCode: x.ItemCode,
                ItemName: x.ItemName
              }));
              data = _.uniqBy(data, x => x.ItemCode);
              data = _.sortBy(data, x => x.EnumValue);
              this.itemOptions = data;
            }
          } else {
            this.showNotify('error', 'ui.PP.ProductionOrderDetail.notFound');
          }
        });
      }
    },
    handleSelectMaterial(material) {
      if (material) {
        var item = this.itemOptions.find(x => x.ItemCode == material);
        if (item) {
          console.log(item);
          var obj = this.listDetail.find(y => y.ItemCode == item.ItemCode);
          if (obj) {} else {
            this.temp.DocNum = this.primary.DocNum;
            this.temp.Qty = item.StockingQty;
            this.temp.ItemCode = item.ItemCode;
            this.temp.ItemName = item.ItemName;
            this.temp.Unit = item.PlanQuantityUnit;
            // this.listDetail.push(JSON.parse(JSON.stringify(this.temp)));
          }
        }
      }
    }
  }
};
</script>
