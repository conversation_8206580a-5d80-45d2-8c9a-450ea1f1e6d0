<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" label-width="100px" :rules="rules" label-position="right">
        <!-- <el-form-item class="filter-item" label="扫描码" prop="ScanningCode">
          <el-input v-model="model.ScanningCode" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="出厂编号">
          <el-input v-model="model.SerialNo" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="生产报工单号">
          <el-input v-model="model.ProductionReportNo" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="生产订单">
          <el-input v-model="model.ProductionOrderNo" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="物料件号">
          <el-input v-model="model.MaterialNo" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="物料名称">
          <el-input v-model="model.MaterialName" disabled />
        </el-form-item> -->
        <el-form-item class="filter-item" label="工序描述" prop="WorkingProcedureDes">
          <el-select v-model="model.WorkingProcedureDes" filterable placeholder="请选择" @change="changeWorkingProcedure">
            <el-option
              v-for="item in options"
              :key="item.WorkingProcedureCode"
              :label="item.WorkingProcedureDes"
              :value="item.WorkingProcedureDes"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="filter-item" label="工序编号">
          <el-input v-model="model.WorkingProcedureCode" disabled />
        </el-form-item>
        <!-- <el-form-item class="filter-item" label="员工号">
          <el-input v-model="model.EmployeeNumber" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="员工姓名">
          <el-input v-model="model.EmployeeName" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="订单数量">
          <el-input v-model="model.OrderQty" disabled />
        </el-form-item>
        <el-form-item class="filter-item" label="单位">
          <el-input v-model="model.Unit" disabled />
        </el-form-item> -->
        <el-form-item class="filter-item" label="合格数量" prop="QualifiedQty">
          <el-input v-model="model.QualifiedQty" type="number" @blur="blurReportTotal" />
        </el-form-item>
        <el-form-item class="filter-item" label="不合格数量" prop="UnqualifiedQty">
          <el-input v-model="model.UnqualifiedQty" type="number" @blur="blurReportTotal" />
        </el-form-item>
        <el-form-item class="filter-item" label="不合格备注">
          <el-input v-model="model.UnqualifiedRemarks" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
        <!-- <el-form-item class="filter-item" label="报工总数">
          <el-input v-model="model.ReportTotal" disabled />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="model.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item> -->
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetWorkingProcedure
} from '@/api/PP/PP_ProductionReportController';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    const validate = (rule, value, callback) => {
      if (value < 0) {
        callback(new Error('请输入大于0的数字'))
      } else if (value === '') {
        callback(new Error('请输入大于0的数字'))
      } else {
        callback()
      }
    };
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {

      },
      rules: {
        WorkingProcedureDes: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        QualifiedQty: [{
          required: true,
          validator: validate,
          trigger: 'blur'
        }],
        UnqualifiedQty: [{
          required: true,
          validator: validate,
          trigger: 'blur'
        }]
      },
      options: []
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.model = Object.assign({}, record);
      this.GetXZ_SAP();
      this.drawer = true
    },
    handleSave() {
      this.$refs['dataForm'].validate((valid) => {
        console.log(valid, 123);
        if (valid) {
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    GetXZ_SAP() {
      const query = {
        OrderNo: this.model.ProductionOrderNo
      };
      GetWorkingProcedure(query).then(res => {
        if (res.Code === 2000) {
          this.options = res.Data
        }
      })
    },
    changeWorkingProcedure(e) {
      const obj = this.options.find(v => v.WorkingProcedureDes === e);
      this.model.WorkingProcedureCode = obj.WorkingProcedureCode
    },
    blurReportTotal() {
      if (this.model.QualifiedQty) {
        this.model.ReportTotal = Number(this.model.QualifiedQty) + Number(this.model
          .UnqualifiedQty)
      }
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
