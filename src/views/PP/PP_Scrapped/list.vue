<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateTimes"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleCountFilter"
      />
      <el-select
        v-model="isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleCountFilter"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.EnumKey"
          :label="item.EnumValue"
          :value="item.EnumKey"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleCountFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleCountFilter"
      />
      <hr>
      <el-button
        v-permission="{ name: 'PP.PP_Scrapped.Posting' }"
        v-waves
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="deletable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_Scrapped.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>
    <!-- 汇总主表 -->
    <el-table
      v-loading="listCountLoading"
      :data="countList"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="handleFilter"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.PP.Scrapped.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.ScrappedNum')"
        prop="ScrappedNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScrappedNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.IsPosted')"
        prop="IsPosted"
        fixed="right"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.PostUser')"
        prop="PostUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('ui.PP.Scrapped.PostTime')"
        prop="PostTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime}}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="countTotal>0"
      :total="countTotal"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getCountList"
    />
    <!-- 明细子表 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />-->
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.ScrappedID')"
        prop="ScrappedID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScrappedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.ScrappedNum')"
        prop="ScrappedNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScrappedNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.BarCode')"
        prop="BarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.BaseNum')"
        prop="BaseNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--        <el-table-column :label="$t('ui.PP.Scrapped.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode}}</span> </template> </el-table-column>-->
      <!--        <el-table-column :label="$t('ui.PP.Scrapped.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="220"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName}}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.Scrapped.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.STypeCode')"
        prop="STypeCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.STypeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.Subject')"
        prop="Subject"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Subject }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.CostCenter')"
        prop="CostCenter"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CostCenter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.PTime')"
        prop="PTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.BatchNum')"
        prop="BatchNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.PLine')"
        prop="PLine"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.OutRegionCode')"
        prop="OutRegionCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.OutRegionName')"
        prop="OutRegionName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.OutBinLocationCode')"
        prop="OutBinLocationCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.OutBinLocationName')"
        prop="OutBinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.IsPosted')"
        prop="IsPosted"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.Scrapped.ERPDocNum')"
        prop="ERPDocNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.PostUser')"
        prop="PostUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.Scrapped.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />-->
  </div>
</template>

<script>
import {
  fetchPage,
  fetchCountPage,
  fetchDetailPage,
  add,
  update,
  batchDelete,
  postToSAP,
  exportExcelFile
} from '@/api/PP/PP_Scrapped';
import { exportToExcel } from '@/utils/excel-export';
import { convertToKeyValue } from '@/utils';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'PP.PP_Scrapped',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      countList: null,
      total: 0,
      countTotal: 0,
      listLoading: false,
      listCountLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateTimes: [
          new Date(),
          new Date()
        ],
        isPosted: false
      },
      isPosted: 1,
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      multipleSelection: [],
      isPostedOptions: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) {
        return true;
      }
      while (i--) {
        if (this.multipleSelection[i].IsPosted) {
          return true;
        }
      }
    }
  },
  created() {
    this.getDict('SYS001').then(data => {
      this.isPostedOptions = data;
      this.getCountList();
    });
  },
  methods: {
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleCountFilter();
    },
    getCountList() {
      // 获取数据
      this.listCountLoading = true;
      this.list = null;
      if (this.isPosted == 1) this.listQuery.isPosted = false;
      else if (this.isPosted == 2) this.listQuery.isPosted = true;
      else this.listQuery.isPosted = '';

      fetchCountPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.countList = response.Data.items;
          this.countTotal = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listCountLoading = false;
      });
    },
    getList(row) {
      // 获取数据
      this.listLoading = true;
      this.list = null;
      console.log(row);
      fetchDetailPage({ DocNums: row.DocNum, isPosted: row.IsPosted }).then(
        response => {
          if (response.Code === 2000) {
            this.list = response.Data;
            // this.total = response.Data.total;
          } else {
            this.showNotify('error', response.Message);
          }
          this.listLoading = false;
        }
      );
    },
    handleCountFilter() {
      this.getCountList();
    },
    handleFilter(row) {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList(row);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handlePosting() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.postToSAPConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.showNotify('warning', 'Common.syncStart');
          this.isProcessing = true;
          postToSAP(selectRows)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.operationSuccess');
                this.handleCountFilter(1);
              } else {
                this.isProcessing = false;
                this.showNotify('error', response.Message);
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      if (this.isPosted == 1) this.listQuery.isPosted = false;
      else if (this.isPosted == 2) this.listQuery.isPosted = true;
      else this.listQuery.isPosted = '';
      exportExcelFile({
        Keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateTimes,
        isPosted: this.listQuery.isPosted
      }).then(res => exportToExcel(res.data, '生产报废管理'));
    }
  }
};
</script>
