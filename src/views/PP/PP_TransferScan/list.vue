<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateRangeValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        :picker-options="pickerOptions"
        :range-separator="$t('Common.timeTo')"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >{{ $t('Common.search') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handlePosting"
      >{{ $t('Common.posting') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="100" />
      <el-table-column
        v-if="false"
        :label="$t('PP.TransferScan.ScanID')"
        prop="ScanID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.DocNum')"
        prop="DocNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.WaveNum')"
        prop="WaveNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('PP.TransferScan.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.BaseNum')"
        prop="BaseNum"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.PLine')"
        prop="PLine"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('PP.TransferScan.BaseLine')"
        prop="BaseLine"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.BarCode')"
        prop="BarCode"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.BatchNum')"
        prop="BatchNum"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.ItemName')"
        prop="ItemName"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('PP.TransferScan.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="100"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode }}</span> </template> </el-table-column>-->
      <!--      <el-table-column :label="$t('PP.TransferScan.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="100"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('PP.TransferScan.Qty')"
        prop="Qty"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.Unit')"
        prop="Unit"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.OutRegionCode')"
        prop="OutRegionCode"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.OutRegionName')"
        prop="OutRegionName"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.OutBinLocationCode')"
        prop="OutBinLocationCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.OutBinLocationName')"
        prop="OutBinLocationName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.InRegionCode')"
        prop="InRegionCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.InRegionName')"
        prop="InRegionName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.InBinLocationCode')"
        prop="InBinLocationCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.InBinLocationName')"
        prop="InBinLocationName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.IsPosted')"
        prop="IsPosted"
        fixed="right"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.PostUser')"
        prop="PostUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('PP.TransferScan.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { fetchPage } from '@/api/PP/PP_TransferScan';
// import { exportToExcel } from '@/utils/excel-export'
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination

_ = require('lodash');

export default {
  name: 'PP.PP_TransferScan',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },

      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: 'BarCode asc',
        keyword: '',
        apiEventType: '',
        dateRangeValue: [
          new Date(),
          new Date()
        ],
        isPosted: undefined
      },
      // importanceOptions: [1, 2, 3],
      isPostedOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: 'all'
        },
        {
          label: this.$i18n.t('Common.posted'),
          key: true
        },
        {
          label: this.$i18n.t('Common.notPosted'),
          key: false
        }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$i18n.t('Common.view')
        // create: this.$i18n.t('Common.add'),
      },
      multipleSelection: [],
      isProcessing: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    fetchPage,

    getList() {
      this.listLoading = true;
      fetchPage(
        this.listQuery.page,
        this.listQuery.limit,
        this.listQuery.sort,
        this.listQuery.keyword
      ).then(response => {
        this.list = response.Data.Items;
        this.total = response.Data.Total;

        // Just to simulate the time of the request
        setTimeout(() => {
          this.listLoading = false;
        }, 1.5 * 1000);
      });
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop === 'ScanID') {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = 'ScanID asc';
      } else {
        this.listQuery.sort = 'ScanID desc';
      }
      this.handleFilter();
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
