<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <p>
      <label style="width:100%">{{ $t('ui.PP.StockingWave.title') }}</label>
    </p>
    <el-form
      ref="dataForm"
      :inline="true"
      :rules="rules"
      :model="primary"
      label-position="right"
      label-width="80px"
    >
      <el-form-item class="filter-item" :label="$t('ui.PP.StockingWave.DocNum')" prop="DocNum">
        <el-input v-model="primary.DocNum" readonly />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.StockingWave.STime')" prop="STime">
        <el-date-picker
          v-model="primary.STime"
          type="date"
          :clearable="false"
          :placeholder="$t('Common.pleaseSelectDate')"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.StockingWave.PLine')" prop="PLine">
        <el-select v-model="primary.PLine" filterable>
          <el-option
            v-for="item in pLineOptions"
            :key="item.EnumValue"
            :value="item.EnumValue"
            :label="item.Remark"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('Common.Remark')" prop="Remark">
        <el-input v-model="primary.Remark" style="width:500px" />
      </el-form-item>
      <p>
        <label style="width:100%">{{ $t('ui.PP.StockingWaveDetailed.title') }}</label>
      </p>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PP.StockingWaveDetailed.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" style="width: 220px" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.PP.StockingWaveDetailed.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" style="width: 380px" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-form-item :label="$t('ui.PP.StockingWaveDetailed.Qty')" prop="Qty">
          <el-input-number v-model="temp.Qty" controls-position="right" :min="0" />
        </el-form-item>
        <!-- <el-form-item :label="$t('Common.Remark')" prop="Remark">
               <el-input v-model="temp.Remark" v-waves style="width: 350px" class="filter-item" disabled />
        </el-form-item>-->
        <el-form-item class="filter-item">
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            icon="el-icon-edit"
            @click="handleUpdateDetail"
          >{{ $t('Common.update') }}</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleShowAdd"
      >{{ $t('ui.PP.StockingWaveDetailed.selectMaterial') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDeleteDetail"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleCommit"
      >{{ $t('Common.confirm') }}</el-button>
    </div>
    <el-table
      :key="tableKey"
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%;margin-top:10px;height:100%;"

      max-height="100%"
      @row-click="handleSelectDetail"
      @sort-change="sortDetailChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.DetailedID')"
        prop="DetailedID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.DocNum')"
        prop="DocNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.BaseEntry')"
        prop="BaseEntry"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BaseNum')"
        prop="BaseNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.PLine')"
        prop="PLine"

        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BinLocationCode')"
        prop="BinLocationCode"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BinLocationName')"
        prop="BinLocationName"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.BaseLine')"
        prop="BaseLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.ItemCode')"
        prop="ItemCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.ItemName')"
        prop="ItemName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column :label="$t('ui.PP.StockingWaveDetailed.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.ItmsGrpCode }}</span>
            </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PP.StockingWaveDetailed.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.ItmsGrpName }}</span>
            </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.Unit')"
        prop="Unit"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
    <MaterialReqKanbanDlg
      ref="MaterialReqKanbanDlg"
      :show.sync="dialogFormVisible"
      :p-line.sync="primary.PLine"
      :is-multiple="true"
      @close="handleSelectMaterialsConfirm"
    />
  </div>
</template>

<script>
import { fetchEntityByPLine as fetchBinLocationByPLine } from '@/api/MD/MD_BinLocation';
import { fetchListByPLine } from '@/api/PP/PP_MaterialReq';
import {
  fetchDetailPage,
  addDetail,
  updateDetail,
  batchDeleteDetail,
  addDetails,
  updateDetails
} from '@/api/PP/PP_StockingWaveDetailed';
import { fetchDocNum, fetchFIFOStock } from '@/api/PP/PP_StockingWave';
import { fetchAllUser } from '@/api/Sys/Sys_User';
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import MaterialReqKanbanDlg from '@/components/FLD/MaterialReqKanbanDlg';

_ = require('lodash');

export default {
  name: 'PP.PP_StockingWaveDetailed',
  components: {
    Pagination,
    MaterialReqKanbanDlg
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    var validateQty = (rule, value, callback) => {
      // 自定义验证器
      if (value > 0) {
        callback();
      } else if (value <= 0) {
        callback(new Error(this.$i18n.t('Common.mustBeGreaterThanZero')));
      } else {
        callback(new Error(this.$i18n.t('Common.numberRequired')));
      }
    };
    return {
      editFlag: 0, // 0，表示页面为创建，1，表示页面为编辑
      tableKey: 0,
      list: [],
      listDetail: [],
      total: 0,
      totalDetail: 0,
      listLoading: true,
      listDetailLoading: true,
      listQuery: {
        productionLine: undefined
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 20,
        docNum: undefined,
        sort: 'DetailedID asc'
      },
      primary: {
        WaveID: undefined,
        DocNum: undefined,
        STime: new Date(),
        PLine: undefined,
        Remark: '',
        Status: 1
      },
      temp: {
        DetailedID: undefined,
        DocNum: undefined,
        BaseEntry: undefined,
        BaseNum: undefined,
        PLine: undefined,
        BinLocationCode: undefined,
        BinLocationName: undefined,
        BaseLine: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        ItmsGrpCode: undefined,
        ItmsGrpName: undefined,
        Qty: undefined,
        Unit: undefined,
        Status: 1,
        Remark: undefined,
        ProductID: undefined,
        ProductDescription: undefined,
        PlannedQuantity: undefined,
        PlannedQuantityUnit: undefined,
        BOMQuantity: undefined,
        BOMQuantityUnit: undefined,
        CheckQty: undefined
      },
      pLineOptions: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        create: this.$i18n.t('Common.add')
      },
      multipleSelection: [],
      rules: {
        STime: [
          {
            required: true,
            message: this.$i18n.t('ui.PP.StockingWave.sTimeIsNull'),
            trigger: 'blur'
          }
        ],
        PLine: [
          {
            required: true,
            message: this.$i18n.t('ui.PP.StockingWave.pLineIsNull'),
            trigger: 'change'
          }
        ]
      },
      isProcessing: false
    };
  },
  created() {
    // this.getPLineOptions();

    this.getDict('PP005').then(data => {
      data = _.uniqBy(data, x => x.EnumValue);
      data = _.sortBy(data, x => x.EnumValue);
      console.log(data);
      this.pLineOptions = data;
      if (this.primary.PLine) {
      } else {
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        if (this.pLineOptions && this.pLineOptions.length > 0) {
          this.primary.PLine = this.pLineOptions[0].EnumValue;
        }
      }
      this.getPageParams(this.getDetailList);
    });
  },
  methods: {
    getDocNum(doneCallback) {
      fetchDocNum().then(response => {
        if (response.Code === 2000) {
          this.primary.DocNum = response.Data;
          if (doneCallback) doneCallback();
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getPageParams(doneCallback) {
      this.primary = Object.assign({}, this.$route.params);
      if (this.primary.DocNum) {
        // 编辑
        this.editFlag = 1;
        if (doneCallback) doneCallback();
      } else {
        // 新增
        this.editFlag = 0;
        this.getDocNum(doneCallback);
      }
      if (this.primary.STime) {
      } else {
        this.primary.STime = new Date();
      }
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.primary.DocNum;
      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    getPLineOptions() {
      this.getDict('PP005').then(data => {
        data = _.uniqBy(data, x => x.EnumValue);
        data = _.sortBy(data, x => x.EnumValue);
        console.log(data);
        this.pLineOptions = data;
        if (this.primary.PLine != undefined) {
        } else {
          // 如果主单没有生产线信息,默认选择下拉列表中的第一项
          if (this.pLineOptions && this.pLineOptions.length > 0) {
            this.primary.PLine = this.pLineOptions[0].EnumKey;
          }
        }
      });
    },
    getPLineValue(key) {
      var obj = this.pLineOptions.find(x => x.EnumKey == key);
      if (obj) return obj.EnumValue;
      else return '';
    },
    sortDetailChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleDetailFilter();
    },
    resetDialog() {
      this.temp = {
        DocNum: this.primary.DocNum,
        STime: new Date(),
        PLine: undefined,
        Remark: '',
        ItemCode: '',
        Qty: 0
      };
      this.listQuery.keyword = '';
    },
    commit() {
      this.isProcessing = true;
      this.primary.BaseNum = this.listDetail[0].BaseNum;
      console.log(this.primary);
      console.log(this.listDetail);
      if (this.editFlag == 1) {
        updateDetails({
          primary: this.primary,
          list: this.listDetail
        }).then(response => {
          if (response.Code === 2000) {
            this.isProcessing = false;
            // 跳转回主单页面
            this.backTo('PP.PP_StockingWave');
          } else {
            this.showNotify('error', response.Message);
            this.isProcessing = false;
          }
        });
      } else {
        this.primary.Status = 1;
        addDetails({
          primary: this.primary,
          list: this.listDetail
        }).then(response => {
          if (response.Code === 2000) {
            this.isProcessing = false;
            // 跳转回主单页面
            this.backTo('PP.PP_StockingWave');
          } else {
            this.showNotify('error', response.Message);
            this.isProcessing = false;
          }
        });
      }
    },
    handleDetailFilter() {
      this.getDetailList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleShowAdd() {
      if (this.primary.PLine) {
        this.listQuery.productionLine = this.primary.PLine;
        this.resetDialog();
        this.dialogStatus = 'create';
        this.dialogFormVisible = true;
      } else {
        this.showNotify('error', 'ui.PP.StockingWave.pLineIsNull');
      }
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.listDetail.indexOf(v);
          this.listDetail.splice(i, 1);
        });
      }
    },
    handleCommit() {
      var commitDetail = this.listDetail;
      if (commitDetail.length > 0) {
        var isSameOrder = commitDetail.find(
          x => x.BaseNum != commitDetail[0].BaseNum
        );
        if (isSameOrder) {
          this.showNotify('warning', 'ui.PP.StockingWave.sameOrderNum');
          return;
        }
      } else {
        this.showNotify('warning', 'ui.PP.StockingWave.PrimaryIsNull');
        return;
      }
      this.$confirm(
        this.$i18n.t('Common.committingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.commit();
      });
    },
    handleSelectMaterialsConfirm(materials) {
      if (materials && materials.length > 0) {
        materials.forEach(x => {
          var detail = this.listDetail.find(
            obj => obj.ItemCode == x.MaterialID
          );

          Object.assign(this.temp, x);
          this.temp.CheckQty = x.StockingQty;
          var r = Object.assign({}, this.temp);
          r.PlannedQuantity = x.PlanQty;
          r.BOMQuantity = x.BOMReqQty;
          r.BOMQuantityUnit = x.BOMUnit;
          r.ConversionRate = x.ConversionRate;
          r.PlannedStartDate = x.PlannedStartDate;
          r.StockingQty = x.StockingQty;
          r.Qty = x.ReqQty;
          r.BaseNum = x.ProductionOrderID;
          r.PLine = x.ProductionLine;
          r.ItemCode = x.MaterialID;
          r.ItemName = x.MaterialDescription;
          r.Qty = x.StockingQty;
          r.Unit = x.BOMUnitCode;
          r.PlannedQuantity = x.PlanQty;
          r.PlannedQuantityUnit = x.BOMUnitCode;
          r.BOMQuantity = x.BOMQty;
          r.BOMQuantityUnit = x.BOMUnitCode;
          r.CheckQty = x.StockingQty;
          this.primary.STime = x.CTime;
          if (detail) {
            // 已存在的同种物料不添加
          } else {
            this.listDetail.push(r);
          }
        });
      }
    },
    handleSelectDetail(row, col, evt) {
      this.temp = Object.assign({}, row);
      this.temp.DocNum = this.primary.DocNum;
    },
    handleUpdateDetail() {
      var detail = this.listDetail.find(
        obj =>
          obj.ItemCode == this.temp.ItemCode &&
          obj.ItmsGrpCode == this.temp.ItmsGrpCode
      );
      if (detail) {
        var updateQty = this.temp.Qty;
        var checkQty = this.temp.CheckQty;
        console.log(this.temp);
        if (updateQty > checkQty) {
          this.showNotify('warning', 'Common.inCorrectSaleQty');
          return;
        }
        detail.Qty = this.temp.Qty;
        detail.Remark = this.temp.Remark;
      }
    }
  }
};
// todo: 判断寄售物料
</script>
