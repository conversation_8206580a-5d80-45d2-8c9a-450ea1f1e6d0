<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateRangeValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.PP.StockingWave.Status')"
        @change="getList"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.EnumKey"
          :label="item.EnumValue"
          :value="item.EnumKey"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-permission="{ name: 'PP.PP_StockingWave.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-permission="{ name: 'PP.PP_StockingWave.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}</el-button>

      <el-button
        v-permission="{ name: 'PP.PP_StockingWave.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}</el-button>

      <el-button
        v-permission="{ name: 'PP.PP_StockingWave.Complete' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-success"
        :disabled="completeable"
        @click="handleComplete"
      >{{ $t("ui.PP.StockingWave.complete") }}</el-button>

      <el-button
        v-permission="{ name: 'PP.PP_StockingWave.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-printer"
        :disabled="printable"
        @click="handlePrint"
      >{{ $t("Common.print") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_StockingWave.Export' }"
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWave.WaveID')"
        prop="WaveID"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWave.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWave.PLine')"
        prop="PLine"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWave.STime')"
        prop="STime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.STime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWave.Status')"
        prop="Status"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.Status == 2 ? $t("ui.PP.StockingWave.Status_stockedUp") : scope.row.Status == 3 ? $t("ui.PP.StockingWave.Status_completed") : $t("ui.PP.StockingWave.Status_notBegin") }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.PP.StockingWaveDetailed.title") }}</span>
    </p>

    <el-table
      ref="multipleTable"
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="detailSortChange"
    >
      <el-table-column
        v-if="false"
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.DetailedID')"
        prop="DetailedID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.PLine')"
        prop="PLine"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BinLocationCode')"
        prop="BinLocationCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.StockingWaveDetailed.BaseLine')"
        prop="BaseLine"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.ItemCode')"
        prop="ItemCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.ItemName')"
        prop="ItemName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PP.StockingWaveDetailed.Remark')"
        prop="Remark"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail > 0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
  </div>
</template>

<script>
import {
  fetchPage,
  add,
  update,
  batchDelete,
  fetchDocNum,
  finishPreparation,
  exportExcelFile
} from '@/api/PP/PP_StockingWave';
import { exportToExcel } from '@/utils/excel-export';
import {
  fetchDetailPage,
  addDetail,
  updateDetail,
  batchDeleteDetail
} from '@/api/PP/PP_StockingWaveDetailed';

import { printOrderToPDF } from '@/api/PP/PP_Print';

import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import { convertToKeyValue } from '@/utils';
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'PP.PP_StockingWave',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      listDetail: null,
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        dateRangeValue: [
          new Date(),
          new Date()
        ],
        status: ''
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 10,
        docNum: ''
      },
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      downloadLoading: false,
      multipleSelection: [],
      currentRow: {
        WaveID: '',
        DocNum: '',
        PLine: '',
        STime: '',
        Status: undefined
      },
      statusOptions: [],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      if (this.multipleSelection && this.multipleSelection.length == 1) {
        if (this.multipleSelection[0].Status <= 2) return false;
      }
      return true;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status != 1) return true;
      }
      return false;
    },
    printable() {
      return this.multipleSelection.length !== 1;
    },
    completeable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status !== 2) return true;
      }
      return false;
    }
  },
  created() {
    this.getStatusOptions();
    this.getList();
  },
  methods: {
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.listQuery.dateRangeValue) {
        this.listQuery.fromTime = this.listQuery.dateRangeValue[0];
        this.listQuery.toTime = this.listQuery.dateRangeValue[1];
      }
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listDetail = [];
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.currentRow.DocNum;

      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    getStatusOptions() {
      this.getDict('PP001').then(data => {
        this.statusOptions = data;
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter(1);
    },
    detailSortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.handleFilter(2);
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      if (flag == 2) {
        this.clearTables(2);
        if (this.currentRow) this.getDetailList();
      } else {
        this.clearTables(3);
        this.listQuery.PageNumber = 1;
        this.listQuery.PageSize = 10;
        this.getList();
      }
    },
    handleCreate() {
      this.routeTo('PP.PP_StockingWaveDetailed');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        this.routeTo(
          'PP.PP_StockingWaveDetailed',
          Object.assign(selectRows[0])
        );
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          var arrRowsID = selectRows.map(function(v) {
            return v.WaveID;
          });
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.fromTime, this.listQuery.toTime],
        state: this.listQuery.status
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '生产备货波次单管理')
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.handleFilter(2);
    },
    handleComplete() {
      var selectRows = this.multipleSelection;
      // if (this.checkSingleSelection(selectRows)) {
      this.$confirm(
        this.$i18n.t('ui.PP.StockingWave.finishingPreparationConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        var DocNums = selectRows.map(x => x.DocNum);
        finishPreparation(DocNums).then(response => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter(1);
          } else {
            this.showNotify('error', response.Message);
          }
        });
      });
      // }
    },
    handlePrint() {
      var selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (this.checkSingleSelection(selectRows)) {
        printOrderToPDF({
          docNum: selectRows[0].DocNum,
          templateCode: 'PP_StockingWave'
        }).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    }
  }
};
</script>
