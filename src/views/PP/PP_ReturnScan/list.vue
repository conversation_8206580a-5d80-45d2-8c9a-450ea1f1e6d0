<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{name:'PP.PP_ReturnScan.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>

      <el-button
        v-waves
        v-permission="{name:'PP.PP_ReturnScan.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_ReturnScan.Print' }"
        class="filter-item"
        type="primary"
        icon="el-icon-printer"
        size="small"
        @click="handlePrint"
      >{{ $t("Common.print") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_ReturnScan.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="审核状态" prop="ExamineStatus" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ExamineStatus === 0">未审核</span>
          <span v-if="scope.row.ExamineStatus === 1">已审核</span>
        </template>
      </el-table-column>
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="SAP物料凭证号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip/>
      <el-table-column label="SAP物料凭证行号" prop="SapLine" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>车间退料明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="车间退料单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="车间退料行号" prop="Line" align="center" width="160" /> -->
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" />
      <!-- <el-table-column label="组件行项目号" prop="ComponentLineNo" align="center" width="160" /> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="需求数量" prop="DemandQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="移动类型" prop="MovementType" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MovementType === '261'">工废</span>
          <span v-if="scope.row.MovementType === '311'">料废</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="工厂代码" prop="FactoryCode" align="center" width="100" show-overflow-tooltip/> -->
      <el-table-column label="出库名称" prop="OutWhsName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库区域" prop="OutRegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库库位" prop="OutBinName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="入库名称" prop="InWhsName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="入库区域" prop="InRegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="入库库位" prop="InBinName" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.AssessmentCategory === '01'">自制</span>
          <span v-if="scope.row.AssessmentCategory === '02'">外购</span>
        </template>
      </el-table-column>
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="120"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="SAP物料凭证号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="SAP物料凭证行号" prop="SapLine" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" show-overflow-tooltip/>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160"
        :formatter="formatDateTime" /> -->

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  doPost,
  batchDelete,
  printToPDF,
  GetDetailedPageList
} from '@/api/PP/PP_ReturnScan';

export default {
  name: 'PP.PP_ReturnScan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 过账功能模块
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          doPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.postSuccess');
              } else {
                this.showNotify('error', 'Common.operationFailed');
              }
              this.getList();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '车间退料管理');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.getList();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          var arrRowsID = selectRows.map(v => v.ID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.getList();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    // 打印
    handlePrint() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      const DocNums = selectRows.map(v => v.DocNum);
      const query = {
        docNums: DocNums,
        templateCode: ''
      };
      printToPDF(query).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = {
        DocNum: this.currentRow.DocNum,
        PageNumber: this.listDetailQuery.PageNumber,
        PageSize: this.listDetailQuery.PageSize
      };
      GetDetailedPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    }
  }
};
</script>
