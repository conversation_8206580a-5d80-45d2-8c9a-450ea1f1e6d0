<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        size="small"
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>

      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_MaterialDistributionController.Add'}"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleSave"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_MaterialDistributionController.Edit' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-edit"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >
        {{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_MaterialDistributionController.Delete'}"
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_MaterialDistributionController.Posting'}"
        class="filter-item"
        type="success"
        size="small"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_MaterialDistributionController.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>

      <!-- <el-button v-waves v-permission="{ name: 'PP.PP_MaterialDistributionController.ExportGather' }" class="filter-item" type="primary"
      icon="el-icon-document" size="small" @click="handleExportGather">导出汇总</el-button> -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="配送单号" prop="DeliveryOrderNo" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryOrderNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="装配线" prop="ProductionLineDes" width="100" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionLineDes }}</span>
        </template>
      </el-table-column>
      <el-table-column label="装配日期" prop="StartTime" align="center" width="100" show-overflow-tooltip :formatter="formatDate" />
      <el-table-column label="员工号" prop="EmployeeNumber" width="80" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.EmployeeNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="员工姓名" prop="EmployeeName" width="80" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.EmployeeName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ReturnScan.PostUser')" prop="DoPostMan" width="80" align="center" />
      <el-table-column
        :label="$t('ui.PO.PO_ReturnScan.PostTime')"
        prop="DoPostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column
        label="SAP过账日期"
        prop="ManualPost"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip /> -->
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column :label="$t('ui.PO.PO_ReturnScan.IsPosted')" prop="IsDoPost" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDoPost | yesnoFilter }}</span>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>工单物料配送明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      height="300"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="配送单号" prop="DeliveryOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="装配日期" prop="StartTime" align="center" :formatter="formatDate" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="线体编码" prop="ProductionLineCode" align="center" show-overflow-tooltip /> -->
      <el-table-column label="装配线" prop="ProductionLineDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="配送数量" prop="DemandQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="供应商数量" prop="Supplier" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="自制数量" prop="HomemadeQty" align="center" width="80" show-overflow-tooltip />
      <!-- <el-table-column label="员工号" prop="EmployeeNumber" align="center" show-overflow-tooltip /> -->
      <el-table-column label="员工姓名" prop="EmployeeName" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="ComponentUnit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转出仓库" prop="OutWarehouse" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="转入仓库" prop="DeliverLocation" align="center" show-overflow-tooltip />
      <el-table-column label="配置描述" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="物料组" prop="MaterialGroupCode" align="center" show-overflow-tooltip />
      <el-table-column label="物料组描述" prop="MaterialGroupDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentType" align="center" show-overflow-tooltip />
      <el-table-column :label="$t('ui.PO.PO_ReturnScan.IsPosted')" prop="IsDoPost" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDoPost | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ReturnScan.PostUser')"
        prop="DoPostMan"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        :label="$t('ui.PO.PO_ReturnScan.PostTime')"
        prop="DoPostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
    <!-- 过账日期 -->
    <el-dialog title="请选择过账日期" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <el-form-item label="过账日期" label-width="120px" prop="PostTime">
          <el-date-picker
            v-model="ruleForm.PostTime"
            :clearable="false"
            type="date"
            placeholder="过账日期"
            style="width: 100%;"
            format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handlePosting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '../../../directive/permission/permission';
import Pagination from '@/components/Pagination'; // 分页

import {
  fetchList,
  batchDelete,
  exportExcelFile,
  DoPost,
  GetDetailPageList,
  ExportSummary
} from '../../../api/PP/PP_MaterialDistributionController';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  formatDate,
  formatDateTime
} from '../../../utils';

export default {
  name: 'PP.PP_MaterialDistributionController',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      postDisableStatus: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10
      },
      currentRow: {},
      dialogVisible: false,
      ruleForm: {
        PostTime: ''
      },
      rules: {
        PostTime: [{
          type: 'date',
          required: true,
          message: '请选择过账日期',
          trigger: 'change'
        }]
      }
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    },
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/PP/PP/PP_MaterialDistributionController') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '配送单号为：' + v.DeliveryOrderNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '配送单号为：' + v.DeliveryOrderNo + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          var arrRowsID = selectRows.map(v => v.DeliveryOrderNo);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handlePostingBtn() {
      this.ruleForm.PostTime = '';
      this.dialogVisible = true;
    },
    handlePosting() {
      // 过账功能模块
      // this.$refs['ruleForm'].validate((valid) => {
      //   if (valid) {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsDoPost === true) {
            this.showNotify('warning', '配送单号为：' + v.DeliveryOrderNo + '信息已过账，禁止重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          this.dialogVisible = false;
          const query = {
            ManualPostTime: this.$moment(this.ruleForm.PostTime).format('YYYY-MM-DD'),
            entities: this.multipleSelection
          };
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', res.Message || 'Common.postSuccess');
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
      // } else {
      //   console.log('error submit!!');
      //   return false;
      // }
      // });
      // 过账功能
    },
    handleExport() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        DeliveryOrderNos: selectRows.map(v => v.DeliveryOrderNo) || []
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '工单物料配送单');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleExportGather() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      ExportSummary(exportQuery).then(res => {
        exportToExcel(res.data, '工单物料配送单汇总');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
        console.log(this.hasPostedData);
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign({
        deliveryNo: this.currentRow.DeliveryOrderNo
      }, this.listDetailQuery);
      GetDetailPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
        }
        this.listDetailLoading = false;
      }).catch(err => {
        console.log(err);
        this.listDetailLoading = false;
      });
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },

    handleSave() {
      this.routeTo('PP.PP_MaterialDistributionControllerDetail', { status: 'add' });
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '配送单号为：' + v.DeliveryOrderNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '配送单号为：' + v.DeliveryOrderNo + '信息已过账，禁止编辑');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('PP.PP_MaterialDistributionControllerDetail', this.multipleSelection[0]);
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    }
  }
};
</script>
