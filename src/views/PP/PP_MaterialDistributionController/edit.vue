<template>
  <div class="app-container">
    <p>
      <label style="width:100%">工单物料配送单明细</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox"
      :inline="true"
      :rules="rules"
      :model="listQuery"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="物料">
            <el-input v-model="searchQuery.BaseNum" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">
        {{ $t("Common.confirm") }}</el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        label="装配日期"
        prop="StartTime"
        align="center"
        :formatter="formatDate"
        width="100"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="线体编码" prop="ProductionLineCode" align="center" show-overflow-tooltip /> -->
      <el-table-column label="装配线" prop="ProductionLineDes" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="配送数量" prop="DemandQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="供应商数量" prop="Supplier" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="自制数量" prop="HomemadeQty" align="center" width="80" show-overflow-tooltip />
      <!-- <el-table-column label="员工号" prop="EmployeeNumber" align="center" show-overflow-tooltip /> -->
      <el-table-column label="员工姓名" prop="EmployeeName" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="ComponentUnit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转出仓库" prop="OutWarehouse" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="转入仓库" prop="DeliverLocation" align="center" show-overflow-tooltip />
      <el-table-column label="配置描述" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="物料组" prop="MaterialGroupCode" align="center" show-overflow-tooltip />
      <el-table-column label="物料组描述" prop="MaterialGroupDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentType" align="center" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!--查询采购退货申请单号  -->
    <add-select-model ref="modalForm" :data-list="list" :edit-status="editStatus" @ok="modalFormOk" />
    <!-- 编辑明细 -->
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'
import {
  parseTime,
  formatDate,
  formatDateTime
} from '@/utils';
import {
  GetDetailList,
  Add,
  Update,
  DeleteDeliveryDetail
} from '../../../api/PP/PP_MaterialDistributionController';

export default {
  name: 'PP.PP_MaterialDistributionControllerDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        DocNum: '',
        BaseNum: '',
        PageNumber: 1,
        PageSize: 10
      },
      multipleSelection: [],
      rules: {
        BaseNum: [{
          required: true,
          message: this.$i18n.t('ui.PO.PO_ReturnScanDetail.BaseNum'),
          trigger: 'blur'
        }]
      },
      drawer: false,
      searchQuery: {
        BaseNum: '',
        DeliveryOrderNo: '',
        ManualPostTime: new Date()
      },
      editStatus: 'create',
      delList: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    order() {
      return this.editStatus === 'create'
    }
  },
  created() {
    this.getPageParams();
  },
  activated() {
    if (Object.keys(this.$route.params).length > 0) {
      console.log('activated调用了', this.$route.params);
      if (this.$route.params.status === 'add') {
        this.searchQuery = {
          BaseNum: '',
          ManualPostTime: new Date()
        };
        this.list = [];
      }
      this.getPageParams();
    }
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ID) {
            v.IsDelete = true;
            this.delList.push(v.ID)
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择生产订单');
        return;
      }
      if (!this.searchQuery.ManualPostTime) {
        this.showNotify('warning', '过账时间不能为空');
        return;
      }
      let switchBtn = true;
      this.list.some(res => {
        if (res.DemandQty === null || res.DemandQty === 0 || res.DemandQty === '0' || res.DemandQty === '') {
          this.showNotify('warning', '需求数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.AssessmentCategory === 'B') {
          if (res.AssessmentType === null || res.AssessmentType === '') {
            this.showNotify('warning', '评估类型不能为空');
            switchBtn = false;
            return true;
          }
        }
      });

      if (switchBtn) {
        this.startLoading();
        const query = {
          ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
          details: this.list,
          DelDetailIds: this.delList,
          DeliveryOrderNo: this.searchQuery.DeliveryOrderNo
        };
        if (this.editStatus === 'create') {
          Add(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PP.PP_MaterialDistributionController');
            } else {
              this.showNotify('error', response.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        } else {
          Update(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PP.PP_MaterialDistributionController');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ComponentCode + v.ProductionLineCode + v.StartTime === record.ComponentCode + record
            .ProductionLineCode + record.StartTime) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const obj = {};
      this.list = this.list.concat(record).reduce((cur, next) => {
        obj[next.ComponentCode + next.ProductionLineCode + next.StartTime + next.EmployeeNumber] ? '' : obj[next.ComponentCode + next.ProductionLineCode + next.StartTime + next.EmployeeNumber] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        deliveryNo: this.searchQuery.DeliveryOrderNo
      };
      this.listLoading = true;
      GetDetailList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      console.log(this.searchQuery, 123);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        this.editStatus = 'create';
      }
    }
  }
};
</script>
