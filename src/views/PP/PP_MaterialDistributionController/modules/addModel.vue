<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form :model="model">
        <el-form-item label="评估类型" label-width="100px">
          <el-select v-model="model.AssessmentType" filterable placeholder="请选择" :disabled="disabled" style="width: 100%;" @change="changeType">
            <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="转出仓库" label-width="100px">
          <el-select v-model="model.OutWarehouse" filterable placeholder="请选择" style="width: 100%;" @change="changeWhsName">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="转入仓库" label-width="100px">
          <el-select v-model="model.DeliverLocation" filterable placeholder="请选择" style="width: 100%;" @change="changeInWhsName">
            <el-option v-for="item in optionsIn" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="需求数量" label-width="100px">
          <el-input v-model="model.DemandQty" @blur="changeQty" />
        </el-form-item>

      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZ_SAP
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      disabled: false,
      direction: 'rtl',
      model: {
        OutWarehouse: '',
        AssessmentType: '',
        AssessmentCategory: '',
        DeliverLocation: '',
        DemandQty: ''
      },
      options: [],
      optionsIn: [],
      types: [
        {
          value: '01',
          label: '自制'
        },
        {
          value: '02',
          label: '外购'
        }],
      StockQty: 0
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.GetXZ_SAP();
      this.GetXZ_SAP_In();
      this.drawer = true;
      this.model = Object.assign({}, record);
      this.StockQty = this.model.DemandQty;
      this.disabled = !(record.AssessmentCategory === 'B');
      if (record.AssessmentCategory === 'B') {
        this.model.AssessmentType = '02'
      }
      console.log(this.disabled)
    },
    handleSave() {
      if (this.model.DemandQty <= 0) {
        this.showNotify('warning', '当前数量不能为0或者小于0');
        return
      } else if (this.model.DemandQty > this.StockQty) {
        this.showNotify('warning', '当前数量不能大于需求数量');
        return
      }
      this.$emit('ok', this.model);
      this.drawer = false;
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      // console.log(e,this.options)
      // this.model.OutWarehouse = this.options.filter(item => item.value === e )[0].label
      console.log(this.model);
    },
    GetXZ_SAP_In() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.optionsIn = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.optionsIn.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeInWhsName(e) {
      // console.log(e,this.options)
      // this.model.OutWarehouse = this.optionsIn.filter(item => item.value === e )[0].label
      console.log(this.model);
    },
    changeType(e) {
      // this.model.AssessmentType = this.types.filter(item => item.value === e )[0].label
      console.log(this.model);
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '当前数量不能为0或者小于0');
      } else if (e > this.StockQty) {
        this.showNotify('warning', '当前数量不能大于需求数量');
      }
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
