<template>
  <el-dialog
    v-loading="isProcessing"
    title="物料明细"
    :visible.sync="dialogCustomerFormVisible"
    width="80%"
    top="5vh"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        size="small"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <!-- 线体下拉 -->
      <el-select
        v-model="listQuery.ProductionLineCode"
        size="small"
        class="filter-item"
        filterable
        clearable
        placeholder="请选择线体"
      >
        <el-option
          v-for="item in options"
          :key="item.ProductionLineCode"
          :label="item.ProductionLineCode+'-'+item.ProductionLineDes"
          :value="item.ProductionLineCode"
        />
      </el-select>
      <el-input
        v-model="listQuery.ProductionOrderNo"
        size="small"
        class="filter-item"
        style="width: 120px"
        placeholder="员工信息"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.MaterialNo"
        size="small"
        class="filter-item"
        style="width: 120px"
        placeholder="物料信息"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.ProductionLineDes"
        size="small"
        class="filter-item"
        style="width: 120px"
        placeholder="车间"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 120px"
        placeholder="物料组/评估类别"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <br>
      <span>员工是否为空：</span>
      <el-switch
        v-model="listQuery.IsEmpty"
        active-color="#13ce66"
        inactive-color="#ff4949"
        active-text="是"
        inactive-text="否"
        @change="handleSearchFilter"
      />
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
      <el-button v-waves class="filter-item" type="primary" size="small" icon="el-icon-document" @click="handleExport">
        {{ $t("Common.export") }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="员工号" prop="EmployeeNumber" align="center" show-overflow-tooltip />
      <el-table-column label="员工姓名" prop="EmployeeName" align="center" show-overflow-tooltip />
      <el-table-column label="线体编码" prop="ProductionLineCode" align="center" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" align="center" width="140" show-overflow-tooltip />
      <el-table-column
        label="装配日期"
        prop="StartTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="需求数量" prop="DemandQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="供应商数量" prop="Supplier" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="自制数量" prop="HomemadeQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="ComponentUnit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转出仓库" prop="OutWarehouse" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="转入仓库" prop="DeliverLocation" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="配置描述" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="物料组" prop="MaterialGroupCode" align="center" show-overflow-tooltip />
      <el-table-column label="物料组描述" prop="MaterialGroupDes" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="100" show-overflow-tooltip /> -->
      <el-table-column label="评估类别" prop="AssessmentType" align="center" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetSapOrderList,
  GetAllLine,
  ExportToExcelFileForSAP
} from '@/api/PP/PP_MaterialDistributionController';
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  exportToExcel
} from '@/utils/excel-export';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    },
    editStatus: {
      type: String,
      defaule: 'create'
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      isProcessing: false,
      listQuery: {
        ProductionOrderNo: '', // 生产订单
        MaterialNo: '', // 主机物料号
        ProductionLineCode: '', // 线体
        ProductionLineDes: '',
        IsEmpty: false, // 员工是否为空
        dateValue: [
          new Date(),
          new Date()
        ], // 时间区间
        PageNumber: 1,
        PageSize: 10,
        keyword: ''
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      options: [],
      listLoading: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      }
    }
  },
  computed: {

  },
  created() {},
  methods: {
    formatDate,
    formatDateTime,
    add() {
      this.listQuery = {
        ProductionOrderNo: '', // 生产订单
        MaterialNo: '', // 主机物料号
        ProductionLineCode: '', // 线体
        ProductionLineDes: '', // 车间文本
        IsEmpty: false, // 员工是否为空
        dateValue: [
          new Date(),
          new Date()
        ], // 时间区间
        PageNumber: 1,
        PageSize: 10,
        keyword: ''
      };
      this.handleCustomerFilter();
      this.GetAllLine();
      this.dialogCustomerFormVisible = true
    },
    edit(record) {
      this.model = Object.assign({}, record)
    },
    handleCustomerRowSelectEvent(selection) {
      console.log(this.dataList, selection, this.editStatus);
      const data = [];
      if (selection.length >= 1) {
        let switchBtn = true;
        selection.some(v => {
          this.dataList.some(res => {
            if (this.editStatus === 'edit') {
              console.log(v.ProductionLineCode + v.EmployeeNumber + v.StartTime, res.ProductionLineCode + res
                .EmployeeNumber + res.StartTime)
              if (v.ProductionLineCode + v.EmployeeNumber + v.StartTime !== res.ProductionLineCode + res
                .EmployeeNumber + res.StartTime) {
                this.showNotify('warning', '物料件号为：' + v.ComponentCode + '不在配送范围内，请勿添加');
                switchBtn = false;
                return true;
              }
            } else {
              if (v.ComponentCode + v.ProductionLineCode + v.StartTime === res.ComponentCode + res
                .ProductionLineCode + res.StartTime) {
                this.showNotify('warning', '物料件号为：' + v.ComponentCode + '已存在，请勿重复添加');
                switchBtn = false;
                return true;
              }
            }
          })
        });
        if (switchBtn) {
          this.multipleSelection = selection
        }
      } else {
        this.multipleSelection = selection
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      console.log(query, 123);
      GetSapOrderList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      this.multipleSelection.forEach((item) => {
        if (item.AssessmentCategory === 'B') {
          item.AssessmentType = '01'
        }
      });
      this.$emit('ok', this.multipleSelection);
      this.dialogCustomerFormVisible = false;
    },
    GetAllLine() {
      GetAllLine().then(res => {
        if (res.Code === 2000) {
          this.options = res.Data;
        }
      })
    },
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        dateValue: this.listQuery.dateValue,
        ProductionOrderNo: this.listQuery.ProductionOrderNo, // 生产订单
        ProductionLineDes: this.listQuery.ProductionLineDes,
        MaterialNo: this.listQuery.MaterialNo, // 主机物料号
        ProductionLineCode: this.listQuery.ProductionLineCode, // 线体
        IsEmpty: this.listQuery.IsEmpty, // 员工是否为空
        keyword: this.listQuery.keyword
      };
      ExportToExcelFileForSAP(exportQuery).then(res => {
        exportToExcel(res.data, '工单物料配送明细单');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    }
  }
}
</script>

<style scoped>
</style>
