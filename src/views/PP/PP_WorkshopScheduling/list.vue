<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="装配日期">
              <el-date-picker
                v-model="listQuery.dateValue"
                size="small"
                :clearable="false"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.createDateValue"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产管理员">
              <el-input
                v-model="listQuery.ProductionScheduler"
                size="small"
                class="filter-item"
                placeholder="生产管理员"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装配线">
              <el-input
                v-model="listQuery.ProductionLineCode"
                size="small"
                class="filter-item"
                placeholder="装配线"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户订单号">
              <el-input
                v-model="listQuery.OrderNo"
                size="small"
                class="filter-item"
                placeholder="客户订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关键字">
              <el-input
                v-model="listQuery.keyword"
                size="small"
                class="filter-item"
                :placeholder="$t('Common.keyword')"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!--
      <el-button v-permission="{ name: 'PP.PP_WorkshopScheduling.Print' }" v-waves class="filter-item" type="primary"
        icon="el-icon-document" :disabled="canNotUpdate" @click="handlePrint">打印排产单</el-button> -->
      <el-button
        v-permission="{ name: 'PP.PP_WorkshopScheduling.PrintIdCard' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handlePrintIdCard(1)"
      >
        打印定子组件身份卡</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_WorkshopScheduling.PrintIdCard' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handlePrintIdCard(2)"
      >
        打印转子组件身份卡</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_WorkshopScheduling.PrintLabel' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handlePrintLabel"
      >打印入库标签</el-button>
      <!-- <el-button v-waves v-permission="{ name: 'PP.PP_WorkshopScheduling.Export' }" class="filter-item" type="primary"
        icon="el-icon-document" size="small" @click="handleExport">{{ $t("Common.export") }}</el-button> -->
      <el-dropdown
        v-waves
        v-permission="{ name: 'PP.PP_WorkshopScheduling.Export' }"
        class="filter-item"
        split-button
        type="primary"
        size="small"
        style="margin-left: 10px;"
        @click="handleExport"
        @command="handleCommand"
      >
        {{ $t("Common.export") }}
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">导出排产单</el-dropdown-item>
          <el-dropdown-item command="2">导出常规线物料分解单</el-dropdown-item>
          <el-dropdown-item command="3">导出DT线物料分解单</el-dropdown-item>
          <el-dropdown-item command="4">导出试制线物料分解单</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-left: 10px;"
        @click="handleFilter"
      >查询</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="出厂编号" prop="SerialNo" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="EAP出厂编号" prop="EapSerialNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="发货单位" prop="Shippers" align="center" width="140" show-overflow-tooltip />
      <el-table-column
        label="交货日期"
        prop="DeliveryTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="产品型号" prop="ProductModel" align="center" width="200" show-overflow-tooltip />
      <el-table-column label="装配线" prop="ProductionLineDes" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        label="装配日期"
        prop="StartTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="生产批次" prop="ProductionBatch" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="客户订单号" prop="OrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="产品件号" prop="MaterialNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="产品描述" prop="MaterialName" align="center" width="120" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="通知状态" prop="IsNoticed" align="center" width="80" fixed="right" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.IsNoticed === 1 || scope.row.IsNoticed === '1' ">已通知</span>
          <span v-if="scope.row.IsNoticed === 0 || scope.row.IsNoticed === '0' ">未通知</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  PrintScheduling,
  PrintStatorLable,
  PrintRotorLable,
  PrintStorageLabel,
  ExportToExcelFileForHostLine,
  ExportToExcelFileForDTLine,
  ExportToExcelFileForRepairLine
} from '@/api/PP/PP_WorkshopScheduling';

export default {
  name: 'PP.PP_WorkshopScheduling',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        createDateValue: [],
        ProductionLineCode: '',
        ProductionScheduler: '',
        OrderNo: '',
        ContractNo: ''
        // isPosted: ""
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      tableHeight: '300px'
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    // this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue && this.listQuery.dateValue.length > 0) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      if (this.listQuery.createDateValue && this.listQuery.createDateValue.length > 0) {
        this.listQuery.createDateValue[0] = this.$moment(this.listQuery.createDateValue[0]).format('YYYY-MM-DD');
        this.listQuery.createDateValue[1] = this.$moment(this.listQuery.createDateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        ProductionLineCode: this.listQuery.ProductionLineCode,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        dateValue: this.listQuery.dateValue,
        createDateValue: this.listQuery.createDateValue,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, res.fileName);
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleCommand(command) {
      this.isProcessing = true;
      console.log(command);
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        ProductionLineCode: this.listQuery.ProductionLineCode,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        dateValue: this.listQuery.dateValue,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      if (command === '1') {
        this.handleExport()
      } else if (command === '2') {
        ExportToExcelFileForHostLine(exportQuery).then(res => {
          exportToExcel(res.data, res.fileName);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      } else if (command === '3') {
        ExportToExcelFileForDTLine(exportQuery).then(res => {
          exportToExcel(res.data, res.fileName);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      } else if (command === '4') {
        ExportToExcelFileForRepairLine(exportQuery).then(res => {
          exportToExcel(res.data, res.fileName);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    handlePrint() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.isProcessing = true;
        PrintScheduling({
          docNums: selectRows.map(x => x.ProductionOrderNo)
        }).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      }
    },
    handlePrintIdCard(type) {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        if (type === 1) {
          let switchBtn = true;
          selectRows.some(v => {
            // if (v.IsComponent === 2) {
            //   this.showNotify("warning", '出厂编号为：' + v.SerialNo + '属于转子，请进行转子组件身份卡打印');
            //   switchBtn = false;
            //   return true
            // }
            // if (v.IsComponent === 0) {
            //   this.showNotify("warning", '出厂编号为：' + v.SerialNo + '不属于定子，请勿进行定子组件身份卡打印');
            //   switchBtn = false;
            //   return true
            // }
            if (v.IsComponent === 1 || v.IsComponent === 2) {
              this.showNotify('warning', '出厂编号为：' + v.SerialNo + '，只允许主机打印');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            this.isProcessing = true;
            PrintStatorLable({
              docNums: selectRows.map(x => x.ProductionOrderNo)
            }).then(response => {
              console.log(response);
              window.open(this.API.BaseURL + response.Data.PrintedPDF);
              this.isProcessing = false;
            }).catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
          }
        } else {
          let switchBtn = true;
          selectRows.some(v => {
            // if (v.IsComponent === 1) {
            //   this.showNotify("warning", '出厂编号为：' + v.SerialNo + '属于定子，请进行定子组件身份卡打印');
            //   switchBtn = false;
            //   return true
            // }
            // if (v.IsComponent === 0) {
            //   this.showNotify("warning", '出厂编号为：' + v.SerialNo + '不属于转子，请勿进行转子组件身份卡打印');
            //   switchBtn = false;
            //   return true
            // }
            if (v.IsComponent === 1 || v.IsComponent === 2) {
              this.showNotify('warning', '出厂编号为：' + v.SerialNo + '，只允许主机打印');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            this.isProcessing = true;
            PrintRotorLable({
              docNums: selectRows.map(x => x.ProductionOrderNo)
            }).then(response => {
              console.log(response);
              window.open(this.API.BaseURL + response.Data.PrintedPDF);
              this.isProcessing = false;
            }).catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
          }
        }
      }
    },
    handlePrintLabel() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.isProcessing = true;
        PrintStorageLabel({
          docNums: selectRows.map(x => x.SerialNo)
        }).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      }
    }
  }
};
</script>
