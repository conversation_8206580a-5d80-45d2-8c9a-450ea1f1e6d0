<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" label-width="100px" :rules="rules">
        <el-form-item label="序列号">
          <el-input v-model="model.SerialNo" disabled />
        </el-form-item>
        <el-form-item label="生产订单">
          <el-input v-model="model.ProductionOrderNo" disabled />
        </el-form-item>
        <el-form-item label="线体编码" prop="ProductionLineCode">
          <el-select
            v-model="model.ProductionLineCode"
            style="width: 100%;"
            filterable
            placeholder="请选择"
            @change="changeProductionLineCode"
          >
            <el-option
              v-for="item in ProductionLineOptions"
              :key="item.ProductionLineCode"
              :label="item.ProductionLineCode+'-'+item.ProductionLineDes"
              :value="item.ProductionLineCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="线体描述">
          <el-input v-model="model.ProductionLineDes" disabled />
        </el-form-item>
        <el-form-item label="工序编号" prop="WorkingProcedureCode">
          <el-select
            v-model="model.WorkingProcedureCode"
            style="width: 100%;"
            filterable
            placeholder="请选择"
            :disabled="WorkingProcedureDisabled"
            @change="changeWorkingProcedure"
          >
            <el-option
              v-for="item in WorkingProcedureOptions"
              :key="item.WorkingProcedureCode"
              :label="item.WorkingProcedureCode+'-'+item.WorkingProcedureDes"
              :value="item.WorkingProcedureCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工序描述">
          <el-input v-model="model.WorkingProcedureDes" disabled />
        </el-form-item>
        <el-form-item label="检验项" prop="InspectionItem">
          <el-select
            v-model="model.InspectionItem"
            style="width: 100%;"
            filterable
            placeholder="请选择"
            :disabled="InspectionItemDisabled"
          >
            <el-option v-for="item in InspectionItemOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="测量值" prop="Measurements">
          <el-input-number v-model="model.Measurements" :precision="4" :step="0.0001" :max="10000000" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="model.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetProductionLine,
  GetWorkingProcedure,
  GetInspection
} from '@/api/PP/PP_ProcessInspection';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        SerialNo: '',
        ProductionOrderNo: '',
        ProductionLineCode: '',
        ProductionLineDes: '',
        WorkingProcedureCode: '',
        WorkingProcedureDes: '',
        InspectionItem: '',
        Measurements: '',
        Remark: ''
      },
      WorkingProcedureDisabled: true,
      InspectionItemDisabled: true,
      ProductionLineOptions: [],
      WorkingProcedureOptions: [],
      InspectionItemOptions: [],
      rules: {
        ProductionLineCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        WorkingProcedureCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InspectionItem: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Measurements: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: 'create'
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {
      this.model = {
        SerialNo: '',
        ProductionOrderNo: '',
        ProductionLineCode: '',
        ProductionLineDes: '',
        WorkingProcedureCode: '',
        WorkingProcedureDes: '',
        InspectionItem: '',
        Measurements: '',
        Remark: ''
      };
      this.editStatus = 'create';
      this.WorkingProcedureDisabled = true;
      this.InspectionItemDisabled = true;
      this.ProductionLineOptions = [];
      this.WorkingProcedureOptions = [];
      this.InspectionItemOptions = [];
      this.getProductionLine();
      this.drawer = true;
    },
    edit(record) {
      this.editStatus = 'edit';
      this.getProductionLine();
      this.model = Object.assign({}, record);
      this.WorkingProcedureDisabled = false;
      this.InspectionItemDisabled = false;
      this.drawer = true;
    },
    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit('ok', this.editStatus, this.model);
          this.drawer = false;
        }
      })
    },
    getProductionLine() {
      GetProductionLine().then(res => {
        if (res.Code === 2000) {
          this.ProductionLineOptions = res.Data;
        }
      })
    },
    changeProductionLineCode(e) {
      const obj = this.ProductionLineOptions.find(v => v.ProductionLineCode === e);
      this.model.ProductionLineDes = obj.ProductionLineDes;
      this.WorkingProcedureDisabled = false;
      this.getWorkingProcedure();
    },
    getWorkingProcedure() {
      const query = {
        LineNo: this.model.ProductionLineCode
      };
      GetWorkingProcedure(query).then(res => {
        if (res.Code === 2000) {
          this.WorkingProcedureOptions = res.Data;
        }
      })
    },
    changeWorkingProcedure(e) {
      const obj = this.WorkingProcedureOptions.find(v => v.WorkingProcedureCode === e);
      this.model.WorkingProcedureDes = obj.WorkingProcedureDes;
      this.InspectionItemDisabled = false;
      this.getInspection();
    },
    getInspection() {
      const query = {
        lineNo: this.model.ProductionLineCode,
        procedureCode: this.model.WorkingProcedureCode
      };
      GetInspection(query).then(res => {
        if (res.Code === 2000) {
          this.InspectionItemOptions = res.Data;
        }
      })
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }
</style>
