<template>
  <div class="app-container">
    <p>
      <label style="width:100%">生产过程检验登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="扫描码">
            <el-input v-model="searchQuery.ScanningCode" @keyup.enter.native="changeSerialNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="出厂编号">
            <el-input v-model="searchQuery.SerialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产订单" prop="ProductionOrderNo">
            <el-input v-model="searchQuery.ProductionOrderNo" @keyup.enter.native="changeProductionOrderNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="工厂">
            <el-input v-model="searchQuery.FactoryCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料件号">
            <el-input v-model="searchQuery.MaterialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料名称">
            <el-input v-model="searchQuery.MaterialName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="订单类型">
            <el-input v-model="searchQuery.OrderType" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="订单数量">
            <el-input v-model="searchQuery.OrderQty" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="单位">
            <el-input v-model="searchQuery.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="装配日期">
            <el-date-picker v-model="searchQuery.StartTime" type="date" placeholder="装配日期" format="yyyy-MM-dd" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="合同号">
            <el-input v-model="searchQuery.ContractNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="客户订单号">
            <el-input v-model="searchQuery.SalesOrderNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="发货单位">
            <el-input v-model="searchQuery.Shippers" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产调度员">
            <el-input v-model="searchQuery.ProductionScheduler" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="交货时间">
            <el-date-picker v-model="searchQuery.DeliveryTime" type="date" placeholder="交货时间" format="yyyy-MM-dd" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="检验类型">
            <el-select v-model="searchQuery.TestType" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="searchQuery.TestType === '返修'" :span="8">
          <el-form-item class="filter-item" label="相关联生产订单">
            <el-input v-model="searchQuery.RelevantOrderNo" @change="changeRelevantOrderNo" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button v-if="!switchValue" type="primary" size="small" icon="el-icon-edit" @click="handleAdd">
        新增
      </el-button>
      <el-button v-if="!switchValue" type="success" size="small" icon="el-icon-edit" @click="handleCommit">
        {{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" show-overflow-tooltip />
      <el-table-column label="序列号" prop="SerialNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="线体编码" prop="ProductionLineCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="工序编号" prop="WorkingProcedureCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="工序描述" prop="WorkingProcedureDes" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="检验项" prop="InspectionItem" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="测量值" prop="Measurements" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <add-select-model ref="modalForm" @ok="modalFormOk" /> -->
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  Update,
  GetOrderBySerialNo,
  GetOrderByOrderNo,
  GetOrderByRelevant,
  SubmitCheck
} from '@/api/PP/PP_ProcessInspection';
export default {
  name: 'PP.PP_ProcessInspectionDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        productionOrderNo: '',
        ScanningCode: ''
      },
      searchQuery: {
        SerialNo: '',
        ScanningCode: '',
        ProductionOrderNo: '',
        FactoryCode: '',
        MaterialNo: '',
        MaterialName: '',
        OrderType: '',
        OrderQty: '',
        Unit: '',
        StartTime: '',
        ContractNo: '',
        SalesOrderNo: '',
        Shippers: '',
        ProductionScheduler: '',
        DeliveryTime: '',
        TestType: '',
        RelevantOrderNo: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ProductionOrderNo: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: 'create',
      switchValue: false,
      options: [{
        value: '正常',
        label: '正常'
      }, {
        value: '返修',
        label: '返修'
      }],
      delList: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    // this.GetOrderNoSerialNo()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ID) {
            v.IsDelete = true;
            this.delList.push(v.ID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.list.length === 0) {
            this.showNotify('warning', '请添加明细');
            return
          }
          let query = {
            DetailList: this.list,
            DelDetailIds: this.delList
          };
          query = Object.assign(this.searchQuery, query);
          SubmitCheck(query).then(res => {
            if (res.Code === 2000) {
              if (res.Message) {
                this.$confirm(res.Message, '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  this.handleSave()
                }).catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消操作'
                  });
                });
              } else {
                this.handleSave()
              }
            }
          });
          if (switchBtn) {

          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSave() {
      this.startLoading();
      let query = {
        DetailList: this.list,
        DelDetailIds: this.delList
      };
      query = Object.assign(this.searchQuery, query);
      if (this.editStatus === 'create') {
        SubmitScanInfo(query).then(res => {
          if (res.Code === 2000) {
            if (!this.switchValue) {
              this.backTo('PP.PP_ProcessInspection');
            } else {
              this.fetchDocNum();
              this.listQuery.ScanningCode = '';
              this.list = [];
              this.showNotify('success', '添加成功');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.endLoading();
        }).catch(err => {
          console.log(err);
          this.endLoading();
        })
      } else {
        Update(query).then(res => {
          if (res.Code === 2000) {
            if (!this.switchValue) {
              this.backTo('PP.PP_ProcessInspection');
            } else {
              this.listQuery.ScanningCode = '';
              this.list = [];
              this.showNotify('success', '添加成功');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.endLoading()
        }).catch(err => {
          console.log(err);
          this.endLoading();
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleAdd() {
      if (!this.searchQuery.ScanningCode && !this.searchQuery.ProductionOrderNo) {
        this.showNotify('warning', '请扫描身份识别卡或者输入生产订单');
        return;
      }
      this.$refs.modalFormAdd.add();
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.add');
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(status, record) {
      if (status === 'create') {
        const obj = {};
        this.list = this.list.concat(record).reduce((cur, next) => {
          obj[next.SerialNo + next.ProductionOrderNo + next.ProductionLineCode + next.WorkingProcedureCode +
            next.InspectionItem] ? '' : obj[next.SerialNo + next.ProductionOrderNo + next.ProductionLineCode +
            next.WorkingProcedureCode + next.InspectionItem] = true && cur.push(next);
          return cur;
        }, [])
      } else {
        this.list.forEach((v, index) => {
          if (v.ID) {
            if (v.ID === record.ID) {
              this.$set(this.list, index, record)
            }
          } else {
            if (v.SerialNo + v.ProductionOrderNo + v.ProductionLineCode + v.WorkingProcedureCode + v
              .InspectionItem === record.SerialNo + record.ProductionOrderNo + record.ProductionLineCode + record
              .WorkingProcedureCode + record.InspectionItem) {
              this.$set(this.list, index, record)
            }
          }
        });
      }
    },
    getDetailList() {
      const query = {
        SerialNo: this.searchQuery.SerialNo,
        ProductionOrderNo: this.searchQuery.ProductionOrderNo
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        this.fileList = [];
        this.editStatus = 'create';
      }
    },
    // 根据出厂编号查询生产订单
    changeSerialNo() {
      if (!this.searchQuery.ScanningCode) {
        this.showNotify('warning', '请扫描身份识别卡');
        return;
      }
      const query = {
        SerialNo: this.searchQuery.ScanningCode
      };
      this.startLoading();
      GetOrderBySerialNo(query).then(res => {
        if (res.Code === 2000) {
          if (res.Data !== null) {
            this.searchQuery.ProductionOrderNo = res.Data.ProductionOrderNo;
            this.searchQuery.SerialNo = res.Data.SerialNo;
            this.searchQuery.FactoryCode = res.Data.FactoryCode;
            this.searchQuery.MaterialNo = res.Data.MaterialNo;
            this.searchQuery.MaterialName = res.Data.MaterialName;
            this.searchQuery.OrderType = res.Data.OrderType;
            this.searchQuery.OrderQty = res.Data.OrderQty;
            this.searchQuery.Unit = res.Data.Unit;

            this.searchQuery.StartTime = res.Data.StartTime;
            this.searchQuery.ContractNo = res.Data.ContractNo;
            this.searchQuery.SalesOrderNo = res.Data.SalesOrderNo;
            this.searchQuery.Shippers = res.Data.Shippers;
            this.searchQuery.ProductionScheduler = res.Data.ProductionScheduler;
            this.searchQuery.DeliveryTime = res.Data.DeliveryTime;
            this.listQuery.ScanningCode = '';
            this.searchQuery.TestType = '';
            this.searchQuery.RelevantOrderNo = '';
            this.list = []
          } else {
            this.searchQuery.ProductionOrderNo = '';
            this.searchQuery.SerialNo = '';
            this.searchQuery.FactoryCode = '';
            this.searchQuery.MaterialNo = '';
            this.searchQuery.MaterialName = '';
            this.searchQuery.OrderType = '';
            this.searchQuery.OrderQty = '';
            this.searchQuery.Unit = '';

            this.searchQuery.StartTime = '';
            this.searchQuery.ContractNo = '';
            this.searchQuery.SalesOrderNo = '';
            this.searchQuery.Shippers = '';
            this.searchQuery.ProductionScheduler = '';
            this.searchQuery.DeliveryTime = '';
            this.searchQuery.TestType = '';
            this.searchQuery.RelevantOrderNo = '';
            this.listQuery.ScanningCode = '';
            this.list = [];
            this.showNotify('warning', '未查询到数据');
          }
          this.endLoading()
        }
      }).catch(err => {
        console.log(err);
        this.endLoading();
      })
    },
    // 查询物料
    handleFilter() {
      if (!this.searchQuery.ProductionOrderNo) {
        this.showNotify('warning', '请选择生产订单');
        return;
      }
      if (!this.listQuery.ScanningCode) {
        this.showNotify('warning', '请扫描物料码');
        return;
      }
      if (this.list.length > 0) {
        let switchBtn = true;
        this.list.some(res => {
          if (res.ComponentCode === this.listQuery.ScanningCode) {
            res.IsConfirm = true;
            res.ScanningCode = this.listQuery.ScanningCode;
            switchBtn = true;
            return true;
          } else {
            switchBtn = false;
          }
        });
        console.log(this.list);
        if (!switchBtn) {
          this.showNotify('warning', '未从明细列表匹配到相应数据');
        }
      } else {
        this.showNotify('warning', '未查询到信息');
      }
    },
    changeSwitchValue(e) {
      if (e) {
        if (this.listQuery.ScanningCode) {
          this.handleCommit();
        }
      }
    },
    async GetOrderNoSerialNo() {
      await GetOrderNoSerialNo().then(res => {
        if (res.Code === 2000) {
          this.ProductionOrderNoOptions = res.Data;
        }
      })
    },
    changeProductionOrderNo() {
      const query = {
        ProductionOrderNo: this.searchQuery.ProductionOrderNo
      };
      this.startLoading();
      GetOrderByOrderNo(query).then(res => {
        if (res.Code === 2000) {
          if (res.Data !== null) {
            this.searchQuery.SerialNo = res.Data.SerialNo;
            this.searchQuery.FactoryCode = res.Data.FactoryCode;
            this.searchQuery.MaterialNo = res.Data.MaterialNo;
            this.searchQuery.MaterialName = res.Data.MaterialName;
            this.searchQuery.OrderType = res.Data.OrderType;
            this.searchQuery.OrderQty = res.Data.OrderQty;
            this.searchQuery.Unit = res.Data.Unit;
            this.searchQuery.StartTime = res.Data.StartTime;
            this.searchQuery.ContractNo = res.Data.ContractNo;
            this.searchQuery.SalesOrderNo = res.Data.SalesOrderNo;
            this.searchQuery.Shippers = res.Data.Shippers;
            this.searchQuery.ProductionScheduler = res.Data.ProductionScheduler;
            this.searchQuery.DeliveryTime = res.Data.DeliveryTime;
            this.listQuery.ScanningCode = '';
            this.searchQuery.TestType = '';
            this.searchQuery.RelevantOrderNo = '';
            this.list = []
          } else {
            this.searchQuery.ProductionOrderNo = '';
            this.searchQuery.SerialNo = '';
            this.searchQuery.FactoryCode = '';
            this.searchQuery.MaterialNo = '';
            this.searchQuery.MaterialName = '';
            this.searchQuery.OrderType = '';
            this.searchQuery.OrderQty = '';
            this.searchQuery.Unit = '';

            this.searchQuery.StartTime = '';
            this.searchQuery.ContractNo = '';
            this.searchQuery.SalesOrderNo = '';
            this.searchQuery.Shippers = '';
            this.searchQuery.ProductionScheduler = '';
            this.searchQuery.DeliveryTime = '';
            this.listQuery.ScanningCode = '';
            this.searchQuery.TestType = '';
            this.searchQuery.RelevantOrderNo = '';
            this.list = [];
            this.showNotify('warning', '未查询到数据');
          }
          this.endLoading()
        }
      }).catch(err => {
        console.log(err);
        this.endLoading();
      })
    },
    changeRelevantOrderNo() {
      this.startLoading();
      const query = {
        ProductionOrderNo: this.searchQuery.RelevantOrderNo
      };
      GetOrderByRelevant(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.endLoading();
        }
      }).catch(err => {
        console.log(err);
        this.endLoading();
      })
    }
  }
}
</script>
<style>

</style>
