<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-select
        v-model="listQuery.productionLine"
        style="width: 140px"
        filterable
        class="filter-item"
        :placeholder="$t('ui.PP.ProductionOrder.ProductionLine')"
      >
        <el-option
          v-for="item in pLineOptions"
          :key="item.EnumValue"
          :value="item.EnumValue"
          :label="item.Remark"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>
      <el-button
        v-permission="{ name: 'PP.PP_OrderClose.Add' }"
        v-waves
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t('ui.PP.OrderClose.BtnSelectProductionOrder') }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_OrderClose.Delete' }"
        v-waves
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_OrderClose.Posting' }"
        v-waves
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-edit"
        :disabled="deletable"
        @click="handlePosting"
      >{{ $t('ui.PP.OrderClose.BtnCloseProductionOrder') }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.OrderClose.Oid')"
        prop="Oid"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Oid }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.OrderClose.BaseEntry')"
        prop="BaseEntry"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.BaseNum')"
        prop="BaseNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.ProductionLine')"
        prop="ProductionLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.ProductID')"
        prop="ProductID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.ProductDescription')"
        prop="ProductDescription"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.PlannedQuantity')"
        prop="PlannedQuantity"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlannedQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.PlannedStartDate')"
        prop="PlannedStartDate"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlannedStartDate | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.IsPosted')"
        prop="IsPosted"

        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.PostUser')"
        prop="PostUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.OrderClose.PostTime')"
        prop="PostTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime |datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <ProductionOrderDialog
      ref="productionOrderDlg"
      :show.sync="dlgVisible"
      :is-multiple="false"
      @close="handleProductionOrderSelected"
    />
  </div>
</template>

<script>
import {
  fetchPage,
  add,
  update,
  batchDelete,
  postToSAP,
  addList
} from '@/api/PP/PP_OrderClose';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import ProductionOrderDialog from '@/components/FLD/ProductionOrderDialogClose';
import permission from '@/directive/permission/index.js'; // 权限判断指令
_ = require('lodash');

export default {
  name: 'PP.PP_OrderClose',
  components: {
    Pagination,
    ProductionOrderDialog
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        isPosted: '',
        productionLine: ''
      },
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      isPostedOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.posted'),
          key: true
        },
        {
          label: this.$i18n.t('Common.notPosted'),
          key: false
        }
      ],
      multipleSelection: [],
      dlgVisible: false,
      temp: {
        Oid: undefined,
        BaseEntry: undefined,
        BaseNum: undefined,
        ProductionLine: undefined,
        ProductID: undefined,
        ProductDescription: undefined,
        PlannedQuantity: undefined,
        PlannedStartDate: undefined,
        IsPosted: undefined,
        PostUser: undefined,
        PostTime: undefined
      },
      pLineOptions: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) {
        return true;
      }
      while (i--) {
        if (this.multipleSelection[i].IsPosted) {
          return true;
        }
      }
    }
  },
  created() {
    this.getList();
    this.getPLineOptions();
  },
  methods: {
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    getPLineOptions() {
      // if (this.showProductionLineSearch === false) {
      //   return;
      // }
      this.getDict('PP005').then(data => {
        // console.log(1,data)
        data = _.uniqBy(data, x => x.EnumValue);
        data = _.sortBy(data, x => x.EnumValue);
        console.log(data);
        this.pLineOptions = data;
        // console.log(2,this.pLineOptions)
        this.pLineOptions.unshift({
          EnumValue: '',
          Remark: this.$i18n.t('Common.all')
        });
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        // if (this.pLineOptions && this.pLineOptions.length > 0) {
        //     this.listQuery.productionLine = this.pLineOptions[0].EnumValue;
        // }
      });
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';

      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }

      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          var tmpRow = selectRows.find(val => val.IsPosted);
          if (tmpRow) {
            this.showNotify('error', 'Common.operationNotPermitted');
            return;
          }
          this.isProcessing = true;

          var arrRowsID = selectRows.map(function(v) {
            return v.OID;
          });

          // console.log('arrRowsID',arrRowsID)

          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter();
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleCreate() {
      this.dlgVisible = true;
    },
    handleProductionOrderSelected(multiSelections) {
      // if (productionOrder) {
      //   this.temp = Object.assign(this.temp, productionOrder);
      //   console.log(this.temp);
      //   this.temp.ProductID = productionOrder.ItemCode;
      //   this.temp.ProductDescription = productionOrder.ItemName;
      //   this.temp.PlannedQuantity =
      //     productionOrder.ProductionOrderPlannedQuantity;
      //   this.temp.PlannedStartDate = productionOrder.PlannedStartDate;
      //   add(this.temp).then(response => {
      //     if (response.Code === 2000) {
      //       this.showNotify("success", "Common.createSuccess");
      //     } else {
      //       this.showNotify("error", response.Message);
      //     }
      //     this.handleFilter();
      //   });
      // }

      if (multiSelections) {
        var Rows = [];
        multiSelections.forEach(productionOrder => {
          var newTemp = productionOrder;
          newTemp.ProductID = productionOrder.ItemCode;
          newTemp.ProductDescription = productionOrder.ItemName;
          newTemp.PlannedQuantity =
            productionOrder.ProductionOrderPlannedQuantity;
          newTemp.PlannedStartDate = productionOrder.PlannedStartDate;
          Rows.push(newTemp);
        });
        addList(Rows).then(response => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.createSuccess');
          } else {
            this.showNotify('error', response.Message);
          }
          this.handleFilter();
        });
      }
    },
    handlePosting() {
      var selectedRows = this.multipleSelection;
      if (this.checkSingleSelection(selectedRows)) {
        this.$confirm(
          this.$i18n.t('Common.postToSAPConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          console.log(selectedRows);
          var tmpRow = selectedRows.find(val => val.IsPosted);
          if (tmpRow) {
            this.showNotify('error', 'Common.operationNotPermitted');
            return;
          }

          this.isProcessing = true;
          postToSAP(selectedRows[0])
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.operationSuccess');
                this.handleFilter();
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        });
      }
    }
  }
};
</script>
