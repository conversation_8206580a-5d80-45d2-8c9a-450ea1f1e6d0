<template>
  <el-dialog title="工单退料明细" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.ComponentCode"
        size="small"
        class="filter-item"
        style="width: 200px"
        placeholder="物料信息"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="预留编号" prop="ReservedNo" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="预留行号" prop="ComponentLineNo" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="数量" prop="DemandQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="退料数量" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model="scope.row.OutQty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="工厂" prop="FactoryCode" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="发料库存地" prop="DeliverLocation" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="是否是倒冲方式" prop="IsBackflush" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsBackflush | yesnoFilter}}</span>
        </template>
      </el-table-column> -->
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetDetailList
} from '../../../../api/PP/PP_ProductionReturn';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        dateValue: [
          new Date(),
          new Date()
        ],
        keyword: '',
        ComponentCode: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      }
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {
      this.listQuery = {
        dateValue: [
          new Date(),
          new Date()
        ],
        keyword: '',
        ComponentCode: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      let switchBtn = true;
      selection.some(v => {
        if (this.dataList.length > 0) {
          this.dataList.some(res => {
            if (v.ProductionOrderNo + v.ComponentLineNo === res.ProductionOrderNo + res.ComponentLineNo) {
              this.showNotify('warning', '生产订单为：' + v.ProductionOrderNo + '已选择，请勿重复选择');
              switchBtn = false;
              return true;
            }
          })
        }
      });
      if (switchBtn) {
        this.multipleSelection = selection
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      GetDetailList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '退料数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.OutQty > res.DemandQty) {
          this.showNotify('warning', '退料数量不能大于库存数量');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
        this.dialogCustomerFormVisible = false;
      }
    },
    inputOutQty(e) {
      if (e.OutQty <= 0) {
        this.showNotify('warning', '退料数量不得小于等于0');
        return
      } else if (e.OutQty > e.DemandQty) {
        this.showNotify('warning', '退料数量不得大于库存数量');
        return
      }
    }
  }
}
</script>

<style scoped>
</style>
