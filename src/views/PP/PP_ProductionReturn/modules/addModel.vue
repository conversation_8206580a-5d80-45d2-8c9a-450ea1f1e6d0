<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" label-width="100px" :rules="rules" label-position="right">
        <el-form-item label="评估类型">
          <el-select
            v-model="model.AssessmentType"
            filterable
            placeholder="请选择"
            :disabled="disabled"
            style="width: 100%;"
            @change="changeType"
          >
            <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单数量" prop="DemandQty">
          <el-input v-model="model.DemandQty" />
        </el-form-item>
        <el-form-item label="库存">
          <el-select v-model="model.DeliverLocation" filterable placeholder="请选择" style="width: 100%;" @change="changeWhsName">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="model.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetWorkingProcedure
} from '@/api/PP/PP_ProductionReportController';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    const validate = (rule, value, callback) => {
      if (value === 0 || value < 0) {
        callback(new Error('请输入大于0的数字'))
      } else if (value > this.DemandQty) {
        callback(new Error('不能大于需求数量'))
      } else {
        callback()
      }
    };
    return {
      title: '',
      drawer: false,
      disabled: true,
      direction: 'rtl',
      model: {
        DemandQty: '',
        DeliverLocation: '',
        AssessmentType: '', // 评估类型
        AssessmentTypeName: '', // 评估类型
        Remark: ''
      },
      rules: {
        WorkingProcedureDes: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        DemandQty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }, {
          required: true,
          validator: validate,
          trigger: 'blur'
        }]
      },
      options: [],
      DemandQty: 0,
      types: [{
        value: '01',
        label: '自制'
      },
      {
        value: '02',
        label: '外购'
      }
      ]
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.DemandQty = record.DemandQty;

      this.model = Object.assign({}, record);
      this.GetXZ_SAP();
      if (record.AssessmentCategory === 'B') {
        this.disabled = false;
        this.model.AssessmentType = '01';
      }
      this.drawer = true;
    },
    handleSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      console.log(e)
    },
    changeType(e) {
      this.model.AssessmentTypeName = this.types.filter(item => item.value === e)[0].label;
      console.log(this.model);
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
