<template>
  <div class="app-container">
    <p>
      <label style="width:100%">工单退料登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="单号">
            <el-input v-model="searchQuery.ProductionFeedingNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工单退料">
            <el-input v-model="searchQuery.purchase" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型">
            <el-select v-model="searchQuery.MovementType" filterable placeholder="请选择" disabled>
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="过账时间" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="过账时间"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发料库存地">
            <el-select
              v-model="searchQuery.DeliverLocation"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeWhsName"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.value+'-'+item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" show-overflow-tooltip />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="产品型号" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getCaption(scope.row.MaterialName) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="装配线" prop="ProductionLineDes" align="center" width="80px" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="120px" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="120px" show-overflow-tooltip />
      <!-- <el-table-column label="物料件号" prop="" align="center" width="120px" show-overflow-tooltip /> -->
      <el-table-column label="移动类型" prop="MovementType" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MovementType === '261'">工废</span>
          <span v-if="scope.row.MovementType === '311'">料废</span>
        </template>
      </el-table-column>
      <el-table-column label="退料数量" prop="DemandQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="ComponentUnit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="客户订单号" prop="SalesOrderNo" align="center" width="120" show-overflow-tooltip /> -->
      <!-- <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip /> -->
      <!-- <el-table-column label="发货单位" prop="Shippers" align="center" width="120" show-overflow-tooltip /> -->
      <!-- <el-table-column label="装配日期" prop="StartTime" align="center" width="100" :formatter="formatDate" show-overflow-tooltip /> -->
      <el-table-column label="生产投料单号" prop="ProductionFeedingNo" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="发料库存地" prop="DeliverLocation" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" width="120" show-overflow-tooltip /> -->
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="是否是倒冲方式" prop="IsBackflush" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsBackflush | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工厂" prop="FactoryCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <add-select-model ref="modalForm" :data-list="list" @ok="modalFormOk" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  SubmitScanInfo
} from '../../../api/PP/PP_ProductionReturn';
import {
  GetDocNum,
  GetList,
  Update,
  GetOrderBySerialNo,
  GetMaterial
} from '@/api/PP/PP_ProductionFeeding';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'PP.PP_ProductionReturnDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        productionOrderNo: '',
        ScanningCode: ''
      },
      searchQuery: {
        ProductionFeedingNo: '',
        MovementType: '262',
        ManualPostTime: new Date(),
        Remark: '',
        DeliverLocation: '',
        DeliverLocationName: ''
      },
      multipleSelection: [],
      rules: {
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: 'create',
      delList: [],
      options: [{
        value: '261',
        label: '投料'
      }, {
        value: '262',
        label: '退料'
      }]
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetXZ_SAP();
  },
  activated() {
    if (Object.keys(this.$route.params).length > 0) {
      console.log('activated调用了', this.$route.params);
      if (this.$route.params.status === 'add') {
        this.searchQuery = {
          ProductionFeedingNo: '',
          purchase: '',
          MovementType: '262',
          ManualPostTime: new Date(),
          Remark: '',
          DeliverLocation: '',
          DeliverLocationName: ''
        };
        this.list = [];
      }
      this.getPageParams();
    }
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ID) {
            v.IsDelete = true;
            this.delList.push(v.ID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      console.log(this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'));
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.list.length === 0) {
            this.showNotify('warning', '请选择生产投料');
            return;
          }
          let switchBtn = true;
          this.list.some(res => {
            console.log(res);
            if (res.DemandQty === null || res.DemandQty === 0 || res.DemandQty === '0' || res.DemandQty === '') {
              this.showNotify('warning', '需求数量不能为空或者为零');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            this.startLoading();
            let query = {
              DetailList: this.list,
              DelDetailIds: this.delList
            };
            query = Object.assign(this.searchQuery, query);
            query.ManualPostTime = this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD');
            if (this.editStatus === 'create') {
              SubmitScanInfo(query).then(res => {
                if (res.Code === 2000) {
                  this.backTo('PP.PP_ProductionReturn');
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            } else {
              Update(query).then(res => {
                if (res.Code === 2000) {
                  this.backTo('PP.PP_ProductionReturn');
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            }
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      console.log(key);
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ProductionOrderNo + v.ComponentLineNo === record.ProductionOrderNo + record.ComponentLineNo) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.ProductionFeedingNo = response.Data;
          console.log(this.searchQuery);
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const obj = {};
      record.forEach((v, index) => {
        v.DeliverLocation = this.searchQuery.DeliverLocation; // 转入仓库
        v.DeliverLocationName = this.searchQuery.DeliverLocationName;
        v.DemandQty = v.OutQty;
      });
      this.list = this.list.concat(record).reduce((cur, next) => {
        obj[next.ProductionOrderNo + next.ComponentLineNo] ? '' : obj[next.ProductionOrderNo + next.ComponentLineNo] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        productionFeedingNo: this.searchQuery.ProductionFeedingNo
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    getCaption(obj) {
      if (obj) {
        const index = obj.lastIndexOf('&');
        obj = obj.substring(index + 1, obj.length);
        return obj;
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      const obj = this.options.find(v => v.value === e);
      this.searchQuery.DeliverLocationName = obj.label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'DeliverLocationName', this.searchQuery.DeliverLocationName);
          this.$set(res, 'DeliverLocation', this.searchQuery.DeliverLocation);
        })
      }
      this.$forceUpdate();
    }
  }
}
</script>
<style>

</style>
