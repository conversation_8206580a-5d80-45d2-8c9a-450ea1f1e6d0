<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 300px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('ui.PP.ProductionOrder.CreatedDateStart')"
        :end-placeholder="$t('ui.PP.ProductionOrder.CreatedDateEnd')"
      />
      <el-input
        v-model="listQuery.productionOrderID"
        type="text"
        :placeholder="$t('ui.PP.ProductionOrder.BaseNum')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.productionOrderStatus"
        filterable
        :placeholder="$t('ui.PP.ProductionOrder.ProductionOrderStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in statusOptions" :key="item.Key" :label="item.Value" :value="item.Key" />
      </el-select>
      <el-select
        v-model="listQuery.productionLine"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.PP.ProductionOrder.ProductionLine')"
      >
        <el-option v-for="item in pLineOptions" :key="item.EnumValue" :value="item.EnumValue" :label="item.Remark" />
      </el-select>
      <el-select
        v-model="listQuery.from"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.PP.ProductionOrder.datasource')"
      >
        <el-option value="1" :label="$t('ui.PP.ProductionOrder.fromRemote')" />
        <el-option value="2" :label="$t('ui.PP.ProductionOrder.fromLocal')" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />

      <!-- v-permission="{ name: 'PP.PP_ProductionOrder.Sync' }" -->
      <el-button
        v-waves
        :disabled="isProcessing"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-download"
        @click="handleSync"
      >{{ $t("Common.sync") }}</el-button>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-document" size="small" @click="handlePrint">
        {{ $t('Common.print') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        :disabled="updatePlineable"
        @click="handleUpdatePline"
      >{{ $t("ui.PP.ProductionOrder.UpdatePline") }}</el-button>
      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-document" @click="handleExport">
        {{ $t("Common.export") }}</el-button>
      <!-- <el-button
        v-permission="{ name: 'PP.PP_ProductionOrder.Kanban' }"
        :disabled="isProcessing"
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-download"
        @click="handleGenerateKanbanData"
      >生成看板数据</el-button>-->
      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :height="200"
      :data="list"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      @row-click="handleRowClick"
      @selection-change="handleMultiSelection"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionLine')"
        prop="ProductionLine"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.RequestedEndDate')"
        prop="RequestedEndDate"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RequestedEndDate | date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderSiteID')"
        prop="ProductionOrderSiteID"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderSiteID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderStatus')"
        prop="ProductionOrderStatus"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderStatus|OrderStates }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderPriority')"
        prop="ProductionOrderPriority"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderPriority }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrder.BaseNum')" prop="BaseNum" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrder.ItemCode')" prop="ItemCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrder.ItemName')" prop="ItemName" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderOpenQuantity')"
        prop="ProductionOrderOpenQuantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderOpenQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderReleaseDate')"
        prop="ProductionOrderReleaseDate"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderReleaseDate | date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderPlannedQuantity')"
        prop="ProductionOrderPlannedQuantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderPlannedQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderPlannedQuantityUnit')"
        prop="ProductionOrderPlannedQuantityUnit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderPlannedQuantityUnit }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PP.ProductionOrder.ProductionOrderFullfilledQuantity')"
        prop="ProductionOrderFullfilledQuantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderFullfilledQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrder.CreatedDate')" prop="CreatedDate" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.CreatedDate | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrder.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleFilter"
    />

    <p>{{ $t("ui.PP.ProductionOrderDetail.title") }}</p>
    <el-table
      v-loading="listLoadingDetail"
      :height="300"
      :data="listDetail"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
    >
      <el-table-column :label="$t('ui.PP.ProductionOrderDetail.BaseNum')" prop="BaseNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.ProductionLine')"
        prop="ProductionLine"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.LineItemGroupID')"
        prop="LineItemGroupID"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.LineItemGroupID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrderDetail.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ProductionOrderDetail.ItemName')" prop="ItemName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.PlanQuantity')"
        prop="PlanQuantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlanQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.PlanQuantityUnit')"
        prop="PlanQuantityUnit"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlanQuantityUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.OpenQuantity')"
        prop="OpenQuantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OpenQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.ProductionOrderDetail.FulfilledQuantity')"
        prop="FulfilledQuantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.FulfilledQuantity }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="$t('Common.edit')" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form>
        <el-form-item :label="$t('ui.PP.ProductionOrder.ProductionLine')" prop="ProductionLine">
          <el-select v-model="value" filterable>
            <el-option
              v-for="item in pLineOptions"
              :key="item.EnumValue"
              :value="item.EnumValue"
              :label="item.Remark"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchStatusList,
  fetchList,
  fetchListSAP,
  fetchDetailList,
  fetchDetailListSAP,
  fetchPage,
  syncProductionOrder,
  exportExcelFile,
  UpdatePLine
} from '@/api/PP/PP_ProductionOrder';
import {
  generateKanbanData
} from '@/api/PP/PP_MaterialReqKanban';
import {
  printOrderToPDF
} from '@/api/PP/PP_Print';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import ProductionOrderDialog from '@/components/FLD/ProductionOrderDialog';
import permission from '@/directive/permission/index.js'; // 权限判断指令
import {
  exportToExcel
} from '@/utils/excel-export';
_ = require('lodash');
export default {
  name: 'PP.PP_ProductionOrder',
  components: {
    Pagination,
    ProductionOrderDialog
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        productionOrderStatus: undefined,
        productionOrderID: '',
        productionLine: undefined,
        from: '2'
      },
      queryParams: {},
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      listDetail: [],
      listLoadingDetail: false,
      dialogVisible: false,
      listQueryDetail: {
        productionOrderID: '',
        productionLine: undefined,
        from: '2'
      },
      productionOrder: undefined,
      statusOptions: [],
      pLineOptions: [],
      productionOrderDetails: undefined,
      multiSelection: [],
      // multipleSelection:[],
      isProcessing: false,
      value: ''
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    updatePlineable() {
      return this.multiSelection.length === 0 || this.listQuery.from !== '2';
    }
  },
  created() {
    this.getStatusOptions();
    this.getPLineOptions();
  },
  methods: {
    getStatusOptions() {
      if (this.showStatusSearch === false) {
        return;
      }
      fetchStatusList().then(response => {
        if (response.Code === 2000) {
          this.statusOptions = response.Data;
          this.statusOptions.unshift({
            Key: '',
            Value: this.$i18n.t('Common.all')
          });
          console.log(this.statusOptions);
          // if (this.statusOptions && this.statusOptions.length > 0) {
          //     this.listQuery.productionOrderStatus = this.statusOptions[0].Key;
          // }
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getPLineOptions() {
      if (this.showProductionLineSearch === false) {
        return;
      }
      this.getDict('PP005').then(data => {
        // console.log(1,data)
        data = _.uniqBy(data, x => x.EnumValue);
        data = _.sortBy(data, x => x.EnumValue);
        console.log(data);
        this.pLineOptions = data;
        // console.log(2,this.pLineOptions)
        this.pLineOptions.unshift({
          EnumValue: '',
          Remark: this.$i18n.t('Common.all')
        });
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        // if (this.pLineOptions && this.pLineOptions.length > 0) {
        //     this.listQuery.productionLine = this.pLineOptions[0].EnumValue;
        // }
      });
    },
    sortChange(data) {
      if (this.listQuery.from !== '2') {
        return false;
      }
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      this.queryParams = JSON.parse(JSON.stringify(this.listQuery));
      this.queryParams.fromTime = '';
      this.queryParams.toTime = '';
      this.listDetail = [];
      // this.queryParams.productionLine = ''
      // this.queryParams.productionOrderStatus = ''

      if (this.dateRangeValue) {
        this.queryParams.fromTime = this.dateRangeValue[0];
        this.queryParams.toTime = this.dateRangeValue[1];
      }

      if (this.queryParams.from == '2') {
        fetchPage(this.queryParams)
          .then(response => {
            if (response.Code === 2000) {
              this.list = response.Data.items;
              this.total = response.Data.total;
            } else {
              this.showNotify('error', response.Message);
            }
            this.listLoading = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      } else {
        this.queryParams.PageNumber = undefined;
        this.queryParams.PageSize = undefined;
        fetchListSAP(this.queryParams)
          .then(response => {
            if (response.Code === 2000) {
              this.total = 0;
              this.list = response.Data;
            } else {
              this.showNotify('error', response.Message);
            }
            this.listLoading = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      }
    },
    getDetailList() {
      this.listLoadingDetail = true;
      if (this.listQuery.from == '2') {
        fetchDetailList(this.listQueryDetail)
          .then(response => {
            if (response.Code === 2000) {
              this.listDetail = response.Data;
              if (this.listDetail) {
                this.listDetail.forEach(x => {
                  x.BaseNum = this.productionOrder.BaseNum;
                  x.ProductionLine = this.productionOrder.ProductionLine;
                  x.PLine = this.productionOrder.ProductionLine;
                });
              }
            } else {
              this.showNotify('error', response.Message);
            }
            this.listLoadingDetail = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      } else {
        fetchDetailListSAP(this.listQueryDetail)
          .then(response => {
            if (response.Code === 2000) {
              this.listDetail = response.Data;
              if (this.listDetail) {
                this.listDetail.forEach(x => {
                  x.BaseNum = this.productionOrder.BaseNum;
                  x.ProductionLine = this.productionOrder.ProductionLine;
                  x.PLine = this.productionOrder.ProductionLine;
                });
              }
            } else {
              this.showNotify('error', response.Message);
            }
            this.listLoadingDetail = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      }
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRowClick(row) {
      this.productionOrder = Object.assign({}, row);
      // 根据所选生产订单的产出品加载BOM信息
      this.listQueryDetail.productionOrderID = row.BaseNum;
      this.listQueryDetail.productionLine = this.listQuery.productionLine;
      this.getDetailList();
    },
    handleMultiSelection(val) {
      this.multiSelection = val;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleUpdatePline() {
      this.dialogVisible = true;

      var selectRowPerson = this.multiSelection[0].ProductionLine;
      this.value = selectRowPerson;
    },
    handleSave() {
      this.startLoading();
      var selectRows = this.multiSelection;
      selectRows.forEach(x => {
        x.ProductionLine = this.value;
      });
      UpdatePLine(selectRows)
        .then(response => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
          this.endLoading();
          this.dialogVisible = false;
        })
        .catch(error => {
          this.endLoading();
          this.showNotify('error', res.Message);
          this.dialogVisible = false;
        });
    },
    handleSync() {
      // 同步生产订单信息
      var ids = this.multiSelection.map(x => x.BaseNum);
      this.showNotify('warning', 'Common.syncStart');
      this.isProcessing = true;
      syncProductionOrder({
        ProductionOrderIDs: ids,
        CreatedTimeFrom: this.dateRangeValue ? this.dateRangeValue[0] : null,
        CreatedTimeTo: this.dateRangeValue ? this.dateRangeValue[1] : null
      })
        .then(response => {
          this.isProcessing = false;
          if (response && response.Code === 2000) {
            this.showNotify('success', 'ui.PP.ProductionOrder.syncSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    handleGenerateKanbanData() {
      // 同步生产订单信息
      var ids = this.multiSelection.map(x => x.BaseNum);
      this.isProcessing = true;
      generateKanbanData(ids)
        .then(response => {
          this.isProcessing = false;
          if (response && response.Code === 2000) {
            this.showNotify('success', '生成看板数据成功');
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    handleExport() {
      this.isProcessing = true;
      var newListQuery = {
        dateTimes: this.dateRangeValue,
        productionOrderStatus: this.listQuery.productionOrderStatus,
        productionOrderID: this.listQuery.productionOrderID,
        productionLine: this.listQuery.productionLine
      };
      exportExcelFile(newListQuery).then(res => {
        exportToExcel(res.data, '生产订单管理');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handlePrint() {
      var selectRows = this.multiSelection;
      this.isProcessing = true;
      if (this.checkMultiSelection(selectRows)) {
        printOrderToPDF({
          docNums: selectRows.map(x => x.BaseNum),
          templateCode: 'PP_Order'
        }).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    }
  }
};
</script>
