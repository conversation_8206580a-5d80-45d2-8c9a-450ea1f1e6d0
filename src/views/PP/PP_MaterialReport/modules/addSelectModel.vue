<template>
  <el-dialog title="生产订单明细" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleSearchFilter">
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="出厂编号" prop="SerialNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="组件行项目号" prop="ComponentLineNo" align="center"  width="160" show-overflow-tooltip/> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="工厂" prop="FactoryCode" align="center" show-overflow-tooltip/> -->
      <el-table-column label="需求数量" prop="DemandQty" align="center" show-overflow-tooltip />
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发料库存地" prop="DeliverLocation" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" show-overflow-tooltip/>
      <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" show-overflow-tooltip/> -->
      <el-table-column label="移动类型" prop="MovementType" align="center" show-overflow-tooltip />
      <el-table-column label="员工号" prop="EmployeeNumber" align="center" show-overflow-tooltip />
      <el-table-column label="员工姓名" prop="EmployeeName" align="center" show-overflow-tooltip />
      <el-table-column label="线体编码" prop="ProductionLineCode" align="center" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" align="center" show-overflow-tooltip />
      <el-table-column label="物料组" prop="MaterialGroupCode" align="center" show-overflow-tooltip />
      <el-table-column label="物料组描述" prop="MaterialGroupDes" align="center" show-overflow-tooltip />

    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetSapOrderList
} from '@/api/PP/PP_MaterialDistributionController';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record)
    },
    handleCustomerRowSelectEvent(selection) {
      this.multipleSelection = selection
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetSapOrderList(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      this.$emit('ok', this.multipleSelection);
      this.dialogCustomerFormVisible = false;
    }
  }
}
</script>

<style scoped>
</style>
