<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <!-- <el-select v-model="listQuery.isPosted" :placeholder="$t('Common.postingStatus')" style="width: 140px"
        class="filter-item">
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select> -->

      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_MaterialReport.Add'}"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleSave"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_MaterialReport.Edit' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-edit"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_MaterialReport.Delete'}"
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_MaterialReport.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="生产报工单号" prop="ProductionReportNo" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="扫描码" prop="ScanningCode" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MaterialNo" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="线体编号" prop="ProductionLineCode" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="工序编号" prop="WorkingProcedureCode" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="工序描述" prop="WorkingProcedureDes" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="员工号" prop="EmployeeNumber" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="员工姓名" prop="EmployeeName" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" width="80" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报工总数" prop="ReportTotal" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="合格数量" prop="QualifiedQty" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="不合格数量" prop="UnqualifiedQty" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="不合格备注" prop="UnqualifiedRemarks" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="120" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '../../../directive/permission/permission';
import Pagination from '@/components/Pagination'; // 分页

import {
  fetchList,
  batchDelete,
  exportExcelFile
} from '../../../api/PP/PP_MaterialReport';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  formatDate,
  formatDateTime
} from '../../../utils';

export default {
  name: 'PP.PP_MaterialReport',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      postDisableStatus: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10
      },
      currentRow: {},
      dialogVisible: false,
      ruleForm: {
        PostTime: ''
      },
      rules: {
        PostTime: [{
          type: 'date',
          required: true,
          message: '请选择过账日期',
          trigger: 'change'
        }]
      },
      tableHeight: '300px'
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    },
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/PP/PP_MaterialReport') {
        this.handleFilter();
      }
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '生产报工单号为：' + v.ProductionReportNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        // if (v.IsPosted === true) {
        //   this.showNotify("warning", '配送单号为：' + v.DeliveryOrderNo + '信息已过账，禁止删除');
        //   switchBtn = false
        //   return true
        // }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          var arrRowsID = selectRows.map(v => v.ID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '物料报工');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
        console.log(this.hasPostedData);
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      // this.getListDetail()
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },

    handleSave() {
      this.routeTo('PP.PP_MaterialReportDetail');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '生产报工单号为：' + v.ProductionReportNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
      });
      if (switchBtn) {
        this.routeTo('PP.PP_MaterialReportDetail', this.multipleSelection[0]);
      }
    }
  }
};
</script>
