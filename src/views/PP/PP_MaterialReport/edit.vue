<template>
  <div class="app-container">
    <p>
      <label style="width:100%">物料报工申请登记单</label>
    </p>

    <el-form
      ref="searchQuery"
      class="formBox"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="扫描物料码" prop="ScanningCode">
            <el-input
              ref="input"
              v-model="searchQuery.ScanningCode"
              :autofocus="autofocus"
              @change="changeSerialNo"
              @keyup.enter.native="changeSerialNo"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产报工单号">
            <el-input v-model="searchQuery.ProductionReportNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料件号">
            <el-input v-model="searchQuery.MaterialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料名称">
            <el-input v-model="searchQuery.MaterialName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="线体描述" prop="ProductionLineCode">
            <el-select v-model="searchQuery.ProductionLineCode" filterable placeholder="请选择" @change="changeProductionLine">
              <el-option
                v-for="item in ProductionLineOptions"
                :key="item.ProductionLineCode"
                :label="item.ProductionLineCode+'-'+item.ProductionLineDes"
                :value="item.ProductionLineCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="线体编号">
            <el-input v-model="searchQuery.ProductionLineCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="工序描述" prop="WorkingProcedureCode">
            <el-select v-model="searchQuery.WorkingProcedureCode" filterable placeholder="请选择" @change="changeWorkingProcedure">
              <el-option
                v-for="item in options"
                :key="item.WorkingProcedureCode"
                :label="item.WorkingProcedureCode+'-'+item.WorkingProcedureDes"
                :value="item.WorkingProcedureCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="工序编号">
            <el-input v-model="searchQuery.WorkingProcedureCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="员工号">
            <el-input v-model="searchQuery.EmployeeNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="员工姓名">
            <el-input v-model="searchQuery.EmployeeName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="单位">
            <el-input v-model="searchQuery.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="合格数量" prop="QualifiedQty">
            <el-input v-model="searchQuery.QualifiedQty" type="number" @blur="blurReportTotal" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="不合格数量">
            <el-input v-model="searchQuery.UnqualifiedQty" type="number" @blur="blurReportTotal" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="报工总数">
            <el-input v-model="searchQuery.ReportTotal" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="不合格备注">
            <el-input v-model="searchQuery.UnqualifiedRemarks" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-switch
      v-if="editStatus === 'create'"
      v-model="switchValue"
      active-color="#13ce66"
      inactive-color="#ff4949"
      active-text="自动提交"
      inactive-text="手动提交"
      @change="changeSwitchValue"
    />
    <p>
      <el-button
        v-if="editStatus === 'create' && !switchValue"
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleSave"
      >
        {{ $t('Common.add') }}</el-button>
      <el-button
        v-if="editStatus === 'create'"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDeleteDetail"
      >
        {{ $t("Common.delete") }}</el-button>
      <el-button v-if="!switchValue" type="success" size="small" icon="el-icon-edit" @click="handleCommit">
        {{ $t("Common.confirm") }}
      </el-button>
    </p>
    <el-table
      v-if="editStatus === 'create'"
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      size="mini"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column label="生产报工单号" prop="ProductionReportNo" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="扫描码" prop="ScanningCode" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MaterialNo" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="线体编号" prop="ProductionLineCode" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="工序编号" prop="WorkingProcedureCode" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="工序描述" prop="WorkingProcedureDes" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="员工号" prop="EmployeeNumber" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="员工姓名" prop="EmployeeName" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" width="100" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报工总数" prop="ReportTotal" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="合格数量" prop="QualifiedQty" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="不合格数量" prop="UnqualifiedQty" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="不合格备注" prop="UnqualifiedRemarks" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="120" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="100" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>

    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetWorkingProcedure,
  Update,
  GetEntity,
  GetOrderBySerialNo,
  GetProductionLine
} from '@/api/PP/PP_MaterialReport';
export default {
  name: 'PP.PP_MaterialReportDetail',
  components: {
    Pagination,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    const validate = (rule, value, callback) => {
      if (value === 0 || value < 0) {
        callback(new Error('请输入大于0的数字'))
      } else {
        callback()
      }
    };
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        ProductionReportNo: '',
        ScanningCode: '',
        SerialNo: '',
        ProductionOrderNo: '',
        MaterialNo: '',
        MaterialName: '',
        WorkingProcedureCode: '',
        WorkingProcedureDes: '',
        EmployeeNumber: this.$store.getters.userinfo.LoginAccount,
        EmployeeName: this.$store.getters.userinfo.UserName,
        Unit: '',
        ReportTotal: 1,
        QualifiedQty: 1,
        UnqualifiedQty: 0,
        UnqualifiedRemarks: '',
        ManualPostTime: new Date(),
        IsCompleted: false,
        OrderType: '',
        ProductionScheduler: '', //  生产管理员
        StartTime: '',
        AssessmentType: ''
      },
      multipleSelection: [],
      rules: {
        ScanningCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        ProductionLineCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        WorkingProcedureCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        QualifiedQty: [{
          required: true,
          validator: validate,
          trigger: 'blur'
        }],
        UnqualifiedQty: [{
          required: true,
          validator: validate,
          trigger: 'blur'
        }],
        ProductionOrderNo: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: 'create',
      delList: [],
      options: [],
      dataObj: {},
      ProductionReportNo: '',
      switchValue: false,
      autofocus: false,
      ProductionOrderNoOptions: [],
      ProductionLineOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetProductionLine()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      if (this.editStatus === 'create') {
        if (this.list.length === 0) {
          this.showNotify('warning', '请添加信息');
          return
        }
        this.startLoading();
        SubmitScanInfo(this.list).then(res => {
          if (res.Code === 2000) {
            if (!this.switchValue) {
              this.backTo('PP.PP_MaterialReport');
            } else {
              this.list = [];
              this.fetchDocNum();
              this.searchQuery.WorkingProcedureDes = '';
              this.searchQuery.WorkingProcedureCode = '';
              this.searchQuery.ReportTotal = 1;
              this.searchQuery.QualifiedQty = 1;
              this.autofocus = true;
              this.$refs.input.focus();
              this.showNotify('success', '添加成功');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.endLoading()
        }).catch(err => {
          console.log(err);
          this.endLoading();
        })
      } else {
        this.$refs['searchQuery'].validate((valid) => {
          if (valid) {
            this.startLoading();
            const query = Object.assign({}, this.searchQuery);
            Update(query).then(res => {
              if (res.Code === 2000) {
                this.backTo('PP.PP_MaterialReport');
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading();
            }).catch(err => {
              console.log(err);
              this.endLoading();
            })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.ProductionReportNo = response.Data;
          this.searchQuery.ProductionReportNo = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    GetEntity() {
      const query = {
        key: this.searchQuery.ID
      };
      GetEntity(query).then(res => {
        if (res.Code === 2000) {

        }
      })
    },
    GetWorkingProcedure() {
      const query = {
        LineNo: this.searchQuery.ProductionLineCode
      };
      GetWorkingProcedure(query).then(res => {
        if (res.Code === 2000) {
          this.options = res.Data;
          if (res.Data.length > 0) {
            this.searchQuery.WorkingProcedureDes = res.Data[res.Data.length - 1].WorkingProcedureDes;
            this.searchQuery.WorkingProcedureCode = res.Data[res.Data.length - 1].WorkingProcedureCode;
            this.searchQuery.IsCompleted = true;
            if (this.switchValue) {
              this.handleSave();
            }
          }
        }
      })
    },
    changeWorkingProcedure(e) {
      const obj = this.options.find(v => v.WorkingProcedureCode === e);
      this.searchQuery.WorkingProcedureDes = obj.WorkingProcedureDes;
      const index = this.options.indexOf(obj);
      if (index === this.options.length - 1) {
        this.searchQuery.IsCompleted = true
      } else {
        this.searchQuery.IsCompleted = false
      }
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        if (this.searchQuery.Remark === null) {
          this.searchQuery.Remark = '';
        }
        // this.GetEntity()
        this.GetWorkingProcedure();
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    changeSerialNo() {
      let switchBtn = true;
      console.log(this.list);
      this.list.some(res => {
        console.log(res);
        if (res.ScanningCode === this.searchQuery.ScanningCode) {
          this.showNotify('warning', '请勿重复扫描');
          switchBtn = false;
          this.searchQuery.ScanningCode = '';
          this.autofocus = true;
          this.$refs.input.focus();
          return true
        }
      });

      if (switchBtn) {
        this.searchQuery.SerialNo = this.searchQuery.ScanningCode;
        const query = {
          code: this.searchQuery.ScanningCode
        };
        GetOrderBySerialNo(query).then(res => {
          if (res.Code === 2000) {
            this.searchQuery.MaterialNo = res.Data.MaterialNo; // 物料件号
            this.searchQuery.MaterialName = res.Data.MaterialName; // 物料名称
            this.searchQuery.Unit = res.Data.Unit; // 单位
            this.searchQuery.MaterialGroupCode = res.Data.MaterialGroupCode; // 物料组
            this.searchQuery.MaterialGroupDes = res.Data.MaterialGroupDes; // 物料组描述
            this.dataObj = this.searchQuery;
            // if (this.switchValue) {
            //   this.handleSave()
            // }
          }
        })
      }
    },
    handleSave() {
      const data = [];
      this.$refs['searchQuery'].validate((valid) => {
        if (valid) {
          console.log(this.searchQuery, 1);
          data.push(this.dataObj);
          const obj = {};
          this.list = this.list.concat(data).reduce((cur, next) => {
            obj[next.ScanningCode] ? '' : obj[next.ScanningCode] = true && cur.push(next);
            return cur;
          }, []);
          console.log(this.list, 2);
          this.searchQuery = {
            ProductionReportNo: this.ProductionReportNo,
            ScanningCode: '',
            SerialNo: '',
            ProductionOrderNo: '',
            MaterialNo: '',
            MaterialName: '',
            WorkingProcedureCode: '',
            WorkingProcedureDes: '',
            EmployeeNumber: this.$store.getters.userinfo.LoginAccount,
            EmployeeName: this.$store.getters.userinfo.UserName,
            Unit: '',
            ReportTotal: 1,
            QualifiedQty: 1,
            UnqualifiedQty: 0,
            UnqualifiedRemarks: '',
            OrderType: '',
            ProductionScheduler: '', //  生产管理员
            MaterialGroupCode: '',
            MaterialGroupDes: '',
            StartTime: '',
            AssessmentType: ''
          };
          this.$nextTick(() => {
            this.$refs.searchQuery.clearValidate();
          });
          if (this.switchValue) {
            this.handleCommit();
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
    blurReportTotal() {
      if (this.searchQuery.QualifiedQty) {
        this.searchQuery.ReportTotal = Number(this.searchQuery.QualifiedQty) + Number(this.searchQuery
          .UnqualifiedQty)
      }
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ScanningCode === record.ScanningCode) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    changeSwitchValue(e) {
      if (e) {
        this.handleSave();
      }
    },
    GetProductionLine() {
      GetProductionLine().then(res => {
        if (res.Code === 2000) {
          this.ProductionLineOptions = res.Data;
        }
      })
    },
    changeProductionLine(e) {
      const obj = this.ProductionLineOptions.find(v => v.ProductionLineCode === e);
      this.searchQuery.ProductionLineDes = obj.ProductionLineDes;
      this.GetWorkingProcedure();
    }
  }
}
</script>
