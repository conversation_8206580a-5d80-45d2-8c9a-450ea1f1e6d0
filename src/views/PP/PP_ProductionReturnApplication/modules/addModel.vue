<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="ruleForm" :model="model" :rules="rules" label-width="100px">
        <el-form-item label="数量" prop="DemandQty">
          <el-input v-model="model.DemandQty" @blur="changeQty" />
        </el-form-item>
        <el-form-item label="入库名称" prop="InWhsCode">
          <el-select v-model="model.InWhsCode" filterable placeholder="请选择" @change="changeWhsName">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="入库区域" prop="InRegionCode">
          <el-select v-model="model.InRegionCode" filterable placeholder="请选择" @change="changeRegionName">
            <el-option v-for="item in RegionOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="入库库位" prop="InBinCode">
          <el-select v-model="model.InBinCode" filterable placeholder="请选择" @change="changeBinLocationName">
            <el-option v-for="item in BinLocationOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="出库名称" prop="OutWhsCode">
          <el-select v-model="model.OutWhsCode" filterable placeholder="请选择" @change="changeOutWhsName">
            <el-option v-for="item in optionsOut" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="出库区域" prop="OutRegionCode">
          <el-select v-model="model.OutRegionCode" filterable placeholder="请选择" @change="changeOutRegionName">
            <el-option v-for="item in OutRegionOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="出库库位" prop="OutBinCode">
          <el-select v-model="model.OutBinCode" filterable placeholder="请选择" @change="changeOutBinLocationName">
            <el-option v-for="item in OutBinLocationOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估类型">
          <el-select
            v-model="model.AssessmentType"
            filterable
            placeholder="请选择"
            :disabled="disabled"
            @change="changeType"
          >
            <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        DemandQty: '',
        InWhsCode: '',
        InWhsName: '',
        InRegionCode: '',
        InRegionName: '',
        InBinCode: '',
        InBinName: ''
      },
      options: [],
      RegionOptions: [],
      BinLocationOptions: [],
      optionsOut: [],
      OutRegionOptions: [],
      OutBinLocationOptions: [],
      type: '',
      rules: {
        DemandQty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InRegionCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InBinCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        OutWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        OutRegionCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        OutBinCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      disabled: true,
      types: [{
        value: '01',
        label: '自制'
      },
      {
        value: '02',
        label: '外购'
      }
      ]
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record, type) {
      console.log(record, type);
      this.type = type;

      this.model = Object.assign({}, record);
      this.model.StockQty = this.model.DemandQty;
      this.GetXZ_SAP();
      this.getRegion();
      this.getBinLocation();
      this.GetXZ_SAP_Out();
      this.getOutRegion();
      this.getOutBinLocation();
      if (this.model.OutWarehouse === ' ') {
        this.model.OutWarehouse = '';
      }
      if (record.AssessmentCategory === 'B') {
        this.disabled = false;
        this.model.AssessmentType = '01';
      }
      this.drawer = true;
    },
    handleSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.model.DemandQty <= 0) {
            this.showNotify('warning', '当前数量不能为0或者小于0');
            return
          } else if (this.model.DemandQty > this.model.StockQty) {
            this.showNotify('warning', '当前数量不能大于库存数量');
            return
          }
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.model.InWhsName = this.options.filter(item => item.value === e)[0].label;
      this.getRegion();
    },
    getRegion() {
      if (this.model.InWhsCode) {
        const query = {
          WhsCode: this.model.InWhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.model.InRegionName = res.Data[0].RegionName;
              this.model.InRegionCode = res.Data[0].RegionCode;
              this.getBinLocation();
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.model.InRegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.model.InRegionCode) {
        const query = {
          regionCode: this.model.InRegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.model.InBinName = res.Data[0].BinLocationName;
              this.model.InBinCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.model.InBinName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    },
    GetXZ_SAP_Out() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.optionsOut = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.optionsOut.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeOutWhsName(e) {
      this.model.OutWhsName = this.optionsOut.filter(item => item.value === e)[0].label;
      this.getOutRegion();
    },
    getOutRegion() {
      if (this.model.OutWhsCode) {
        const query = {
          WhsCode: this.model.OutWhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.OutRegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.OutRegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.model.OutRegionName = res.Data[0].RegionName;
              this.model.OutRegionCode = res.Data[0].RegionCode;
              this.getOutBinLocation();
            }
          }
        })
      }
    },
    changeOutRegionName(e) {
      this.model.OutRegionName = this.OutRegionOptions.filter(item => item.value === e)[0].label;
      this.getOutBinLocation();
    },
    getOutBinLocation() {
      if (this.model.OutRegionCode) {
        const query = {
          regionCode: this.model.OutRegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.OutBinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.OutBinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.model.OutBinName = res.Data[0].BinLocationName;
              this.model.OutBinCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeOutBinLocationName(e) {
      this.model.OutBinName = this.OutBinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '当前数量不能为0或者小于0');
      } else if (e > this.model.StockQty) {
        this.showNotify('warning', '当前数量不能大于库存数量');
      }
    },
    changeType(e) {
      this.model.AssessmentTypeName = this.types.filter(item => item.value === e)[0].label;
      console.log(this.model);
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
