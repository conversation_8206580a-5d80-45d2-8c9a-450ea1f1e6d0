<template>
  <el-dialog title="库存信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <!-- <el-date-picker  :clearable="false" size="small" v-model="listQuery.dateValue" class="filter-item" type="daterange" style="width: 220px"
        :picker-options="pickerOptions" range-separator="-" :unlink-panels="true" :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')" /> -->
      <!-- <el-input size="small" v-model="listQuery.ProductionOrderNo" class="filter-item" style="width: 140px" placeholder="生产订单"
        @keyup.enter.native="handleSearchFilter" clearable /> -->
      <!-- <el-input size="small" v-model="listQuery.MaterialNo" class="filter-item" style="width: 200px" placeholder="主机物料号"
        @keyup.enter.native="handleSearchFilter" clearable /> -->
      <!-- <el-input size="small" v-model="listQuery.ComponentCode" class="filter-item" style="width: 140px" placeholder="物料件号"
        @keyup.enter.native="handleSearchFilter" clearable /> -->
      <el-input
        v-model="listQuery.ItemCode"
        size="small"
        clearable
        class="filter-item"
        placeholder="物料件号"
        style="width: 140px"
        @keydown.enter.native="handleSearchFilter"
      />
      <el-select
        v-model="listQuery.WhsCode"
        size="small"
        style="width: 140px"
        class="filter-item"
        clearable
        filterable
        placeholder="请选择仓库"
      >
        <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.SpecialStock"
        size="small"
        style="width: 140px"
        class="filter-item"
        clearable
        filterable
        placeholder="请选择特殊库存"
        @change="changeSpecialStock"
      >
        <el-option
          v-for="item in SpecialStockOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        />
      </el-select>
      <!-- <el-select size="small" style="width: 140px" class="filter-item" clearable v-model="listQuery.SupplierCode"
        filterable placeholder="请选择供应商" @change="changeSupplierName" :disabled="true">
        <el-option v-for="item in SupplierOptions" :key="item.SupplierCode"
          :label="item.SupplierCode+'-'+item.SupplierName" :value="item.SupplierCode">
        </el-option>
      </el-select> -->
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
        添加</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <!-- <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" /> -->
      <!-- <el-table-column label="组件行项目号" prop="ComponentLineNo" align="center" width="160" /> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="数量" prop="DemandQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="退料数量" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model="scope.row.OutQty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动类型" prop="MovementType" align="center" width="160" show-overflow-tooltip/> -->
      <!-- <el-table-column label="工厂代码" prop="FactoryCode" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column label="出库编号" prop="OutWhsCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库名称" prop="OutWhsName" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="出库区域编号" prop="OutRegionCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库区域名称" prop="OutRegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库库位编号" prop="OutBinCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库库位名称" prop="OutBinName" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="入库名称" prop="InWhsName" align="center" width="160" show-overflow-tooltip/>
      <el-table-column label="入库区域" prop="InRegionName" align="center" width="160" show-overflow-tooltip/>
      <el-table-column label="入库库位" prop="InBinName" align="center" width="160" show-overflow-tooltip/> -->
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="160" show-overflow-tooltip />

    </el-table>
    <!-- <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter" /> -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetSapOrderList
} from '@/api/PP/PP_ProductionReturnApplication';
import {
  GetStockList_PP
} from '@/api/RPT/RPT_Stock';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import {
  GetSRM_SupplierInfo
} from '@/api/MM/MM_SubcontractingApplication';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        ItemCode: '',
        WhsCode: '',
        SpecialStock: '',
        SupplierCode: ''
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      options: [],
      SpecialStockOptions: [{
        label: '正常',
        value: ''
      }, {
        label: '供应商',
        value: 'O',
        disabled: true
      }, {
        label: '销售',
        value: 'E',
        disabled: true
      }],
      SupplierOptions: [],
      SupplierDisabled: true
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {
      this.listQuery = {
        ItemCode: '',
        WhsCode: '',
        SpecialStock: '',
        SupplierCode: ''
      };
      this.list = [];
      this.GetXZ_SAP();
      this.GetSRM();
      this.dialogCustomerFormVisible = true;
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      const switchBtn = true;
      // selection.some(v => {
      //   if (v.StockQty === 0 || v.StockQty === null || v.StockQty === '0') {
      //     this.showNotify("warning", '生产订单为：' + v.ProductionOrderNo + '库存数量为0，禁止添加');
      //     switchBtn = false;
      //     return true
      //   }else if(this.dataList.length>0){
      //     this.dataList.some(res => {
      //       if (v.ProductionOrderNo + v.ComponentLineNo +v.DeliverLocation+v.RegionCode+ v.BinLocationCode === res.onlyId) {
      //         this.showNotify("warning", '生产订单为：' + v.ProductionOrderNo + '已选择，请勿重复选择');
      //         switchBtn = false;
      //         return true
      //       }
      //     })
      //   }
      // })
      if (switchBtn) {
        this.multipleSelection = selection;
      }
    },
    handleAdd() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '退料数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.OutQty > res.DemandQty) {
          this.showNotify('warning', '退料数量不能大于库存数量');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
      }
    },
    handleSearchFilter() {
      if (this.listQuery.SpecialStock !== 'O') {
        if (this.listQuery.ItemCode === '' && this.listQuery.WhsCode === '') {
          this.showNotify('warning', '请输入物料件号或者选择仓库');
          return
        }
      } else {
        if (this.listQuery.SupplierCode === '') {
          this.showNotify('warning', '请选择供应商');
          return
        }
      }
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      // if (this.listQuery.dateValue) {
      //   this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD')
      //   this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD')
      // }
      const query = Object.assign({}, this.listQuery);
      GetStockList_PP(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '退料数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.OutQty > res.DemandQty) {
          this.showNotify('warning', '退料数量不能大于库存数量');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
        this.dialogCustomerFormVisible = false;
      }
    },
    inputOutQty(e) {
      if (e.OutQty <= 0) {
        this.showNotify('warning', '退料数量不得小于等于0');
        return;
      } else if (e.OutQty > e.DemandQty) {
        this.showNotify('warning', '退料数量不得大于库存数量');
        return;
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.WhsName = this.options.filter(item => item.value === e)[0].label;
      // this.handleFilter()
    },
    GetSRM() {
      GetSRM_SupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.SupplierOptions = res.Data;
        }
      })
    },
    changeSupplierName(e) {
      // const obj = this.SupplierOptions.find(v => v.SupplierCode === e)
      // this.searchQuery.SupplierName = obj.SupplierName
    },
    changeSpecialStock(e) {
      if (e === 'O') {
        this.SupplierDisabled = false;
      } else {
        this.SupplierDisabled = true;
        this.listQuery.SupplierCode = '';
      }
    }
  }
}
</script>

<style scoped>
</style>
