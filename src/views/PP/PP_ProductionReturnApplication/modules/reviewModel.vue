<template>
  <div>
    <el-dialog title="审核明细" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
      <div class="filter-container">
        <el-form ref="ruleForm" :model="model" :inline="true" :rules="rules" label-width="120px">
          <el-form-item label="过账时间" prop="ManualPostTime">
            <el-date-picker v-model="model.ManualPostTime" :clearable="false" type="date" placeholder="过账时间" format="yyyy-MM-dd" />
          </el-form-item>
          <!-- <el-form-item label="入库名称" prop="WhsCode">
            <el-select v-model="model.WhsCode" filterable placeholder="请选择" @change="changeWhsName">
              <el-option v-for="item in WhsOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库区域" prop="RegionCode">
            <el-select v-model="model.RegionCode" filterable placeholder="请选择" @change="changeRegionName">
              <el-option v-for="item in RegionOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库库位" prop="BinLocationCode">
            <el-select v-model="model.BinLocationCode" filterable placeholder="请选择" @change="changeBinLocationName">
              <el-option v-for="item in BinLocationOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item> -->
        </el-form>

      </div>
      <el-table
        ref="CustomerSelectTable"
        v-loading="listLoading"
        :data="list"
        border
        size="mini"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="车间退料单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
        <!-- <el-table-column label="车间退料行号" prop="Line" align="center" width="160" /> -->
        <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" />
        <!-- <el-table-column label="组件行项目号" prop="ComponentLineNo" align="center" width="160" /> -->
        <el-table-column label="物料件号" prop="ComponentCode" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="物料名称" prop="MaterialName" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="需求数量" prop="DemandQty" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
            <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
            <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
            <span v-else>{{ scope.row.ComponentUnit }}</span>
          </template>
        </el-table-column>
        <el-table-column label="移动类型" prop="MovementType" align="center" width="160" show-overflow-tooltip />
        <!-- <el-table-column label="工厂代码" prop="FactoryCode" align="center" width="160" show-overflow-tooltip/> -->
        <el-table-column label="出库名称" prop="OutWhsName" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="出库区域" prop="OutRegionName" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="出库库位" prop="OutBinName" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="入库名称" prop="InWhsName" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="入库区域" prop="InRegionName" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="入库库位" prop="InBinName" align="center" width="160" show-overflow-tooltip />
        <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip/>
        <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" width="160" show-overflow-tooltip/> -->
        <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="160" show-overflow-tooltip />
        <el-table-column label="评估类型" prop="AssessmentType" align="center" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.AssessmentCategory === '01'">自制</span>
            <span v-if="scope.row.AssessmentCategory === '02'">外购</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
          <template slot-scope="scope">
            <span @click="toggle(scope.row)">编辑</span>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.PageNumber"
        :limit.sync="listQuery.PageSize"
        @pagination="handleCustomerFilter"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSelectCustomer">提交</el-button>
      </div>
    </el-dialog>
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>

</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddModel from './addModel'
import {
  GetDetailList,
  Audit
} from '@/api/PP/PP_ProductionReturnApplication';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'ReviewModal',
  components: {
    Pagination,
    AddModel
  },
  directives: {
    waves
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {
        ManualPostTime: new Date()
        // WhsCode: '',
        // WhsName: '',
        // RegionCode: '',
        // RegionName: '',
        // BinLocationCode: '',
        // BinLocationName: '',
      },
      WhsOptions: [],
      RegionOptions: [],
      BinLocationOptions: [],
      modelData: [],
      list: [],
      multipleSelection: [],
      listLoading: false,
      rules: {
        ManualPostTime: [{
          type: 'date',
          required: true,
          message: '请选择日期',
          trigger: 'change'
        }],
        WhsCode: [{
          required: true,
          message: '请选择入库名称',
          trigger: 'change'
        }],
        RegionCode: [{
          required: true,
          message: '请选择入库区域',
          trigger: 'change'
        }],
        BinLocationCode: [{
          required: true,
          message: '请选择入库库位',
          trigger: 'change'
        }]
      },
      DocNumList: []
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      //   this.model = Object.assign({}, record)
      this.DocNumList = record;
      const DocNums = record.map(v => v.DocNum);
      this.model = {
        ManualPostTime: new Date()
        // WhsCode: '',
        // WhsName: '',
        // RegionCode: '',
        // RegionName: '',
        // BinLocationCode: '',
        // BinLocationName: '',
      };
      // this.WhsOptions= []
      // this.RegionOptions= []
      // this.BinLocationOptions= []
      this.modelData = DocNums;
      this.handleCustomerFilter();
      // this.GetXZ_SAP()
      this.dialogCustomerFormVisible = true;
      this.$nextTick(() => {
        this.$refs['ruleForm'].clearValidate(); // 清除校验
      })
    },
    handleCustomerFilter() {
      this.listLoading = true;
      const query = {
        DocNums: this.modelData
      };
      GetDetailList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let switchBtn = true;
          this.list.some(v => {
            if (v.InWhsCode === v.OutWhsCode) {
              this.showNotify('warning', '生产订单为：' + v.ProductionOrderNo + ',物料件号为：' + v.ComponentCode + '不允许选择相同的仓库进行操作');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            const query = {
              ManualPostTime: this.$moment(this.model.ManualPostTime).format('YYYY-MM-DD'),
              // WhsCode: this.model.WhsCode,
              // WhsName: this.model.WhsName,
              // RegionCode: this.model.RegionCode,
              // RegionName: this.model.RegionName,
              // BinLocationCode: this.model.BinLocationCode,
              // BinLocationName: this.model.BinLocationName,
              entities: this.DocNumList,
              DetailList: this.list,
              status: 1
            };
            Audit(query).then(res => {
              if (res.Code === 2000) {
                this.$emit('ok');
                this.dialogCustomerFormVisible = false;
                if (!res.Message) {
                  this.showNotify('success', res.Message || '审核成功！');
                } else {
                  this.showNotify('success', res.Message);
                }
              } else {
                this.$emit('ok');
                this.dialogCustomerFormVisible = false;
                this.showNotify('success', res.Message);
              }
            })
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key, 'review');
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.DetailID) {
          if (v.DetailID === record.DetailID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.onlyId === record.onlyId) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.WhsOptions = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.WhsOptions.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.model.WhsName = this.WhsOptions.filter(item => item.value === e)[0].label;
      this.getRegion();
    },
    getRegion() {
      if (this.model.WhsCode) {
        this.model.RegionCode = '';
        this.model.RegionName = '';
        this.model.BinLocationCode = '';
        this.model.BinLocationName = '';
        this.RegionOptions = [];
        this.BinLocationOptions = [];
        const query = {
          WhsCode: this.model.WhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            })
          }
        })
      }
    },
    changeRegionName(e) {
      this.model.RegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.model.RegionCode) {
        this.model.BinLocationCode = '';
        this.model.BinLocationName = '';
        this.BinLocationOptions = [];
        const query = {
          regionCode: this.model.RegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            })
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.model.BinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      // this.$forceUpdate();
    }
  }
}
</script>

<style scoped>
</style>
