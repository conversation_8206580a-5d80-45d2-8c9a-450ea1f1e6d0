<template>
  <el-dialog title="库存信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <!-- <el-date-picker  :clearable="false" size="small" v-model="listQuery.dateValue" class="filter-item" type="daterange" style="width: 220px"
        :picker-options="pickerOptions" range-separator="-" :unlink-panels="true" :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')" /> -->
      <!-- <el-input size="small" v-model="listQuery.ProductionOrderNo" class="filter-item" style="width: 140px" placeholder="生产订单"
        @keyup.enter.native="handleSearchFilter" clearable /> -->
      <!-- <el-input size="small" v-model="listQuery.MaterialNo" class="filter-item" style="width: 200px" placeholder="主机物料号"
        @keyup.enter.native="handleSearchFilter" clearable /> -->
      <!-- <el-input size="small" v-model="listQuery.ComponentCode" class="filter-item" style="width: 140px" placeholder="物料件号"
        @keyup.enter.native="handleSearchFilter" clearable /> -->
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 140px"
        placeholder="物料件号"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.Whs"
        size="small"
        class="filter-item"
        style="width: 140px"
        placeholder="仓库信息"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleSearchFilter">
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <!-- <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" /> -->
      <!-- <el-table-column label="组件行项目号" prop="ComponentLineNo" align="center" width="160" /> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="需求数量" prop="DemandQty" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动类型" prop="MovementType" align="center" width="160" show-overflow-tooltip/> -->
      <!-- <el-table-column label="工厂代码" prop="FactoryCode" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column label="出库编号" prop="OutWhsCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库名称" prop="OutWhsName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库区域编号" prop="OutRegionCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库区域名称" prop="OutRegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库库位编号" prop="OutBinCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="出库库位名称" prop="OutBinName" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="入库名称" prop="InWhsName" align="center" width="160" show-overflow-tooltip/>
      <el-table-column label="入库区域" prop="InRegionName" align="center" width="160" show-overflow-tooltip/>
      <el-table-column label="入库库位" prop="InBinName" align="center" width="160" show-overflow-tooltip/> -->
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="160" show-overflow-tooltip />

    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetSapOrderList
} from '@/api/PP/PP_ProductionReturnApplication';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        // ProductionOrderNo: "", //生产订单
        // MaterialNo: "", //主机物料号
        // ComponentCode: '', //物料件号
        // dateValue: [
        //   new Date(),
        //   new Date()
        // ], //时间区间
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      }
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {
      this.listQuery = {
        // ProductionOrderNo: "", //生产订单
        // MaterialNo: "", //主机物料号
        // ComponentCode: '', //物料件号
        // dateValue: [
        //   new Date(),
        //   new Date()
        // ], //时间区间
        keyword: '',
        Whs: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      const switchBtn = true;
      // selection.some(v => {
      //   if (v.StockQty === 0 || v.StockQty === null || v.StockQty === '0') {
      //     this.showNotify("warning", '生产订单为：' + v.ProductionOrderNo + '库存数量为0，禁止添加');
      //     switchBtn = false;
      //     return true
      //   }else if(this.dataList.length>0){
      //     this.dataList.some(res => {
      //       if (v.ProductionOrderNo + v.ComponentLineNo +v.DeliverLocation+v.RegionCode+ v.BinLocationCode === res.onlyId) {
      //         this.showNotify("warning", '生产订单为：' + v.ProductionOrderNo + '已选择，请勿重复选择');
      //         switchBtn = false;
      //         return true
      //       }
      //     })
      //   }
      // })
      if (switchBtn) {
        this.multipleSelection = selection;
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      // if (this.listQuery.dateValue) {
      //   this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD')
      //   this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD')
      // }
      const query = Object.assign({}, this.listQuery);
      GetSapOrderList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      this.$emit('ok', this.multipleSelection);
      this.dialogCustomerFormVisible = false;
    }
  }
}
</script>

<style scoped>
</style>
