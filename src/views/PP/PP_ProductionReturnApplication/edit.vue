<template>
  <div class="app-container">
    <p>
      <label style="width:100%">车间退料登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="单号">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="searchQuery.purchase" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型">
            <el-select v-model="searchQuery.MovementType" filterable placeholder="请选择">
              <el-option v-for="item in MovementTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库名称">
            <el-select v-model="searchQuery.InWhsCode" filterable placeholder="请选择" @change="changeWhsName">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.value+'-'+item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上传附件">
            <el-upload
              class="upload-demo"
              :on-success="handleAvatarSuccess"
              :limit="1"
              action="customize"
              :file-list="fileList"
              :before-upload="beforeAvatarUpload"
              :accept="acceptFileType"
              :on-exceed="handleExceed"
              :on-preview="handlePreview"
              :on-change="handleChange"
              :http-request="uploadFile"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">上传附件只能是 JPG、PNG、BMP、PDF 格式!</div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />

      <!-- <el-table-column label="车间退料单号" prop="DocNum" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="车间退料行号" prop="Line" align="center" width="160" /> -->
      <!-- <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="120" /> -->
      <!-- <el-table-column label="组件行项目号" prop="ComponentLineNo" align="center" width="160" /> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="需求数量" prop="DemandQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="移动类型" prop="MovementType" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="工厂代码" prop="FactoryCode" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="出库仓库编号" prop="OutWhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出库仓库名称" prop="OutWhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="出库区域编号" prop="OutRegionCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出库区域名称" prop="OutRegionName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出库库位编号" prop="OutBinCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出库库位名称" prop="OutBinName" align="center" width="120" show-overflow-tooltip /> -->
      <el-table-column label="入库仓库编号" prop="InWhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="入库仓库名称" prop="InWhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="入库区域编号" prop="InRegionCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="入库区域名称" prop="InRegionName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="入库库位编号" prop="InBinCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="入库库位名称" prop="InBinName" align="center" width="120" show-overflow-tooltip /> -->
      <!-- <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.AssessmentCategory === '01'">自制</span>
          <span v-if="scope.row.AssessmentCategory === '02'">外购</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <add-select-model ref="modalForm" :data-list="list" @ok="modalFormOk" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  GetDetailList,
  update,
  Upload
} from '@/api/PP/PP_ProductionReturnApplication';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'PP.PP_ProductionReturnApplicationDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        purchase: '',
        materials: '',
        DocNum: '',
        MovementType: '',
        Remark: '',
        InWhsCode: '',
        InWhsName: '',
        InRegionCode: '',
        InRegionName: '',
        InBinCode: '',
        InBinName: ''
      },
      multipleSelection: [],
      rules: {

      },
      editStatus: 'create',
      delList: [],
      acceptFileType: '.jpeg,.png,.bmp,.pdf,.jpg',
      uploadTemplateFile: null,
      TempleteFile: null,
      fileList: [],
      MovementTypeOptions: [{
        value: '',
        label: '无类型'
      }, {
        value: '261',
        label: '工废'
      }, {
        value: '311',
        label: '料废'
      }],
      options: [],
      RegionOptions: [],
      BinLocationOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetXZ_SAP();
    this.getRegion();
    this.getBinLocation();
  },
  activated() {
    if (Object.keys(this.$route.params).length > 0) {
      console.log('activated调用了', this.$route.params);
      if (this.$route.params.status === 'add') {
        this.searchQuery = {
          purchase: '',
          materials: '',
          DocNum: '',
          MovementType: '',
          Remark: '',
          FilePath: '',
          FileName: '',
          InWhsCode: '',
          InWhsName: '',
          InRegionCode: '',
          InRegionName: '',
          InBinCode: '',
          InBinName: ''
        };
        this.list = [];
        this.fileList = [];
      }
      this.getPageParams();
    }
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ID) {
            v.IsDelete = true;
            this.delList.push(v.ID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList);
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择生产订单');
        return;
      }
      let switchBtn = true;
      this.list.some(res => {
        //   console.log(res)
        //   if (res.SupplierName === '' || res.SupplierName === null) {
        //     this.showNotify("warning", '供应商名称不能为空');
        //     switchBtn = false
        //     return true
        //   }
        //   if (res.SubcontractingApplicationQty === null || res.SubcontractingApplicationQty === 0 || res
        //     .SubcontractingApplicationQty === '0' || res.SubcontractingApplicationQty === '') {
        //     this.showNotify("warning", '申请单的数量不能为空或者为零');
        //     switchBtn = false
        //     return true
        //   }
        //   if (res.Unit === '' || res.Unit === null) {
        //     this.showNotify("warning", '单位不能为空');
        //     switchBtn = false
        //     return true
        //   }
        if (res.InBinCode === '' || res.InBinCode === null) {
          this.showNotify('warning', '入库信息不能为空');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.startLoading();
        const query = {
          DocNum: this.searchQuery.DocNum,
          Remark: this.searchQuery.Remark,
          MovementType: this.searchQuery.MovementType,
          FilePath: this.searchQuery.FilePath,
          FileName: this.TempleteFile,
          detailed: this.list,
          deletedetail: this.delList,
          ID: this.searchQuery.ID
        };
        if (this.editStatus === 'create') {
          SubmitScanInfo(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PP.PP_ProductionReturnApplication');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        } else {
          update(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PP.PP_ProductionReturnApplication');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading()
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      // record.forEach((v, index) => {
      //   data.push({
      //     BaseNum: v.ProductionOrderNo, //生产订单号
      //     ComponentLineNo: v.ComponentLineNo, // 组件行项目行号
      //     ComponentNo: v.ComponentNo, // 组件行项目编号
      //     ItemCode: v.MaterialCode, // 物料件号
      //     ItemName: v.MaterialName, // 物料名称
      //     Qty: v.DemandQty, // 数量
      //     Unit: v.ComponentUnit, // 单位
      //     WhsCode: v.DeliverLocation, // 仓库
      //     WhsName: v.DeliverLocationName, // 仓库名称
      //     RegionCode: v.RegionCode, // 区域
      //     RegionName: v.RegionName, // 区域名称
      //     BinLocationCode: v.BinLocationCode, // 库位
      //     BinLocationName: v.BinLocationName, // 库位名称
      //     SalesOrderNo: v.SalesOrderNo, // 销售订单
      //     SalesOrderLineNo: v.SalesOrderLineNo, // 销售订单行项目
      //     MovementType: v.MovementType, // 移动类型
      //     ItmsGrpCode: v.MaterialGroupCode, // 物料组
      //     ItmsGrpName: v.MaterialGroupDes, // 物料组描述
      //     onlyId: v.ProductionOrderNo + v.ComponentLineNo + v.DeliverLocation + v.RegionCode + v
      //       .BinLocationCode
      //   })
      // })
      record.forEach((v, index) => {
        v.InWhsCode = this.searchQuery.InWhsCode;
        v.InWhsName = this.searchQuery.InWhsName;
        v.InRegionCode = this.searchQuery.InRegionCode;
        v.InRegionName = this.searchQuery.InRegionName;
        v.InBinCode = this.searchQuery.InBinCode;
        v.InBinName = this.searchQuery.InBinName;
        v.DemandQty = v.OutQty;
      });
      console.log(record);
      const obj = {};
      this.list = this.list.concat(record).reduce((cur, next) => {
        obj[next.ComponentCode + next.OutWhsCode] ? '' : obj[next.ComponentCode + next.OutWhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        DocNums: [this.searchQuery.DocNum]
      };
      this.listLoading = true;
      GetDetailList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        if (this.searchQuery.FilePath) {
          this.fileList = [{
            name: this.searchQuery.FileName,
            url: this.searchQuery.FilePath
          }]
        }
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.fileList = [];
        this.editStatus = 'create';
      }
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key, 'edit');
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ComponentCode + v.OutWhsCode === record.ComponentCode + record.OutWhsCode) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    handleAvatarSuccess(res, file) {
      this.searchQuery.FilePath = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isBMP = file.type === 'image/bmp';
      const isPDF = file.type === 'application/pdf';
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isJPG && !isPNG && !isBMP && !isPDF) {
        this.$message.error('上传附件只能是 JPG、PNG、BMP、PDF 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 5MB!');
      }
      return isJPG && isLt2M;
    },
    uploadFile() {
      console.log('s');
      this.uploadLoading = this.$loading({
        lock: true,
        text: this.$t('ui.MD.MD_LabelTemplate.Uploading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      const fd = new FormData();
      fd.append('file', this.uploadTemplateFile);
      fd.append('TempleteFile', this.TempleteFile);
      Upload(fd).then(rsp => {
        this.uploadLoading.close();
        this.searchQuery.FilePath = rsp.Data.FilePath;
        this.fileList = [{
          name: this.TempleteFile,
          url: this.searchQuery.FilePath
        }];
        this.$notify({
          title: this.$t('Common.success'),
          message: this.$t('Common.operationSuccess'),
          type: 'success',
          duration: 2000
        });
        this.getList();
      }).catch(() => {
        this.uploadLoading.close();
      });
    },
    handlePreview(file) {
      window.open(his.API.BaseURL + this.searchQuery.FilePath);
    },
    beforeRemove(file, fileList) {
      console.log(this.fileList);
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    handleChange(file, fileList) {
      this.uploadTemplateFile = file.raw;
      this.TempleteFile = file.name;
      this.uploadFile();
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.InWhsName = this.options.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'InWhsName', this.searchQuery.InWhsName);
          this.$set(res, 'InWhsCode', this.searchQuery.InWhsCode);
        })
      }
      this.getRegion();
    },
    getRegion() {
      if (this.searchQuery.InWhsCode) {
        const query = {
          WhsCode: this.searchQuery.InWhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.InRegionName = res.Data[0].RegionName;
              this.searchQuery.InRegionCode = res.Data[0].RegionCode;
              if (this.list.length > 0) {
                this.list.forEach(res => {
                  this.$set(res, 'InRegionName', this.searchQuery.InRegionName);
                  this.$set(res, 'InRegionCode', this.searchQuery.InRegionCode);
                })
              }
              this.getBinLocation();
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.searchQuery.InRegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'InRegionName', this.searchQuery.InRegionName);
          this.$set(res, 'InRegionCode', this.searchQuery.InRegionCode);
        })
      }
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.searchQuery.InRegionCode) {
        const query = {
          regionCode: this.searchQuery.InRegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.InBinName = res.Data[0].BinLocationName;
              this.searchQuery.InBinCode = res.Data[0].BinLocationCode;
              if (this.list.length > 0) {
                this.list.forEach(res => {
                  this.$set(res, 'InBinName', this.searchQuery.InBinName);
                  this.$set(res, 'InBinCode', this.searchQuery.InBinCode);
                })
              }
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.searchQuery.InBinName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'InBinName', this.searchQuery.InBinName);
          this.$set(res, 'InBinCode', this.searchQuery.InBinCode);
        })
      }
      this.$forceUpdate();
    }
  }
}
</script>
