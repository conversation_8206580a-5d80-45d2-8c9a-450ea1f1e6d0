<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>
      <el-button
        v-permission="{ name: 'PP.PP_FTTP.Add' }"
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >{{ $t('Common.log') }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_FTTP.Edit' }"
        v-waves
        :disabled="!editable"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_FTTP.Delete' }"
        v-waves
        :disabled="!deletable"
        size="small"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_FTTP.Export' }"
        :loading="downloadLoading"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-permission="{ name: 'PP.PP_FTTP.Print' }"
        v-waves
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTP.FTTPID')"
        prop="FTTPID"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.FTTPID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTP.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column v-if="false" :label="$t('PP.FTTP.BaseEntry')" prop="BaseEntry"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BaseEntry }}</span> </template> </el-table-column> -->
      <el-table-column
        :label="$t('ui.PP.FTTP.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTP.PLine')"
        prop="PLine"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTP.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTP.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.FTTP.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode }}</span> </template> </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.PP.FTTP.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column> -->
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTP.FType')"
        prop="FType"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.FType }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.FTTP.IsRepairReturn')" prop="IsRepairReturn"   align="center" width="160"> <template slot-scope="scope"> <span>{{ scope.row.IsRepairReturn|yesnoFilter}}</span> </template> </el-table-column> -->

      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>{{ $t('ui.PP.FTTPDetailed.title') }}</p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column
        v-if="false"
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="100"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTPDetailed.DetailedID')"
        prop="DetailedID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.Station')"
        prop="Station"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Station }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.FTTPDetailed.ActualStation')" prop="ActualStation"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ActualStation}}</span> </template> </el-table-column> -->
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.ReasonsCode')"
        prop="ReasonsCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.ReasonsDesc')"
        prop="ReasonsDesc"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.InputQty')"
        prop="InputQty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InputQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.RejectQty')"
        prop="RejectQty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RejectQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.OutputQty')"
        prop="OutputQty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutputQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.DealWith')"
        prop="DealWith"
        align="center"
        width="120"
        :formatter="dealWithFormatter"
      />
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.IConfirm')"
        prop="IConfirm"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.WConfirm')"
        prop="WConfirm"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
  </div>
</template>

<script>
import {
  fetchPage,
  add,
  update,
  batchDelete,
  fetchDocNum,
  exportExcelFile
} from '@/api/PP/PP_FTTP';
import { exportToExcel } from '@/utils/excel-export';
import {
  fetchDetailPage,
  addDetail,
  updateDetail,
  batchDeleteDetail
} from '@/api/PP/PP_FTTPDetailed';
import { printOrderToPDF } from '@/api/PP/PP_Print';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import { convertToKeyValue } from '@/utils';
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'PP.PP_FTTP',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      listDetail: null,
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        dateRangeValue: [
          new Date(),
          new Date()
        ]
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 10,
        docNum: ''
      },
      dealWithOptions: [],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      downloadLoading: false,
      multipleSelection: [],
      currentRow: {
        FTTPID: '',
        DocNum: '',
        PLine: '',
        STime: '',
        Status: undefined
      },
      isProcessing: false
    };
  },
  computed: {
    editable() {
      return this.multipleSelection && this.multipleSelection.length === 1;
    },
    deletable() {
      return this.multipleSelection && this.multipleSelection.length > 0;
    }
  },
  created() {
    this.getDict('PP003').then(data => {
      this.dealWithOptions = data;
      this.getList();
    });
  },
  methods: {
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.listQuery.dateRangeValue) {
        this.listQuery.fromTime = this.listQuery.dateRangeValue[0];
        this.listQuery.toTime = this.listQuery.dateRangeValue[1];
      }
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.currentRow.DocNum;

      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter(1);
    },
    detailSortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.handleFilter(2);
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    dealWithFormatter(row) {
      var dealWith = this.dealWithOptions.find(x => x.EnumKey == row.DealWith);
      if (dealWith) {
        return dealWith.EnumValue;
      }
    },
    handleFilter(flag) {
      if (flag == 2) {
        this.clearTables(2);
        if (this.currentRow) this.getDetailList();
      } else {
        this.clearTables(3);
        this.listQuery.PageNumber = 1;
        this.listQuery.PageSize = 10;
        this.getList();
      }
    },
    handleCreate() {
      this.routeTo('PP.PP_FTTPDetailed');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        this.currentRow = selectRows[0];
        this.listDetailQuery.docNum = this.currentRow.DocNum;
        fetchDetailPage(this.listDetailQuery).then(response => {
          if (response.Code === 2000) {
            this.listDetail = response.Data.items;
            this.totalDetail = response.Data.total;
            var objTmp = this.listDetail.find(x => x.WConfirm === true);
            if (objTmp) {
              // 如果已经进行过仓库确认，则不允许和编辑
              this.showNotify('error', 'ui.PP.FTTP.confirmRecordExists');
              return false;
            } else {
              this.routeTo('PP.PP_FTTPDetailed', Object.assign(selectRows[0]));
            }
          }
        });
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          var arrRowsID = selectRows.map(function(v) {
            return v.FTTPID;
          });

          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              this.isProcessing = false;
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.handleFilter(2);
    },
    handlePrint() {
      var selectRows = this.multipleSelection;
      var plineSame = selectRows[0].PLine;
      var isSamePline = selectRows.find(x => x.PLine != plineSame);
      if (isSamePline) {
        this.showNotify('warning', 'Common.NotSamePline');
        return;
      }
      //   if (this.checkSingleSelection(selectRows)) {
      var docNums = selectRows.map(v => v.DocNum);
      console.log(docNums);
      printOrderToPDF({
        docNums: docNums,
        templateCode: 'PP_FTTP'
      }).then(response => {
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
      //   }
    },
    handleExport() {
      // eslint-disable-next-line no-undef
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => exportToExcel(res.data, '生产FTT管理'));
    }
  }
};
</script>
