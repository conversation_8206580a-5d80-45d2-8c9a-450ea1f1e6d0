<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("ui.PP.FTTP.title") }}</label>
    </p>
    <el-form
      ref="dataForm"
      :inline="true"
      :rules="rules"
      :model="primary"
      label-position="right"
      label-width="100px"
    >
      <el-form-item :label="$t('ui.PP.FTTP.DocNum')" prop="DocNum">
        <el-input v-model="primary.DocNum" disabled />
      </el-form-item>
      <el-form-item v-show="false" :label="$t('ui.PP.FTTP.BaseEntry')" prop="BaseEntry">
        <el-input v-model="primary.BaseEntry" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTP.BaseNum')" prop="BaseNum">
        <el-input
          v-model="primary.BaseNum"
          :placeholder="$t('ui.PP.ProductionOrder.select')"
          @keyup.enter.native="handleEnterBaseNum"
        >
          <el-button slot="append" icon="el-icon-more" @click="handleOrderBtnClick" />
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTP.PLine')" prop="PLine">
        <el-input v-model="primary.PLine" disabled />
        <!-- <el-select v-model="primary.PLine" @change="handlePLineChange">
                <el-option v-for="item in pLineOptions" :key="item.EnumKey" :label="item.EnumValue" :value="item.EnumKey" />
        </el-select>-->
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTP.ItemCode')" prop="ItemCode">
        <el-input v-model="primary.ItemCode" readonly />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTP.ItemName')" prop="ItemName">
        <el-input v-model="primary.ItemName" readonly />
      </el-form-item>
      <el-form-item v-if="false" :label="$t('ui.PP.FTTP.FType')" prop="FType">
        <el-select v-model="primary.FType" filterable @change="handleFTypeChange">
          <el-option
            v-for="item in fTypeOptions"
            :key="item.EnumKey"
            :label="item.EnumValue"
            :value="item.EnumKey"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('ui.PP.FTTP.IsRepairReturn')" prop="IsRepairReturn">
            <el-checkbox v-model="primary.IsRepairReturn" />
      </el-form-item>-->
      <el-form-item :label="$t('ui.PP.FTTP.CTime')" prop="CTime">
        <el-date-picker
          v-model="primary.CTime"
          class="filter-item"
          type="date"
          :clearable="false"
          :placeholder="$t('ui.PP.FTTP.CTime')"
        />
      </el-form-item>
    </el-form>
    <p>
      <label style="width:100%">{{ $t("ui.PP.FTTPDetailed.title") }}</label>
    </p>
    <el-form :inline="true" label-position="right" label-width="100px">
      <el-form-item :label="$t('ui.PP.FTTPDetailed.Station')" prop="Station">
        <el-select
          v-model="temp.Station"
          filterable
          :placeholder="$t('ui.PP.FTTPDetailed.Station')"
          @change="handleStationChange"
        >
          <el-option
            v-for="item in stationOptions"
            :key="item.EnumValue1"
            :label="item.EnumValue1"
            :value="item.EnumValue1"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTPDetailed.InputQty')" prop="InputQty">
        <el-input-number
          v-model="temp.InputQty"
          controls-position="right"
          :min="0"
          :placeholder="$t('ui.PP.FTTPDetailed.InputQty')"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTPDetailed.RejectQty')" prop="RejectQty">
        <el-input-number
          v-model="temp.RejectQty"
          controls-position="right"
          :min="0"
          :placeholder="$t('ui.PP.FTTPDetailed.RejectQty')"
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTPDetailed.OutputQty')" prop="OutputQty">
        <el-input
          :value="outputQty"
          controls-position="right"
          :placeholder="$t('ui.PP.FTTPDetailed.OutputQty')"
          readonly
        />
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTPDetailed.ReasonsDesc')" prop="ReasonsCode">
        <el-select v-model="temp.ReasonsCode" filterable @change="handleSelectReason">
          <el-option
            v-for="item in reasonOptions"
            :key="item.EnumKey"
            :label="item.EnumValue"
            :value="item.Remark"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('ui.PP.FTTPDetailed.DealWith')" prop="DealWith">
        <el-select v-model="temp.DealWith" filterable>
          <el-option
            v-for="item in dealWithOptions"
            :key="item.EnumKey"
            :label="item.EnumValue"
            :value="item.EnumKey"
          />
        </el-select>
      </el-form-item>

      <el-row style="margin-bottom:5px;">
        <el-button
          type="primary"
          icon="el-icon-edit"
          @click="handleAddDetail"
        >{{ $t("Common.log") }}</el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="handleDeleteDetail"
        >{{ $t("Common.delete") }}</el-button>
        <el-button type="success" icon="el-icon-edit" @click="handleCommit">
          {{ $t("Common.confirm") }}
        </el-button>
      </el-row>
    </el-form>
    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="100" />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.FTTPDetailed.DetailedID')"
        prop="DetailedID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.DocNum')"
        prop="DocNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.Station')"
        prop="Station"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Station }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PP.FTTPDetailed.ActualStation')" prop="ActualStation"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ActualStation}}</span> </template> </el-table-column> -->
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.ReasonsCode')"
        prop="ReasonsCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.ReasonsDesc')"
        prop="ReasonsDesc"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReasonsDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.InputQty')"
        prop="InputQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InputQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.RejectQty')"
        prop="RejectQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RejectQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.OutputQty')"
        prop="OutputQty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutputQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.FTTPDetailed.DealWith')" prop="DealWith" align="center" width="120" :formatter="dealWithFormatter" />
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.IConfirm')"
        prop="IConfirm"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PP.FTTPDetailed.WConfirm')"
        prop="WConfirm"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail > 0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
    <ProductionOrderDialog :show.sync="dialogFormVisible" @close="handleSelectProductionOrder" />
  </div>
</template>

<script>
import {
  fetchDetailPage,
  addDetails,
  updateDetails
} from '@/api/PP/PP_FTTPDetailed';
import { fetchDocNum } from '@/api/PP/PP_FTTP';
import {
  fetchList as fetchProductionOrderList,
  fetchDetailList as fetchProductionOrderDetailList
} from '@/api/PP/PP_ProductionOrder';
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import ProductionOrderDialog from '@/components/FLD/ProductionOrderDialog'; // secondary package based on el-pagination

_ = require('lodash');

export default {
  name: 'PP.PP_FTTPDetailed',
  components: {
    Pagination,
    ProductionOrderDialog
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      editFlag: 0, // 0，表示页面为创建，1，表示页面为编辑
      fTypeOptions: [],
      reasonOptions: [],
      dealWithOptions: [],
      // pLineOptions: [],
      stationOptions: [],
      list: [],
      listDetail: [],
      total: 0,
      totalDetail: 0,
      listLoading: true,
      listDetailLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        sort: 'ItemName asc',
        keyword: ''
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 20,
        docNum: undefined,
        sort: 'DetailedID asc',
        keyword: ''
      },
      primary: {
        DocNum: undefined,
        BaseEntry: undefined,
        BaseNum: undefined,
        PLine: undefined,
        ItmsGrpCode: undefined,
        ItmsGrpName: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        FType: undefined,
        Remark: '',
        CTime: new Date()
      },
      temp: {
        DetailedID: undefined,
        DocNum: undefined,
        Station: undefined,
        ActualStation: undefined,
        ReasonsCode: undefined,
        ReasonsDesc: undefined,
        InputQty: 0,
        RejectQty: 0,
        OutputQty: 0,
        DealWith: undefined,
        IConfirm: undefined,
        WConfirm: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        select: this.$i18n.t('Common.select')
      },
      multipleSelection: [],
      rules: {
        Station: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ],
        InputQty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ],
        // RejectQty: [
        //   {
        //     required: true,
        //     validator: this.QtyValidator,
        //     trigger: "change"
        //   }
        // ],
        OutputQty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ]
      }
    };
  },

  computed: {
    outputQty() {
      if (
        (this.temp && this.temp.InputQty && this.temp.RejectQty) ||
        (this.temp && this.temp.InputQty) ||
        (this.temp && this.temp.RejectQty)
      ) {
        if (this.temp.DealWith !== 1) {
          return this.temp.InputQty - this.temp.RejectQty;
        } else {
          return this.temp.InputQty;
        }
      }
    }
  },
  created() {
    this.getPageParams(this.initPage);

    // 不良登记状态下拉框
    this.getDict('PP004').then(data => {
      this.fTypeOptions = data;
      if (this.primary.FType) {
      } else {
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        if (this.fTypeOptions && this.fTypeOptions.length > 0) {
          this.primary.FType = this.fTypeOptions[0].EnumKey;
        }
      }
    });

    // 不良处理方式下拉框
    this.getDict('PP003').then(data => {
      this.dealWithOptions = data;
    });

    // //站点不良原因下拉框
    // this.getDict('PP006').then(data => {
    //     this.reasonOptions = data
    // })
    this.primary.CTime = new Date();
  },
  methods: {
    dealWithFormatter(row) {
      if (row && row.DealWith) {
        var obj = this.dealWithOptions.find(x => x.EnumKey == row.DealWith);
        return obj.EnumValue;
      }
    },
    handleFTypeChange(val) {
      this.$forceUpdate();
    },
    loadStationReasons(station, pLine) {
      // 站点不良原因下拉框
      this.getDict('PP006').then(data => {
        this.reasonOptions = data.filter(
          x => x.EnumValue2 == pLine && x.EnumValue1 == station
        );
        this.$forceUpdate();
      });
    },
    initPage() {
      this.handlePLineChange(this.primary.PLine);
      this.getDetailList();
    },
    getDocNum(doneCallback) {
      fetchDocNum().then(response => {
        if (response.Code === 2000) {
          this.primary.DocNum = response.Data;
          if (doneCallback) doneCallback();
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getPageParams(doneCallback) {
      this.primary = Object.assign({}, this.$route.params);
      if (this.primary.DocNum) {
        // 编辑
        this.editFlag = 1;
        if (doneCallback) doneCallback();
      } else {
        // 新增
        this.editFlag = 0;
        this.getDocNum(doneCallback);
      }
      if (this.primary.STime) {
      } else {
        this.primary.STime = new Date();
      }
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.primary.DocNum;

      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    resetTemp() {
      this.temp = {
        DetailedID: undefined,
        DocNum: this.primary.DocNum,
        Station: undefined,
        ActualStation: undefined,
        ReasonsCode: undefined,
        ReasonsDesc: undefined,
        InputQty: 0,
        RejectQty: 0,
        OutputQty: 0,
        DealWith: undefined,
        IConfirm: undefined,
        WConfirm: undefined
      };
    },
    commit() {
      if (this.editFlag == 1) {
        updateDetails({
          primary: this.primary,
          list: this.listDetail
        }).then(response => {
          if (response.Code === 2000) {
            // 跳转回主单页面
            this.backTo('PP.PP_FTTP');
          } else {
            this.showNotify('error', response.Message);
          }
        });
      } else {
        addDetails({
          primary: this.primary,
          list: this.listDetail
        }).then(response => {
          if (response.Code === 2000) {
            // 跳转回主单页面
            this.backTo('PP.PP_FTTP');
          } else {
            this.showNotify('error', response.Message);
          }
        });
      }
    },
    submitAddDetail() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          var current = JSON.parse(JSON.stringify(this.temp));
          current.OutputQty = current.InputQty - current.RejectQty;
          if (!(current.OutputQty && current.InputQty)) { // && current.RejectQty
            this.showNotify('warning', 'Common.inCorrectQuantity');
            return false;
          }
          if (current.RejectQty > current.InputQty) {
            this.showNotify(
              'error',
              'ui.PP.FTTPDetailed.rejectShouldBeLessThanInput'
            );
            return false;
          }

          var reason = this.reasonOptions.find(
            x => x.Remark == current.ReasonsCode
          );

          var detail = this.listDetail.find(
            obj =>
              obj.BaseNum == current.BaseNum &&
              obj.PLine == current.PLine &&
              obj.Station == current.Station &&
              obj.ReasonsDesc == current.ReasonsDesc &&
              obj.DealWith == current.DealWith
          );
          if (detail) {
            // 如果找到已添加的同种物料只增加数量不新增条目
            detail.OutputQty += current.OutputQty;
            detail.RejectQty += current.RejectQty;
            detail.InputQty += current.InputQty;
          } else {
            this.listDetail.push(current);
          }
          this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleDetailFilter() {
      this.getDetailList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleAddDetail() {
      if (!this.temp.Station) {
        this.showNotify('warning', 'Common.inSiteID');
        return;
      }

      this.temp.DocNum = this.primary.DocNum;
      this.submitAddDetail();
      this.resetTemp();
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.listDetail.indexOf(v);
          this.listDetail.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.$confirm(
        this.$i18n.t('Common.committingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.listDetail.forEach(x => {
          if (x.RejectQty > x.InputQty) {
            this.showNotify(
              'error',
              'ui.PP.FTTPDetailed.rejectShouldBeLessThanInput'
            );
            return false;
          }
        });
        this.commit();
      });
    },
    handleOrderBtnClick() {
      this.dialogStatus = 'select';
      this.dialogFormVisible = true;
    },
    handlePLineChange(val) {
      // 登记站点下拉框
      this.getDict('PP005').then(data => {
        this.stationOptions = data.filter(s => s.EnumValue === val);
      });
    },
    handleSelectReason(val) {
      var reason = this.reasonOptions.find(x => x.Remark === val);

      this.temp.ReasonsDesc = reason ? reason.EnumValue : '';
    },
    handleSelectProductionOrder(productionOrder) {
      if (productionOrder) {
        Object.assign(this.primary, productionOrder);
        this.primary.PLine = productionOrder.ProductionLine;
        this.handlePLineChange(this.primary.PLine);
      }
    },
    handleStationChange(val) {
      var station = this.stationOptions.find(x => x.EnumValue1 == val);
      if (station) {
        this.temp.Station = station.EnumValue1;
        this.temp.ActualStation = station.EnumValue2;
        this.loadStationReasons(this.temp.Station, this.primary.PLine);
      }
    },
    handleEnterBaseNum() {
      if (this.primary.BaseNum && this.primary.BaseNum.trim() !== '') {
        fetchProductionOrderList({
          productionOrderStatus: '',
          productionLine: '',
          productionOrderID: this.primary.BaseNum
        })
          .then(response => {
            if (response && response.Code == 2000) {
              var orders = response.Data;
              if (orders && orders.length > 0) {
                orders.forEach(x => {
                  x.value = x.BaseNum;
                });
                this.handleSelectProductionOrder(orders[0]);
              } else {
                this.showNotify('error', 'ui.PP.ProductionOrder.notFound');
              }
            } else {
              this.showNotify('error', 'ui.PP.ProductionOrder.notFound');
            }
          })
          .catch(err => {
            this.showNotify('error', 'ui.PP.ProductionOrder.notFound');
          });
      }
    }
  }
};
</script>
