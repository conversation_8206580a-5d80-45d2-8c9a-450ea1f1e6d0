<template>
  <div class="app-container">
    <p>
      <label style="width:100%">装箱材料分拣登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="单号">
            <el-input v-model="searchQuery.PackSortingNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="扫描码">
            <el-input v-model="searchQuery.ScanningCode" @keyup.enter.native="changeSerialNo" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="移动类型">
            <el-select v-model="searchQuery.MovementType" filterable placeholder="请选择" disabled>
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item class="filter-item" label="出厂编号">
            <el-input v-model="searchQuery.SerialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产订单" prop="ProductionOrderNo">
            <el-input v-model="searchQuery.ProductionOrderNo" disabled />
            <!-- <el-select v-model="searchQuery.ProductionOrderNo" filterable placeholder="请选择"
              @change="changeProductionOrderNo">
              <el-option v-for="item in ProductionOrderNoOptions" :key="item.ProductionOrderNo"
                :label="item.ProductionOrderNo" :value="item.ProductionOrderNo">
              </el-option>
            </el-select> -->
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item class="filter-item" label="工厂">
            <el-input v-model="searchQuery.FactoryCode" disabled />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料件号">
            <el-input v-model="searchQuery.MaterialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料名称">
            <el-input v-model="searchQuery.MaterialName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="订单类型">
            <el-input v-model="searchQuery.OrderType" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="订单数量">
            <el-input v-model="searchQuery.OrderQty" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="单位">
            <el-input v-model="searchQuery.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产批次">
            <el-input v-model="searchQuery.ProductionBatch" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="收货库存地点">
            <el-input v-model="searchQuery.ReceivingLocation" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="订单状态">
            <el-input v-model="searchQuery.OrderStatus" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产调度员">
            <el-input v-model="searchQuery.ProductionScheduler" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="线体编号">
            <el-input v-model="searchQuery.ProductionLineCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="线体名称">
            <el-input v-model="searchQuery.ProductionLineDes" disabled />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item class="filter-item" label="销售订单">
            <el-input v-model="searchQuery.SalesOrderNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="销售订单行项目">
            <el-input v-model="searchQuery.SalesOrderLineNo" disabled />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8">
          <el-form-item class="filter-item" label="过账时间" prop="ManualPostTime">
            <el-date-picker :clearable="false" type="date" placeholder="过账时间" v-model="searchQuery.ManualPostTime"
              format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="16">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <el-switch v-model="switchValue" active-color="#13ce66" inactive-color="#ff4949" active-text="自动提交"
      inactive-text="手动提交" @change="changeSwitchValue">
    </el-switch> -->
    <p>
      <el-input
        ref="input"
        v-model="listQuery.ScanningCode"
        class="filter-item"
        placeholder="请扫描物料编码"
        style="width: 220px"
        :autofocus="autofocus"
        @keyup.enter.native="handleFilter"
      />

      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button v-if="!switchValue" type="success" size="small" icon="el-icon-edit" @click="handleCommit">
        {{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" show-overflow-tooltip />
      <!-- <el-table-column label="序列号关联单号" prop="PackSortingNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="序列号关联行号" prop="FeedingLineNo" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="预留编号" prop="ReservedNo" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="预留行号" prop="ComponentLineNo" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="需求数量" prop="DemandQty" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工厂" prop="FactoryCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="发料库存地" prop="DeliverLocation" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="是否是倒冲方式" prop="IsBackflush" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.IsBackflush === 'X'">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column label="是否确认" prop="IsConfirm" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- <add-select-model ref="modalForm" @ok="modalFormOk" /> -->
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  Update,
  GetOrderBySerialNo,
  GetMaterial,
  GetOrderNoSerialNo,
  GetMaterialByOrder
} from '@/api/PP/PP_PackSorting';
export default {
  name: 'PP.PP_PackSortingDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        productionOrderNo: '',
        ScanningCode: ''
      },
      searchQuery: {
        PackSortingNo: '',
        SerialNo: '',
        ScanningCode: '',
        // MovementType: '261',
        ProductionOrderNo: '',
        FactoryCode: '',
        MaterialNo: '',
        MaterialName: '',
        OrderType: '',
        OrderQty: '',
        Unit: '',
        ProductionBatch: '',
        ReceivingLocation: '',
        OrderStatus: '',
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        // ManualPostTime: new Date(),
        Remark: '',
        ProductionLineCode: '',
        ProductionLineDes: '',
        ProductionScheduler: '',
        StartTime: ''
      },
      multipleSelection: [],
      rules: {
        ProductionOrderNo: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: 'create',
      delList: [],
      action: '/PP/PP_OverReceive/Upload',
      options: [{
        value: '261',
        label: '投料'
      }, {
        value: '262',
        label: '退料'
      }],
      acceptFileType: '.jpeg,.png,.bmp,.pdf,.jpg',
      uploadTemplateFile: null,
      TempleteFile: null,
      fileList: [],
      switchValue: false,
      autofocus: false,
      ProductionOrderNoOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    // this.GetOrderNoSerialNo()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ID) {
            v.IsDelete = true;
            this.delList.push(v.ID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      // if (this.searchQuery.ScanningCode === '') {
      //   this.showNotify("warning", '移动类型不能为空');
      //   return
      // }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.list.length === 0) {
            this.showNotify('warning', '请扫描物料码');
            return;
          }
          let switchBtn = true;
          this.list.some(res => {
            console.log(res);
            if (res.DemandQty === null || res.DemandQty === 0 || res.DemandQty === '0' || res.DemandQty === '') {
              this.showNotify('warning', '需求数量不能为空或者为零');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            this.startLoading();
            let query = {
              DetailList: this.list,
              DelDetailIds: this.delList
            };
              // console.log(query)
              // return
            query = Object.assign(this.searchQuery, query);
            // query.ManualPostTime = this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD')
            if (this.editStatus === 'create') {
              SubmitScanInfo(query).then(res => {
                if (res.Code === 2000) {
                  if (!this.switchValue) {
                    this.backTo('PP.PP_PackSorting');
                  } else {
                    this.fetchDocNum();
                    this.listQuery.ScanningCode = '';
                    this.list = [];
                    this.showNotify('success', '添加成功');
                  }
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            } else {
              Update(query).then(res => {
                if (res.Code === 2000) {
                  if (!this.switchValue) {
                    this.backTo('PP.PP_PackSorting');
                  } else {
                    this.listQuery.ScanningCode = '';
                    this.list = [];
                    this.showNotify('success', '添加成功');
                  }
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            }
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      console.log(key);
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ComponentCode + v.ComponentLineNo === record.ComponentCode + record.ComponentLineNo) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.PackSortingNo = response.Data;
          console.log(this.searchQuery);
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const obj = {};
      this.list = this.list.concat(record).reduce((cur, next) => {
        obj[next.ProductionOrderNo + next.ComponentLineNo] ? '' : obj[next.ProductionOrderNo + next.ComponentLineNo] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        PackSortingNo: this.searchQuery.PackSortingNo
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        if (this.searchQuery.FilePath) {
          this.fileList = [{
            name: this.searchQuery.FileName,
            url: this.searchQuery.FilePath
          }]
        }
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.fileList = [];
        this.editStatus = 'create';
      }
    },
    // 根据出厂编号查询生产订单
    changeSerialNo() {
      if (!this.searchQuery.ScanningCode) {
        this.showNotify('warning', '请扫描身份识别卡');
        return;
      }
      const query = {
        serialNo: this.searchQuery.ScanningCode
      };
      this.startLoading();
      GetOrderBySerialNo(query).then(res => {
        if (res.Code === 2000) {
          if (res.Data !== null) {
            this.searchQuery.ProductionOrderNo = res.Data.Order.ProductionOrderNo;
            this.searchQuery.SerialNo = res.Data.Order.SerialNo;
            this.searchQuery.FactoryCode = res.Data.Order.FactoryCode;
            this.searchQuery.MaterialNo = res.Data.Order.MaterialNo;
            this.searchQuery.MaterialName = res.Data.Order.MaterialName;
            this.searchQuery.OrderType = res.Data.Order.OrderType;
            this.searchQuery.OrderQty = res.Data.Order.OrderQty;
            this.searchQuery.Unit = res.Data.Order.Unit;
            this.searchQuery.ProductionBatch = res.Data.Order.ProductionBatch;
            this.searchQuery.ReceivingLocation = res.Data.Order.ReceivingLocation;
            this.searchQuery.OrderStatus = res.Data.Order.OrderStatus;
            this.searchQuery.SalesOrderNo = res.Data.Order.SalesOrderNo;
            this.searchQuery.SalesOrderLineNo = res.Data.Order.SalesOrderLineNo;
            this.searchQuery.StartTime = res.Data.Order.StartTime;
            this.searchQuery.ProductionScheduler = res.Data.Order.ProductionScheduler;
            this.searchQuery.ProductionLineCode = res.Data.Order.ProductionLineCode;
            this.searchQuery.ProductionLineDes = res.Data.Order.ProductionLineDes;
            this.list = res.Data.Materials;
            this.listQuery.ScanningCode = '';
            setTimeout(() => {
              this.autofocus = true;
              this.$refs.input.focus();
            }, 100);
          } else {
            this.searchQuery.ProductionOrderNo = '';
            this.searchQuery.SerialNo = '';
            this.searchQuery.FactoryCode = '';
            this.searchQuery.MaterialNo = '';
            this.searchQuery.MaterialName = '';
            this.searchQuery.OrderType = '';
            this.searchQuery.OrderQty = '';
            this.searchQuery.Unit = '';
            this.searchQuery.ProductionBatch = '';
            this.searchQuery.ReceivingLocation = '';
            this.searchQuery.OrderStatus = '';
            this.searchQuery.SalesOrderNo = '';
            this.searchQuery.SalesOrderLineNo = '';
            this.searchQuery.Remark = '';
            // this.searchQuery.MovementType = '261';
            // this.searchQuery.ManualPostTime = new Date();
            this.searchQuery.StartTime = '';
            this.searchQuery.ProductionScheduler = '';
            this.searchQuery.ProductionLineCode = '';
            this.searchQuery.ProductionLineDes = '';
            this.showNotify('warning', '未查询到数据');
          }
          this.endLoading();
        }
      }).catch(err => {
        console.log(err);
        this.endLoading();
      })
    },
    // 查询物料
    handleFilter() {
      if (!this.searchQuery.ProductionOrderNo) {
        this.showNotify('warning', '请选择生产订单');
        return;
      }
      if (!this.listQuery.ScanningCode) {
        this.showNotify('warning', '请扫描物料码');
        return;
      }
      if (this.list.length > 0) {
        let switchBtn = true;
        this.list.some(res => {
          if (res.ComponentCode === this.listQuery.ScanningCode) {
            res.IsConfirm = true;
            res.ScanningCode = this.listQuery.ScanningCode;
            switchBtn = true;
            return true;
          } else {
            switchBtn = false;
          }
        });
        console.log(this.list);
        if (!switchBtn) {
          this.showNotify('warning', '未从明细列表匹配到相应数据');
        }
      } else {
        this.showNotify('warning', '未查询到信息');
      }
    },
    changeSwitchValue(e) {
      if (e) {
        if (this.listQuery.ScanningCode) {
          this.handleCommit();
        }
      }
    },
    async GetOrderNoSerialNo() {
      await GetOrderNoSerialNo().then(res => {
        if (res.Code === 2000) {
          this.ProductionOrderNoOptions = res.Data;
        }
      })
    },
    changeProductionOrderNo(e) {
      console.log(e);
      const obj = this.ProductionOrderNoOptions.find(v => v.ProductionOrderNo === e);
      this.searchQuery.ProductionOrderNo = obj.ProductionOrderNo;
      this.searchQuery.SerialNo = obj.SerialNo || '';
      this.searchQuery.FactoryCode = obj.FactoryCode || '';
      this.searchQuery.MaterialNo = obj.MaterialNo || '';
      this.searchQuery.MaterialName = obj.MaterialName || '';
      this.searchQuery.OrderType = obj.OrderType || '';
      this.searchQuery.OrderQty = obj.OrderQty || '';
      this.searchQuery.Unit = obj.Unit || '';
      this.searchQuery.ProductionBatch = obj.ProductionBatch || '';
      this.searchQuery.ReceivingLocation = obj.ReceivingLocation || '';
      this.searchQuery.OrderStatus = obj.OrderStatus || '';
      this.searchQuery.SalesOrderNo = obj.SalesOrderNo || '';
      this.searchQuery.SalesOrderLineNo = obj.SalesOrderLineNo || '';
      this.searchQuery.StartTime = obj.StartTime || '';
      this.searchQuery.ProductionScheduler = obj.ProductionScheduler || '';
      this.searchQuery.ProductionLineCode = obj.ProductionLineCode || '';
      this.searchQuery.ProductionLineDes = obj.ProductionLineDes || '';
      this.listQuery.ScanningCode = '';
      setTimeout(() => {
        this.autofocus = true;
        this.$refs.input.focus();
      }, 100);
      const query = {
        OrderNo: e
      };
      GetMaterialByOrder(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
        }
      })
    }
  }
}
</script>

<style>

</style>
