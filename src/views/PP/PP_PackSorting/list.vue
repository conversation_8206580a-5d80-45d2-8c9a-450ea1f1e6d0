<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-input
        v-model="listQuery.ProductionScheduler"
        size="small"
        class="filter-item"
        placeholder="生产管理员"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ProductionLineDes"
        size="small"
        class="filter-item"
        placeholder="线体"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_PackSorting.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_PackSorting.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_PackSorting.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-permission="{ name: 'PP.PP_PackSorting.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-printer"
        :disabled="canNotUpdate"
        @click="handlePrint"
      >
        {{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_PackSorting.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>

    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="PackSortingNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="ScanningCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="工厂" prop="FactoryCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MaterialNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="订单类型" prop="OrderType" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="订单数量" prop="OrderQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="装配日期"
        prop="StartTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="客户订单号" prop="SalesOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="发货单位" prop="Shippers" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="生产调度员" prop="ProductionScheduler" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="线体编码" prop="ProductionLineCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="交货时间"
        prop="DeliveryTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>装箱材料分拣明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="PackSortingNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="扫描码" prop="ScanningCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="生产订单" prop="ProductionOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="预留编号" prop="ReservedNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="预留行号" prop="ComponentLineNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ComponentCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="需求数量" prop="DemandQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="组件单位" prop="ComponentUnit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ComponentUnit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.ComponentUnit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.ComponentUnit ==='ST'">PC</span>
          <span v-else>{{ scope.row.ComponentUnit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工厂" prop="FactoryCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="发料仓库" prop="DeliverLocation" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售订单" prop="SalesOrderNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售订单行项目" prop="SalesOrderLineNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="评估类别" prop="AssessmentCategory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessmentType" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="特殊库存标识" prop="SpecialInventory" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="是否是倒冲方式" prop="IsBackflush" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.IsBackflush === 'X'">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column label="是否确认" prop="IsConfirm" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export';

import {
  fetchList,
  GetDetailedPageList,
  batchDelete,
  exportExcelFile,
  DoPost,
  exportExcelModel,
  improtExcelFile,
  printToPDF
} from '@/api/PP/PP_PackSorting';

export default {
  name: 'PP.PP_PackSorting',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        ProductionLineDes: '',
        ProductionScheduler: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/PP/PP/PP_PackSorting') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        // isPosted: this.listQuery.isPosted,
        ProductionLineDes: this.listQuery.ProductionLineDes,
        ProductionScheduler: this.listQuery.ProductionScheduler
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '装箱材料分拣');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.PackSortingNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.PackSortingNo + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          const arrRowsID = selectRows.map(v => v.ID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = {
        PackSortingNo: this.currentRow.PackSortingNo,
        PageNumber: this.listDetailQuery.PageNumber,
        PageSize: this.listDetailQuery.PageSize
      };
      GetDetailedPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('PP.PP_PackSortingDetail');
    },
    handleUpdate() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.PackSortingNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.PackSortingNo + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('PP.PP_PackSortingDetail', this.multipleSelection[0]);
      }
    },
    // 过账
    handlePosting() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.PackSortingNo + '信息已过账，请勿重复过账');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn === true) {
        this.isProcessing = true;
        DoPost(this.multipleSelection).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', res.Message || 'Common.postSuccess');
          } else {
            this.showNotify('error', res.Message || 'Common.operationFailed');
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(err => {
          this.isProcessing = false;
        });
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    getCaption(obj) {
      if (obj) {
        const index = obj.lastIndexOf('&');
        obj = obj.substring(index + 1, obj.length);
        return obj;
      }
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },

    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    // 打印
    handlePrint() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      const DocNums = selectRows.map(v => v.PackSortingNo);
      const query = {
        docNums: DocNums
      };
      printToPDF(query).then(response => {
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    }
  }
};
</script>
