<template>
  <div ref="divLayout" class="divLayout">
    <div ref="divTree" v-loading="isProcessing" class="listTreeDiv" element-loading-text="正在加载中...">
      <div style="font-size:12px; color:#999999; margin-left:23px;">总分录行:{{ SumCount }}</div>
      <el-tree
        v-if="treeShow"
        ref="listTree"
        :key="tree.key"
        :props="defaultProps"
        :load="loadNode"
        node-key="Value"
        lazy
        show-checkbox
        highlight-current
        @check="onCheckNode"
      />
    </div>
    <div ref="devLine" class="divLine" @mousedown="reactDiv" />
    <div ref="divApp" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
      <!--检索区域-->
      <div class="filter-container">
        <el-form label-width="80px" class="search" inline>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="listQuery.CTme"
              :clearable="false"
              size="small"
              class="filter-item"
              type="daterange"
              style="width: 220px"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
          <el-form-item label="发运状态">
            <el-select
              v-model="listQuery.Status"
              size="small"
              filterable
              placeholder="全部状态"
              style="width: 140px"
              class="filter-item"
            >
              <el-option v-for="item in statusOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="下载状态">
            <el-select
              v-model="listQuery.IsDownload"
              size="small"
              filterable
              placeholder="全部"
              style="width: 140px"
              class="filter-item"
            >
              <el-option v-for="item in downloadOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否交货导入" label-width="100px">
            <el-select
              v-model="listQuery.IsDeliveryImport"
              size="small"
              filterable
              placeholder="是否交货导入"
              style="width: 140px"
              class="filter-item"
            >
              <el-option v-for="item in isDeliveryImportOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键字">
            <el-input
              v-model="listQuery.keyword"
              size="small"
              class="filter-item"
              :placeholder="$t('Common.keyword')"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户">
            <el-input
              v-model="listQuery.Customer"
              size="small"
              class="filter-item"
              placeholder="客户"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户订单号" label-width="100px">
            <el-input
              v-model="listQuery.CustomerOrderNo"
              size="small"
              class="filter-item"
              placeholder="订单号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="合同号">
            <el-input
              v-model="listQuery.ContractNo"
              size="small"
              class="filter-item"
              placeholder="合同号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
        </el-form>
        <hr>
        <el-dropdown v-waves v-permission="{ name: 'SD.SD_ShippingPlan.Print' }" class="filter-item" size="small" @command="handlePrint">
          <el-button type="primary" :disabled="!selectAble" size="small">
            打印<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu>
            <el-dropdown-item command="2">按单号打印</el-dropdown-item>
            <el-dropdown-item command="2">合并地址打印</el-dropdown-item>
            <el-dropdown-item command="3">雷登打印</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          size="small"
          style="margin-left: 10px"
          type="primary"
          @command="handleExport"
        >
          <el-button type="primary" size="small">
            导出<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu>
            <el-dropdown-item command="1">导出全部</el-dropdown-item>
            <el-dropdown-item command="2">按物流供应商导出</el-dropdown-item>
            <el-dropdown-item command="3">按结算地址导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.UploadSRM' }"
          style="margin-left: 10px"
          class="filter-item"
          type="primary"
          icon="el-icon-document"
          size="small"
          :disabled="!selectAble"
          @click="handleUploadSRM"
        >同步SRM</el-button>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.UploadSRM' }"
          class="filter-item"
          type="primary"
          icon="el-icon-document"
          size="small"
          :disabled="!selectAble"
          @click="handleSetDelivery"
        >设置交货仓库</el-button>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.UploadSRM' }"
          class="filter-item"
          type="primary"
          icon="el-icon-document"
          size="small"
          @click="dialogSetShippingToDeliveryVisible = !dialogSetShippingToDeliveryVisible"
        >同步交货仓库</el-button>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.UploadSRM' }"
          class="filter-item"
          type="success"
          icon="el-icon-document"
          size="small"
          :disabled="!selectAble"
          @click="postSap"
        >过账</el-button>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.UploadSRM' }"
          class="filter-item"
          type="danger"
          icon="el-icon-document"
          size="small"
          :disabled="!selectAble"
          @click="revokeSap"
        >冲销</el-button>
      </div>

      <div>
        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          highlight-current-row
          style="width: 100%"
          :height="tableHeight"
          size="mini"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
          <el-table-column label="发运单号" prop="DocNum" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单行号" prop="DocLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="订单类型" prop="OrderType" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="状态" prop="Status" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-for="item of statusOptions" v-if="scope.row.Status === item.key">{{ item.label }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否已下载" prop="IsDownload" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SRM同步" prop="SrmStatus" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户订单号" prop="CustomerOrderNo" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="销售订单号" prop="SaleSapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="合同号" prop="ContractNo" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="发货单位" prop="CustomerName" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="要求发运日期" prop="DeliveryDate" align="center" width="100" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="件号描述" prop="ItemName" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="数量" prop="Quantity" align="center" width="80" show-overflow-tooltip />
          <el-table-column label="重量" prop="Weight" align="center" width="80" show-overflow-tooltip />
          <el-table-column label="出厂编号" prop="FactoryNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="地址" prop="SettlementAdd" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="联系人" prop="Contact" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="联系电话" prop="Telephone" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="发货地址" prop="CustomerAdd" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="物流供应商编号" prop="SupplierCode" align="center" width="140" show-overflow-tooltip />
          <el-table-column label="物流供应商" prop="SupplierName" align="center" width="140" show-overflow-tooltip />
          <el-table-column label="销售交货批" prop="BatchNum" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="仓库代码" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="仓库名称" prop="WhsName" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="计划入库时间" prop="PlanStockTime" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="实际入库时间" prop="ActualStockTime" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="计划发运时间" prop="DeliveryDate" align="center" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="实际发货时间" prop="ActualDate" align="center" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.Remark }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
          <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="150" :formatter="formatDateTime" />
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.PageNumber"
          :limit.sync="listQuery.PageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 设置交货 -->
    <el-dialog title="设置交货" :visible.sync="dialogSetShipmentDateVisible" width="30%">
      <div>
        <el-form ref="setDelivery" label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="交货仓库" prop="WhsCode" style="width:93%">
                <el-select v-model="syncDeliveryReq.WhsCode" size="small" class="filter-item" filterable placeholder="请选择库位">
                  <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="!dialogSetShipmentDateVisible">取消</el-button>
          <el-button type="primary" @click="onClickConfirm">设定发运日期并同步WMS</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 同步完工日期设置 -->
    <el-dialog title="同步完工日期设置" :visible.sync="dialogSetShippingToDeliveryVisible" width="30%">
      <div>
        <el-form ref="setShipmentDate" label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="同步日期" prop="Package_ConfigName" style="width:93%">
                <el-date-picker
                  v-model="setDeliveryDate"
                  class="filter-item"
                  :picker-options="pickerOptions"
                  range-separator="-"
                  :unlink-panels="true"
                  type="daterange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
                  :start-placeholder="$t('Common.startTime')"
                  :end-placeholder="$t('Common.endTime')"
                />

              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogSetShippingToDeliveryVisible = !dialogSetShippingToDeliveryVisible">取消</el-button>
          <el-button type="primary" @click="syncCompleted">设定发运日期并同步WMS</el-button>
        </div>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime, parseTime
} from '@/utils';
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  GetPageList,
  DoPost,
  PassPost,
  GetTree,
  UploadSRM,
  SyncSaleDelivery
} from '@/api/Sale/Sale_ShippingPlan';
import { GetXZ_SAP } from '@/api/PO/PO_ReturnScan';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import includingPriceTemplateBox from './print/includingPrice.json';
import notIncludingPriceTemplateBox from './print/notIncludingPrice.json';
import { getPrintInfo } from '@/api/SD/SD_ProductionExecutionScheduling';
import { SetShippingToDelivery } from '@/api/PP/PP_WorkshopScheduling';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

const includingPriceTempBox = new hiprint.PrintTemplate({ template: includingPriceTemplateBox });
const notIncludingPriceTempBox = new hiprint.PrintTemplate({ template: notIncludingPriceTemplateBox });

export default {
  name: 'SD.SD_ShippingPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      treeShow: [],
      options: [],
      tree: {
        treeNode: null,
        treeResolve: null,
        key: new Date().getTime()
      },
      defaultProps: {
        children: 'Children',
        label: 'Label',
        isLeaf: 'Leaf'
      },
      syncDeliveryReq: {
        WhsCode: '',
        Ids: ''
      },
      dialogSetShipmentDateVisible: false,
      SumCount: 0,
      total: 0,
      tableHeight: 600,
      isProcessing: false,
      listLoading: true,
      dialogSetShippingToDeliveryVisible: false,
      listDetailLoading: false,
      setDeliveryDate: [],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        CTme: [
          new Date(),
          new Date()
        ],
        Status: '',
        ContractNo: '',
        IsDownload: '',
        Customer: '',
        CustomerOrderNo: '',
        PSStatus: '',
        IsDeliveryImport: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      statusOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '未发运',
        key: 0
      },
      {
        label: '已下达',
        key: 1
      },
      {
        label: '交货中',
        key: 2
      },
      {
        label: '已完成',
        key: 3
      }
      ],
      downloadOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '未下载',
        key: 0
      },
      {
        label: '已下载',
        key: 1
      }],
      isDeliveryImportOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '未导入',
        key: '0'
      },
      {
        label: '已导入',
        key: '1'
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImportVisible: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    selectAble() {
      return this.multipleSelection.length !== 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ShippingPlan') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.listQuery.Level = 0;
      this.listQuery.TreeReqList = [];
      this.getList();
      this.reRenderTree();
    },
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        console.log(this.list);
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    reRenderTree() {
      this.treeShow = false;
      this.isProcessing = true;
      this.$nextTick(() => {
        this.treeShow = true;
        setTimeout(() => {
          this.isProcessing = false;
        }, 300)
      });
    },
    syncCompleted() {
      if (this.setDeliveryDate.length !== 2) {
        this.showNotify('error', '请选择日期范围');
        return;
      }
      this.$confirm(
        '此操作将同步完工的发运输入导入交货, 是否继续?',
        '同步确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        SetShippingToDelivery({ dateValue: this.setDeliveryDate }).then(res => {
          this.isProcessing = false;
          if (res.Code === 2000) {
            this.showNotify('success', '同步成功');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        }).catch(error => {
          this.isProcessing = false;
        });
      });
    },
    handlePrint(command) {
      this.isProcessing = true;
      if (command === '1') {
        this.notMergePrint(notIncludingPriceTempBox);
      } else if (command === '2') {
        this.print(notIncludingPriceTempBox)
      } else if (command === '3') {
        const selectRows = this.multipleSelection;
        for (let i = 0;i < selectRows.length;i++) {
          if (selectRows[i].CustomerCode !== 'Z001201606') {
            this.isProcessing = false;
            this.showNotify('error', '只允许打印客户：Z0012*01606-雷登电梯有限公司');
            return;
          }
        }
        this.print(includingPriceTempBox)
      }
    },
    notMergePrint(tempBox) {
      const selectRows = this.multipleSelection;
      const docNums = selectRows.map(v => v.DocNum);
      getPrintInfo({
        DocNums: docNums
      }).then(response => {
        const printData = [];
        const res = response.Data;
        const detailMap = res.reduce((map, detail) => {
          // 如果没有这个 customerId，初始化一个空数组
          if (!map.has(detail.CustomerName)) {
            map.set(detail.CustomerName, []);
          }
          // 将当前客户对象推入对应 customerId 的数组中
          map.get(detail.CustomerName).push(detail);
          return map;
        }, new Map());
        for (const [key, details] of detailMap) {
          if (details.length > 0) {
            for (let j = 0;j < details.length;j++) {
              const detailList = []
              detailList.push({
                No: j + 1,
                CONT: details[j].CONT,
                ItemName: details[j].ItemName,
                EapSerialNo: details[j].EapSerialNo,
                Project: details[j].Project,
                DeliveryScanQty: details[j].DeliveryScanQty,
                Price: details[j].Price
              })
              printData.push({
                // 主表
                CustomerName: details[0].CustomerName,
                CustomerAdd: details[0].CustomerAdd,
                CustomerCode: details[0].CustomerCode,
                ContactTelephone: details[0].Contact + '/' + details[0].Telephone,
                Creator: details[0].CUserName,
                CreateTime: parseTime(details[0].CTime, '{y}-{m}-{d}'),
                DetailList: detailList
              })
            }
          }
        }
        // 设置模板并 打印
        tempBox.print(printData, {
          // 将覆盖面板偏移设置
          leftOffset: 0, // 左偏移
          topOffset: 0// 上偏移
        }, {
          callback: () => {
            // 关闭打印窗口回调
            this.isProcessing = false;
          }
        });
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    print(tempBox) {
      const selectRows = this.multipleSelection;
      const printData = [];
      const detailMap = selectRows.reduce((map, detail) => {
        // 如果没有这个 customerId，初始化一个空数组
        if (!map.has(detail.CustomerName + detail.CustomerAdd)) {
          map.set(detail.CustomerName + detail.CustomerAdd, []);
        }
        // 将当前客户对象推入对应 customerId 的数组中
        map.get(detail.CustomerName + detail.CustomerAdd).push(detail);
        return map;
      }, new Map());
      for (const [key, details] of detailMap) {
        if (details.length > 0) {
          const detailList = []
          for (let j = 0;j < details.length;j++) {
            detailList.push({
              No: j + 1,
              CONT: details[j].ContractNo,
              ItemName: details[j].ItemName,
              EapSerialNo: details[j].FactoryNo,
              Project: details[j].ProjectName,
              DeliveryScanQty: details[j].Quantity,
              Price: details[j].Price
            })
          }
          printData.push({
            // 主表
            CustomerName: details[0].CustomerName,
            CustomerAdd: details[0].CustomerAdd,
            CustomerCode: details[0].CustomerCode,
            ContactTelephone: details[0].Contact + '/' + details[0].Telephone,
            Creator: details[0].CUserName,
            CreateTime: parseTime(details[0].CTime, '{y}-{m}-{d}'),
            DetailList: detailList
          })
        }
      }
      // 设置模板并 打印
      tempBox.print(printData, {
        // 将覆盖面板偏移设置
        leftOffset: 0, // 左偏移
        topOffset: 0 // 上偏移
      }, {
        callback: () => {
          // 关闭打印窗口回调
          this.isProcessing = false;
        }
      });
    },
    getWarehouseOption() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    // 导出
    handleExport(command) {
      this.isProcessing = true;
      let title = '';
      const date = this.$moment(new Date()).format('YYYY-MM-DD');
      if (command === '1') {
        title = date + '发运计划';
      } else if (command === '2') {
        title = date + '发运计划-按物流供应商导出';
      }
      if (command === '3') {
        title = date + '发运计划-按结算地址导出';
      }
      this.listQuery.type = command;
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res.data, title);
        this.handleFilter();
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    // 打印
    handleCommand(command) {
      this.isProcessing = true;
      if (command === '1') {
        this.notMergePrint(notIncludingPriceTempBox);
      } else if (command === '2') {
        this.print(notIncludingPriceTempBox)
      } else if (command === '3') {
        const selectRows = this.multipleSelection;
        for (let i = 0;i < selectRows.length;i++) {
          if (selectRows[i].CustomerCode !== 'Z001201606') {
            this.isProcessing = false;
            this.showNotify('error', '只允许打印客户：Z001201606-雷登电梯有限公司');
            return;
          }
        }
        this.print(includingPriceTempBox)
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleUploadSRM() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.ShippingPlanStatus === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步，请勿重复同步');
          switchBtn = false;
          return true;
        }
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿进行同步操作');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        UploadSRM(selectRows).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', '同步成功！');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        })
      }
    },
    handleSetDelivery() {
      const selectRows = this.multipleSelection;
      for (let i = 0;i < selectRows.length;i++) {
        const selectRow = selectRows[i];
        if (selectRow.Status === 2) {
          this.showNotify('warning', '单号为：' + selectRow.DocNum + '信息已同步，请勿重复同步');
          return true;
        }
      }
      this.getWarehouseOption();
      this.dialogSetShipmentDateVisible = true;
    },
    postSap() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const v = this.multipleSelection[i];
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            this.isProcessing = false;
            return
          }
          if (v.Status !== 2) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息未设置仓库，请先设置仓库');
            this.isProcessing = false;
            return
          }
        }
        const ids = this.multipleSelection.map(v => v.Id);
        DoPost(ids)
          .then(res => {
            if (res.Code === 2000) {
              this.$alert(res.Message || 'Common.postSuccess', res.MessageParam === 2000 ? '成功' : '失败', {
                confirmButtonText: '确定',
                closeOnClickModal: false,
                showCancelButton: false,
                callback: action => {
                  this.handleFilter();
                  this.isProcessing = false;
                }
              });
            } else {
              this.showNotify('error', res.Message || 'Common.operationFailed');
            }
          }).catch(err => {
            console.log(err);
            this.isProcessing = false;
          });
      }
    },
    revokeSap() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const v = this.multipleSelection[i];
          if (v.IsPosted !== 1) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，请检查');
            this.isProcessing = false;
            return
          }
        }
        const ids = this.multipleSelection.map(v => v.Id);
        PassPost(ids)
          .then(res => {
            if (res.Code === 2000) {
              this.$alert(res.Message || 'Common.postSuccess', res.MessageParam === 2000 ? '成功' : '失败', {
                confirmButtonText: '确定',
                closeOnClickModal: false,
                showCancelButton: false,
                callback: action => {
                  this.handleFilter();
                  this.isProcessing = false;
                }
              });
            } else {
              this.showNotify('error', res.Message || 'Common.operationFailed');
            }
          }).catch(err => {
            console.log(err);
            this.isProcessing = false;
          });
      }
    },
    onClickConfirm() {
      this.syncDeliveryReq.Ids = this.multipleSelection.map(v => v.Id);
      SyncSaleDelivery(this.syncDeliveryReq).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', '同步成功！');
        } else {
          this.showNotify('error', res.Message);
        }
      })
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    reactDiv() {
      var oBox = this.$refs.divLayout;
      var oTop = this.$refs.divTree;
      var oBottom = this.$refs.divApp;
      var oLine = this.$refs.devLine;
      var disX;
      oLine.onmousedown = function(e) {
        disX = (e || event).clientX;
        oLine.left = oLine.offsetLeft;
      }
      document.onmousemove = function(e) {
        var iT = oLine.left + ((e || event).clientX - disX);
        var maxT = oBox.clientWight - oLine.offsetWidth;
        oLine.style.margin = 0;
        iT < 0 && (iT = 0);
        iT > maxT && (iT = maxT);
        oLine.style.left = oTop.style.width = iT + 'px';
        oBottom.style.width = oBox.clientWidth - iT + 'px';
        return false
      }
      document.onmouseup = function() {
        document.onmousemove = null;
        document.onmouseup = null;
        oLine.releaseCapture && oLine.releaseCapture();
      }
      oLine.setCapture && oLine.setCapture();
      return false
    },
    loadNode(node, resolve) {
      console.log('node', node, 'resolve', resolve)
      this.tree.treeNode = node;
      this.tree.treeResolve = resolve;
      if (node.level === 0) {
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 1) {
        this.listQuery.Level = 1
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 2) {
        this.listQuery.Level = 2
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      return resolve([]);
    },
    // 树节点选择改变
    onCheckNode(data, event) {
      this.listQuery.TreeReqList = []
      const checkNodes = this.$refs['listTree'].getCheckedNodes(false, false);
      if (checkNodes.length > 0) {
        const listInfo = [];
        checkNodes.map(item => {
          listInfo.push(item.Value);
          this.listQuery.TreeReqList.push({
            Level: item.Level,
            Value: item.Value
          })
        });
      }
      this.getList()
    }
  }
};
</script>
<style scoped lang="scss">

.divLayout {
  display: flex;
  // border:1px solid #631010;
  height: 96%;
}

.divLine {
  position: absolute;
  left: 22%;
  height: calc(100vh - 90px);
  // width:4px;
  overflow: hidden;
  // background:red;
  cursor: w-resize;
  border-right: 4px dotted #F6F7F7;
}

.listTreeDiv {
  border: 1px solid #F6F7F7;
  width: 25%;
  padding-top: 10px;
  height: calc(100vh - 90px);
  overflow: auto;
  scrollbar-width: auto;
}

.app-container {
  padding: 10px;
  width: 78%;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}
</style>
