<template>
  <div ref="divLayout" class="divLayout">
    <div ref="divTree" v-loading="isProcessing" class="listTreeDiv" element-loading-text="正在加载中...">
      <div style="font-size:12px; color:#999999; margin-left:23px;">总分录行:{{ SumCount }}</div>
      <el-tree
        v-if="treeShow"
        ref="listTree"
        :key="tree.key"
        :props="defaultProps"
        :load="loadNode"
        node-key="Value"
        lazy
        show-checkbox
        highlight-current
        @check="onCheckNode"
      />
    </div>
    <div ref="devLine" class="divLine" @mousedown="reactDiv" />
    <div ref="divApp" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
      <!--检索区域-->
      <div class="filter-container">
        <el-form label-width="80px" class="search" inline>
          <el-form-item label="装配日期">
            <el-date-picker
              v-model="listQuery.PlanAssemblyDate"
              :clearable="true"
              size="small"
              class="filter-item"
              type="daterange"
              style="width: 220px"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
          <el-form-item label="关键字">
            <el-input
              v-model="listQuery.keyword"
              size="small"
              class="filter-item"
              :placeholder="$t('Common.keyword')"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户">
            <el-input
              v-model="listQuery.Customer"
              size="small"
              class="filter-item"
              placeholder="客户"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户订单号" label-width="100px">
            <el-input
              v-model="listQuery.CustomerOrderNo"
              size="small"
              class="filter-item"
              placeholder="订单号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="合同号">
            <el-input
              v-model="listQuery.ContractNo"
              size="small"
              class="filter-item"
              placeholder="合同号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="销售单号">
            <el-input
              v-model="listQuery.SaleSapNo"
              size="small"
              class="filter-item"
              placeholder="销售单号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="销售单行">
            <el-input
              v-model="listQuery.SaleSapLine"
              size="small"
              class="filter-item"
              placeholder="销售单行"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="计划订单状态" label-width="100px">
            <el-input
              v-model="listQuery.PlanStatus"
              size="small"
              class="filter-item"
              placeholder="计划订单状态"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="采购申请状态" label-width="100px">
            <el-input
              v-model="listQuery.PurchaseApplyStatus"
              size="small"
              class="filter-item"
              placeholder="采购申请状态"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
        </el-form>
        <hr>
        <el-button v-permission="{ name: 'SD.SD_ShippingPlan.Export' }" v-waves size="small" :disabled="multipleSelection.length === 0" class="filter-item" type="primary" @click="setProduceSchedulingDate">更新装配时间
        </el-button>
        <el-button v-permission="{ name: 'SD.SD_ShippingPlan.Export' }" v-waves size="small" :disabled="multipleSelection.length === 0" class="filter-item" type="primary" @click="setProduceSchedulingLine">更新生产线体
        </el-button>
        <el-button v-permission="{ name: 'SD.SD_ShippingPlan.Export' }" v-waves size="small" :disabled="multipleSelection.length === 0" class="filter-item" type="primary" @click="completePreProduce">完成预排产
        </el-button>
        <el-dropdown
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          size="small"
          style="margin-left: 10px"
          type="primary"
          @command="handleExport"
        >
          <el-button type="primary" size="small">
            导出<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu>
            <el-dropdown-item command="1">导出全部</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <div>
        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          highlight-current-row
          style="width: 100%"
          :height="tableHeight"
          size="mini"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
          <el-table-column label="生产序号" prop="ProduceSort" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单类型" prop="OrderType" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ getOrderTypeName(scope.row.OrderType) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单号" prop="CustomerOrderNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户名称" prop="CustomerName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="型号" prop="ItemName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户名称" prop="CustomerName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户件号" prop="CustomerPart" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="数量" prop="Quantity" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SaleSapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SaleSapLine" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单状态" prop="Status" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="生产线" prop="ProduceLine" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="计划装配日期" prop="PlanAssemblyDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="入库时间" prop="InStoreTime" align="center" width="120" show-overflow-tooltip :formatter="formatDateTime" />
          <el-table-column label="交货日期" prop="DeliveryDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="排产日期" prop="ProduceSchedulingDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="催货日期" prop="ExpeditingDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="批次号" prop="BatchNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="顺序号" prop="SequenceNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="编号" prop="SerialNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
          <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="150" :formatter="formatDateTime" />
          <el-table-column label="计划状态" prop="PlanStatus" width="80" fixed="right" />
          <el-table-column label="采购申请" prop="PurchaseApplyStatus" width="80" fixed="right" />
          <el-table-column label="操作" align="center" width="100" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.PlanMsg || scope.row.PurchaseApplyMsg"
                size="mini"
                type="text"
                style="color: #E6A23C;"
                @click.stop="showErrorMessages(scope.row)"
              >错误查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.PageNumber"
          :limit.sync="listQuery.PageSize"
          @pagination="getList"
        />
      </div>

      <!-- 装配日期设置 -->
      <el-dialog title="装配日期设置" :visible.sync="dialogSetProduceSchedulingDateVisible" width="30%" :close-on-click-modal="false">
        <div>
          <el-form label-position="right" label-width="90px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="装配日期" style="width:93%">
                  <el-date-picker
                    v-model="produceSchedulingDate"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm"
                    style="width: 100%;"
                    placeholder="装配日期"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogSetProduceSchedulingDateVisible = !dialogSetProduceSchedulingDateVisible">取消</el-button>
            <el-button type="primary" @click="onClickConfirmSetProduceSchedulingDate">设定装配日期</el-button>
          </div>
        </div>
      </el-dialog>

      <!-- 更新生产线体 -->
      <el-dialog title="更新生产线体" :visible.sync="dialogSetProduceSchedulingLineVisible" width="30%" :close-on-click-modal="false">
        <div>
          <el-form label-position="right" label-width="90px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="生产线体" style="width:93%">
                  <el-select
                    v-model="produceLine"
                    size="small"
                    filterable
                    placeholder="全部线体"
                    style="width: 140px"
                    class="filter-item"
                  >
                    <el-option v-for="item in options" :key="item.MATNR" :label="item.KTEXT" :value="item.VERID + '&' + item.MATNR + '&' + item.KTEXT" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogSetProduceSchedulingLineVisible = !dialogSetProduceSchedulingLineVisible">取消</el-button>
            <el-button type="primary" @click="onClickConfirmSetProduceLine">设定生产线</el-button>
          </div>
        </div>
      </el-dialog>

      <!-- 完成预排产 -->
      <el-dialog title="完成预排产" :visible.sync="dialogCompletePreProduceVisible" width="30%" :close-on-click-modal="false">
        <div>
          <el-form label-position="right" label-width="90px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="计划时间" style="width:93%">
                  <el-date-picker
                    v-model="planTime"
                    type="date"
                    value-format="yyyy-MM-dd HH:mm"
                    style="width: 100%;"
                    placeholder="计划时间"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogCompletePreProduceVisible = !dialogCompletePreProduceVisible">取消</el-button>
            <el-button type="primary" @click="confirmCompletePreProduce">提交并确认</el-button>
          </div>
        </div>
      </el-dialog>

      <!-- 错误信息查看 -->
      <el-dialog title="错误信息查看" :visible.sync="dialogErrorMessagesVisible" width="50%" :close-on-click-modal="false">
        <div>
          <el-form label-position="right" label-width="120px">
            <el-row v-if="currentErrorMessages.PlanMsg">
              <el-col :span="24">
                <el-form-item label="计划订单消息">
                  <el-input
                    v-model="currentErrorMessages.PlanMsg"
                    type="textarea"
                    :rows="4"
                    readonly
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="currentErrorMessages.PurchaseApplyMsg">
              <el-col :span="24">
                <el-form-item label="采购申请消息">
                  <el-input
                    v-model="currentErrorMessages.PurchaseApplyMsg"
                    type="textarea"
                    :rows="4"
                    readonly
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button type="primary" @click="dialogErrorMessagesVisible = false">关闭</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>

</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  GetPageList,
  setProduceSchedulingDate,
  setProduceLine,
  GetTree,
  completePreProduce,
  getProduceVersionList
} from '@/api/Produce/Produce_Scheduling';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import { OrderTypeList } from '@/utils/constants';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

export default {
  name: 'SD.SD_ShippingPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      treeShow: [],
      options: [],
      OrderTypeList,
      tree: {
        treeNode: null,
        treeResolve: null,
        key: new Date().getTime()
      },
      defaultProps: {
        children: 'Children',
        label: 'Label',
        isLeaf: 'Leaf'
      },
      SumCount: 0,
      total: 0,
      tableHeight: 600,
      produceSchedulingDate: null,
      planTime: null,
      produceLine: null,
      isProcessing: false,
      dialogSetProduceSchedulingDateVisible: false,
      dialogSetProduceSchedulingLineVisible: false,
      dialogCompletePreProduceVisible: false,
      dialogErrorMessagesVisible: false,
      currentErrorMessages: {
        PlanMsg: '',
        PurchaseApplyMsg: ''
      },
      listLoading: true,
      listDetailLoading: false,
      setDeliveryDate: [],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        PlanAssemblyDate: [
        ],
        Status: 1,
        ContractNo: '',
        SaleSapNo: '',
        SaleSapLine: '',
        IsDownload: '',
        Customer: '',
        CustomerOrderNo: '',
        PurchaseApplyStatus: '',
        PlanStatus: '',
        IsDeliveryImport: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImportVisible: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ShippingPlan') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.listQuery.Level = 0;
      this.listQuery.TreeReqList = [];
      this.getList();
      this.reRenderTree()
    },
    getList() {
      this.listLoading = true;
      if (this.listQuery.CTme) {
        this.listQuery.CTme[0] = this.$moment(this.listQuery.CTme[0]).format('YYYY-MM-DD');
        this.listQuery.CTme[1] = this.$moment(this.listQuery.CTme[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    reRenderTree() {
      this.treeShow = false;
      this.isProcessing = true;
      this.$nextTick(() => {
        this.treeShow = true;
        setTimeout(() => {
          this.isProcessing = false;
        }, 300)
      });
    },
    onClickConfirmSetProduceSchedulingDate() {
      this.$confirm(
        '此操作将批量更新装配日期, 是否继续?',
        '更新确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        setProduceSchedulingDate({ Ids: this.multipleSelection.map(t => t.Id), ProduceSchedulingDate: this.produceSchedulingDate }).then(res => {
          this.isProcessing = false;
          this.dialogSetProduceSchedulingDateVisible = false;
          if (res.Code === 2000) {
            this.$msgbox({
              title: '成功',
              message: res.Message || '更新成功',
              type: 'success',
              showCancelButton: false,
              confirmButtonText: '确定'
            });
          } else {
            this.$msgbox({
              title: '错误',
              message: res.Message,
              type: 'error',
              showCancelButton: false,
              confirmButtonText: '确定'
            });
          }
          this.handleFilter();
        }).catch(error => {
          console.error('设置排产日期失败:', error);
          this.dialogSetProduceSchedulingDateVisible = false;
          this.isProcessing = false;
        });
      });
    },
    onClickConfirmSetProduceLine() {
      this.$confirm(
        '此操作将批量更新排产线体, 是否继续?',
        '更新确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        setProduceLine({ Ids: this.multipleSelection.map(t => t.Id), ProduceLine: this.produceLine }).then(res => {
          this.isProcessing = false;
          this.dialogSetProduceSchedulingLineVisible = false;
          if (res.Code === 2000) {
            this.showNotify('success', '更新成功');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        }).catch(error => {
          console.error('设置排产线体失败:', error);
          this.dialogSetProduceSchedulingLineVisible = false;
          this.isProcessing = false;
        });
      });
    },
    setProduceSchedulingDate() {
      if (this.multipleSelection.length === 0) {
        this.showNotify('warning', '请选择要设置发运日期的订单');
        return
      }
      this.dialogSetProduceSchedulingDateVisible = true;
    },
    setProduceSchedulingLine() {
      if (this.multipleSelection.length === 0 && this.multipleSelection.length > 1) {
        this.showNotify('warning', '请选择一条设置发运日期的订单');
        return
      }
      getProduceVersionList({ MaterialCode: this.multipleSelection[0].ItemCode }).then(res => {
        this.options = res.Data
      }).catch(error => {
        console.error('获取排产版本列表失败:', error);
      });
      this.dialogSetProduceSchedulingLineVisible = true;
    },
    completePreProduce() {
      if (this.multipleSelection.length === 0) {
        this.showNotify('warning', '请选择要设置发运日期的订单');
        return
      }
      this.dialogCompletePreProduceVisible = true;
    },
    confirmCompletePreProduce() {
      this.isProcessing = true;
      completePreProduce({ Ids: this.multipleSelection.map(t => t.Id), PlanTime: this.planTime }).then(res => {
        this.isProcessing = false;
        this.dialogCompletePreProduceVisible = false;
        if (res.Code === 2000) {
          this.$msgbox({
            title: '预排产完成',
            message: res.Message,
            dangerouslyUseHTMLString: true,
            customClass: 'alertDiv',
            showCancelButton: false,
            confirmButtonText: '好的',
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: true
          });
        } else {
          this.showNotify('error', res.Message);
        }
        this.handleFilter();
      }).catch(error => {
        console.error('完成预排产失败:', error);
        this.dialogCompletePreProduceVisible = false;
        this.isProcessing = false;
      });
    },
    // 导出
    handleExport(command) {
      this.isProcessing = true;
      let title = '';
      const date = this.$moment(new Date()).format('YYYY-MM-DD');
      if (command === '1') {
        title = date + '发运计划';
      } else if (command === '2') {
        title = date + '发运计划-按物流供应商导出';
      }
      if (command === '3') {
        title = date + '发运计划-按结算地址导出';
      }
      this.listQuery.type = command;
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res.data, title);
        this.handleFilter();
        this.isProcessing = false;
      }).catch(error => {
        console.error('导出失败:', error);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
    },
    showErrorMessages(row) {
      this.currentErrorMessages = {
        PlanMsg: row.PlanMsg || '',
        PurchaseApplyMsg: row.PurchaseApplyMsg || ''
      };
      this.dialogErrorMessagesVisible = true;
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      }).catch(error => {
        console.error('获取列表详情失败:', error);
        this.listDetailLoading = false;
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    reactDiv() {
      var oBox = this.$refs.divLayout;
      var oTop = this.$refs.divTree;
      var oBottom = this.$refs.divApp;
      var oLine = this.$refs.devLine;
      var disX;
      oLine.onmousedown = function(e) {
        disX = (e || event).clientX;
        oLine.left = oLine.offsetLeft;
      }
      document.onmousemove = function(e) {
        var iT = oLine.left + ((e || event).clientX - disX);
        var maxT = oBox.clientWight - oLine.offsetWidth;
        oLine.style.margin = 0;
        iT < 0 && (iT = 0);
        iT > maxT && (iT = maxT);
        oLine.style.left = oTop.style.width = iT + 'px';
        oBottom.style.width = oBox.clientWidth - iT + 'px';
        return false
      }
      document.onmouseup = function() {
        document.onmousemove = null;
        document.onmouseup = null;
        oLine.releaseCapture && oLine.releaseCapture();
      }
      oLine.setCapture && oLine.setCapture();
      return false
    },
    loadNode(node, resolve) {
      console.log('node', node, 'resolve', resolve)
      this.tree.treeNode = node;
      this.tree.treeResolve = resolve;
      if (node.level === 0) {
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 1) {
        this.listQuery.Level = 1
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 2) {
        this.listQuery.Level = 2
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 3) {
        this.listQuery.Level = 3
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      return resolve([]);
    },
    // 树节点选择改变
    onCheckNode(data, event) {
      this.listQuery.TreeReqList = []
      const checkNodes = this.$refs['listTree'].getCheckedNodes(false, false);
      if (checkNodes.length > 0) {
        const listInfo = [];
        checkNodes.map(item => {
          listInfo.push(item.Value);
          this.listQuery.TreeReqList.push({
            Level: item.Level,
            Value: item.Value
          })
        });
      }
      this.getList()
    },
    getOrderTypeName(orderType) {
      const orderTypeItem = this.OrderTypeList.find(item => item.key === orderType);
      return orderTypeItem ? orderTypeItem.label : orderType;
    }
  }
};
</script>
<style scoped lang="scss">

.divLayout {
  display: flex;
  // border:1px solid #631010;
  height: 96%;
}

.divLine {
  position: absolute;
  left: 22%;
  height: calc(100vh - 90px);
  // width:4px;
  overflow: hidden;
  // background:red;
  cursor: w-resize;
  border-right: 4px dotted #F6F7F7;
}

.listTreeDiv {
  border: 1px solid #F6F7F7;
  width: 25%;
  padding-top: 10px;
  height: calc(100vh - 90px);
  overflow: auto;
  scrollbar-width: auto;
}

.app-container {
  padding: 10px;
  width: 78%;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}
</style>
