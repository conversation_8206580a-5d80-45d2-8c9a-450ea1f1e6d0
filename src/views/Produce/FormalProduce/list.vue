<template>
  <div ref="divLayout" class="divLayout">
    <div ref="divTree" v-loading="isProcessing" class="listTreeDiv" element-loading-text="正在加载中...">
      <div style="font-size:12px; color:#999999; margin-left:23px;">总分录行:{{ SumCount }}</div>
      <el-tree
        v-if="treeShow"
        ref="listTree"
        :key="tree.key"
        :props="defaultProps"
        :load="loadNode"
        node-key="Value"
        lazy
        show-checkbox
        highlight-current
        @check="onCheckNode"
      />
    </div>
    <div ref="devLine" class="divLine" @mousedown="reactDiv" />
    <div ref="divApp" v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
      <!--检索区域-->
      <div class="filter-container">
        <el-form label-width="80px" class="search" inline>
          <el-form-item label="装配日期">
            <el-date-picker
              v-model="listQuery.PlanAssemblyDate"
              :clearable="true"
              size="mini"
              class="filter-item"
              type="daterange"
              style="width: 220px"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
          <el-form-item label="关键字">
            <el-input
              v-model="listQuery.keyword"
              size="mini"
              class="filter-item"
              :placeholder="$t('Common.keyword')"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户">
            <el-input
              v-model="listQuery.Customer"
              size="mini"
              class="filter-item"
              placeholder="客户"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户订单号" label-width="100px">
            <el-input
              v-model="listQuery.CustomerOrderNum"
              size="mini"
              class="filter-item"
              placeholder="订单号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="合同号">
            <el-input
              v-model="listQuery.ContractNo"
              size="mini"
              class="filter-item"
              placeholder="合同号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="销售单号">
            <el-input
              v-model="listQuery.SaleSapNo"
              size="mini"
              class="filter-item"
              placeholder="合同号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="计划订单状态" label-width="100px">
            <el-input
              v-model="listQuery.ProduceStatus"
              size="mini"
              class="filter-item"
              placeholder="计划订单状态"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="采购申请状态" label-width="100px">
            <el-input
              v-model="listQuery.PurchaseStatus"
              size="mini"
              class="filter-item"
              placeholder="采购申请状态"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-button v-waves size="mini" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
        </el-form>
        <hr>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          type="primary"
          :disabled="multipleSelection.length !== 1"
          size="mini"
          @click="SetSortAndBatch"
        >设置序号&批次</el-button>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          type="success"
          :disabled="multipleSelection.length === 0"
          size="mini"
          @click="confirmFormalProduce"
        >确认正式排产</el-button>
        <el-button
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          type="success"
          size="mini"
          @click="batchUpdate"
        >批量更新序号批次</el-button>
        <el-button v-permission="{ name: 'SD.SD_ShippingPlan.Export' }" v-waves size="mini" :disabled="multipleSelection.length === 0" class="filter-item" type="primary" @click="setProduceSchedulingDate">更新装配时间
        </el-button>
        <el-button v-permission="{ name: 'SD.SD_ShippingPlan.Export' }" v-waves size="mini" :disabled="multipleSelection.length === 0" class="filter-item" type="primary" @click="setProduceSchedulingLine">更新生产线体
        </el-button>
        <el-dropdown
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          size="mini"
          style="margin-left: 10px"
          type="primary"
          @command="handleExport"
        >
          <el-button type="primary" size="mini">
            导出<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu>
            <el-dropdown-item command="1">导出更新模板</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <div>
        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          highlight-current-row
          style="width: 100%"
          :height="tableHeight"
          size="mini"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
          <el-table-column label="生产序号" prop="ProduceSort" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单类型" prop="OrderType" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ getOrderTypeName(scope.row.OrderType) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单号" prop="CustomerOrderNum" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户名称" prop="CustomerName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="型号" prop="ItemName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户件号" prop="CustomerPart" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户型号" prop="CustomerModel" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="数量" prop="Quantity" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SaleSapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SaleSapLine" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单状态" prop="Status" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="生产线" prop="ProduceLine" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="计划装配日期" prop="PlanAssemblyDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="入库时间" prop="InStoreTime" align="center" width="120" show-overflow-tooltip :formatter="formatDateTime" />
          <el-table-column label="交货日期" prop="DeliveryDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="排产日期" prop="ProduceSchedulingDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="催货日期" prop="ExpeditingDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="顺序号" prop="ProduceSort" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="批次号" prop="ProduceBatch" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="编号" prop="SerialNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
          <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="150" :formatter="formatDateTime" />
          <el-table-column label="生产状态" prop="ProduceStatus" width="80" fixed="right" />
          <el-table-column label="采购订单" prop="PurchaseStatus" width="80" fixed="right" />
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                style="color: #409EFF;"
                @click.stop="showProductionDetail(scope.row)"
              >生产明细</el-button>
              <!--              <el-button-->
              <!--                size="mini"-->
              <!--                type="text"-->
              <!--                style="color: #67C23A;"-->
              <!--                @click.stop="showPurchaseDetail(scope.row)"-->
              <!--              >采购明细</el-button>-->
              <el-button
                v-if="scope.row.ProduceMsg || scope.row.PurchaseMsg"
                size="mini"
                type="text"
                style="color: #E6A23C;"
                @click.stop="showErrorMessages(scope.row)"
              >错误查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.PageNumber"
          :limit.sync="listQuery.PageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 设置交货 -->
    <el-dialog title="设置序号&批次" :visible.sync="dialogSetSortAndBatchVisible" width="30%" :close-on-click-modal="false">
      <div>
        <el-form ref="setDelivery" label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="合同号" prop="Contract" style="width:93%">
                <span>{{ form.Contract }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="序号" prop="ProduceSort" style="width:93%">
                <el-input
                  v-model="form.ProduceSort"
                  size="mini"
                  class="filter-item"
                  placeholder="序号"
                  style="width: 140px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="生产批次" prop="ProduceBatch" style="width:93%">
                <el-input
                  v-model="form.ProduceBatch"
                  size="mini"
                  class="filter-item"
                  placeholder="生产批次"
                  style="width: 140px"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogSetSortAndBatchVisible = !dialogSetSortAndBatchVisible">取消</el-button>
          <el-button type="primary" @click="confirmSetSortAndBatch">确定</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog :title="importTitle" :visible.sync="dialogImportVisible" width="50%" :close-on-click-modal="false">
      <div>
        <el-row style="margin-bottom:0;">
          <el-col :span="24">
            <el-upload
              ref="upload"
              class="upload-demo"
              action=""
              :on-change="handleChange"
              :on-remove="handleRemove"
              :on-exceed="handleExceed"
              :limit="1"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel,application/vnd.ms-excel.sheet.macroEnabled.12"
              :auto-upload="false"
              width="50px"
            >
              <el-button size="mini" type="primary">选择文件</el-button>
              <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
            </el-upload>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 错误信息查看 -->
    <el-dialog title="错误信息查看" :visible.sync="dialogErrorMessagesVisible" width="50%" :close-on-click-modal="false">
      <div>
        <el-form label-position="right" label-width="120px">
          <el-row v-if="currentErrorMessages.ProduceMsg">
            <el-col :span="24">
              <el-form-item label="生产订单消息">
                <el-input
                  v-model="currentErrorMessages.ProduceMsg"
                  type="textarea"
                  :rows="4"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="currentErrorMessages.PurchaseMsg">
            <el-col :span="24">
              <el-form-item label="采购订单消息">
                <el-input
                  v-model="currentErrorMessages.PurchaseMsg"
                  type="textarea"
                  :rows="4"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button type="primary" @click="dialogErrorMessagesVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 生产明细对话框 -->
    <el-dialog title="生产明细" :visible.sync="dialogDetailVisible" width="70%" :close-on-click-modal="false">
      <div v-loading="detailLoading">
        <el-table
          :data="detailList"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          style="width: 100%"
          max-height="400"
          size="mini"
        >
          <el-table-column label="物料编码" prop="MaterialCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="生产订单号" prop="ProduceOrderNo" align="center" width="150" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SapLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="状态" prop="Status" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="消息" prop="Massage" align="center" show-overflow-tooltip />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button type="primary" @click="dialogDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 采购明细对话框 -->
    <el-dialog title="采购明细" :visible.sync="dialogPurchaseDetailVisible" width="70%" :close-on-click-modal="false">
      <div v-loading="purchaseDetailLoading">
        <el-table
          :data="purchaseDetailList"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          style="width: 100%"
          max-height="400"
          size="mini"
        >
          <el-table-column label="物料编码" prop="MaterialCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="采购订单号" prop="PurchaseOrderNo" align="center" width="150" show-overflow-tooltip />
          <el-table-column label="采购订单行" prop="PurchaseOrderLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="数量" prop="Quantity" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SapLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="状态" prop="Status" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="消息" prop="Massage" align="center" show-overflow-tooltip />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button type="primary" @click="dialogPurchaseDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 装配日期设置 -->
    <el-dialog title="装配日期设置" :visible.sync="dialogSetProduceSchedulingDateVisible" width="30%" :close-on-click-modal="false">
      <div>
        <el-form label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="装配日期" style="width:93%">
                <el-date-picker
                  v-model="produceSchedulingDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm"
                  style="width: 100%;"
                  placeholder="装配日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogSetProduceSchedulingDateVisible = !dialogSetProduceSchedulingDateVisible">取消</el-button>
          <el-button type="primary" @click="onClickConfirmSetProduceSchedulingDate">设定装配日期</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 更新生产线体 -->
    <el-dialog title="更新生产线体" :visible.sync="dialogSetProduceSchedulingLineVisible" width="30%" :close-on-click-modal="false">
      <div>
        <el-form label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="生产线体" style="width:93%">
                <el-select
                  v-model="produceLine"
                  size="mini"
                  filterable
                  placeholder="全部线体"
                  style="width: 140px"
                  class="filter-item"
                >
                  <el-option v-for="item in options" :key="item.MATNR" :label="item.KTEXT" :value="item.VERID + '&' + item.MATNR + '&' + item.KTEXT" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogSetProduceSchedulingLineVisible = !dialogSetProduceSchedulingLineVisible">取消</el-button>
          <el-button type="primary" @click="onClickConfirmSetProduceLine">设定生产线</el-button>
        </div>
      </div>
    </el-dialog>

  </div>

</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  GetPageList,
  SetSortAndBatch,
  GetTree, ConfirmFormalProduce, exportUpdateTemp,
  GetDetailsByPid,
  GetPurchaseDetailsByPid,
  setProduceSchedulingDate,
  setProduceLine,
  getProduceVersionList
} from '@/api/Produce/Produce_Scheduling';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import { OrderTypeList } from '@/utils/constants';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

export default {
  name: 'SD.SD_ShippingPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      treeShow: [],
      options: [],
      OrderTypeList,
      tree: {
        treeNode: null,
        treeResolve: null,
        key: new Date().getTime()
      },
      defaultProps: {
        children: 'Children',
        label: 'Label',
        isLeaf: 'Leaf'
      },
      syncDeliveryReq: {
        WhsCode: '',
        Ids: ''
      },
      form: {
        Id: null,
        Contract: null,
        ProduceSort: null,
        ProduceBatch: null
      },
      dialogSetSortAndBatchVisible: false,
      dialogImportVisible: false,
      dialogErrorMessagesVisible: false,
      dialogSetProduceSchedulingDateVisible: false,
      dialogSetProduceSchedulingLineVisible: false,
      currentErrorMessages: {
        ProduceMsg: '',
        PurchaseMsg: ''
      },
      importTitle: '',
      SumCount: 0,
      total: 0,
      tableHeight: 600,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      setDeliveryDate: [],
      importParams: [],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        PlanAssemblyDate: [
        ],
        Status: 2,
        ContractNo: '',
        IsDownload: '',
        Customer: '',
        CustomerOrderNum: '',
        ProduceStatus: '',
        PurchaseStatus: '',
        IsDeliveryImport: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      fileTemp: null,
      uploadExcelData: [],
      dialogDetailVisible: false,
      detailLoading: false,
      detailList: [],
      currentDetailRow: null,
      dialogPurchaseDetailVisible: false,
      purchaseDetailLoading: false,
      purchaseDetailList: [],
      produceSchedulingDate: null,
      produceLine: null
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    selectAble() {
      return this.multipleSelection.length !== 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ShippingPlan') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        console.log(this.list);
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    SetSortAndBatch() {
      if (this.multipleSelection.length !== 1) {
        this.showNotify('error', '请选择一条数据进行设置');
        return;
      }
      this.form.Id = this.multipleSelection[0].Id;
      this.form.ProduceBatch = this.multipleSelection[0].ProduceBatch;
      this.form.ProduceSort = this.multipleSelection[0].ProduceSort
      this.form.Contract = this.multipleSelection[0].ContractNo
      this.dialogSetSortAndBatchVisible = true;
    },
    confirmFormalProduce() {
      this.$confirm(
        '此操作将确认正式排产,确认后将无法线上调整批次及序号, 是否继续?',
        '同步确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        ConfirmFormalProduce(this.multipleSelection.map(t => t.Id)).then(res => {
          this.isProcessing = false;
          if (res.Code === 2000) {
            this.$msgbox({
              title: '正式排产完成',
              message: res.Message,
              dangerouslyUseHTMLString: true,
              customClass: 'alertDiv',
              showCancelButton: false,
              confirmButtonText: '好的',
              closeOnClickModal: false,
              closeOnPressEscape: false,
              showClose: true
            });
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        }).catch(error => {
          this.isProcessing = false;
        });
      });
    },
    batchUpdate() {
      this.importTitle = '批量更新序号&批次'
      this.dialogImportVisible = true;
    },
    confirmSetSortAndBatch() {
      this.isProcessing = true;
      const reqParams = [];
      reqParams.push(this.form)
      SetSortAndBatch(reqParams).then(res => {
        this.isProcessing = false;
        this.dialogSetSortAndBatchVisible = !this.dialogSetSortAndBatchVisible;
        if (res.Code === 2000) {
          this.showNotify('success', '设置成功');
        } else {
          this.showNotify('error', res.Message);
        }
        this.handleFilter();
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.listQuery.Level = 0;
      this.listQuery.TreeReqList = [];
      this.getList();
      this.reRenderTree()
    },
    reRenderTree() {
      this.treeShow = false;
      this.isProcessing = true;
      this.$nextTick(() => {
        this.treeShow = true;
        setTimeout(() => {
          this.isProcessing = false;
        }, 300)
      });
    },
    // 导出
    handleExport(command) {
      this.isProcessing = true;
      let title = '';
      const date = this.$moment(new Date()).format('YYYY-MM-DD');
      if (command === '1') {
        title = date + '排产更新模板';
      }
      this.listQuery.type = command;
      exportUpdateTemp(this.multipleSelection.map(t => t.Id)).then(res => {
        exportToExcel(res.data, title);
        this.handleFilter();
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
    },
    showErrorMessages(row) {
      this.currentErrorMessages = {
        ProduceMsg: row.ProduceMsg || '',
        PurchaseMsg: row.PurchaseMsg || ''
      };
      this.dialogErrorMessagesVisible = true;
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    // 导入上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      this.fullscreenLoading = true;
      if (this.fileTemp) {
        if (this.fileTemp.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type === 'application/vnd.ms-excel' ||
            this.fileTemp.type === 'application/vnd.ms-excel.sheet.macroEnabled.12') {
          importExcel(this, this.getImportData);
        } else {
          this.$alert('附件格式错误，请删除后重新上传！', this.importTitle, {
            confirmButtonText: '好的',
            type: 'warning'
          }).then((rst) => {
            this.fullscreenLoading = false;
          });
        }
      } else {
        this.$alert('请上传附件！', this.importTitle, {
          confirmButtonText: '好的',
          type: 'warning'
        }).then((rst) => {
          this.fullscreenLoading = false;
        });
      }
    },
    // 导入上传文件个数限制
    handleExceed() {
      this.$alert('每次只可选择一个文件进行操作！', this.importTitle, {
        confirmButtonText: '确定',
        type: 'warning'
      });
    },
    // 导入移除上传文件
    handleRemove(file, fileList) {
      this.$alert('确认移除上传的文件吗？', this.importTitle, {
        confirmButtonText: '是的'
      }).then((t) => {
        this.fileTemp = null;
        const _this = this;
        _this.uploadExcelData = [];
      });
    },
    // 导入回调：excel表转换list
    getImportData(data) {
      for (let i = 0;i < data.length;i++) {
        const x = data[i];
        this.importParams.push({
          Id: x['ID'],
          ContractNo: x['合同号'],
          ProduceSort: x['生产序号'],
          ProduceBatch: x['生产批次']
        });
      }
      SetSortAndBatch(this.importParams).then((response) => {
        if (response.Message === '') {
          this.getList();
          this.$alert('导入数据保存完成！', this.twoImportTitle, {
            confirmButtonText: '确定',
            type: 'info'
          }).then(() => {
            this.importParams = [];
            this.dialogImportVisible = false;
            this.fullscreenLoading = false;
          });
        } else {
          this.$alert(response.Message, this.twoImportTitle, {
            confirmButtonText: '确定',
            type: 'warning'
          }).then(() => {
            this.fullscreenLoading = false;
          });
        }
      });
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    reactDiv() {
      var oBox = this.$refs.divLayout;
      var oTop = this.$refs.divTree;
      var oBottom = this.$refs.divApp;
      var oLine = this.$refs.devLine;
      var disX;
      oLine.onmousedown = function(e) {
        disX = (e || event).clientX;
        oLine.left = oLine.offsetLeft;
      }
      document.onmousemove = function(e) {
        var iT = oLine.left + ((e || event).clientX - disX);
        var maxT = oBox.clientWight - oLine.offsetWidth;
        oLine.style.margin = 0;
        iT < 0 && (iT = 0);
        iT > maxT && (iT = maxT);
        oLine.style.left = oTop.style.width = iT + 'px';
        oBottom.style.width = oBox.clientWidth - iT + 'px';
        return false
      }
      document.onmouseup = function() {
        document.onmousemove = null;
        document.onmouseup = null;
        oLine.releaseCapture && oLine.releaseCapture();
      }
      oLine.setCapture && oLine.setCapture();
      return false
    },
    loadNode(node, resolve) {
      console.log('node', node, 'resolve', resolve)
      this.tree.treeNode = node;
      this.tree.treeResolve = resolve;
      if (node.level === 0) {
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 1) {
        this.listQuery.Level = 1
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 2) {
        this.listQuery.Level = 2
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 3) {
        this.listQuery.Level = 3
        this.listQuery.Value = node.data.Value
        GetTree(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      return resolve([]);
    },
    // 树节点选择改变
    onCheckNode(data, event) {
      this.listQuery.TreeReqList = []
      const checkNodes = this.$refs['listTree'].getCheckedNodes(false, false);
      if (checkNodes.length > 0) {
        const listInfo = [];
        checkNodes.map(item => {
          listInfo.push(item.Value);
          this.listQuery.TreeReqList.push({
            Level: item.Level,
            Value: item.Value
          })
        });
      }
      this.getList()
    },
    getOrderTypeName(orderType) {
      const orderTypeItem = this.OrderTypeList.find(item => item.key === orderType);
      return orderTypeItem ? orderTypeItem.label : orderType;
    },
    showProductionDetail(row) {
      this.currentDetailRow = row;
      this.dialogDetailVisible = true;
      this.detailLoading = true;
      this.detailList = [];

      // 使用当前行的Id作为Pid查询明细
      GetDetailsByPid(row.Id).then(response => {
        this.detailLoading = false;
        if (response.Code === 2000) {
          this.detailList = response.Data || [];
        } else {
          this.showNotify('error', response.Message || '获取生产明细失败');
        }
      }).catch(error => {
        this.detailLoading = false;
        this.showNotify('error', '获取生产明细失败');
        console.error(error);
      });
    },
    showPurchaseDetail(row) {
      this.currentDetailRow = row;
      this.dialogPurchaseDetailVisible = true;
      this.purchaseDetailLoading = true;
      this.purchaseDetailList = [];

      // 使用当前行的Id作为Pid查询明细
      GetPurchaseDetailsByPid(row.Id).then(response => {
        this.purchaseDetailLoading = false;
        if (response.Code === 2000) {
          this.purchaseDetailList = response.Data || [];
        } else {
          this.showNotify('error', response.Message || '获取采购明细失败');
        }
      }).catch(error => {
        this.purchaseDetailLoading = false;
        this.showNotify('error', '获取采购明细失败');
        console.error(error);
      });
    },
    setProduceSchedulingDate() {
      if (this.multipleSelection.length === 0) {
        this.showNotify('warning', '请选择要设置装配时间的订单');
        return
      }
      this.dialogSetProduceSchedulingDateVisible = true;
    },
    setProduceSchedulingLine() {
      if (this.multipleSelection.length === 0) {
        this.showNotify('warning', '请选择要设置生产线体的订单');
        return
      }
      getProduceVersionList({ MaterialCode: this.multipleSelection[0].ItemCode }).then(res => {
        this.options = res.Data
      }).catch(error => {
        console.error('获取排产版本列表失败:', error);
      });
      this.dialogSetProduceSchedulingLineVisible = true;
    },
    onClickConfirmSetProduceSchedulingDate() {
      this.$confirm(
        '此操作将批量更新装配日期, 是否继续?',
        '更新确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        setProduceSchedulingDate({ Ids: this.multipleSelection.map(t => t.Id), ProduceSchedulingDate: this.produceSchedulingDate }).then(res => {
          this.isProcessing = false;
          this.dialogSetProduceSchedulingDateVisible = false;
          if (res.Code === 2000) {
            this.$msgbox({
              title: '成功',
              message: res.Message || '更新成功',
              type: 'success',
              showCancelButton: false,
              confirmButtonText: '确定'
            });
          } else {
            this.$msgbox({
              title: '错误',
              message: res.Message,
              type: 'error',
              showCancelButton: false,
              confirmButtonText: '确定'
            });
          }
          this.handleFilter();
        }).catch(error => {
          console.error('设置排产日期失败:', error);
          this.dialogSetProduceSchedulingDateVisible = false;
          this.isProcessing = false;
        });
      });
    },
    onClickConfirmSetProduceLine() {
      this.$confirm(
        '此操作将批量更新排产线体, 是否继续?',
        '更新确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        setProduceLine({ Ids: this.multipleSelection.map(t => t.Id), ProduceLine: this.produceLine }).then(res => {
          this.isProcessing = false;
          this.dialogSetProduceSchedulingLineVisible = false;
          if (res.Code === 2000) {
            this.showNotify('success', '更新成功');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        }).catch(error => {
          console.error('设置排产线体失败:', error);
          this.dialogSetProduceSchedulingLineVisible = false;
          this.isProcessing = false;
        });
      });
    }
  }
};
</script>
<style scoped lang="scss">

.divLayout {
  display: flex;
  // border:1px solid #631010;
  height: 96%;
}

.divLine {
  position: absolute;
  left: 22%;
  height: calc(100vh - 90px);
  // width:4px;
  overflow: hidden;
  // background:red;
  cursor: w-resize;
  border-right: 4px dotted #F6F7F7;
}

.listTreeDiv {
  border: 1px solid #F6F7F7;
  width: 25%;
  padding-top: 10px;
  height: calc(100vh - 90px);
  overflow: auto;
  scrollbar-width: auto;
}

.app-container {
  padding: 10px;
  width: 78%;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}
</style>
