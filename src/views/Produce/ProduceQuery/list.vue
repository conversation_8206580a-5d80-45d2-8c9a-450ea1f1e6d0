<template>
  <div ref="divLayout" class="divLayout">
    <div ref="divTree" v-loading="isProcessing" class="listTreeDiv" element-loading-text="正在加载中...">
      <div style="font-size:12px; color:#999999; margin-left:23px;">总分录行:{{ SumCount }}</div>
      <el-tree
        v-if="treeShow"
        ref="listTree"
        :key="tree.key"
        :props="defaultProps"
        :load="loadNode"
        node-key="Value"
        lazy
        show-checkbox
        highlight-current
        @check="onCheckNode"
      />
    </div>
    <div ref="devLine" class="divLine" @mousedown="reactDiv" />
    <div ref="divApp" v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
      <!--检索区域-->
      <div class="filter-container">
        <el-form label-width="80px" class="search" inline>
          <el-form-item label="装配日期">
            <el-date-picker
              v-model="listQuery.PlanAssemblyDate"
              :clearable="true"
              size="mini"
              class="filter-item"
              type="daterange"
              style="width: 220px"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
          <el-form-item label="排产时间">
            <el-date-picker
              v-model="listQuery.ProduceSchedulingDate"
              :clearable="true"
              size="mini"
              class="filter-item"
              type="daterange"
              style="width: 220px"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
          <el-form-item label="关键字">
            <el-input
              v-model="listQuery.keyword"
              size="mini"
              class="filter-item"
              :placeholder="$t('Common.keyword')"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户">
            <el-input
              v-model="listQuery.Customer"
              size="mini"
              class="filter-item"
              placeholder="客户"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="客户订单号" label-width="100px">
            <el-input
              v-model="listQuery.CustomerOrderNum"
              size="mini"
              class="filter-item"
              placeholder="订单号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="合同号">
            <el-input
              v-model="listQuery.ContractNo"
              size="mini"
              class="filter-item"
              placeholder="合同号"
              style="width: 140px"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-button v-waves size="mini" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
        </el-form>
        <hr>
        <el-dropdown
          v-waves
          v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
          class="filter-item"
          size="mini"
          style="margin-left: 10px"
          type="primary"
          @command="handleExport"
        >
          <el-button type="primary" size="mini">导出<i class="el-icon-arrow-down el-icon--right" /></el-button>
          <el-dropdown-menu>
            <el-dropdown-item command="1">导出全部</el-dropdown-item>
            <el-dropdown-item command="4">导出配送清单</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <div>
        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          highlight-current-row
          style="width: 100%"
          :height="tableHeight"
          size="mini"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
          <el-table-column label="生产序号" prop="ProduceSort" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单类型" prop="OrderType" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ getOrderTypeName(scope.row.OrderType) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单号" prop="CustomerOrderNum" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户名称" prop="CustomerName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="型号" prop="ItemName" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户件号" prop="CustomerPart" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="客户型号" prop="CustomerModel" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="数量" prop="Quantity" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SaleSapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SaleSapLine" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="订单状态" prop="Status" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="生产线" prop="ProduceLine" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="计划装配日期" prop="PlanAssemblyDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="入库时间" prop="InStoreTime" align="center" width="120" show-overflow-tooltip :formatter="formatDateTime" />
          <el-table-column label="交货日期" prop="DeliveryDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="排产日期" prop="ProduceSchedulingDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="催货日期" prop="ExpeditingDate" align="center" width="120" show-overflow-tooltip :formatter="formatDate" />
          <el-table-column label="顺序号" prop="ProduceSort" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="批次号" prop="ProduceBatch" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="编号" prop="SerialNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
          <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="150" :formatter="formatDateTime" />
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                style="color: #409EFF;"
                @click.stop="showProductionDetail(scope.row)"
              >生产明细</el-button>
              <el-button
                size="mini"
                type="text"
                style="color: #67C23A;"
                @click.stop="showPurchaseDetail(scope.row)"
              >采购明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.PageNumber"
          :limit.sync="listQuery.PageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 生产明细对话框 -->
    <el-dialog title="生产明细" :visible.sync="dialogDetailVisible" width="70%" :close-on-click-modal="false">
      <div v-loading="detailLoading">
        <el-table
          :data="detailList"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          style="width: 100%"
          max-height="400"
          size="mini"
        >
          <el-table-column label="物料编码" prop="MaterialCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="生产订单号" prop="ProduceOrderNo" align="center" width="150" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SapLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="状态" prop="Status" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="消息" prop="Massage" align="center" show-overflow-tooltip />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button type="primary" @click="dialogDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 采购明细对话框 -->
    <el-dialog title="采购明细" :visible.sync="dialogPurchaseDetailVisible" width="70%" :close-on-click-modal="false">
      <div v-loading="purchaseDetailLoading">
        <el-table
          :data="purchaseDetailList"
          border
          fit
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          style="width: 100%"
          max-height="400"
          size="mini"
        >
          <el-table-column label="物料编码" prop="MaterialCode" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="采购订单号" prop="PurchaseOrderNo" align="center" width="150" show-overflow-tooltip />
          <el-table-column label="采购订单行" prop="PurchaseOrderLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="数量" prop="Quantity" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="SAP单号" prop="SapNo" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="SAP行号" prop="SapLine" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="状态" prop="Status" align="center" width="100" show-overflow-tooltip />
          <el-table-column label="消息" prop="Massage" align="center" show-overflow-tooltip />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button type="primary" @click="dialogPurchaseDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>

</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  getTreeBySchedulingDate,
  exportExcelFile,
  GetPageList,
  GetDetailsByPid,
  GetPurchaseDetailsByPid,
  exportDeliveryList
} from '@/api/Produce/Produce_Scheduling';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import { SetShippingToDelivery } from '@/api/PP/PP_WorkshopScheduling';
import { OrderTypeList } from '@/utils/constants';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

export default {
  name: 'SD.SD_ShippingPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      treeShow: [],
      options: [],
      OrderTypeList,
      tree: {
        treeNode: null,
        treeResolve: null,
        key: new Date().getTime()
      },
      defaultProps: {
        children: 'Children',
        label: 'Label',
        isLeaf: 'Leaf'
      },
      syncDeliveryReq: {
        WhsCode: '',
        Ids: ''
      },
      dialogSetShipmentDateVisible: false,
      SumCount: 0,
      total: 0,
      tableHeight: 600,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      setDeliveryDate: [],
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        PlanAssemblyDate: [
        ],
        ProduceSchedulingDate: [
        ],
        Status: 3,
        ContractNo: '',
        IsDownload: '',
        Customer: '',
        CustomerOrderNum: '',
        PSStatus: '',
        IsDeliveryImport: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImportVisible: false,
      fileTemp: null,
      uploadExcelData: [],
      // 生产明细对话框相关变量
      dialogDetailVisible: false,
      detailList: [],
      detailLoading: false,
      currentDetailRow: null,
      // 采购明细对话框相关变量
      dialogPurchaseDetailVisible: false,
      purchaseDetailList: [],
      purchaseDetailLoading: false
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    selectAble() {
      return this.multipleSelection.length !== 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ShippingPlan') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        console.log(this.list);
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      }).catch(error => {
        this.isProcessing = false;
        this.showNotify('error', '操作失败，请重试');
        console.error(error);
      });
    },
    syncCompleted() {
      if (this.setDeliveryDate.length !== 2) {
        this.showNotify('error', '请选择日期范围');
        return;
      }
      this.$confirm(
        '此操作将同步完工的发运输入导入交货, 是否继续?',
        '同步确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        SetShippingToDelivery({ dateValue: this.setDeliveryDate }).then(res => {
          this.isProcessing = false;
          if (res.Code === 2000) {
            this.showNotify('success', '同步成功');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        }).catch(error => {
          this.isProcessing = false;
          this.showNotify('error', '操作失败，请重试');
          console.error(error);
        });
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.listQuery.Level = 0;
      this.listQuery.TreeReqList = [];
      this.getList();
      this.reRenderTree()
    },
    reRenderTree() {
      this.treeShow = false;
      this.isProcessing = true;
      this.$nextTick(() => {
        this.treeShow = true;
        setTimeout(() => {
          this.isProcessing = false;
        }, 300)
      });
    },
    // 导出
    handleExport(command) {
      this.isProcessing = true;
      let title = '';
      const date = this.$moment(new Date()).format('YYYY-MM-DD');

      if (command === '4') {
        this.listQuery.type = command;
        exportDeliveryList(this.listQuery).then(res => {
          exportToExcel(res.data, title);
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
          this.showNotify('error', '操作失败，请重试');
          console.error(error);
        });
      } else {
        // 导出发运计划 - 使用原有的API接口
        if (command === '1') {
          title = date + '发运计划';
        } else if (command === '2') {
          title = date + '发运计划-按物流供应商导出';
        } else if (command === '3') {
          title = date + '发运计划-按结算地址导出';
        }

        this.listQuery.type = command;
        exportExcelFile(this.listQuery).then(res => {
          exportToExcel(res.data, title);
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
          this.showNotify('error', '操作失败，请重试');
          console.error(error);
        });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    reactDiv() {
      var oBox = this.$refs.divLayout;
      var oTop = this.$refs.divTree;
      var oBottom = this.$refs.divApp;
      var oLine = this.$refs.devLine;
      var disX;
      oLine.onmousedown = function(e) {
        disX = (e || event).clientX;
        oLine.left = oLine.offsetLeft;
      }
      document.onmousemove = function(e) {
        var iT = oLine.left + ((e || event).clientX - disX);
        var maxT = oBox.clientWight - oLine.offsetWidth;
        oLine.style.margin = 0;
        iT < 0 && (iT = 0);
        iT > maxT && (iT = maxT);
        oLine.style.left = oTop.style.width = iT + 'px';
        oBottom.style.width = oBox.clientWidth - iT + 'px';
        return false
      }
      document.onmouseup = function() {
        document.onmousemove = null;
        document.onmouseup = null;
        oLine.releaseCapture && oLine.releaseCapture();
      }
      oLine.setCapture && oLine.setCapture();
      return false
    },
    loadNode(node, resolve) {
      console.log('node', node, 'resolve', resolve)
      this.tree.treeNode = node;
      this.tree.treeResolve = resolve;
      if (node.level === 0) {
        getTreeBySchedulingDate(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 1) {
        this.listQuery.Level = 1
        this.listQuery.Value = node.data.Value
        getTreeBySchedulingDate(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 2) {
        this.listQuery.Level = 2
        this.listQuery.Value = node.data.Value
        getTreeBySchedulingDate(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      if (node.level === 3) {
        this.listQuery.Level = 3
        this.listQuery.Value = node.data.Value
        getTreeBySchedulingDate(this.listQuery).then(response => {
          return resolve(response.Data);
        });
      }
      return resolve([]);
    },
    // 树节点选择改变
    onCheckNode(data, event) {
      this.listQuery.TreeReqList = []
      const checkNodes = this.$refs['listTree'].getCheckedNodes(false, false);
      if (checkNodes.length > 0) {
        const listInfo = [];
        checkNodes.map(item => {
          listInfo.push(item.Value);
          this.listQuery.TreeReqList.push({
            Level: item.Level,
            Value: item.Value
          })
        });
      }
      this.getList()
    },
    getOrderTypeName(orderType) {
      const orderTypeItem = this.OrderTypeList.find(item => item.key === orderType);
      return orderTypeItem ? orderTypeItem.label : orderType;
    },
    // 显示生产明细
    showProductionDetail(row) {
      this.currentDetailRow = row;
      this.dialogDetailVisible = true;
      this.detailLoading = true;
      this.detailList = [];

      // 使用当前行的Id作为Pid查询明细
      GetDetailsByPid(row.Id).then(response => {
        this.detailLoading = false;
        if (response.Code === 2000) {
          this.detailList = response.Data || [];
        } else {
          this.showNotify('error', response.Message || '获取生产明细失败');
        }
      }).catch(error => {
        this.detailLoading = false;
        this.showNotify('error', '获取生产明细失败');
        console.error(error);
      });
    },
    // 显示采购明细
    showPurchaseDetail(row) {
      this.currentDetailRow = row;
      this.dialogPurchaseDetailVisible = true;
      this.purchaseDetailLoading = true;
      this.purchaseDetailList = [];

      // 使用当前行的Id作为Pid查询明细
      GetPurchaseDetailsByPid(row.Id).then(response => {
        this.purchaseDetailLoading = false;
        if (response.Code === 2000) {
          this.purchaseDetailList = response.Data || [];
        } else {
          this.showNotify('error', response.Message || '获取采购明细失败');
        }
      }).catch(error => {
        this.purchaseDetailLoading = false;
        this.showNotify('error', '获取采购明细失败');
        console.error(error);
      });
    }
  }
};
</script>
<style scoped lang="scss">

.divLayout {
  display: flex;
  // border:1px solid #631010;
  height: 96%;
}

.divLine {
  position: absolute;
  left: 22%;
  height: calc(100vh - 90px);
  // width:4px;
  overflow: hidden;
  // background:red;
  cursor: w-resize;
  border-right: 4px dotted #F6F7F7;
}

.listTreeDiv {
  border: 1px solid #F6F7F7;
  width: 25%;
  padding-top: 10px;
  height: calc(100vh - 90px);
  overflow: auto;
  scrollbar-width: auto;
}

.app-container {
  padding: 10px;
  width: 78%;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}
</style>
