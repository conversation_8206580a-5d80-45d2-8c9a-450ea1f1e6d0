<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!-- 站点选择对话框 -->
    <el-dialog title="选择报工站点" :visible.sync="stationDialogVisible" width="50%" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="stationList.length > 0">
      <el-table
        v-if="stationList && stationList.length > 0"
        :data="stationList"
        border
        stripe
        size="small"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        style="width: 100%"
        highlight-current-row
        @row-click="handleStationSelect"
      >
        <el-table-column prop="WorkCenterCode" label="工作中心编码" width="150" align="center" />
        <el-table-column prop="WorkCenterName" label="工作中心名称" min-width="150" align="center" />
        <el-table-column prop="StationCode" label="站点代码" width="120" align="center" />
        <el-table-column prop="StationName" label="站点名称" min-width="150" align="center" />
        <el-table-column prop="ProcessNo" label="工序号" width="80" align="center" />
        <el-table-column prop="ProcessShortText" label="工序短文本" min-width="150" align="center" />
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.Enable === 1" type="success">启用</el-tag>
            <el-tag v-else type="info">未启用</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div v-else class="empty-station">
        <i class="el-icon-warning-outline" />
        <p>您没有关联任何报工站点，请联系管理员进行配置</p>
      </div>
    </el-dialog>

    <!-- 未选择站点时的页面 -->
    <div v-if="!currentStation" class="no-station-container">
      <div class="no-station-content">
        <i class="el-icon-s-operation" />
        <h2>请先选择报工站点</h2>
        <p>您需要先选择一个报工站点才能进行生产报工操作</p>
        <el-button type="primary" size="medium" @click="showStationDialog">选择站点</el-button>
      </div>
    </div>

    <!-- 已选择站点时的页面 -->
    <template v-else>
      <el-card class="box-card" shadow="hover">
        <div slot="header" class="clearfix">
          <span>生产报工扫描</span>
          <el-button type="text" style="float: right; padding: 3px 0" @click="showStationDialog">
            切换站点 (当前: {{ currentStation.StationName }})
          </el-button>
        </div>
        <el-form ref="scanForm" :model="scanForm" label-width="120px" size="small">
          <el-row>
            <el-col :span="8">
              <el-form-item label="生产订单号" prop="produceOrderNo">
                <el-input
                  ref="scanInput"
                  v-model="scanForm.produceOrderNo"
                  placeholder="请输入或扫描生产订单号"
                  clearable
                  :autofocus="true"
                  @keyup.enter.native="handleScan"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="handleScan">扫描</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 扫描结果显示 -->
      <el-row v-if="scanResult.showResult" :gutter="20" class="mt-20">
        <el-col :span="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>扫描结果</span>
            </div>
            <div class="scan-result" :class="{'success': scanResult.success, 'error': !scanResult.success}">
              <i :class="scanResult.success ? 'el-icon-success' : 'el-icon-error'" />
              <span>{{ scanResult.message }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 工单信息显示 -->
      <el-row v-if="workOrderInfo" :gutter="20" class="mt-20">
        <el-col :span="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>工单信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">生产报工单号：</span>
                  <span class="value">{{ workOrderInfo.ProduceReportNo }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">生产订单号：</span>
                  <span class="value">{{ workOrderInfo.ProduceOrderNo }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">出厂编号：</span>
                  <span class="value">{{ workOrderInfo.SerialNo }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">物料编码：</span>
                  <span class="value">{{ workOrderInfo.MaterialCode }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">物料描述：</span>
                  <span class="value">{{ workOrderInfo.MaterialName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">订单数量：</span>
                  <span class="value">{{ workOrderInfo.OrderQty }} {{ workOrderInfo.Unit }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">工作中心：</span>
                  <span class="value">{{ workOrderInfo.WorkCenterCode }} - {{ workOrderInfo.WorkCenterName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">客户名称：</span>
                  <span class="value">{{ workOrderInfo.CustomerName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">当前站点：</span>
                  <span class="value">{{ workOrderInfo.CurrentStationCode }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <el-row v-if="orderInfo" :gutter="20" class="mt-20">
        <el-col :span="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>订单信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">生产订单号：</span>
                  <span class="value">{{ orderInfo.ProduceOrderNo }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">物料编码：</span>
                  <span class="value">{{ orderInfo.MaterialNo }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">物料描述：</span>
                  <span class="value">{{ orderInfo.MaterialName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">客户名称：</span>
                  <span class="value">{{ orderInfo.CustomerName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">订单数量：</span>
                  <span class="value">{{ orderInfo.Quantity }} {{ orderInfo.Unit }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">排产日期：</span>
                  <span class="value">{{ formatDate(orderInfo.ProduceSchedulingDate) }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <el-row v-if="processList && processList.length > 0" :gutter="20" class="mt-20">
        <el-col :span="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>工序信息</span>
              <div v-if="hasWarning" class="warning-text">{{ warningMessage }}</div>
            </div>
            <el-table
              :data="processList"
              border
              stripe
              size="small"
              :header-cell-style="{background:'#eef1f6',color:'#606266'}"
              style="width: 100%"
            >
              <el-table-column prop="VORNR" label="工序号" width="80" align="center" />
              <el-table-column prop="LTXA1" label="工序描述" min-width="150" align="center" />
              <el-table-column prop="ARBPL" label="工作中心" width="120" align="center" />
              <el-table-column label="状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.Status === 1" type="success">已完成</el-tag>
                  <el-tag v-else-if="scope.row.Status === 2" type="warning">待报工</el-tag>
                  <el-tag v-else type="info">未开始</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    :disabled="!canReportProcess(scope.row)"
                    @click="selectProcess(scope.row)"
                  >
                    报工
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </template>

    <el-dialog title="工序报工" :visible.sync="reportDialogVisible" width="50%" :close-on-click-modal="false">
      <el-form ref="reportForm" :model="reportForm" label-width="120px" :rules="reportRules">
        <el-form-item label="工序号" prop="processNo">
          <el-input v-model="reportForm.processNo" disabled />
        </el-form-item>
        <el-form-item label="工序描述">
          <el-input v-model="reportForm.processDesc" disabled />
        </el-form-item>
        <el-form-item label="员工编号" prop="employeeNumber">
          <el-input v-model="reportForm.employeeNumber" placeholder="请输入员工编号" />
        </el-form-item>
        <el-form-item label="员工名称" prop="employeeName">
          <el-input v-model="reportForm.employeeName" placeholder="请输入员工名称" />
        </el-form-item>
        <el-form-item label="报工数量" prop="reportQty">
          <el-input-number v-model="reportForm.reportQty" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="合格数量" prop="qualifiedQty">
          <el-input-number v-model="reportForm.qualifiedQty" :min="0" :precision="2" @change="calculateUnqualifiedQty" />
        </el-form-item>
        <el-form-item label="不合格数量" prop="unqualifiedQty">
          <el-input-number v-model="reportForm.unqualifiedQty" :min="0" :precision="2" @change="calculateQualifiedQty" />
        </el-form-item>
        <el-form-item label="不合格备注" prop="unqualifiedRemarks">
          <el-input
            v-model="reportForm.unqualifiedRemarks"
            type="textarea"
            :rows="3"
            :disabled="reportForm.unqualifiedQty <= 0"
            placeholder="请输入不合格原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="reportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReport">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="报工结果" :visible.sync="resultDialogVisible" width="40%" :close-on-click-modal="false">
      <div class="result-container">
        <div class="result-icon" :class="{'success': reportSuccess, 'error': !reportSuccess}">
          <i :class="reportSuccess ? 'el-icon-check' : 'el-icon-close'" />
        </div>
        <div class="result-message">{{ reportMessage }}</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeResultDialog">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves' // waves directive 特效
import permission from '@/directive/permission/permission' // 权限
import { formatDate, formatDateTime } from '@/utils'
import {
  scanReport,
  doReport,
  getSelfStationList,
  scanStation,
  getEntityByProduceOrderNo
} from '@/api/Produce/Produce_Report'

export default {
  name: 'Produce.ProduceReportScan',
  directives: {
    waves,
    permission
  },
  data() {
    return {
      isProcessing: false,
      scanForm: {
        produceOrderNo: ''
      },
      orderInfo: null,
      processList: [],
      currentProcess: null,
      hasWarning: false,
      warningMessage: '',
      reportDialogVisible: false,
      resultDialogVisible: false,
      reportSuccess: false,
      reportMessage: '',
      reportForm: {
        processNo: '',
        processDesc: '',
        employeeNumber: '',
        employeeName: '',
        reportQty: 1,
        qualifiedQty: 1,
        unqualifiedQty: 0,
        unqualifiedRemarks: ''
      },
      reportRules: {
        employeeNumber: [
          { required: true, message: '请输入员工编号', trigger: 'blur' }
        ],
        employeeName: [
          { required: true, message: '请输入员工名称', trigger: 'blur' }
        ],
        reportQty: [
          { required: true, message: '请输入报工数量', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '报工数量必须大于0', trigger: 'blur' }
        ],
        qualifiedQty: [
          { required: true, message: '请输入合格数量', trigger: 'blur' },
          { type: 'number', min: 0, message: '合格数量不能小于0', trigger: 'blur' }
        ]
      },
      // 站点相关数据
      stationDialogVisible: false,
      stationList: [],
      currentStation: null,

      // 扫描结果相关数据
      scanResult: {
        success: false,
        message: '',
        showResult: false
      },

      // 工单信息
      workOrderInfo: null
    }
  },
  created() {
    // 初始化员工信息
    this.reportForm.employeeNumber = this.$store.getters.userinfo.LoginAccount
    this.reportForm.employeeName = this.$store.getters.userinfo.UserName
    // 获取用户关联的站点列表
    this.getUserStations()
  },
  mounted() {
    // 自动聚焦到扫描输入框（仅当已选择站点时）
    this.$nextTick(() => {
      if (this.currentStation && this.$refs.scanInput) {
        this.$refs.scanInput.focus()
      }
    })
  },
  methods: {
    formatDate,
    formatDateTime,

    // 获取用户关联的站点列表
    getUserStations() {
      this.isProcessing = true
      getSelfStationList().then(response => {
        this.isProcessing = false
        if (response.Code === 2000) {
          this.stationList = response.Data || []
          // 如果只有一个站点，直接选择该站点
          if (this.stationList.length === 1) {
            this.selectStation(this.stationList[0])
          } else if (this.stationList.length > 1) {
            // 如果有多个站点，显示选择对话框
            this.stationDialogVisible = true
          } else {
            // 没有站点
            this.showNotify('warning', '您没有关联任何报工站点，请联系管理员进行配置')
          }
        } else {
          this.showNotify('error', response.Message || '获取站点列表失败')
        }
      }).catch(error => {
        this.isProcessing = false
        console.error('获取站点列表失败:', error)
        this.showNotify('error', '获取站点列表请求失败')
      })
    },

    // 显示站点选择对话框
    showStationDialog() {
      this.stationDialogVisible = true
    },

    // 选择站点行
    handleStationSelect(row) {
      this.selectStation(row)
      this.stationDialogVisible = false
    },

    // 选择站点
    selectStation(station) {
      if (!station || !station.StationCode) {
        this.showNotify('warning', '无效的站点信息')
        return
      }
      this.currentStation = station
      this.stationDialogVisible = false
      // 选择站点后清空之前的数据
      this.orderInfo = null
      this.processList = []
      this.scanForm.produceOrderNo = ''
      // 聚焦到扫描输入框
      this.$nextTick(() => {
        if (this.$refs.scanInput) {
          this.$refs.scanInput.focus()
        }
      })
    },

    // 处理扫描
    handleScan() {
      if (!this.currentStation) {
        this.showNotify('warning', '请先选择报工站点')
        this.showStationDialog()
        return
      }
      if (!this.scanForm.produceOrderNo) {
        this.showNotify('warning', '请输入生产订单号')
        return
      }

      // 重置扫描结果和订单信息
      this.scanResult.showResult = false
      this.orderInfo = null
      this.processList = []
      this.workOrderInfo = null

      this.isProcessing = true

      // 首先获取工单信息
      getEntityByProduceOrderNo(this.scanForm.produceOrderNo).then(response => {
        if (response.Code === 2000 && response.Data) {
          this.workOrderInfo = response.Data

          // 然后调用站点扫描接口
          const scanData = {
            StationCode: this.currentStation.StationCode,
            ProduceOrderNo: this.scanForm.produceOrderNo
          }

          scanStation(scanData).then(scanResponse => {
            console.log('1111')
            if (scanResponse.Code === 2000) {
              this.isProcessing = false;
              // 站点验证通过
              this.scanResult = {
                success: true,
                message: scanResponse.Message || '站点验证通过',
                showResult: true
              }
            } else {
              // 站点验证失败
              this.isProcessing = false
              this.scanResult = {
                success: false,
                message: scanResponse.Message || '站点验证失败',
                showResult: true
              }
            }
          }).catch(error => {
            this.isProcessing = false
            console.error('站点扫描请求失败:', error)
            this.scanResult = {
              success: false,
              message: error || '站点扫描请求失败',
              showResult: true
            }
          })
        } else {
          // 未找到工单信息
          this.isProcessing = false
          this.showNotify('warning', response.Message || '未找到工单信息')
        }
      }).catch(error => {
        this.isProcessing = false
        console.error('获取工单信息失败:', error)
        this.showNotify('error', '获取工单信息失败')
      })
    },

    // 判断是否可以报工此工序
    canReportProcess(process) {
      if (this.currentProcess) {
        return process.VORNR === this.currentProcess.VORNR
      }
      return false
    },

    // 选择工序进行报工
    selectProcess(process) {
      this.reportForm.processNo = process.VORNR
      this.reportForm.processDesc = process.LTXA1
      this.reportForm.reportQty = 1
      this.reportForm.qualifiedQty = 1
      this.reportForm.unqualifiedQty = 0
      this.reportForm.unqualifiedRemarks = ''

      this.reportDialogVisible = true
    },

    // 计算不合格数量
    calculateUnqualifiedQty() {
      this.reportForm.unqualifiedQty = Math.max(0, this.reportForm.reportQty - this.reportForm.qualifiedQty)
    },

    // 计算合格数量
    calculateQualifiedQty() {
      this.reportForm.qualifiedQty = Math.max(0, this.reportForm.reportQty - this.reportForm.unqualifiedQty)
    },

    // 提交报工
    submitReport() {
      this.$refs.reportForm.validate(valid => {
        if (valid) {
          // 验证合格数量和不合格数量是否等于报工数量
          const total = this.reportForm.qualifiedQty + this.reportForm.unqualifiedQty
          if (Math.abs(total - this.reportForm.reportQty) > 0.01) {
            this.showNotify('warning', '合格数量与不合格数量之和必须等于报工数量')
            return
          }

          // 如果有不合格品但没有备注
          if (this.reportForm.unqualifiedQty > 0 && !this.reportForm.unqualifiedRemarks) {
            this.showNotify('warning', '存在不合格品，请填写不合格备注')
            return
          }

          const reportData = {
            produceOrderNo: this.scanForm.produceOrderNo,
            employeeNumber: this.reportForm.employeeNumber,
            employeeName: this.reportForm.employeeName,
            processNo: parseInt(this.reportForm.processNo),
            reportQty: this.reportForm.reportQty,
            qualifiedQty: this.reportForm.qualifiedQty,
            unqualifiedQty: this.reportForm.unqualifiedQty,
            unqualifiedRemarks: this.reportForm.unqualifiedRemarks,
            stationCode: this.currentStation.StationCode,
            stationName: this.currentStation.StationName
          }

          this.isProcessing = true
          doReport(reportData).then(response => {
            this.isProcessing = false
            this.reportDialogVisible = false

            this.reportSuccess = response.Code === 2000
            this.reportMessage = response.Message || (this.reportSuccess ? '报工成功' : '报工失败')
            this.resultDialogVisible = true

            if (this.reportSuccess) {
              // 重新加载工序信息
              this.loadOrderInfo()
            }
          }).catch(error => {
            this.isProcessing = false
            this.reportDialogVisible = false
            console.error('报工请求失败:', error)
            this.reportSuccess = false
            this.reportMessage = '报工请求失败'
            this.resultDialogVisible = true
          })
        }
      })
    },

    // 关闭结果对话框
    closeResultDialog() {
      this.resultDialogVisible = false
      if (this.reportSuccess) {
        // 清空扫描输入框，准备下一次扫描
        this.scanForm.produceOrderNo = ''
        this.$nextTick(() => {
          if (this.$refs.scanInput) {
            this.$refs.scanInput.focus()
          }
        })
      }
    },

    // 显示提示信息
    showNotify(type, message) {
      this.$notify({
        title: type === 'success' ? '成功' : type === 'warning' ? '警告' : '错误',
        message: message,
        type: type,
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mt-20 {
  margin-top: 20px;
}

.info-item {
  margin-bottom: 10px;

  .label {
    font-weight: bold;
    color: #606266;
  }

  .value {
    margin-left: 5px;
    color: #333;
  }
}

.warning-text {
  float: right;
  color: #e6a23c;
  font-size: 14px;
}

.scan-result {
  display: flex;
  align-items: center;
  padding: 15px;
  font-size: 16px;
  border-radius: 4px;

  i {
    font-size: 24px;
    margin-right: 10px;
  }

  &.success {
    background-color: #f0f9eb;
    color: #67c23a;
    border: 1px solid #e1f3d8;
  }

  &.error {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fde2e2;
  }
}

.result-container {
  text-align: center;
  padding: 20px 0;

  .result-icon {
    font-size: 60px;
    margin-bottom: 20px;

    &.success {
      color: #67c23a;
    }

    &.error {
      color: #f56c6c;
    }
  }

  .result-message {
    font-size: 16px;
    color: #606266;
  }
}

.empty-station {
  text-align: center;
  padding: 30px 0;

  i {
    font-size: 48px;
    color: #e6a23c;
  }

  p {
    margin-top: 15px;
    color: #606266;
  }
}

.no-station-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background-color: #f8f8f8;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.no-station-content {
  text-align: center;
  padding: 40px;

  i {
    font-size: 80px;
    color: #409EFF;
    margin-bottom: 20px;
  }

  h2 {
    font-size: 24px;
    color: #303133;
    margin-bottom: 15px;
  }

  p {
    color: #606266;
    margin-bottom: 30px;
    font-size: 16px;
  }
}
</style>
