<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="7">
            <el-form-item label="装配日期">
              <el-date-picker
                v-model="listQuery.dateValue"
                size="small"
                :clearable="false"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生产调度员">
              <el-input
                v-model="listQuery.ProduceScheduler"
                size="small"
                class="filter-item"
                placeholder="生产调度员"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生产报工单号">
              <el-input
                v-model="listQuery.ProduceReportNo"
                size="small"
                class="filter-item"
                placeholder="生产报工单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="生产订单号">
              <el-input
                v-model="listQuery.ProduceOrderNo"
                size="small"
                class="filter-item"
                placeholder="生产订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="工作中心">
              <el-input
                v-model="listQuery.WorkCenterName"
                size="small"
                class="filter-item"
                placeholder="工作中心"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关键字">
              <el-input
                v-model="listQuery.keyword"
                size="small"
                class="filter-item"
                :placeholder="$t('Common.keyword')"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料编码">
              <el-input
                v-model="listQuery.MaterialCode"
                size="small"
                class="filter-item"
                placeholder="物料编码"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="出厂编号">
              <el-input
                v-model="listQuery.SerialNo"
                size="small"
                class="filter-item"
                placeholder="出厂编号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="过账状态">
              <el-select
                v-model="listQuery.isPosted"
                size="small"
                :placeholder="$t('Common.postingStatus')"
                class="filter-item"
                style="width: 100%;"
              >
                <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="生产报工单号" prop="ProduceReportNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="CA状态" prop="CaStatus" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="CA流程编号" prop="CaSequence·No" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="SerialNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="生产订单号" prop="ProduceOrderNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="主机生产订单号" prop="HostProduceOrderNo" width="150" align="center" show-overflow-tooltip />
      <el-table-column label="生产调度员" prop="ProduceScheduler" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="物料编码" prop="MaterialCode" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="订单类型" prop="OrderType" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="订单数量" prop="OrderQty" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" width="80" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收货库存地点" prop="ReceiveLocation" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="报工数量" prop="ReportTotal" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="合格数量" prop="QualifiedQty" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="不合格数量" prop="UnqualifiedQty" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="不合格备注" prop="UnqualifiedRemarks" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="装配日期" prop="AssemblDate" width="120" align="center" :formatter="formatDate" show-overflow-tooltip />
      <el-table-column label="发货单位" prop="CustomerName" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="物料组编码" prop="MaterialGroupCode" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="物料组描述" prop="MaterialGroupDes" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="是否完成" prop="IsCompleted" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IsCompleted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否过账" prop="IsPosted" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账时间" prop="PostTime" width="120" align="center" :formatter="formatDateTime" show-overflow-tooltip />
      <el-table-column label="过账人" prop="PostUser" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="手动过账时间" prop="ManualPostTime" width="120" align="center" :formatter="formatDateTime" show-overflow-tooltip />
      <el-table-column label="SAP单号" prop="SapDocNum" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="SAP行号" prop="SapLine" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="当前站点代码" prop="CurrentStationCode" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="工作中心名称" prop="WorkCenterName" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="工作中心编码" prop="WorkCenterCode" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="创建时间" prop="CTime" width="120" align="center" :formatter="formatDateTime" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click.stop="handleViewDetail(scope.row)"
          >查看明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%" :close-on-click-modal="false">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 明细对话框 -->
    <el-dialog title="生产报工明细" :visible.sync="detailDialogVisible" width="80%" :close-on-click-modal="false">
      <el-table
        v-loading="detailLoading"
        :data="detailList"
        border
        fit
        :height="400"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%"
        size="mini"
      >
        <el-table-column type="index" align="center" width="50" label="行号" />
        <el-table-column label="物料编码" prop="MaterialCode" width="160" align="center" show-overflow-tooltip />
        <el-table-column label="物料描述" prop="MaterialDesc" width="160" align="center" show-overflow-tooltip />
        <el-table-column label="工作中心名称" prop="WorkCenterName" width="120" align="center" show-overflow-tooltip />
        <el-table-column label="工作中心编码" prop="WorkCenterCode" width="120" align="center" show-overflow-tooltip />
        <el-table-column label="人工工时" prop="LaborHour" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="机器工时" prop="MachineHour" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="可控制费工时" prop="ControllerCostLaborHour" width="120" align="center" show-overflow-tooltip />
        <el-table-column label="一级分摊工时" prop="FirstLevelSharedWorkHour" width="120" align="center" show-overflow-tooltip />
        <el-table-column label="确认时间" prop="ConfirmTime" width="120" align="center" :formatter="formatDateTime" show-overflow-tooltip />
        <el-table-column label="状态" prop="Status" width="100" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.Status === 1 ? '正常' : '等待报工' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="员工编号" prop="EmployeeCode" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="员工名称" prop="EmployeeName" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="站点名称" prop="StationName" width="120" align="center" show-overflow-tooltip />
        <el-table-column label="站点代码" prop="StationCode" width="120" align="center" show-overflow-tooltip />
        <el-table-column label="站点序号" prop="StationSort" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="是否多站点配送" prop="IsMultistation" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.IsMultistation | yesnoFilter }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission';
import Pagination from '@/components/Pagination'; // 分页

import {
  fetchList,
  batchDelete,
  exportExcelFile,
  exportExcelModel,
  improtExcelFile,
  getDetailList
} from '@/api/Produce/Produce_Report';
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export';
import {
  formatDate,
  formatDateTime
} from '@/utils';

export default {
  name: 'PP.PP_ProductionReportController',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        WorkCenterName: '',
        ProduceScheduler: '',
        ProduceOrderNo: '',
        SerialNo: '',
        ProduceReportNo: '',
        MaterialNo: ''
      },
      hasPostedData: false,
      postDisableStatus: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10
      },
      currentRow: {},
      dialogVisible: false,
      ruleForm: {
        PostTime: ''
      },
      rules: {
        PostTime: [{
          type: 'date',
          required: true,
          message: '请选择过账日期',
          trigger: 'change'
        }]
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      tableHeight: '300px',
      detailDialogVisible: false,
      detailLoading: false,
      detailList: []
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    },
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/Produce/ProduceReportDetail') {
        this.handleFilter();
      }
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '生产报工单号为：' + v.ProduceReportNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        // if (v.IsPosted === true) {
        //   this.showNotify("warning", '配送单号为：' + v.DeliveryOrderNo + '信息已过账，禁止删除');
        //   switchBtn = false
        //   return true
        // }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          var arrRowsID = selectRows.map(v => v.ID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
              console.error('删除操作出错:', error);
              this.showNotify('error', '删除操作失败');
            });
        });
      }
    },
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        WorkCenterName: this.listQuery.WorkCenterName,
        MaterialNo: this.listQuery.MaterialNo,
        SerialNo: this.listQuery.SerialNo,
        ProduceReportNo: this.listQuery.ProduceReportNo,
        ProduceOrderNo: this.listQuery.ProduceOrderNo
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '生产报工');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
        console.log(this.hasPostedData);
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      // this.getListDetail()
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleSave() {
      this.routeTo('Produce.ProduceReportDetail');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '生产报工单号为：' + v.ProduceReportNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
      });
      if (switchBtn) {
        this.routeTo('Produce.ProduceReport', this.multipleSelection[0]);
      }
    },
    getCaption(obj) {
      if (obj) {
        const index = obj.lastIndexOf('&');
        obj = obj.substring(index + 1, obj.length);
        return obj;
      }
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
          } else {
            this.showNotify('warning', 'Common.operationFailed');
          }
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
          console.error('导入操作出错:', error);
          this.showNotify('error', '导入操作失败');
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    // 查看明细
    handleViewDetail(row) {
      this.detailDialogVisible = true;
      this.getDetailData(row.Id);
    },
    // 获取明细数据
    getDetailData(id) {
      this.detailLoading = true;
      getDetailList(id).then(response => {
        if (response.Code === 2000) {
          this.detailList = response.Data || [];
        } else {
          this.showNotify('error', response.Message || '获取明细数据失败');
          this.detailList = [];
        }
        this.detailLoading = false;
      }).catch(error => {
        console.error('获取明细数据出错:', error);
        this.showNotify('error', '获取明细数据失败');
        this.detailList = [];
        this.detailLoading = false;
      });
    }
  }
};
</script>
