<template>
  <div class="app-container">
    <p>
      <label style="width:100%">工序报工申请登记单</label>
    </p>

    <el-form
      ref="searchQuery"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="扫描码">
            <el-input
              ref="input"
              v-model="searchQuery.ScanningCodes"
              :autofocus="autofocus"
              :disabled="ScanningDisabled"
              @keyup.enter.native="changeSerialNo"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产订单" prop="ProductionOrderNo">
            <el-input v-model="searchQuery.ProductionOrderNo" clearable @keyup.enter.native="keyupProductionOrderNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="出厂编号">
            <el-input v-model="searchQuery.SerialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产报工单号">
            <el-input v-model="searchQuery.ProductionReportNo" disabled />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item class="filter-item" label="物料件号">
            <el-input v-model="searchQuery.MaterialNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物料名称">
            <el-input v-model="searchQuery.MaterialName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="工序描述" prop="WorkingProcedureCode">
            <el-select
              v-model="searchQuery.WorkingProcedureCode"
              filterable
              placeholder="请选择"
              @change="changeWorkingProcedure"
            >
              <el-option
                v-for="item in options"
                :key="item.WorkingProcedureCode"
                :label="item.WorkingProcedureCode+'-'+item.WorkingProcedureDes"
                :value="item.WorkingProcedureCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="工序编号">
            <el-input v-model="searchQuery.WorkingProcedureCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="员工号">
            <el-input v-model="searchQuery.EmployeeNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="员工姓名">
            <el-input v-model="searchQuery.EmployeeName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="订单数量">
            <el-input v-model="searchQuery.OrderQty" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="单位">
            <el-input v-model="searchQuery.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="合格数量" prop="QualifiedQty">
            <el-input v-model="searchQuery.QualifiedQty" type="number" @blur="blurQualifiedQty" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="不合格数量">
            <el-input v-model="searchQuery.UnqualifiedQty" type="number" @blur="blurReportTotal" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="不合格备注">
            <el-input v-model="searchQuery.UnqualifiedRemarks" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="报工总数">
            <el-input v-model="searchQuery.ReportTotal" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="生产调度员">
            <el-input v-model="searchQuery.ProductionScheduler" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="线体编号">
            <el-input v-model="searchQuery.ProductionLineCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="线体名称">
            <el-input v-model="searchQuery.ProductionLineDes" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="过账日期" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="收货库存地点">
            <el-input v-model="searchQuery.ReceivingLocation" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-switch
      v-if="editStatus === 'create'"
      v-model="switchValue"
      active-color="#13ce66"
      inactive-color="#ff4949"
      active-text="自动提交"
      inactive-text="手动提交"
      @change="changeSwitchValue"
    />
    <p>
      <el-button
        v-if="editStatus === 'create' && !switchValue"
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleSave"
      >
        {{ $t('Common.add') }}</el-button>
      <el-button
        v-if="editStatus === 'create'"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDeleteDetail"
      >
        {{ $t("Common.delete") }}</el-button>
      <el-button v-if="!switchValue" type="success" size="small" icon="el-icon-edit" @click="handleCommit">
        {{ $t("Common.confirm") }}
      </el-button>
    </p>
    <el-table
      v-if="editStatus === 'create'"
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      size="mini"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="生产报工单号" prop="ProductionReportNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="扫描码" prop="ScanningCode" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="SerialNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="生产订单" prop="ProductionOrderNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="主机生产订单" prop="HostProductionOrderNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MaterialNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MaterialName" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="工序编号" prop="WorkingProcedureCode" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="工序描述" prop="WorkingProcedureDes" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="员工号" prop="EmployeeNumber" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="员工姓名" prop="EmployeeName" width="80" align="center" show-overflow-tooltip />
      <el-table-column label="订单数量" prop="OrderQty" width="80" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" width="80" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报工总数" prop="ReportTotal" width="80" align="center" show-overflow-tooltip />
      <el-table-column label="收货库存地点" prop="ReceivingLocation" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="合格数量" prop="QualifiedQty" width="80" align="center" show-overflow-tooltip />
      <el-table-column label="不合格数量" prop="UnqualifiedQty" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="不合格备注" prop="UnqualifiedRemarks" width="200" align="center" show-overflow-tooltip />
      <el-table-column label="生产调度员" prop="ProductionScheduler" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="线体编号" prop="ProductionLineCode" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="线体名称" prop="ProductionLineDes" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="100" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>

    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddModel from './modules/addModel'
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetWorkingProcedure,
  Update,
  GetEntity,
  GetOrderBySerialNo1,
  GetOrderNoSerialNo,
  GetQty,
  GetOrderNoSerialNoByNo
} from '@/api/Produce/Produce_Report';
export default {
  name: 'Produce.ProduceReportDetail',
  components: {
    Pagination,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    const validate = (rule, value, callback) => {
      if (value === 0 || value < 0) {
        callback(new Error('请输入大于0的数字'))
      } else {
        callback()
      }
    };
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        ProductionReportNo: '',
        ScanningCode: '',
        ScanningCodes: '',
        SerialNo: '',
        ProductionOrderNo: '',
        MaterialNo: '',
        MaterialName: '',
        WorkingProcedureCode: '',
        WorkingProcedureDes: '',
        EmployeeNumber: this.$store.getters.userinfo.LoginAccount,
        EmployeeName: this.$store.getters.userinfo.UserName,
        OrderQty: '',
        Unit: '',
        ReportTotal: 1,
        QualifiedQty: 1,
        UnqualifiedQty: 0,
        UnqualifiedRemarks: '',
        ManualPostTime: new Date(),
        IsCompleted: false,
        OrderType: '',
        HostProductionOrderNo: '', //  主机生产订单
        ProductionScheduler: '', //  生产管理员
        StartTime: '',
        AssessmentType: '',
        Shippers: '',
        ProductionLineCode: '',
        ProductionLineDes: ''
      },
      multipleSelection: [],
      rules: {
        ScanningCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        QualifiedQty: [{
          required: true,
          validator: validate,
          trigger: 'blur'
        }],
        ProductionOrderNo: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: 'create',
      delList: [],
      options: [],
      dataObj: {},
      ProductionReportNo: '',
      switchValue: false,
      autofocus: false,
      ProductionOrderNoOptions: [],
      QualifiedQty: '',
      ScanningDisabled: false
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  mounted() {
    this.autofocus = true;
    this.$refs.input.focus();
  },
  created() {
    this.getPageParams();
    this.GetOrderNoSerialNo();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      if (this.editStatus === 'create') {
        if (this.list.length === 0) {
          this.showNotify('warning', '请添加信息');
          return
        }
        this.startLoading();
        this.ScanningDisabled = false;
        console.log(1, this.list);
        SubmitScanInfo(this.list).then(res => {
          if (res.Code === 2000) {
            if (!this.switchValue) {
              this.backTo('PP.PP_ProductionReportController');
            } else {
              this.list = [];
              this.fetchDocNum();
              this.searchQuery.WorkingProcedureDes = '';
              this.searchQuery.WorkingProcedureCode = '';
              this.searchQuery.ReportTotal = 1;
              this.searchQuery.QualifiedQty = 1;
              this.autofocus = true;
              this.$refs.input.focus();
              this.showNotify('success', '添加成功');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.endLoading();
        }).catch(err => {
          console.log(err);
          this.ScanningDisabled = false;
          this.endLoading();
        })
      } else {
        this.$refs['searchQuery'].validate((valid) => {
          if (valid) {
            this.startLoading();
            const query = Object.assign({}, this.searchQuery);
            Update(query).then(res => {
              if (res.Code === 2000) {
                this.backTo('PP.PP_ProductionReportController');
              } else {
                this.showNotify('error', res.Message);
              }
              this.ScanningDisabled = false;
              this.endLoading();
            }).catch(err => {
              console.log(err);
              this.ScanningDisabled = false;
              this.endLoading();
            })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.ProductionReportNo = response.Data;
          this.searchQuery.ProductionReportNo = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    GetEntity() {
      const query = {
        key: this.searchQuery.ID
      };
      GetEntity(query).then(res => {
        if (res.Code === 2000) {

        }
      })
    },
    GetWorkingProcedure() {
      if (this.searchQuery.ProductionOrderNo) {
        const query = {
          OrderNo: this.searchQuery.ProductionOrderNo
        };
        GetWorkingProcedure(query).then(res => {
          if (res.Code === 2000) {
            this.options = res.Data;
            if (res.Data.length > 0) {
              this.searchQuery.WorkingProcedureDes = res.Data[res.Data.length - 1].WorkingProcedureDes;
              this.searchQuery.WorkingProcedureCode = res.Data[res.Data.length - 1].WorkingProcedureCode;
              this.searchQuery.IsCompleted = true;
              this.GetQty();
            }
          }
        })
      }
    },
    changeWorkingProcedure(e) {
      const obj = this.options.find(v => v.WorkingProcedureCode === e);
      this.searchQuery.WorkingProcedureDes = obj.WorkingProcedureDes;
      this.GetQty();
      const index = this.options.indexOf(obj);
      if (index === this.options.length - 1) {
        this.searchQuery.IsCompleted = true;
      } else {
        this.searchQuery.IsCompleted = false;
      }
    },
    GetQty() {
      const query = {
        ProductionOrderNo: this.searchQuery.ProductionOrderNo,
        WorkingProcedureCode: this.searchQuery.WorkingProcedureCode,
        SerialNo: this.searchQuery.SerialNo
      };
      GetQty(query).then(res => {
        if (res.Code === 2000) {
          if (res.Data.QualifiedQty > 0) {
            this.searchQuery.QualifiedQty = res.Data.QualifiedQty;
            this.QualifiedQty = res.Data.QualifiedQty;
            this.blurReportTotal();
            if (this.switchValue) {
              this.handleSave();
            }
          } else {
            this.searchQuery = {
              ProductionReportNo: this.ProductionReportNo,
              ScanningCode: '',
              ScanningCodes: '',
              SerialNo: '',
              ProductionOrderNo: '',
              MaterialNo: '',
              MaterialName: '',
              WorkingProcedureCode: '',
              WorkingProcedureDes: '',
              EmployeeNumber: this.$store.getters.userinfo.LoginAccount,
              EmployeeName: this.$store.getters.userinfo.UserName,
              OrderQty: '',
              Unit: '',
              ReportTotal: 1,
              QualifiedQty: 1,
              UnqualifiedQty: 0,
              UnqualifiedRemarks: '',
              OrderType: '',
              HostProductionOrderNo: '', //  主机生产订单
              ProductionScheduler: '', //  生产管理员
              ManualPostTime: this.searchQuery.ManualPostTime,
              StartTime: '',
              AssessmentType: '',
              Shippers: '',
              ReceivingLocation: '',
              ProductionLineCode: '',
              ProductionLineDes: ''
            };
            this.dataObj = {};
            this.$nextTick(() => {
              this.$refs.searchQuery.clearValidate();
            });
            this.ScanningDisabled = false;
            setTimeout(() => {
              this.autofocus = true;
              this.$refs.input.focus();
            }, 100);
            this.showNotify('warning', '当前工序报工数量已满');
            return false;
          }
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        if (this.searchQuery.Remark === null) {
          this.searchQuery.Remark = '';
        }
        // this.GetEntity()
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    changeSerialNo(e) {
      // if(e.keyCode !== 13){
      //   this.showNotify("warning", '没有触发回车事件');
      //   console.log('无触发回车事件')
      // }else{
      //   console.log('触发回车事件')
      //   this.showNotify("warning", '触发回车事件');
      // }
      this.startLoading();
      this.ScanningDisabled = true;
      if (this.searchQuery.ScanningCodes === 'XZFWDSTATOR') {
        this.showNotify('warning', '未查询到数据！');
        this.searchQuery.ScanningCodes = '';
        this.ScanningDisabled = false;
        setTimeout(() => {
          this.autofocus = true;
          this.$refs.input.focus();
        }, 100);
        this.endLoading();
        return;
      }
      let switchBtn = true;
      console.log(this.list);
      this.list.some(res => {
        console.log(res);
        if (res.ScanningCode === this.searchQuery.ScanningCodes) {
          this.showNotify('warning', '请勿重复扫描');
          switchBtn = false;
          this.searchQuery.ScanningCodes = '';
          this.ScanningDisabled = false;
          setTimeout(() => {
            this.autofocus = true;
            this.$refs.input.focus();
          }, 100);
          this.endLoading();
          return true;
        }
      });
      if (switchBtn) {
        this.searchQuery.SerialNo = this.searchQuery.ScanningCodes;
        this.searchQuery.ScanningCode = this.searchQuery.ScanningCodes;
        const query = {
          serialNo: this.searchQuery.ScanningCode
        };
        GetOrderBySerialNo1(query).then(res => {
          if (res.Code === 2000) {
            if (res.Data) {
              this.autofocus = false;
              this.searchQuery.ProductionOrderNo = res.Data.ProductionOrderNo;
              this.GetWorkingProcedure();
              this.searchQuery.MaterialNo = res.Data.MaterialNo;
              this.searchQuery.MaterialName = res.Data.MaterialName;
              this.searchQuery.OrderQty = res.Data.OrderQty;
              this.searchQuery.Unit = res.Data.Unit;
              this.searchQuery.OrderType = res.Data.OrderType;
              this.searchQuery.HostProductionOrderNo = res.Data.HostProductionOrderNo;
              this.searchQuery.ProductionScheduler = res.Data.ProductionScheduler;
              this.searchQuery.StartTime = res.Data.StartTime;
              this.searchQuery.AssessmentType = res.Data.AssessmentType;
              this.searchQuery.Shippers = res.Data.Shippers;
              this.searchQuery.ReceivingLocation = res.Data.ReceivingLocation;
              this.searchQuery.ProductionScheduler = res.Data.ProductionScheduler;
              this.searchQuery.ProductionLineCode = res.Data.ProductionLineCode;
              this.searchQuery.ProductionLineDes = res.Data.ProductionLineDes;
              this.dataObj = this.searchQuery;
              console.log(this.switchValue, 111);
              // if (this.switchValue) {
              //   console.log(123)
              //   this.handleSave()
              // }
            } else {
              this.showNotify('warning', '未查询到数据！');
              this.ScanningDisabled = false;
              this.searchQuery = {
                ProductionReportNo: this.ProductionReportNo,
                ScanningCode: '',
                ScanningCodes: '',
                SerialNo: '',
                ProductionOrderNo: '',
                MaterialNo: '',
                MaterialName: '',
                WorkingProcedureCode: '',
                WorkingProcedureDes: '',
                EmployeeNumber: this.$store.getters.userinfo.LoginAccount,
                EmployeeName: this.$store.getters.userinfo.UserName,
                OrderQty: '',
                Unit: '',
                ReportTotal: 1,
                QualifiedQty: 1,
                UnqualifiedQty: 0,
                UnqualifiedRemarks: '',
                OrderType: '',
                HostProductionOrderNo: '', //  主机生产订单
                ProductionScheduler: '', //  生产管理员
                ManualPostTime: this.searchQuery.ManualPostTime,
                StartTime: '',
                AssessmentType: '',
                Shippers: '',
                ReceivingLocation: '',
                ProductionLineCode: '',
                ProductionLineDes: ''
              };
              this.dataObj = {};
              setTimeout(() => {
                this.autofocus = true;
                this.$refs.input.focus();
              }, 100);
            }
            this.endLoading()
          }
        }).catch(err => {
          console.log(err);
          this.endLoading();
        })
      }
    },
    handleSave() {
      console.log(this.list);
      this.startLoading();
      this.$refs['searchQuery'].validate((valid) => {
        if (valid) {
          const data = [];
          const listObj = Object.assign(this.dataObj, this.searchQuery);
          console.log(listObj, 3);
          data.push(listObj);
          console.log(data);
          const obj = {};
          this.list = this.list.concat(data).reduce((cur, next) => {
            obj[next.SerialNo + next.ProductionOrderNo + next.WorkingProcedureCode] ? '' : obj[next.SerialNo + next.ProductionOrderNo + next.WorkingProcedureCode] = true && cur.push(next);
            return cur;
          }, []);
          this.searchQuery = {
            ProductionReportNo: this.ProductionReportNo,
            ScanningCode: '',
            ScanningCodes: '',
            SerialNo: '',
            ProductionOrderNo: '',
            MaterialNo: '',
            MaterialName: '',
            WorkingProcedureCode: '',
            WorkingProcedureDes: '',
            EmployeeNumber: this.$store.getters.userinfo.LoginAccount,
            EmployeeName: this.$store.getters.userinfo.UserName,
            OrderQty: '',
            Unit: '',
            ReportTotal: 1,
            QualifiedQty: 1,
            UnqualifiedQty: 0,
            UnqualifiedRemarks: '',
            OrderType: '',
            HostProductionOrderNo: '', //  主机生产订单
            ProductionScheduler: '', //  生产管理员
            ManualPostTime: this.searchQuery.ManualPostTime,
            StartTime: '',
            AssessmentType: '',
            Shippers: '',
            ReceivingLocation: '',
            ProductionLineCode: '',
            ProductionLineDes: ''
          };
          this.dataObj = {};
          this.$nextTick(() => {
            this.$refs.searchQuery.clearValidate();
          });
          if (this.switchValue) {
            if (this.list.length > 0) {
              this.handleCommit();
            }
          } else {
            this.ScanningDisabled = false;
          }
          this.endLoading();
        } else {
          console.log('error submit!!');
          this.endLoading();
          return false;
        }
      })
    },
    blurQualifiedQty() {
      if (this.QualifiedQty) {
        if (this.searchQuery.QualifiedQty > this.QualifiedQty) {
          this.showNotify('warning', '合格数量不能大于剩余数量');
          return false;
        }
      }
      if (this.searchQuery.QualifiedQty) {
        this.searchQuery.ReportTotal = Number(this.searchQuery.QualifiedQty) + Number(this.searchQuery.UnqualifiedQty);
      }
    },
    blurReportTotal() {
      if (this.searchQuery.QualifiedQty) {
        this.searchQuery.ReportTotal = Number(this.searchQuery.QualifiedQty) + Number(this.searchQuery.UnqualifiedQty);
      }
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.ID) {
          if (v.ID === record.ID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.ScanningCode + v.ProductionOrderNo + v.WorkingProcedureCode === record.ScanningCode + record.ProductionOrderNo + record.WorkingProcedureCode) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    changeSwitchValue(e) {
      if (e) {
        this.handleSave();
      }
    },
    GetOrderNoSerialNo() {
      GetOrderNoSerialNo().then(res => {
        if (res.Code === 2000) {
          this.ProductionOrderNoOptions = res.Data;
        }
      })
    },
    changeProductionOrderNo(e) {
      const obj = this.ProductionOrderNoOptions.find(v => v.ProductionOrderNo === e);
      this.GetWorkingProcedure();
      this.searchQuery.ScanningCode = '';
      this.searchQuery.ScanningCodes = '';
      this.searchQuery.SerialNo = '';
      this.searchQuery.MaterialNo = obj.MaterialNo || '';
      this.searchQuery.MaterialName = obj.MaterialName || '';
      this.searchQuery.OrderQty = obj.OrderQty || '';
      this.searchQuery.Unit = obj.Unit || '';
      this.searchQuery.OrderType = obj.OrderType || '';
      this.searchQuery.HostProductionOrderNo = obj.HostProductionOrderNo || '';
      this.searchQuery.ProductionScheduler = obj.ProductionScheduler || '';
      this.searchQuery.StartTime = obj.StartTime || '';
      this.searchQuery.AssessmentType = obj.AssessmentType || '';
      this.searchQuery.Shippers = obj.Shippers || '';
      this.searchQuery.ReceivingLocation = obj.ReceivingLocation || '';
      this.searchQuery.ProductionScheduler = obj.ProductionScheduler || '';
      this.searchQuery.ProductionLineCode = obj.ProductionLineCode || '';
      this.searchQuery.ProductionLineDes = obj.ProductionLineDes || '';
    },
    keyupProductionOrderNo() {
      this.startLoading();
      const query = {
        ProductionOrderNo: this.searchQuery.ProductionOrderNo
      };
      GetOrderNoSerialNoByNo(query).then(res => {
        if (res.Code === 2000) {
          if (res.Data.length > 0) {
            this.GetWorkingProcedure();
            this.searchQuery.ScanningCode = '';
            this.searchQuery.ScanningCodes = '';
            this.searchQuery.SerialNo = '';
            this.searchQuery.MaterialNo = res.Data[0].MaterialNo || '';
            this.searchQuery.MaterialName = res.Data[0].MaterialName || '';
            this.searchQuery.OrderQty = res.Data[0].OrderQty || '';
            this.searchQuery.Unit = res.Data[0].Unit || '';
            this.searchQuery.OrderType = res.Data[0].OrderType || '';
            this.searchQuery.HostProductionOrderNo = res.Data[0].HostProductionOrderNo || '';
            this.searchQuery.ProductionScheduler = res.Data[0].ProductionScheduler || '';
            this.searchQuery.StartTime = res.Data[0].StartTime || '';
            this.searchQuery.AssessmentType = res.Data[0].AssessmentType || '';
            this.searchQuery.Shippers = res.Data[0].Shippers || '';
            this.searchQuery.ReceivingLocation = res.Data[0].ReceivingLocation || '';
            this.searchQuery.ProductionScheduler = res.Data[0].ProductionScheduler || '';
            this.searchQuery.ProductionLineCode = res.Data[0].ProductionLineCode || '';
            this.searchQuery.ProductionLineDes = res.Data[0].ProductionLineDes || '';
          } else {
            this.showNotify('warning', '当前生产订单无效');
          }
          this.endLoading();
        }
      }).catch(err => {
        console.log(err);
        this.endLoading();
      })
    }
  }
}
</script>
