<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="7">
            <el-form-item label="装配日期">
              <el-date-picker
                v-model="listQuery.dateValue"
                size="small"
                :clearable="false"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生产管理员">
              <el-input
                v-model="listQuery.ProductionScheduler"
                size="small"
                class="filter-item"
                placeholder="生产管理员"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户订单号">
              <el-input
                v-model="listQuery.OrderNo"
                size="small"
                class="filter-item"
                placeholder="客户订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单号">
              <el-input
                v-model="listQuery.SalesOrderNo"
                size="small"
                class="filter-item"
                placeholder="销售订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="装配线">
              <el-input
                v-model="listQuery.ProductionLineDes"
                size="small"
                class="filter-item"
                placeholder="装配线"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关键字">
              <el-input
                v-model="listQuery.keyword"
                size="small"
                class="filter-item"
                :placeholder="$t('Common.keyword')"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单行号">
              <el-input
                v-model="listQuery.SalesOrderLineNo"
                size="small"
                class="filter-item"
                placeholder="销售订单行号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="过账状态">
              <el-select
                v-model="listQuery.isPosted"
                size="small"
                :placeholder="$t('Common.postingStatus')"
                class="filter-item"
                style="width: 100%;"
              >
                <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-button
        v-waves
        v-permission="{name:'PP.PP_ProductionWarehousingController.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_ProductionWarehousingController.Audits'}"
        class="filter-item"
        type="danger"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePostingCancel"
      >取消过账状态
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_ProductionWarehousingController.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PP.PP_ProductionWarehousingController.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入销售刷字
      </el-button>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="生产订单号" prop="ProductionOrderNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="SerialNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="EAP出厂编号" prop="EapSerialNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="发货单位" prop="Shippers" width="140" align="center" show-overflow-tooltip />
      <el-table-column label="产品型号" prop="SerialNo" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getCaption(scope.row.MaterialName) }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column label="产品件号" prop="SerialNo" width="120" align="center" show-overflow-tooltip/> -->
      <el-table-column label="生产报工单号" prop="ProductionReportNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MaterialNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="MaterialName" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="销售单号" prop="SalesOrderNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="销售单行号" prop="SalesOrderLineNo" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="客户订单号" prop="CustomerOrderNum" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="合同号" prop="ZORD_CONT" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="报工数量" prop="ReportTotal" width="80" align="center" show-overflow-tooltip />
      <el-table-column label="完工入库数量" prop="QualifiedQty" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" width="80" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收货库存地点" prop="ReceivingLocation" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="过账人" prop="PostUser" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="100"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="100"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="SAP工单报工确认号" prop="SapDocNum" align="center" width="130" show-overflow-tooltip />
      <el-table-column label="生产调度员" prop="ProductionScheduler" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="线体编号" prop="ProductionLineCode" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="线体名称" prop="ProductionLineDes" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%" :close-on-click-modal="false">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  DoPost,
  Audits,
  ImportExcelToData
} from '@/api/Produce/Produce_Warehousing';

export default {
  name: 'PP.PP_ProductionWarehousingController',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        ProductionLineDes: '', // 线体
        ProductionScheduler: '',
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        OrderNo: '',
        ContractNo: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      tableHeight: '300px'
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    // this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 过账功能模块
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.ProductionScheduler.trim() !== this.listQuery.ProductionScheduler) {
            this.showNotify('warning', '生产订单号为：' + v.ProductionOrderNo + '信息，生产调度员信息不正确');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
          if (v.IsPosted === true) {
            this.showNotify('warning', '生产订单号为：' + v.ProductionOrderNo + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                // this.showNotify("success", res.Message || "Common.postSuccess");
                this.$alert(res.Message || 'Common.postSuccess', res.MessageParam === 2000 ? '成功' : '失败', {
                  confirmButtonText: '确定',
                  closeOnClickModal: false,
                  showCancelButton: false,
                  callback: action => {
                    this.handleFilter();
                    this.isProcessing = false;
                  }
                });
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              // this.handleFilter();
              // this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        ProductionLineDes: this.listQuery.ProductionLineDes,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        SalesOrderNo: this.listQuery.SalesOrderNo,
        SalesOrderLineNo: this.listQuery.SalesOrderLineNo,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '生产入库');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    getCaption(obj) {
      if (obj) {
        const index = obj.lastIndexOf('&');
        obj = obj.substring(index + 1, obj.length);
        return obj;
      }
    },
    // 取消过账状态
    handlePostingCancel() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === false) {
            this.showNotify('warning', '生产订单号为：' + v.ProductionOrderNo + '信息未过账，请先过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          const arrRowsID = this.multipleSelection.map(x => x.ID);
          Audits({
            Ids: arrRowsID
          })
            .then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', res.Message || 'Common.operationSuccess');
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      ImportExcelToData(this.uploadExcelData)
        .then((response) => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
          } else {
            this.showNotify('warning', 'Common.operationFailed');
          }
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    }
  }
};
</script>
