<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateTimes"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.FType"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_FTT.FType')"
        @change="handleFilter"
      >
        <el-option value :label="$t('Common.all')" />
        <el-option value="M" :label="$t('ui.RPT.RPT_FTT.M')" />
        <el-option value="P" :label="$t('ui.RPT.RPT_FTT.P')" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_FTT.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <!-- :summary-method="getSummaries"
    show-summary-->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="450px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.ProductionLineID')"
        prop="ProductionLineID"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.FTTID')"
        prop="FTTID"

        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.FType')"
        prop="FType"

        align="center"
        width="120"
        :formatter="FTypeFormatter"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.ProductID')"
        prop="ProductID"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.StationID')"
        prop="StationID"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.CreateDate')"
        prop="CreateDate"

        align="center"
        width="185"
        :formatter="formatDateTime"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.NCConditionDescription')"
        prop="NCConditionDescription"

        align="center"
        width="200"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.HandlingRecommendations')"
        prop="HandlingRecommendations"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.RejectQuantity')"
        prop="RejectQuantity"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.InputQuantity')"
        prop="InputQuantity"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_FTT.OutputQuantity')"
        prop="OutputQuantity"

        align="center"
        width="140"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import { fetchList, exportExcelFile } from '../../../api/RPT/RPT_FTT';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';
import { Row } from 'element-ui';

export default {
  name: 'RPT.RPT_FTT',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        FType: 'M',
        dateTimes: [
          new Date(),
          new Date()
        ]
      }
      // CountOptions: ["OutputQuantity", "InputQuantity", "RejectQuantity"]
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    FTypeFormatter(Row) {
      if (Row.FType === 'M') {
        return this.$i18n.t('ui.RPT.RPT_FTT.M');
      } else {
        return this.$i18n.t('ui.RPT.RPT_FTT.P');
      }
    },
    // getSummaries(param) {
    //   const { columns, data } = param;
    //   const sums = [];
    //   columns.forEach((column, index) => {
    //     if (index === 0) {
    //       sums[index] = this.$i18n.t("Common.CountTotal");
    //       return;
    //     }
    //     var findStr = String(column.property);
    //     var find = false;
    //     this.CountOptions.forEach(item => {
    //       if (item == findStr) {
    //         find = true;
    //       }
    //     });
    //     if (!find) {
    //       return;
    //     }
    //     console.log(findStr, find);
    //     const values = data.map(item => Number(item[column.property]));
    //     if (!values.every(value => isNaN(value))) {
    //       sums[index] = values.reduce((prev, curr) => {
    //         const value = Number(curr);
    //         if (!isNaN(value)) {
    //           return prev + curr;
    //         } else {
    //           return prev;
    //         }
    //       }, 0);
    //     } else {
    //       sums[index] = "";
    //     }
    //   });
    //   return sums;
    // },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      console.log(this.listQuery);
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        FType: this.listQuery.FType,
        dateTimes: this.listQuery.dateTimes
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, 'FTT日报')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
      }
      this.getList();
    }
  }
};
</script>
