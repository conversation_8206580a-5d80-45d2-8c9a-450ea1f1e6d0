<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateTimes"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_PO_InOut.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <!-- :summary-method="getSummaries"
    show-summary-->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="450px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.CTime')"
        prop="CTime"

        align="center"
        width="185"
        :formatter="formatDate"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.SupplierCode')"
        prop="SupplierCode"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.ItemCode')"
        prop="ItemCode"

        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.InQty')"
        prop="InQty"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.SQty')"
        prop="SQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.RQty')"
        prop="RQty"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_InOut.sumQty')"
        prop="sumQty"

        align="center"
        width="200"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import { fetchList, exportExcelFile } from '../../../api/RPT/RPT_PO_InOut';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';
import { Row } from 'element-ui';

export default {
  name: 'RPT.RPT_PO_InOut',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        dateTimes: [
          new Date(),
          new Date()
        ]
      }
      // CountOptions: ["OutputQuantity", "InputQuantity", "RejectQuantity"]
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      console.log(this.listQuery);
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        FType: this.listQuery.FType,
        dateTimes: this.listQuery.dateTimes
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '供应商交货 退货报告')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
      }
      this.getList();
    }
  }
};
</script>
