<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <!-- <el-date-picker
        v-model="dateValue"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-" :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />-->
      <el-input
        v-model="listQuery.ItemCode"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_StockDiff.ItemCode')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.RegionCode"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_StockDiff.RegionCode')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-document"
        :disabled="deletable"
        @click="handleAdjustForWMSToSap"
      >{{ $t("ui.RPT.RPT_StockDiff.ButtonGroup.PostAdjust") }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="480px"
      @sort-change="sortChange"
      @selection-change="handleSelectChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      />
      <!--<el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="220"
      />-->
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.RegionCode')"
        prop="RegionCode"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.RegionName')"
        prop="RegionName"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.WmsQty')"
        prop="WmsQty"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.NotPostQty')"
        prop="NotPostQty"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.WmsUnit')"
        prop="WmsUnit"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.SapQty')"
        prop="SapQty"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.SapUnit')"
        prop="SapUnit"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.DiffQty')"
        prop="DiffQty"
        align="center"
        width="150"
        fixed="right"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.DiffUnit')"
        prop="DiffUnit"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiff.ConsignQty')"
        prop="ConsignQty"
        align="center"
        width="150"
      />
    </el-table>
    <!-- <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />-->
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchStockDiffList,
  adjustForWMSToSap,
  exportExcelFile
} from '../../../api/RPT/RPT_StockDiff';
import { formatDate, formatDateTime } from '../../../utils';
import { exportToExcel } from '@/utils/excel-export';

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'RPT.RPT_StockDiff',
  components: {
    // Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        ItemCode: '',
        RegionCode: '',
        PageNumber: 1,
        PageSize: 20,
        fromTime: '',
        toTime: ''
      },
      selectedRowsData: [],
      dateValue: [
        new Date(),
        new Date()
      ],
      isProcessing: false,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {
    deletable() {
      const i = this.selectedRowsData.length;
      if (i === 0) return true;
      return false;
    }
  },
  created() {
    // this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      if (this.dateValue) {
        this.listQuery.fromTime = this.dateValue[0];
        this.listQuery.toTime = this.dateValue[1];
      }
      this.listLoading = true;
      var newListQuery = {
        ItemCode: this.listQuery.ItemCode,
        RegionCode: this.listQuery.RegionCode
      };

      console.log(newListQuery);
      fetchStockDiffList(newListQuery).then(res => {
        console.log('RPT_STOCKDiff.fetchList', res);
        if (res.Data.length <= 0) {
          this.$notify.info({
            title: i18n.t('Common.information'),
            message: i18n.t('Common.sapNoDiff')
          });
        }
        this.list = res.Data;
        // this.total = res.Data
        this.listLoading = false;
      });
    },
    handleSelectChange(selection) {
      this.selectedRowsData = selection;
    },
    handleAdjustForWMSToSap() {
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        console.log('RPT_STOCKDiff.handleAdjustForWMSToSap');
        this.listLoading = true;
        adjustForWMSToSap(this.selectedRowsData)
          .then(res => {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          })
          .catch(error => {
            this.listLoading = false;
          });
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      this.isProcessing = true;
      var exportQuery = {
        ItemCode: this.listQuery.ItemCode,
        RegionCode: this.listQuery.RegionCode
      };
      exportExcelFile(exportQuery)
        .then(res => {
          exportToExcel(res.data, 'SAP库存差异');
          this.isProcessing = false;
        })
        .catch(error => {
          this.isProcessing = false;
        });
    },
    sortChange(data) {}
  }
};
</script>
