<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="dateValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_DeliverySchedule.Export' }"
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="480px"
      @sort-change="sortChange"
    >
      <!--<el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
      />-->
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.PlanNum')"
        prop="PlanNum"
        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.DeliveryTime')"
        prop="DeliveryTime"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.SupplierCode')"
        prop="SupplierCode"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.SupplierName')"
        prop="SupplierName"
        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.BaseNum')"
        prop="BaseNum"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.BaseLine')"
        prop="BaseLine"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.ItemCode')"
        prop="ItemCode"
        align="center"
        width="160"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.RPT.RPT_DeliverySchedule.ItemName')"
        prop="ItemName"
        align="center"
        width="240"
      />
      <!--<el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.ItmsGrpCode')"
        prop="ItmsGrpCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.ItmsGrpName')"
        prop="ItmsGrpName"
        align="center"
        width="180"
      />-->
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.PurchaseOrderQty')"
        prop="PurchaseOrderQty"
        align="center"
        width="180"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.RPT.RPT_DeliverySchedule.PlanQty')"
        prop="PlanQty"
        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.DeliveryedQty')"
        prop="DeliveryedQty"
        align="center"
        width="180"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.OkQty')"
        prop="OkQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.NoQty')"
        prop="NoQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.NoInspectionQty')"
        prop="NoInspectionQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.StockInQty')"
        prop="StockInQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliverySchedule.DeliveryPlanStatus')"
        prop="DeliveryPlanStatus"
        fixed
        align="center"
        width="140"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchPurchaseDeliverySchedule as fetchList,
  exportExcelFileSchedule
} from '../../../api/RPT/PurchaseDelivery';
import { formatDate, formatDateTime } from '../../../utils';
import { exportToExcel } from '@/utils/excel-export';

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'RPT.RPT_DeliverySchedule',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 20,
        fromTime: '',
        toTime: ''
      },
      dateValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      if (this.dateValue) {
        this.listQuery.fromTime = this.dateValue[0];
        this.listQuery.toTime = this.dateValue[1];
      }
      this.listLoading = true;
      this.listQuery.dateValue = this.dateValue;
      fetchList(this.listQuery).then(res => {
        console.log('123123132', res);
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      // eslint-disable-next-line no-undef
      exportExcelFileSchedule({
        Keyword: this.listQuery.keyword,
        dateTimes: this.dateValue
      }).then(res => exportToExcel(res.data, '交货进度报表'));
    },
    sortChange(data) {}
  }
};
</script>
