<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.DocTypes"
        filterable
        size="small"
        multiple
        collapse-tags
        placeholder="单据类型"
        style="width: 200px"
        class="filter-item"
      >
        <el-option v-for="item in docTypesOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        clearable
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" size="small" @click="handleFilter" />
      <hr>

      <!-- -->
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_ItemMove.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :height="tableHeight"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <!-- <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" /> -->
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单据类型" prop="DocType" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="订单号" prop="BaseNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="订单行号" prop="BaseLine" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="客户编号" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="客户名称" prop="SupplierName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="库存单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="创建人" prop="CUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="创建时间"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="SAP凭证单号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="SAP凭证行号" prop="SapLine" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves/waves';
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination/index';
import {
  fetchList,
  exportExcelFile
} from '@/api/RPT/RPT_ItemMove';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  formatDate,
  formatDateTime
} from '../../../utils';

export default {
  name: 'RPT.RPT_ItemMove',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        DocTypes: []
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      docTypesOptions: [{
        label: '全部单据',
        key: ''
      }, {
        label: '期初库存',
        key: '期初库存'
      }, {
        label: '采购入库',
        key: '采购入库'
      }, {
        label: '采购退货',
        key: '采购退货'
      }, {
        label: '物料配送-出库',
        key: '物料配送-出库'
      }, {
        label: '物料配送-入库',
        key: '物料配送-入库'
      }, {
        label: '超额领料-出库',
        key: '超额领料-出库'
      }, {
        label: '超额领料-入库',
        key: '超额领料-入库'
      }, {
        label: '生产投料-出库',
        key: '生产投料-出库'
      }, {
        label: '完工入库',
        key: '完工入库'
      }, {
        label: '车间退料-出库',
        key: '车间退料-出库'
      }, {
        label: '车间退料-入库',
        key: '车间退料-入库'
      }, {
        label: '委外发料-发料',
        key: '委外发料-发料'
      }, {
        label: '委外发料-入库',
        key: '委外发料-入库'
      }, {
        label: '委外退料-发料',
        key: '委外退料-发料'
      }, {
        label: '委外退料-入库',
        key: '委外退料-入库'
      }, {
        label: '委外入库-发料',
        key: '委外入库-发料'
      }, {
        label: '委外入库-入库',
        key: '委外入库-入库'
      }, {
        label: '设备领料',
        key: '设备领料'
      }, {
        label: '借出单',
        key: '借出单'
      }, {
        label: '部门领料',
        key: '部门领料'
      }, {
        label: '部门退料',
        key: '部门退料'
      }, {
        label: '调拨-出库',
        key: '调拨-出库'
      }, {
        label: '调拨-入库',
        key: '调拨-入库'
      }, {
        label: '销售发货',
        key: '销售发货'
      }, {
        label: '销售退货',
        key: '销售退货'
      }],
      tableHeight: '300px'
    };
  },
  computed: {},
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      exportExcelFile({
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        DocTypes: this.listQuery.DocTypes
      }).then(res => {
        exportToExcel(res.data, '物料移动记录');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    }
  }
};
</script>
