<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        clearable
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.WhsCode"
        size="small"
        clearable
        class="filter-item"
        placeholder="仓库"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" size="small" @click="handleFilter" />
      <hr>

      <!-- -->
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_Stock.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :height="tableHeight"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="handleRowClick"
    >
      <!-- <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" /> -->
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="库存数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="库存单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="销售单号" prop="SaleNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售行号" prop="SaleLine" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessType" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="特殊库存" prop="SpecialStock" align="center" width="80" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves/waves';
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination/index';
import {
  fetchList,
  exportExcelFile
} from '@/api/RPT/RPT_Stock';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  formatDate,
  formatDateTime
} from '../../../utils';

export default {
  name: 'RPT.RPT_Stock',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      listDetailLoading: false,
      list: [],
      total: 0,
      listDetail: [],
      totalDetail: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        WhsCode: ''
      },
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 10,
        ItemCode: '',
        BinLocationCode: ''
      },
      tableHeight: '300px'
    };
  },
  computed: {},
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    IsConsignFormat(val) {
      if (val.Remark === '1') {
        return i18n.t('Dictionary.YesNoMap.YesValue');
      } else {
        return i18n.t('Dictionary.YesNoMap.NoValue');
      }
    },
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        console.log('RPT_STOCK.fetchList', res);
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.ItemCode = this.currentRow.ItemCode;
      this.listDetailQuery.BinLocationCode = this.currentRow.BinLocationCode;
      fetchList(this.listDetailQuery).then(res => {
        // console.log("RPT_STOCK.fetchList", res);
        this.listDetail = res.Data.items;
        this.totalDetail = res.Data.total;
        this.listDetailLoading = false;
      });
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      // this.handleFilter(2);
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      if (flag == 2) {
        // this.clearTables(2);
        if (this.currentRow) this.getDetailList();
      } else {
        // this.clearTables(3);
        this.listQuery.PageNumber = 1;
        this.listQuery.PageSize = 10;
        this.getList();
      }
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword,
        WhsCode: this.listQuery.WhsCode
      }).then(res => {
        exportToExcel(res.data, '库存报表');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    sortChangeDetial(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getDetailList();
    }
  }
};
</script>
