<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="dateValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="480px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.ItemCode')"
        prop="ItemCode"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.ItemName')"
        prop="ItemName"
        align="center"
        width="160"
      />
      <!--<el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      />-->
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.RegionCode')"
        prop="AdjustRegion"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.RegionName')"
        prop="AdjustRegion"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.AdjustQty')"
        prop="AdjustQty"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.AdjustUnit')"
        prop="AdjustUnit"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockDiffHistory.AdjustType')"
        prop="AdjustType"
        align="center"
        width="150"
      />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="185"
      />
    </el-table>
    <!-- <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />-->
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
// import Pagination from '../../../components/Pagination/index';
import {
  fetchList,
  exportExcelFile
} from '../../../api/RPT/RPT_StockDiffHistory';
import { formatDate, formatDateTime } from '../../../utils';
import { exportToExcel } from '@/utils/excel-export';

export default {
  name: 'RPT_RPT_StockDiffHistory',
  components: {
    // Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 20,
        fromTime: '',
        toTime: '',
        regionCode: ''
      },
      selectedRowsData: [],
      dateValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.dateValue
      };
      this.listLoading = true;
      fetchList(exportQuery).then(res => {
        console.log('RPT_STOCKDiffHistory.fetchList', res);
        this.list = res.Data;
        // this.total = res.Data
        this.listLoading = false;
      });
    },
    handleSelectChange(selection) {
      this.selectedRowsData = selection;
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.dateValue
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, 'SAP差异调整履历')
      );
    },
    sortChange(data) {}
  }
};
</script>
