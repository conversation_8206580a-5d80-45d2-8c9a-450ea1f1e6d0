<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateTimes"
        class="filter-item"
        :clearable="false"
        type="datetimerange"
        format="yyyy-MM-dd HH:mm"
        value-format="yyyy-MM-dd HH:mm"
        style="width: 240px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.DType"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_FTT.FType')"
        @change="handleFilter"
      >
        <el-option value="false" :label="$t('ui.RPT.RPT_FTT.M')" />
        <el-option value="true" :label="$t('ui.RPT.RPT_FTT.P')" />
      </el-select>
      <el-input
        v-model="listQuery.ItemCode"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_PLineOutPut.ItemCode')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.PLine"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_PLineOutPut.PLine')"
        @change="handleFilter"
      >
        <el-option
          v-for="item in pLineOptions"
          :key="item.EnumValue"
          :value="item.EnumValue"
          :label="item.Remark"
        />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_PLineOutPut.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <!-- :summary-method="getSummaries"
    show-summary-->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="450px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('ui.RPT.RPT_PLineOutPut.PLine')"
        prop="PLine"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PLineOutPut.ItemCode')"
        prop="ItemCode"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PLineOutPut.Qty')"
        prop="Qty"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PLineOutPut.SumQty')"
        prop="SumQty"
        align="center"
        width="120"
      />
      <!-- <el-table-column
        :label="$t('ui.RPT.RPT_FTT.FType')"
        prop="DType"
        align="center"
        width="120"
        :formatter="formatDType"
      /> -->
      <el-table-column
        :label="$t('ui.RPT.RPT_PLineOutPut.CTime')"
        prop="CTime"
        align="center"
        width="185"
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import { fetchList, exportExcelFile } from '../../../api/RPT/RPT_PLineOutPut';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';
import { Row } from 'element-ui';
_ = require('lodash');

export default {
  name: 'RPT.RPT_PLineOutPut',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      listLoading: false,
      list: [],
      total: 0,
      pLineOptions: [],
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        dateTimes: [
          new Date(),
          new Date()
        ],
        PLine: '',
        ItemCode: '',
        DType: 'false'
      }
      // CountOptions: ["OutputQuantity", "InputQuantity", "RejectQuantity"]
    };
  },
  computed: {},
  created() {
    this.getList();
    this.getPLineOptions();
  },
  methods: {
    formatDate,
    formatDateTime,
    formatDType(val) {
      console.log(val);
      if (val.DType === 'true') {
        return this.$i18n.t('ui.RPT.RPT_FTT.P');
      } else {
        return this.$i18n.t('ui.RPT.RPT_FTT.P');
      }
    },
    getList() {
      this.listLoading = true;
      console.log(this.listQuery);
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var newListQuery = {
        PLine: this.listQuery.PLine,
        ItemCode: this.listQuery.ItemCode,
        dateTimes: this.listQuery.dateTimes,
        DType: this.listQuery.DType
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '生产线产量报告')
      );
    },
    getPLineOptions() {
      if (this.showProductionLineSearch === false) {
        return;
      }
      this.getDict('PP005').then(data => {
        // console.log(1,data)
        data = _.uniqBy(data, x => x.EnumValue);
        data = _.sortBy(data, x => x.EnumValue);
        console.log(data);
        this.pLineOptions = data;
        // console.log(2,this.pLineOptions)
        this.pLineOptions.unshift({
          EnumValue: '',
          Remark: this.$i18n.t('Common.all')
        });
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        // if (this.pLineOptions && this.pLineOptions.length > 0) {
        //     this.listQuery.productionLine = this.pLineOptions[0].EnumValue;
        // }
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
      }
      this.getList();
    }
  }
};
</script>
