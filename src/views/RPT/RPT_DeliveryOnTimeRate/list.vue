<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="dateValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="480px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.SupplierCode')"
        prop="SupplierCode"

        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.SupplierName')"
        prop="SupplierName"

        align="center"
        width="240"
      />

      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.ItemCode')"
        prop="ItemCode"
        align="center"
        width="160"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.ItemName')"
        prop="ItemName"
        align="center"
        width="240"
      />
      <!--<el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.ItmsGrpCode')"
        prop="ItmsGrpCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.ItmsGrpName')"
        prop="ItmsGrpName"
        align="center"
        width="180"
      />-->
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.TotalDeliveryItem')"
        prop="TotalDeliveryItem"
        align="center"
        width="180"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.OnTimeDeliveryItem')"
        prop="OnTimeDeliveryItem"
        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.UnOnTimeDeliveryItem')"
        prop="UnOnTimeDeliveryItem"
        align="center"
        width="180"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.TotalQty')"
        prop="TotalQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.TotalDeliveryedQty')"
        prop="TotalDeliveryedQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.TotalUnOnTimeDeliveryQty')"
        prop="TotalUnOnTimeDeliveryQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_DeliveryOnTimeRate.OTDRate')"
        prop="OTDRate"
        align="center"
        width="120"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchPurchaseDeliveryOnTimeRate as fetchList,
  exportExcelFile
} from '../../../api/RPT/PurchaseDelivery';
import { formatDate, formatDateTime } from '../../../utils';
import { exportToExcel } from '@/utils/excel-export';

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'RPT.RPT_DeliveryOnTimeRate',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 20,
        fromTime: '',
        toTime: ''
      },
      dateValue: [
        new Date(),
        new Date()
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      if (this.dateValue) {
        this.listQuery.fromTime = this.dateValue[0];
        this.listQuery.toTime = this.dateValue[1];
      }
      this.listLoading = true;
      this.listQuery.dateValue = this.dateValue;
      fetchList(this.listQuery).then(res => {
        console.log('123123132', res);
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.dateValue
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '配货及时率')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    }
  }
};
</script>
