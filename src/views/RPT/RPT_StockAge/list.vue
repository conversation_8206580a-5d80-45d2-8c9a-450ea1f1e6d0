<template>
  <div class="app-container">
    <div class="filter-container">
      <!-- <el-date-picker
        v-model="lisQuery.dateValue"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-" :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />-->
      <el-select
        v-model="listQuery.isOverStep"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleSelectChangeValue"
      >
        <el-option
          v-for="item in isOverStepOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-select
        v-model="listQuery.productType"
        filterable
        :placeholder="$t('ui.PP.BarCode.productType')"
        style="width: 140px"
        class="filter-item"
        @change="handleSelectChangeValueP"
      >
        <el-option
          v-for="item in isProductTypeOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="480px"
      @sort-change="sortChange"
    >
      <!--<el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
      />-->
      <el-table-column
        v-if="false"
        :label="$t('ui.RPT.RPT_Stock.StockID')"
        prop="StockID"
        align="center"
        width="240"
      />

      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.BarCode')"
        prop="BarCode"
        align="center"
        width="160"
      />

      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.BatchNum')"
        prop="BatchNum"
        align="center"
        width="160"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.RPT.RPT_Stock.SupplierBatch')"
        prop="SupplierBatch"
        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.PTime')"
        prop="PTime"
        align="center"
        width="120"
        :formatter="formatDate"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockAge.AgeQty')"
        prop="AgeQty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_StockAge.StockAge')"
        prop="StockAge"
        align="center"
      />
      <!--<el-table-column
        :label="$t('ui.RPT.RPT_Stock.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="180"
      />-->
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.Qty')"
        prop="Qty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.Unit')"
        prop="Unit"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.SupplierCode')"
        prop="SupplierCode"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.SupplierName')"
        prop="SupplierName"
        align="center"
        width="240"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.RegionCode')"
        prop="RegionCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.RegionName')"
        prop="RegionName"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.BinLocationCode')"
        prop="BinLocationCode"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_Stock.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="140"
      />

      <!-- <el-table-column
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="240"
      />-->
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>

import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchStockAgeList,
  exportExcelFileAge
} from '../../../api/RPT/RPT_Stock';
import { formatDate, formatDateTime } from '../../../utils';
import { exportToExcel } from '@/utils/excel-export';

export default {
  name: 'RPT_RPT_StockAge',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 20,
        dateTimes: [
          new Date(),
          new Date()
        ],
        regionCode: '',
        isOverStep: 0,
        productType: 0
      },
      dateValue: [
        new Date(),
        new Date()
      ],
      isOverStepOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: 0
        },
        {
          label: this.$i18n.t('Common.notOverStep'),
          key: 1
        },
        {
          label: this.$i18n.t('Common.overStep'),
          key: 2
        }
      ],
      isProductTypeOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: 0
        },
        {
          label: this.$i18n.t('ui.PP.BarCode.product.half'),
          key: 1
        },
        {
          label: this.$i18n.t('ui.PP.BarCode.product.whole'),
          key: 2
        },
        {
          label: this.$i18n.t('ui.PP.BarCode.product.raw'),
          key: 3
        }
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      // var exportQuery = {
      //   PageNumber: this.listQuery.PageNumber,
      //   PageSize: this.listQuery.PageSize,
      //   keyword: this.listQuery.keyword,
      //   isOverStep: this.listQuery.regionCode,
      //   regionCode: this.listQuery.regionCode,
      //   dateTimes: [this.dateValue[0],this.dateValue[1]]
      // };
      this.listQuery.dateTimes = [];
      fetchStockAgeList(this.listQuery).then(res => {
        console.log('RPT_STOCK.fetchList', res);

        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
        console.log(res.Data.items);
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectChangeValue(val) {
      this.listQuery.isOverStep = val;
      this.getList();
    },
    handleSelectChangeValueP(val) {
      this.listQuery.productType = val;
      this.getList();
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateTimes,
        isOverStep: this.listQuery.isOverStep,
        productType: this.listQuery.productType
      };
      this.listQuery.dateTimes = [];
      exportExcelFileAge(exportQuery).then(res =>
        exportToExcel(res.data, '库龄报表')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    }
  }
};
</script>
