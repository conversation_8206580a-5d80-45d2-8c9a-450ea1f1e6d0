<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_BarCodeRetrospect.BarCodeS')"
        style="width: 220px"
        @keyup.enter.native="handleFilter(0)"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter(0)" />
      <hr>
      <el-button
        v-waves
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter(1)"
      >{{ $t("ui.RPT.RPT_BarCodeRetrospect.ButtonGroup.PO_In") }}</el-button>
      <!-- v-permission="{ name: 'RPT.RPT_BarCodeRetrospectS.PO_In' }" -->
      <el-button
        v-waves
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter(2)"
      >{{ $t("ui.RPT.RPT_BarCodeRetrospect.ButtonGroup.PP_Out") }}</el-button>
      <!-- v-permission="{ name: 'RPT.RPT_BarCodeRetrospectS.PP_Out' }" -->
      <el-button
        v-waves
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter(3)"
      >{{ $t("ui.RPT.RPT_BarCodeRetrospect.ButtonGroup.PP_In") }}</el-button>
      <!-- v-permission="{ name: 'RPT.RPT_BarCodeRetrospectS.PP_In' }" -->
      <el-button
        v-waves
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter(4)"
      >{{ $t("ui.RPT.RPT_BarCodeRetrospect.ButtonGroup.SD_Out") }}</el-button>
      <!-- v-permission="{ name: 'RPT.RPT_BarCodeRetrospectS.SD_Out' }" -->
      <el-button
        v-waves
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <!-- v-permission="{ name: 'RPT.RPT_BarCodeRetrospectS.Export' }" -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="480px"
      @sort-change="sortChange"
    >
      <!--<el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
      />-->

      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.BarCode')"
        prop="BarCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.ItemCode')"
        prop="ItemCode"
        align="center"
        width="180"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.ItemName')"
        prop="ItemName"
        align="center"
        width="180"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.docType')"
        prop="docType"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.DocNum')"
        prop="DocNum"
        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.BaseNum')"
        prop="BaseNum"
        align="center"
        width="200"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="220"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.CustomerName')"
        prop="CustomerName"
        align="center"
        width="220"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.BatchNum')"
        prop="BatchNum"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.Qty')"
        prop="Qty"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.RegionCode')"
        prop="RegionCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.RegionName')"
        prop="RegionName"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.BinLocationCode')"
        prop="BinLocationCode"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.CTime')"
        prop="CTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.RPT.RPT_BarCodeRetrospect.CUser')"
        prop="CUser"
        align="center"
        width="120"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchPage as fetchList,
  exportExcelFile
} from '../../../api/RPT/BarCodeRetrospect';
import { formatDate, formatDateTime } from '../../../utils';
import { exportToExcel } from '@/utils/excel-export';

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'RPT.RPT_BarCodeRetrospectS',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 20,
        cType: 0,
        searchType: 1
      },
      dateValue: [
        new Date(),
        new Date()
      ],
      downloadLoading: false,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        console.log('123123132', res);
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter(ctype) {
      this.listQuery.cType = ctype;
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList(ctype);
    },
    handleExport() {
      // eslint-disable-next-line no-undef
      var newListQuery = {
        keyword: this.listQuery.keyword,
        cType: this.listQuery.cType,
        searchType: this.listQuery.searchType
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '正向追溯报表')
      );
    },
    sortChange(data) {}
  }
};
</script>
