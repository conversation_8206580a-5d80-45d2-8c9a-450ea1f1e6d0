<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateTimes"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.States"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.RPT.RPT_PO_Inspection.States')"
        @change="handleFilter"
      >
        <el-option value="" :label="$t('Common.all')" />
        <el-option value="true" :label="$t('ui.RPT.RPT_PO_Inspection.yes')" />
        <el-option value="false" :label="$t('ui.RPT.RPT_PO_Inspection.no')" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'RPT.RPT_PO_Inspection.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <!-- :summary-method="getSummaries"
      show-summary -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="450px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.ItemCode')"
        prop="ItemCode"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.ItemName')"
        prop="ItemName"

        align="center"
        width="180"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.BatchNum')"
        prop="BatchNum"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.SupplierCode')"
        prop="SupplierCode"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.SupplierName')"
        prop="SupplierName"

        align="center"
        width="180"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.Qty')"
        prop="Qty"

        align="center"
        width="120"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.OkQty')"
        prop="OkQty"

        align="center"
        width="200"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.NoQty')"
        prop="NoQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.Status')"
        prop="Status"

        align="center"
        width="140"
        :formatter="StateFormatter"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.IUser')"
        prop="IUser"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.ITime')"
        prop="ITime"

        align="center"
        width="140"
        :formatter="formatDate"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.CTime')"
        prop="CTime"

        align="center"
        width="140"
        :formatter="formatDate"
      />
      <el-table-column
        :label="$t('ui.RPT.RPT_PO_Inspection.RM_FPY')"
        prop="RM_FPY"

        align="center"
        width="140"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import { fetchList, exportExcelFile } from '../../../api/RPT/RPT_PO_Inspection';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';
import { Row } from 'element-ui';

export default {
  name: 'RPT.RPT_PO_Inspection',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        States: '',
        dateTimes: [
          new Date(),
          new Date()
        ]
      }
      // CountOptions: ["OutputQuantity", "InputQuantity", "RejectQuantity"]
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    StateFormatter(Row) {
      if (Row.Status) {
        return this.$i18n.t('ui.RPT.RPT_PO_Inspection.yes');
      } else {
        return this.$i18n.t('ui.RPT.RPT_PO_Inspection.no');
      }
    },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      console.log(this.listQuery);
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        States: this.listQuery.States,
        dateTimes: this.listQuery.dateTimes
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, 'RM-FPY进料合格率')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
      }
      this.getList();
    }
  }
};
</script>
