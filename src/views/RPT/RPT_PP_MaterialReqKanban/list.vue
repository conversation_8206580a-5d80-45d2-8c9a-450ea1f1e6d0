<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateTimes"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.ProductionOrderStatus"
        filterable
        :placeholder="$t('ui.PP.ProductionOrder.ProductionOrderStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.Key"
          :label="item.Value"
          :value="item.Key"
        />
      </el-select>
      <el-select
        v-model="listQuery.productionLine"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.PP.ProductionOrder.ProductionLine')"
      >
        <el-option
          v-for="item in pLineOptions"
          :key="item.EnumValue"
          :value="item.EnumValue"
          :label="item.Remark"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <!-- :summary-method="getSummaries"
    show-summary-->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="450px"
      @sort-change="sortChange"
    >
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductionOrderID')"
        prop="ProductionOrderID"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductionLine')"
        prop="ProductionLine"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductID')"
        prop="ProductID"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductDescription')"
        prop="ProductDescription"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BinLocationCode')"
        prop="BinLocationCode"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BinLocationName')"
        prop="BinLocationName"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.MaterialID')"
        prop="MaterialID"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.MaterialDescription')"
        prop="MaterialDescription"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BOMUnitCode')"
        prop="BOMUnitCode"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BOMQty')"
        prop="BOMQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.PlanQty')"
        prop="PlanQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ConversionRate')"
        prop="ConversionRate"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.UnitCode')"
        prop="UnitCode"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.StockingQty')"
        prop="StockingQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.BufferQty')"
        prop="BufferQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.PreparedQty')"
        prop="PreparedQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.FeededQty')"
        prop="FeededQty"

        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.InventoryQty')"
        prop="InventoryQty"

        align="center"
        width="140"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PP.MaterialReqKanban.WarningStatus')"
        prop="WarningStatus"

        fixed="right"
        align="center"
        width="140"
      />
      <el-table-column
        :label="$t('ui.PP.MaterialReqKanban.ProductionOrderStatus')"
        prop="ProductionOrderStatus"

        fixed="right"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderStatus|OrderStates }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="160"
      />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchRPTPage,
  exportExcelFile
} from '../../../api/PP/PP_MaterialReqKanban';
import { fetchStatusList } from '@/api/PP/PP_ProductionOrder';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';
import { Row } from 'element-ui';
_ = require('lodash');

export default {
  name: 'RPT.RPT_PP_MaterialReqKanban',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        dateTimes: [
          new Date(),
          new Date()
        ],
        productionLine: '',
        ProductionOrderStatus: ''
      },
      statusOptions: [],
      pLineOptions: []
      // CountOptions: ["OutputQuantity", "InputQuantity", "RejectQuantity"]
    };
  },
  computed: {},
  created() {
    this.getStatusOptions();
    this.getPLineOptions();
    this.getList();
  },
  methods: {
    getStatusOptions() {
      if (this.showStatusSearch === false) {
        return;
      }
      fetchStatusList().then(response => {
        if (response.Code === 2000) {
          this.statusOptions = response.Data;
          this.statusOptions.unshift({
            Key: '',
            Value: this.$i18n.t('Common.all')
          });
          console.log(this.statusOptions);
          // if (this.statusOptions && this.statusOptions.length > 0) {
          //     this.listQuery.productionOrderStatus = this.statusOptions[0].Key;
          // }
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getPLineOptions() {
      if (this.showProductionLineSearch === false) {
        return;
      }
      this.getDict('PP005').then(data => {
        // console.log(1,data)
        data = _.uniqBy(data, x => x.EnumValue);
        data = _.sortBy(data, x => x.EnumValue);
        console.log(data);
        this.pLineOptions = data;
        // console.log(2,this.pLineOptions)
        this.pLineOptions.unshift({
          EnumValue: '',
          Remark: this.$i18n.t('Common.all')
        });
        // 如果主单没有生产线信息,默认选择下拉列表中的第一项
        // if (this.pLineOptions && this.pLineOptions.length > 0) {
        //     this.listQuery.productionLine = this.pLineOptions[0].EnumValue;
        // }
      });
    },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      console.log(this.listQuery);
      fetchRPTPage(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    handleFilter(flag) {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateTimes,
        productionLine: this.listQuery.productionLine,
        ProductionOrderStatus: this.listQuery.ProductionOrderStatus
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '生产看板报表')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = 'CTime desc';
      }
      this.getList();
    }
  }
};
</script>
