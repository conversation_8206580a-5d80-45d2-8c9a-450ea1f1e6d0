<template>
  <div class="app-container">
    <div v-if="user">
      <el-row :gutter="20">
        <el-col :span="6" :xs="24">
          <user-card :user="user" />
        </el-col>

        <el-col :span="18" :xs="24">
          <el-card>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="账号信息" name="account">
                <account :user="user" />
              </el-tab-pane>
              <el-tab-pane label="修改密码" name="updatepassword">
                <update-password :user="user" />
              </el-tab-pane>
              <!-- <el-tab-pane label="个人活动" name="activity">
                <activity />
              </el-tab-pane>
              <el-tab-pane label="个人时光" name="timeline">
                <timeline />
              </el-tab-pane> -->
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UserCard from './components/UserCard'
import Account from './components/Account'
import UpdatePassword from './components/UpdatePassword'
import md5 from 'js-md5';
// import Activity from './components/Activity'
// import Timeline from './components/Timeline'

export default {
  name: 'Profile',
  components: { UserCard, Account, UpdatePassword
    // ,Activity, Timeline
  },
  data() {
    return {
      user: {},
      activeTab: 'account'
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'roles',
      'userinfo'
    ])
  },
  created() {
    console.log('panjdfakfjdlk');
    if (this.$route.query.activeTab !== undefined) {
      this.activeTab = this.$route.query.activeTab
    }
    this.getData();
    // const permissionbuttons = this.$store.state.user.permissionbuttons.length>0?this.$store.state.user.permissionbuttons:JSON.parse(sessionStorage.getItem('permissionbuttons'))
    // const userff = this.$store.state.user.userinfo?this.$store.state.user.userinfo:JSON.parse(sessionStorage.getItem('userinfo'))
    // console.log('this.Navbar.permissionbuttons',permissionbuttons)
    // console.log('userff',userff)
    // console.log('this.user',this.user)
    // console.log('this.$store.state.sessionstorage',sessionStorage.getItem('permissionbuttons'))
  },
  methods: {
    getData() {
      this.user = {
        name: this.name,
        userinfo: this.userinfo,
        avatar: this.avatar,
        roles: this.roles.join(' | ')
      }
    }
  }
}
</script>
