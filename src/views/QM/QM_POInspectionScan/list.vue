<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isScan"
        filterable
        style="width: 140px"
        :placeholder="$t('ui.PO.PO_InspectionScan.IsScan')"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isPostOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspectionScan.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>

      <!-- v-permission="{name:'QM.QM_POInspectionScan.Posting'}" -->
      <el-button
        v-waves
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspectionScan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionScan.ScanID')"
        prop="ScanID"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.BarCode')"
        prop="BarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.BatchNum')"
        prop="BatchNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>

      <!--      <el-table-column :label="$t('ui.PO.PO_InspectionScan.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="220">-->
      <!--        <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.PO.PO_InspectionScan.WhsName')" prop="WhsName"   align="center" width="140">-->
      <!--        <template slot-scope="scope"> <span>{{ scope.row.WhsName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.RegionName')"
        prop="RegionName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.PO.PO_InspectionScan.IType')" prop="IType"   align="center" width="140">
      <template slot-scope="scope"> <span>{{ scope.row.IType }}</span> </template> </el-table-column>-->

      <el-table-column
        :label="$t('ui.PO.PO_ShelfScan.IsPosted')"
        prop="IsPosted"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ShelfScan.PostUser')"
        prop="PostUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ShelfScan.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
        :formatter="formatDateTime"
      />

      <el-table-column
        :label="$t('ui.PO.PO_InspectionScan.IsScan')"
        prop="IsScan"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsScan | yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import permission from '../../../directive/permission/permission';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  fetchList,
  batchDelete,
  exportExcelFile,
  doPost
} from '../../../api/QM/QM_POInspectionScan';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';

export default {
  name: 'TransferScan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      haveMovedData: false,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        dateValue: [
          new Date(new Date().setDate(new Date().getDate() - 7)),
          new Date(new Date().setDate(new Date().getDate() + 7))
        ],
        isScan: ''
      },
      hasPostedData: false,
      postDisableStatus: false,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      isScanOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.yes'),
          key: true
        },
        {
          label: this.$i18n.t('Common.no'),
          key: false
        }
      ],
      isPostOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.posted'),
          key: true
        },
        {
          label: this.$i18n.t('Common.notPosted'),
          key: false
        }
      ],
      multipleSelection: []
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0 || this.haveMovedData || this.hasPostedData;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0 ||
        this.hasPostedData ||
        this.postDisableStatus
      );
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.ScanID);
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    handlePosting() {
      console.log(this.multipleSelection);
      this.postDisableStatus = true;
      this.isProcessing = true;
      // 过账功能模块
      if (this.multipleSelection) {
        doPost(this.multipleSelection)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.postSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.postDisableStatus = false;
            this.isProcessing = false;
            this.getList();
            // eslint-disable-next-line handle-callback-err
          })
          .catch(err => {
            this.postDisableStatus = false;
            this.isProcessing = false;
            // this.getList()
          });
      }
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isScan: this.listQuery.isScan
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '退供封存区质检记录')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.haveMovedData = false;
      this.hasPostedData = false;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      var movedData = this.multipleSelection.find(item => {
        return item.IsScan === true;
      });
      if (movedData != undefined) {
        this.haveMovedData = true;
        console.log(this.haveMovedData);
      } else {
        this.haveMovedData = false;
      }
      if (postedData != undefined) {
        this.hasPostedData = true;
        console.log(this.hasPostedData);
      } else {
        this.hasPostedData = false;
      }
    }
  }
};
</script>
