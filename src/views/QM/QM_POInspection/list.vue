<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <!-- 质检状态 -->
      <el-select
        v-model="listQuery.IStatus"
        filterable
        :placeholder="$t('ui.PO.PO_Inspection.IStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option v-for="item in iStatusOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <!-- 过账状态 -->
      <!-- <el-select v-model="listQuery.isPosted" :placeholder="$t('Common.postingStatus')" style="width: 140px" class="filter-item">
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>-->

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspection.Qualify' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit-outline"
        size="small"
        :disabled="canPass"
        @click="handlePass"
      >{{ $t("Common.Inspection.qualify") }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspection.Disqualify' }"
        class="filter-item"
        type="danger"
        icon="el-icon-edit-outline"
        size="small"
        :disabled="isQualified"
        @click="handleAbort"
      >
        {{ $t("Common.Inspection.disqualify") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspection.Disqualify' }"
        class="filter-item"
        type="danger"
        icon="el-icon-edit-outline"
        size="small"
        :disabled="batchNoQualifiedDisable"
        @click="handleBatchAbort"
      >
        {{ $t("Common.Inspection.BatchDisqualify") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspection.Cancellation' }"
        class="filter-item"
        type="primary"
        icon="el-icon-close"
        size="small"
        :disabled="cancellation"
        @click="handleCancel"
      >{{ $t("Common.cancellation") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspection.Print' }"
        class="filter-item"
        type="primary"
        icon="el-icon-printer"
        size="small"
        :disabled="printDisable"
        @click="handlePrint"
      >{{ $t("Common.print") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'QM.QM_POInspection.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      height="300"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_Inspection.InspectionID')"
        prop="InspectionID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InspectionID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.DocNum')" prop="DocNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.NoteNum')" prop="NoteNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.NoteNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_Inspection.SupplierCode')"
        prop="SupplierCode"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.ItemCode')" prop="ItemCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.ItemName')" prop="ItemName" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_Inspection.SampleRange')"
        prop="SampleRange"
        align="center"
        width="200"
        :formatter="formatSampleRange"
      />
      <el-table-column
        :label="$t('ui.PO.PO_Inspection.InspectionGrade')"
        prop="InspectionGrade"
        align="center"
        width="180"
        :formatter="formatInspectionGrade"
      />
      <el-table-column :label="$t('ui.PO.PO_Inspection.IQty')" prop="IQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.SQty')" prop="SQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_Inspection.IStatus')"
        prop="IStatus"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.IStatus" type="primary">
            {{ $t("ui.PO.PO_Inspection.Inspected") }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t("ui.PO.PO_Inspection.Uninspected") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.IUser')" prop="IUser" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_Inspection.ITime')"
        prop="ITime"
        align="center"
        width="120"
        :formatter="formatDate"
      />

      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.PO.PO_InspectionDetailed.title") }}</span>
    </p>

    <!-- 质检明细 -->
    <el-table
      v-loading="listDetailLoading"
      :data="detailList"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="detailsortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.DetailedID')"
        prop="DetailedID"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.BarCode')"
        prop="BarCode"
        sortable
        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.ItemCode')"
        prop="ItemCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.ItemName')"
        prop="ItemName"
        sortable
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.ScanNum')"
        prop="ScanNum"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.DocNum')"
        prop="DocNum"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.BatchNum')"
        prop="BatchNum"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.NoteNum')"
        prop="NoteNum"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.NoteNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.BaseEntry')"
        prop="BaseEntry"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.BaseNum')"
        prop="BaseNum"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.BaseLine')"
        prop="BaseLine"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>

      <!--<el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.ItmsGrpCode')"
        prop="ItmsGrpCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.ItmsGrpName')"
        prop="ItmsGrpName"
        sortable
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <!--<el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InWhsCode')"
        prop="InWhsCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InWhsName')"
        prop="InWhsName"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.InRegionCode')"
        prop="InRegionCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InRegionName')"
        prop="InRegionName"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.InBinLocationCode')"
        prop="InBinLocationCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InBinLocationName')"
        prop="InBinLocationName"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.SupplierBatch')"
        prop="SupplierBatch"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.PTime')"
        prop="PTime"
        sortable
        align="center"
        width="120"
        :formatter="formatDate"
      />
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.Qty')" prop="Qty" sortable align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.OkQty')"
        prop="OkQty"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OkQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.NoQty')"
        prop="NoQty"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.NoQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.Unit')" prop="Unit" sortable align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.Status')"
        prop="Status"
        sortable
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.Status" type="primary">
            {{ $t("Dictionary.YesNoMap.YesValue") }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t("Dictionary.YesNoMap.NoValue") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.IsPosted')"
        prop="IsPosted"
        sortable
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.IsPosted" type="primary">
            {{ $t("Dictionary.YesNoMap.YesValue") }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t("Dictionary.YesNoMap.NoValue") }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!--质检不合格弹窗-->
    <el-dialog :title="formTitle" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="formData" label-position="right" label-width="100px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.DocNum')">
              <el-input v-model="formData.DocNum" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.BaseNum')">
              <el-input v-model="formData.BaseNum" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.SupplierName')">
              <el-input v-model="formData.SupplierName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.ItemName')">
              <el-input v-model="formData.ItemName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.Qty')">
              <el-input v-model="formData.Qty" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.OkQty')">
              <el-input-number v-model="formData.OkQty" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.NoQty')">
              <el-input-number v-model="formData.NoQty" :min="0" :max="formData.Qty" @change="okQty" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.PO.PO_InspectionDetailed.Unit')">
              <el-input v-model="formData.Unit" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item :label="$t('Common.Remark')">
              <el-input v-model="formData.Remark" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button v-waves type="primary" @click="handleSave">
          {{ $t("Common.save") }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import permission from '../../../directive/permission/permission';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchList,
  fetchDetailList,
  cancelInspection,
  qualifyPass,
  qualifyAbort,
  batchQualifyAbort,
  doPost,
  printToPDF,
  exportExcelFile
} from '../../../api/QM/QM_POInspection';
import {
  fetchList as fetchInspectionGradeList
} from '@/api/MD/MD_POInspectionGrade';
// import { getDictDescription } from '@/utils' // 列表内容格式化

export default {
  name: 'QM.QM_POInspection',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      isProcessing: false,
      list: [],
      detailList: [],
      total: 0,
      listLoading: false,
      postDisableStatus: false,
      listDetailLoading: false,
      hasPostedData: false, // 选中行含有已过账数据
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        isPosted: null,
        IStatus: null,
        dateValue: [
          new Date(new Date().setDate(new Date().getDate() - 7)),
          new Date(new Date().setDate(new Date().getDate() + 7))
        ]
      },
      SampleRangeOptions: [],
      InspectionGradeList: [],
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: null
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      iStatusOptions: [{
        label: this.$i18n.t('ui.PO.PO_Inspection.IStatusOption.All'),
        key: null
      },
      {
        label: this.$i18n.t(
          'ui.PO.PO_Inspection.IStatusOption.CheckedStatus'
        ),
        key: true
      },
      {
        label: this.$i18n.t(
          'ui.PO.PO_Inspection.IStatusOption.NoCheckedStatus'
        ),
        key: false
      }
      ],
      dialogFormVisible: false,
      formTitle: this.$i18n.t('Common.Inspection.disqualify'),
      formData: {
        DocNum: '',
        BaseNum: '',
        SupplierName: '',
        ItemName: '',
        Qty: '',
        OkQty: '',
        NoQty: '',
        Unit: '',
        Remark: ''
      },
      multipleSelection: [],
      selectedRow: null
    };
  },
  computed: {
    isQualified() {
      // 不合格操作
      // || this.hasPostedData
      const i = this.multipleSelection.length;
      let x = this.multipleSelection.length;
      while (x--) {
        if (this.multipleSelection[x].Status) return true;
      }
      // console.log(i !== 1 || this.hasPostedData)
      return i !== 1;
    },
    batchNoQualifiedDisable() {
      // 批量不合格
      let i = this.multipleSelection.length;
      while (i--) {
        if (this.multipleSelection[i].Status) return true;
      }
      // console.log(i !== 1 || this.hasPostedData)
      return !this.multipleSelection.length;
    },
    canPass() {
      let i = this.multipleSelection.length;
      while (i--) {
        if (this.multipleSelection[i].Status) return true;
      }
      // || this.hasPostedData
      return !this.multipleSelection.length;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0 ||
          this.hasPostedData ||
          this.postDisableStatus
      );
    },
    printDisable() {
      var printEnable =
          this.multipleSelection.length == 1 &&
          (this.multipleSelection[0].OkQty > 0 ||
            this.multipleSelection[0].NoQty > 0);
      return !printEnable;
    },
    // deletable() {
    //   return this.multipleSelection.length === 0 || this.hasPostedData
    // },
    cancellation() {
      if (
        !this.selectedRow ||
          !this.selectedRow.IStatus ||
          this.hasPostedData
      ) {
        return true;
      }
      return false;
    }
  },
  created() {
    this.getList();
    // 采样范围
    this.getDict('SYS005').then(data => {
      this.SampleRangeOptions = data;
      this.getList();
    });
    this.getInspectionGradeList();
  },
  methods: {
    formatDate,
    formatDateTime,
    formatSampleRange: function(row, column, currentValue) {
      const findedSampleRangeOptions = this.SampleRangeOptions.find(element => {
        return element.EnumKey === currentValue;
      });
      return findedSampleRangeOptions != null
        ? findedSampleRangeOptions.EnumValue
        : '';
    },
    formatInspectionGrade: function(row, column, currentValue) {
      const findedOptions = this.InspectionGradeList.find(element => {
        return element.GradeCode === currentValue;
      });
      return findedOptions != null ? findedOptions.GradeName : '';
    },
    getInspectionGradeList() {
      this.listLoading = true;
      fetchInspectionGradeList().then(response => {
        console.log('InspectionGradeList', response);
        this.InspectionGradeList = response.Data;
      });
    },
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        // Console.log(response)
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    getDetailList(doc) {
      this.listDetailLoading = true;
      fetchDetailList({
        docNum: doc
      }).then(res => {
        this.detailList = res.Data;
        this.listDetailLoading = false;
      });
    },
    handleFilter() {
      this.detailList = null;
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleCancel() {
      this.isProcessing = true;
      // 取消质检状态
      cancelInspection({
        docNum: this.selectedRow.DocNum
      })
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        })
        .catch(err => {
          this.isProcessing = false;
          // this.getList()
        });
    },
    handleRowClick(row) {
      // 调出子表数据
      this.selectedRow = row;
      this.getDetailList(row.DocNum);
    },
    handlePass() {
      this.isProcessing = true;
      // 质检合格
      qualifyPass(this.multipleSelection)
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
            this.getDetailList(this.selectedRow.DocNum);
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        })
        .catch(err => {
          this.isProcessing = false;
          // this.getList()
        });
    },
    handlePosting() {
      console.log(this.multipleSelection);
      this.postDisableStatus = true;
      // 过账功能模块
      if (this.multipleSelection) {
        doPost(this.multipleSelection)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.postSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.postDisableStatus = false;
            this.getList();
            // eslint-disable-next-line handle-callback-err
          })
          .catch(err => {
            this.postDisableStatus = false;
            // this.getList()
          });
      }
    },
    // 质检不合格模块开始
    handleAbort() {
      // 质检不合格,打开一个弹窗填写不合格原因，填写不合格数量
      this.dialogFormVisible = true;
      Object.assign(this.formData, this.multipleSelection[0]);
      this.okQty();
    },
    handleBatchAbort() {
      this.isProcessing = true;
      // 质检不合格,打开一个弹窗填写不合格原因，填写不合格数量
      batchQualifyAbort(this.multipleSelection)
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
            this.getDetailList(this.selectedRow.DocNum);
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        })
        .catch(err => {
          this.isProcessing = false;
          // this.getList()
        });
    },
    handleSave() {
      this.isProcessing = true;
      this.dialogFormVisible = false;
      this.startLoading();
      qualifyAbort(this.formData)
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
            this.handleClose();
          } else {
            this.showNotify('error', res.Message);
          }
          this.endLoading();
          this.isProcessing = false;
        })
        .catch(err => {
          this.isProcessing = false;
          this.endLoading();
          // this.getList()
        });
    },
    handleClose() {
      this.dialogFormVisible = false;
      this.getDetailList(this.selectedRow.DocNum);
    },
    // 质检不合格模块结束
    sortChange(column) {
      const {
        prop,
        order
      } = column;
      if (order !== null) {
        console.log('sort');
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter();
      }
    },
    detailsortChange(column) {
      const {
        prop,
        order
      } = column;
      if (order !== null) {
        console.log('sort');
        this.listQuery.sort = prop + ' ' + order;
        this.handleClose();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.hasPostedData = false;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });

      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    okQty() {
      this.formData.OkQty = this.formData.Qty - this.formData.NoQty;
    },
    handlePrint() {
      const selectRows = this.multipleSelection[0];
      // const barcodes = selectRows.map(v => v.BarCode)
      // console.log(barcodes)
      printToPDF({
        barCode: selectRows.BarCode
      }).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        state: this.listQuery.IStatus
      };
        // console.log(newListQuery);
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '采购质检管理')
      );
    }
  }
};
</script>
