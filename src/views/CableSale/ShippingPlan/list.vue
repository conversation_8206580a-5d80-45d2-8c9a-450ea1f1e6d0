<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-col :span="6">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="listQuery.CreateDate"
              :clearable="true"
              size="small"
              class="filter-item"
              type="daterange"
              style="width: 100%"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单交期">
            <el-date-picker
              v-model="listQuery.ShipmentDate"
              :clearable="true"
              size="small"
              class="filter-item"
              type="daterange"
              style="width: 100%"
              :picker-options="pickerOptions"
              range-separator="-"
              :unlink-panels="true"
              :start-placeholder="$t('Common.startTime')"
              :end-placeholder="$t('Common.endTime')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="发运状态">
            <el-select
              v-model="listQuery.PlanStatus"
              size="small"
              filterable
              placeholder="全部状态"
              style="width: 100%"
              class="filter-item"
            >
              <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下载状态">
            <el-select
              v-model="listQuery.ShipmentDownFlag"
              size="small"
              filterable
              placeholder="全部"
              style="width: 100%"
              class="filter-item"
            >
              <el-option v-for="item in PSStatusOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="关键字">
            <el-input
              v-model="listQuery.keyword"
              size="small"
              class="filter-item"
              :placeholder="$t('Common.keyword')"
              style="width: 100%"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户">
            <el-input
              v-model="listQuery.CustomerName"
              size="small"
              class="filter-item"
              placeholder="客户"
              style="width: 100%"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户单号">
            <el-input
              v-model="listQuery.CustomerOrderNum"
              size="small"
              class="filter-item"
              placeholder="订单号"
              style="width: 100%"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="合同号">
            <el-input
              v-model="listQuery.ContractNo"
              size="small"
              class="filter-item"
              placeholder="合同号"
              style="width: 100%"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="订单类型">
            <el-select v-model="listQuery.OrderTypeName" clearable style="width: 100%">
              <el-option
                v-for="item in OrderTypeList"
                :key="item.EnumValue"
                :value="item.EnumValue"
                :label="item.EnumValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="电梯类型">
            <el-input
              v-model="listQuery.ElevatorType"
              size="small"
              class="filter-item"
              placeholder="电梯类型"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
        </el-col>
      </el-form>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <el-button
        v-waves
        v-permission="{name:'Cable.ShippingPlan.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-dropdown
        v-waves
        v-permission="{ name: 'Cable.ShippingPlan.Export' }"
        class="filter-item"
        size="small"
        style="margin: 0 10px 10px;"
        split-button
        type="primary"
        @command="handleExport"
        @click="handleExport('导出')"
      >
        导出
      </el-dropdown>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column align="center" prop="ShipmentDownFlag" width="130px" show-overflow-tooltip label="下载">
        <template slot-scope="scope">
          <span>{{ scope.row.ShipmentDownFlag === 1 ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderTypeName" width="120px" show-overflow-tooltip label="订单类型">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ElevatorType" width="120px" show-overflow-tooltip label="电梯类型">
        <template slot-scope="scope">
          <span>{{ scope.row.ElevatorType }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerCode" width="120px" show-overflow-tooltip label="客户编码">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerName" width="180px" show-overflow-tooltip label="客户名称">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="CustomerOrderNum"
        width="180px"
        show-overflow-tooltip
        label="客户定单号"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ContractNo" width="180px" show-overflow-tooltip label="合同号">
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ShipmentDate" width="110px" show-overflow-tooltip label="要求发运日期">
        <template slot-scope="scope">
          <span>{{ formatDate('','',scope.row.ShipmentDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="BatchNum" width="180px" show-overflow-tooltip label="批次">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SerialNo" width="180px" show-overflow-tooltip label="顺序号">
        <template slot-scope="scope">
          <span>{{ scope.row.SerialNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ShipmentStatus" width="80px" show-overflow-tooltip label="发运状态">
        <template slot-scope="scope">
          <span>{{ getShippingStatus(scope.row.ShipmentStatus) }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column align="center" prop="CUser" width="110px" show-overflow-tooltip label="制单人">-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{ scope.row.CUser }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column align="center" prop="OrderDate" width="110px" show-overflow-tooltip label="制单日期">
        <template slot-scope="scope">
          <span>{{ formatDate('','',scope.row.OrderDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderNum" width="120px" show-overflow-tooltip label="订单编号">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="LineNum" width="90px" show-overflow-tooltip label="订单行">
        <template slot-scope="scope">
          <span>{{ scope.row.LineNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapNo" width="130px" show-overflow-tooltip label="SAP销售单号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapLine" width="130px" show-overflow-tooltip label="SAP销售行号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="SetShipmentUser" align="center" width="100" />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="SetShipmentDate"
        align="center"
        width="150"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  exportExcel,
  batchDelete,
  fetchList
} from '@/api/CableSale/ShippingPlan';
import { fetchList as dictionaryFetchList } from '@/api/Sys/Sys_Dictionary';

export default {
  name: 'SD.SD_ShippingPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 20,
        CreateDate: [
          new Date(),
          new Date()
        ],
        ShipmentDate: [
        ],
        PlanStatus: '',
        Customer: '',
        CustomerOrderNum: '',
        CONT: '',
        ShipmentDownFlag: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '未发运',
        key: 0
      },
      {
        label: '已同步',
        key: 1
      },
      {
        label: '发运中',
        key: 2
      },
      {
        label: '已完成',
        key: 3
      }
      ],
      PSStatusOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '已下载',
        key: '1'
      },
      {
        label: '未下载',
        key: '0'
      }],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ShippingPlan') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
    this.initOrderType();
  },
  methods: {
    initOrderType() {
      // 订单类型
      dictionaryFetchList({
        typeCode: 'CableOrderType'
      }).then(response => {
        this.OrderTypeList = response.Data;
        this.OrderTypeList = this.OrderTypeList.sort((a, b) => a.EnumKey - b.EnumKey);
        this.fullscreenLoading = false;
      }).catch((c) => {
        this.fullscreenLoading = false;
      });
    },
    getShippingStatus(status) {
      switch (status) {
        case 0:
          return '未发运';
        case 1:
          return '已发运';
      }
    },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        console.log(this.list);
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport(command) {
      this.isProcessing = true;
      const ids = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        ids.push(this.multipleSelection[i].Id)
      }
      this.listQuery.Ids = ids;
      const query = Object.assign({}, this.listQuery);
      exportExcel(query).then(res => {
        exportToExcel(res.data, '发运计划_' + res.fileName);
        this.handleFilter();
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    // 打印
    handlePrint(command) {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      var docNums = selectRows.map(v => v.DocNum);
      if (this.checkSingleSelection(selectRows)) {
        printOrderToPDF({
          docNums: docNums,
          PrintType: command
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.ShippingPlanStatus === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步，请勿删除');
          switchBtn = false;
          return true
        }
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿删除');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const Ids = selectRows.map(v => v.Id);
          // 删除逻辑处理
          batchDelete(Ids)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('SD.SD_ShippingPlanDetail');
    },
    handleUpdate() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.ShippingPlanStatus === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步，请勿编辑');
          switchBtn = false;
          return true
        }
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿编辑');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.routeTo('SD.SD_ShippingPlanDetail', Object.assign(selectRows[0]));
      }
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancel() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    }
  }
};
</script>
