<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form :model="model">
        <el-form-item label="数量" label-width="120px">
          <el-input v-model="model.ShippingPlanDetailQty" placeholder="" />
        </el-form-item>
        <el-form-item label="备注" label-width="120px">
          <el-input v-model="model.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        Quantity: '',
        TotalDeliveredQuantity: ''
      }
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.drawer = true;
      this.model = Object.assign({}, record);
    },
    handleSave() {
      this.$emit('ok', this.model);
      this.drawer = false;
    }
  }
}
</script>

  <style scoped>
    .demo-drawer__content {
      padding: 20px;
    }

    .demo-drawer__footer {
      display: flex;
    }

    .demo-drawer__footer button {
      flex: 1;
    }

  </style>
