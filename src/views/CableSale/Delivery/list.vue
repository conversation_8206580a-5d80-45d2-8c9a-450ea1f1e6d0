<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.CreateDate"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.BaseType"
        size="small"
        filterable
        placeholder="订单类型"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in BaseTypeOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.BaseNum"
        size="small"
        class="filter-item"
        placeholder="销售订单号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.BaseLine"
        size="small"
        class="filter-item"
        placeholder="销售订单行号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CustomerName"
        size="small"
        class="filter-item"
        placeholder="客户名"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ItemCode"
        size="small"
        class="filter-item"
        placeholder="物料件号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.BarCode"
        size="small"
        class="filter-item"
        placeholder="出厂编号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CONT"
        size="small"
        class="filter-item"
        placeholder="合同号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-input
        v-model="listQuery.CUser"
        size="small"
        class="filter-item"
        placeholder="创建人"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.SAPmark"
        size="small"
        filterable
        placeholder="错误消息"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isSapMessageOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{name:'Cable.Delivery.Delete'}"
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.Delivery.Post'}"
        class="filter-item"
        type="success"
        size="small"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.Delivery.UnPost'}"
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePassPosting"
      >
        取消过账
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'Cable.Delivery.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t('Common.export') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'Cable.Delivery.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入模板
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'Cable.Delivery.ImportTempDown' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModel"
      >下载模板
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      height="800"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="交货单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售单号" prop="SapNo" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售单行号" prop="SapLine" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="Quantity" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="订单类型" prop="OrderType" align="center" width="80px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.OrderType === 1 || scope.row.OrderType === '1' ">电缆箱</span>
          <span v-if="scope.row.OrderType === 2 || scope.row.OrderType === '2' ">其他</span>
        </template>
      </el-table-column>
      <el-table-column label="物料编码" prop="MaterialCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="客户编号" prop="CustomerCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="客户名称" prop="CustomerName" align="center" width="200" show-overflow-tooltip />
      <el-table-column label="合同号" prop="ContractNo" align="center" width="200" show-overflow-tooltip />
      <el-table-column label="发货仓库" prop="StoreCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="150"
        :formatter="formatDateTime"
      />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPost | yesnoFilter }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      :page-sizes.sync="pageSizes"
      @pagination="getList"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t('Common.cancel') }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t('Common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelModel,
  createBatch,
  post,
  batchDelete, cancelPost
} from '@/api/CableSale/Delivery';

export default {
  name: 'SD.SD_ProductionExecutionScheduling',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        CreateDate: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        BaseNum: '', //  销售订单号
        BaseLine: '',
        CustomerName: '', //  客户名
        ItemCode: '', //   物料件号
        BarCode: '', //  出厂编号
        CONT: '', // 合同号
        SAPmark: '正常', // 错误消息
        CUser: '', // 创建人
        BaseType: '' // 订单类型
      },
      pageSizes: [10, 50, 100, 200, 500],
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      isSapMessageOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '正常',
        key: '正常'
      },
      {
        label: '错误',
        key: '错误'
      }
      ],
      BaseTypeOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '电缆箱',
        key: '1'
      },
      {
        label: '其他',
        key: '2'
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 50;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        BaseNum: this.listQuery.BaseNum,
        BaseLine: this.listQuery.BaseLine,
        CustomerName: this.listQuery.CustomerName,
        ItemCode: this.listQuery.ItemCode,
        BarCode: this.listQuery.BarCode,
        CONT: this.listQuery.CONT,
        SAPmark: this.listQuery.SAPmark,
        CUser: this.listQuery.CUser,
        BaseType: this.listQuery.BaseType
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, this.$moment(new Date()).format('YYYY-MM-DD') + '销售交货');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          const ids = this.multipleSelection.map(v => v.Id);
          post(ids)
            .then(res => {
              if (res.Code === 2000) {
                this.$alert(res.Message || 'Common.postSuccess', res.MessageParam === 2000 ? '成功'
                  : '失败', {
                  confirmButtonText: '确定',
                  closeOnClickModal: false,
                  showCancelButton: false,
                  callback: action => {
                    this.handleFilter();
                    this.isProcessing = false;
                  }
                });
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    // 取消过账
    handlePassPosting() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === false) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，无法取消');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          const ids = this.multipleSelection.map(v => v.Id);
          cancelPost(ids)
            .then(res => {
              if (res.Code === 2000) {
                this.$alert(res.Message || 'Common.postSuccess', res.MessageParam === 2000 ? '成功'
                  : '失败', {
                  confirmButtonText: '确定',
                  closeOnClickModal: false,
                  showCancelButton: false,
                  callback: action => {
                    this.handleFilter();
                    this.isProcessing = false;
                  }
                });
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const ids = selectRows.map(v => v.Id);
          // 删除逻辑处理
          batchDelete(ids)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        DocNum: this.currentRow.DocNum.trim()
      });
      GetPageDetailList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleConsignmentNote() {
      this.isProcessing = true;
      ManualMakeConsignmentNote(this.multipleSelection)
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.postSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
          this.isProcessing = false;
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    // 打印
    handlePrint() {
      const selectRows = this.multipleSelection;
      console.log('打印：', selectRows);
      this.isProcessing = true;
      if (this.checkMultiSelection(selectRows)) {
        const docNums = selectRows.map(v => v.DocNum);
        console.log(docNums);
        printOrderToPDF({
          docNums: docNums
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      } else {
        this.isProcessing = false;
      }
    },
    handleCommand(command) {
      this.isProcessing = true;
      if (command === '1') {
        this.handlePrint();
      } else if (command === '2') {
        const selectRows = this.multipleSelection;
        const docNums = selectRows.map(v => v.DocNum);
        console.log(docNums);
        Print_SettlementAdd({
          docNums: docNums
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImportData(data) {
      const obj = {};
      for (let i = 0;i < data.length;i++) {
        obj['ShippingNo'] = data[i]['发运单号']
        obj['OmsOrderNum'] = data[i]['订单编号']
        obj['OmsLineNum'] = data[i]['订单行号']
        obj['OrderType'] = data[i]['订单类型']
        obj['SapNo'] = data[i]['销售单号']
        obj['SapLine'] = data[i]['销售行号']
        obj['MaterialCode'] = data[i]['物料编码']
        obj['CustomerPartNo'] = data[i]['客户件号']
        obj['SpecificationModel'] = data[i]['规格型号']
        obj['Quantity'] = data[i]['数量']
        obj['Unit'] = data[i]['单位']
        obj['SingleQuantity'] = data[i]['单根数量']
        obj['FourLineGroup'] = data[i]['四线组']
        obj['Classify'] = data[i]['分类']
        obj['MaterialDes'] = data[i]['物料描述']
        obj['ContractNo'] = data[i]['合同号']
        obj['DeliveryNo'] = data[i]['交货单号']
        obj['StoreCode'] = data[i]['仓库代码']
        obj['CustomerCode'] = data[i]['客户代码']
        obj['CustomerName'] = data[i]['客户名称']
        obj['Remark'] = data[i]['备注']
        this.uploadExcelData.push(obj);
      }
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      createBatch(this.uploadExcelData)
        .then((response) => {
          if (response.Code === 2000) {
            this.showNotify('success', response.Message);
          } else {
            this.showNotify('warning', '导入失败');
          }
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImportData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    handlePassPost() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === false) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，禁止冲销');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.isProcessing = true;
        const docNums = selectRows.map(v => v.DocNum);
        PassPost(docNums).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '操作成功！');
            } else {
              this.showNotify('warning', res.Message);
            }
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    }
  }
};
</script>
