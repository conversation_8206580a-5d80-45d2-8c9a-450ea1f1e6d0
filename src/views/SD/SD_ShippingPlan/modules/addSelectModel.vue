<template>
  <el-dialog title="销售信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <span style="line-height: 36px;vertical-align: top;">交货日期：</span><el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleSearchFilter">
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="销售凭证" prop="VBELN" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="项目" prop="POSNR" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MATNR" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MAKTX" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="订单数量" prop="KWMENG" align="center" show-overflow-tooltip />
      <el-table-column label="重量" prop="NTGEW" align="center" show-overflow-tooltip />

      <!-- <el-table-column label="销售凭证类型" prop="AUART" align="center" show-overflow-tooltip />
      <el-table-column label="销售组织" prop="VKORG" align="center" show-overflow-tooltip />
      <el-table-column label="分销渠道" prop="VTWEG" align="center" show-overflow-tooltip />
      <el-table-column label="产品组" prop="SPART" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="客户参考" prop="BSTNK" align="center" show-overflow-tooltip /> -->
      <el-table-column label="售达方" prop="KUNNR" align="center"width="160" show-overflow-tooltip />
      <el-table-column label="售达方名称" prop="KUNNRNAME1" align="center"width="160" show-overflow-tooltip />
      <el-table-column label="售达方地址" prop="KUNNRADDRESS" align="center"width="160" show-overflow-tooltip />
      <!-- <el-table-column label="项目类别" prop="PSTYV" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="净值" prop="NETWR" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="凭证货币" prop="WAERK" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="参考凭证" prop="VGBEL" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="参考项目" prop="VGPOS" align="center" show-overflow-tooltip /> -->
      <el-table-column label="库存地点" prop="LGORT" align="center" show-overflow-tooltip />
      <el-table-column
        label="承诺的交货日期"
        prop="CMTD_DELIV_DATE"
        align="center"
        width="120"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="销售交货批" prop="ZDELBA" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="合同编号" prop="ZORD_CONT" align="center" show-overflow-tooltip />
      <el-table-column label="生产主机编号/客户出厂编号" prop="ZORD_OUTNO" width="170" align="center" show-overflow-tooltip />
      <el-table-column label="装运点/收货点" prop="VSTEL" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="利润中心" prop="PRCTR" align="center" show-overflow-tooltip /> -->
      <el-table-column
        label="创建日期"
        prop="ERDAT"
        align="center"
        width="120"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="时间"
        prop="ERZET"
        align="center"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="凭证日期"
        prop="AUDAT"
        align="center"
        width="120"
        :formatter="formatDate"
        show-overflow-tooltip
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetXZSAP_PurchaseOrder
} from '@/api/SD/SD_ShippingPlan';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //

export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        dateValue: [
          new Date(new Date().setDate(new Date().getDate() - 1)),
          new Date(new Date().setDate(new Date().getDate() + 1))
        ],
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      }
    }
  },
  computed: {

  },
  created() {},
  methods: {
    formatDate,
    formatDateTime,
    add() {
      this.listQuery = {
        keyword: '',
        dateValue: [
          new Date(new Date().setDate(new Date().getDate() - 1)),
          new Date(new Date().setDate(new Date().getDate() + 1))
        ],
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      let switchBtn = true;
      selection.some(v => {
        if (v.KWMENG === 0 || v.KWMENG === null || v.KWMENG === '0') {
          this.showNotify('warning', '销售凭证为：' + v.VBELN + '订单数量为0，禁止添加');
          switchBtn = false;
          return true
        } else if (v.NTGEW === 0 || v.NTGEW === null || v.NTGEW === '0') {
          this.showNotify('warning', '销售凭证为：' + v.VBELN + '重量为0，禁止添加');
          switchBtn = false;
          return true;
        } else {
          if (this.dataList.length > 0) {
            this.dataList.some(res => {
              if (v.VBELN + v.POSNR === res.onlyId) {
                this.showNotify('warning', '销售凭证为：' + v.VBELN + '已选择，请勿重复选择');
                switchBtn = false;
                return true;
              }
            })
          }
        }
      });
      if (switchBtn) {
        this.multipleSelection = selection;
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetXZSAP_PurchaseOrder(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      this.$emit('ok', this.multipleSelection);
      this.dialogCustomerFormVisible = false;
    }
  }
}
</script>

<style scoped>
</style>
