<template>
  <div class="app-container">
    <p>
      <label style="width:100%">发运计划登记单</label>
    </p>

    <el-row :gutter="20">
      <el-col :span="8">
        <!-- <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input> -->
        <el-tree
          ref="tree"
          :props="props"
          :load="loadNode"
          lazy
          show-checkbox
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          @current-change="handleCheckChange"
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span>{{ node.label }}</span>
          </span>
        </el-tree>
      </el-col>
      <el-col :span="16">
        <el-form
          ref="dataForm"
          class="formBox"
          :inline="true"
          :rules="rules"
          :model="searchQuery"
          label-position="right"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item class="filter-item" label="单号">
                <el-input v-model="searchQuery.DocNum" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="销售信息">
                <el-input v-model="searchQuery.purchase" placeholder="" readonly>
                  <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划发运时间">
                <el-date-picker
                  v-model="searchQuery.DeliveryDate"
                  :clearable="false"
                  type="date"
                  placeholder="选择发运时间"
                  format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注">
                <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <p>
          <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
            {{ $t("Common.delete") }}</el-button>
          <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
          </el-button>
        </p>

        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          style="width: 100%"
          size="mini"
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          @selection-change="handleSelectionChange"
        >
          <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed="left" />
          <el-table-column label="销售单号" prop="SalesOrderNumber" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="销售单行号" prop="SalesLine" align="center" width="160" show-overflow-tooltip />
          <el-table-column
            label="凭证日期"
            prop="VoucherDate"
            align="center"
            width="160"
            :formatter="formatDateTime"
            show-overflow-tooltip
          />
          <!-- <el-table-column label="销售类型" prop="SalesType" align="center" width="160" show-overflow-tooltip />
    <el-table-column label="销售组织" prop="SalesOrganization" align="center" width="160" show-overflow-tooltip /> -->
          <!-- <el-table-column label="分销渠道" prop="DistributionChannels" align="center" width="160" show-overflow-tooltip /> -->
          <!-- <el-table-column label="产品组" prop="ProductGroup" align="center" width="160" show-overflow-tooltip /> -->
          <el-table-column label="客户" prop="CustomerCode" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="客户名称" prop="CustomerName" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="客户地址" prop="CustomerAdd" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="物料" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="物料名称" prop="ItemName" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="项目类别" prop="ProjectCatery" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="交运数量" prop="ShippingPlanDetailQty" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="仓库" prop="WhsCode" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="批次" prop="BatchNum" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="合同号" prop="CONT" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="出厂编号" prop="OUTNO" align="center" width="160" show-overflow-tooltip />
          <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
          <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
            <template slot-scope="scope">
              <span @click="toggle(scope.row)">编辑</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <add-select-model ref="modalForm" :data-list="list" @ok="modalFormOk" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  update
} from '@/api/SD/SD_ShippingPlan';
export default {
  name: 'SD.SD_ShippingPlanDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        purchase: '',
        DocNum: '',
        Remark: '',
        DeliveryDate: new Date(new Date().setDate(new Date().getDate() + 1))
      },
      multipleSelection: [],
      rules: {

      },
      editStatus: 'create',
      delList: [],

      filterText: '',
      props: {
        label: 'name',
        children: 'zones',
        isLeaf: 'leaf'
      }
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getPageParams();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ShippingPlanDetailID) {
            v.IsDelete = true;
            this.delList.push(v.ShippingPlanDetailID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList);
        });
      }
    },
    handleCommit() {
      const switchBtn = true;
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择销售信息');
        return;
      }
      if (!this.searchQuery.DeliveryDate) {
        this.showNotify('warning', '请选择计划发运时间');
        return;
      }
      if (switchBtn) {
        this.startLoading();
        const query = {
          DocNum: this.searchQuery.DocNum,
          Remark: this.searchQuery.Remark,
          DeliveryDate: this.searchQuery.DeliveryDate,
          detaileds: this.list,
          deleteDetailArray: this.delList
        };
        if (this.editStatus === 'create') {
          SubmitScanInfo(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('SD.SD_ShippingPlan');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        } else {
          update(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('SD.SD_ShippingPlan');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.ShippingPlanDetailID) {
          if (v.ShippingPlanDetailID === record.ShippingPlanDetailID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.onlyId === record.onlyId) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          SalesOrderNumber: v.VBELN, // 销售单号
          VoucherDate: v.AUDAT, // 凭证日期
          SalesType: v.AUART, // 销售类型
          SalesOrganization: v.VKORG, // 销售组织
          DistributionChannels: v.VTWEG, // 分销渠道
          ProductGroup: v.SPART, // 产品组
          CustomerCode: v.KUNNR, // 客户
          SalesLine: v.POSNR, // 项目
          ItemCode: v.MATNR, // 物料
          ProjectCatery: v.PSTYV, // 项目类别
          ShippingPlanDetailQty: v.KWMENG, // 交运数量
          WhsCode: v.LGORT, // 仓库
          BatchNum: v.ZDELBA, // 批次
          ReferenceDocuments: v.VGBEL, // 参考凭证
          ReferenceProject: v.VGPOS, // 参考项目
          ProjectCategory: v.PSTYV, // 项目类别
          CustomerName: v.KUNNRNAME1, // 客户名称
          CustomerAdd: v.KUNNRADDRESS, // 客户地址
          ItemName: v.MAKTX, // 物料名称
          CONT: v.ZORD_CONT, //  合同单号
          OUTNO: v.ZORD_OUTNO, // 生产主机编号
          PRCTR: v.PRCTR, // 利润中心
          VGBEL: v.VGBEL, // 参考凭证
          VGPOS: v.VGPOS, // 参考项目
          // CONT: v.ZORD_CONT, // 合同号
          // OUTNO: v.ZORD_OUTNO, //出厂编号
          Unit: v.MEINS, // 单位
          onlyId: v.VBELN + v.POSNR
        })
      });

      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.onlyId] ? '' : obj[next.onlyId] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        keyword: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      console.log(this.searchQuery, 1);
      if (this.searchQuery.ShippingPlanID) {
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        return resolve([{
          name: '催货日期'
        }]);
      }
      if (node.level > 1) return resolve([]);

      setTimeout(() => {
        const data = [{
          name: 'leaf',
          leaf: true
        }, {
          name: 'zone'
        }];
        resolve(data);
      }, 500);

      // if (node.level === 0) {
      //   store.dispatch('personnel/getWorkerTree', {
      //     parentId: 1
      //   }).then(data => {
      //     this.data2 = data.departmentList
      //     return resolve(this.data2)
      //   })
      // }
      // if (node.level >= 1) {
      //   let dataList = []
      //   if (node.data) {
      //     if (node.data.leaf === true) {
      //     } else if (node.data.leaf === false) {
      //       store.dispatch('personnel/getWorkerTree', {
      //         parentId: node.data.id
      //       }).then(data => {
      //         dataList = data.departmentList
      //         resolve(dataList)
      //       })
      //     }
      //   }
      // }
      // if (node.level > 10) {}
      // return resolve([])
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheckChange(data, checked, indeterminate) {
      console.log(checked);
      // if (checked.level === 2) {
      //   this.ruleForm.username = checked.label
      //   this.dialogVisible = false
      // }
    },
    handleNodeClick(data) {
      if (data.leaf === true) {

      }
    }
  }
}
</script>
