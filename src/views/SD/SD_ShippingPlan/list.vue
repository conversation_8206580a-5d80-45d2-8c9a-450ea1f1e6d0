<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.PlanStatus"
        size="small"
        filterable
        placeholder="全部状态"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.PSStatus"
        size="small"
        filterable
        placeholder="全部"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in PSStatusOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.IsDeliveryImport"
        size="small"
        filterable
        placeholder="是否交货导入"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isDeliveryImportOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.Customer"
        size="small"
        class="filter-item"
        placeholder="客户"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CustomerOrderNum"
        size="small"
        class="filter-item"
        placeholder="订单号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CONT"
        size="small"
        class="filter-item"
        placeholder="合同号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'SD.SD_ShippingPlan.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>

      <el-dropdown
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.Print' }"
        class="filter-item"
        size="small"
        @command="handlePrint"
      >
        <el-button type="primary" :disabled="deletable" size="small" style="margin-left: 10px;">
          {{ $t("Common.print") }}<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="物流供应商">按物流供应商打印</el-dropdown-item>
          <el-dropdown-item command="结算地址">按结算地址打印</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.Export' }"
        class="filter-item"
        size="small"
        style="margin: 0 10px 10px;"
        split-button
        type="primary"
        @command="handleExport"
        @click="handleExport('导出')"
      >
        导出
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="导出">导出全部</el-dropdown-item>
          <el-dropdown-item command="物流供应商">按物流供应商导出</el-dropdown-item>
          <el-dropdown-item command="结算地址">按结算地址导出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.DownLoadTemp' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModel"
      >下载模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.UploadSRM' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleUploadSRM"
      >同步SRM</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ShippingPlan.Successf' }"
        class="filter-item"
        type="success"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleSuccessf"
      >完成</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单类型" prop="SalesOrderType" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="是否已下载" prop="PSStatus" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="状态" prop="ShippingPlanStatus" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.ShippingPlanStatus === 0">未发运</span>
          <span v-if="scope.row.ShippingPlanStatus === 1">已同步</span>
          <span v-if="scope.row.ShippingPlanStatus === 2">发运中</span>
          <span v-if="scope.row.ShippingPlanStatus === 3">已完成</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划发运时间"
        prop="DeliveryDate"
        align="center"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="实际发货时间" prop="ActualDate" align="center" show-overflow-tooltip :formatter="formatDate" />
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="150"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>发运计划明细表</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="发运单号" prop="DocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="订单类型" prop="SalesOrderType" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="订单行号" prop="SalesLine" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="客户订单号" prop="CustomerOrderNum" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="订单号" prop="SalesOrderNumber" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="合同号" prop="CONT" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="发货单位" prop="CustomerName" align="center" width="160" show-overflow-tooltip />
      <el-table-column
        label="要求发运日期"
        prop="DeliveryDate"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="件号描述" prop="ItemName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="数量" prop="ShippingPlanDetailQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="重量" prop="Weight" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="OUTNO" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="地址" prop="SettlementAdd" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="联系人" prop="Contact" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="联系电话" prop="Telephone" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="发货地址" prop="CustomerAdd" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物流供应商编号" prop="SupplierCode" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="物流供应商" prop="SupplierName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="销售交货批" prop="BatchNum" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="计划入库时间" prop="PlanTime" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="实际入库时间" prop="ActualTime" align="center" width="100" show-overflow-tooltip />

      <!-- <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="创建用户" prop="CUser" align="center" width="100" />
      <el-table-column label="创建时间" prop="CTime" align="center" width="160"
        :formatter="formatDateTime" /> -->

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  batchDelete,
  exportExcelFile,
  GetPageList,
  printOrderToPDF,
  UploadSRM,
  Finish,
  exportExcelModel,
  improtExcelFile
} from '@/api/SD/SD_ShippingPlan';

export default {
  name: 'SD.SD_ShippingPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        PlanStatus: '',
        Customer: '',
        CustomerOrderNum: '',
        CONT: '',
        PSStatus: '',
        IsDeliveryImport: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '未发运',
        key: 0
      },
      {
        label: '已同步',
        key: 1
      },
      {
        label: '发运中',
        key: 2
      },
      {
        label: '已完成',
        key: 3
      }
      ],
      PSStatusOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '已下载',
        key: '已下载'
      },
      {
        label: '未下载',
        key: '未下载'
      }],
      isDeliveryImportOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '未导入',
        key: '0'
      },
      {
        label: '已导入',
        key: '1'
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ShippingPlan') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        console.log(this.list);
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport(command) {
      this.isProcessing = true;
      console.log(command);
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        ExportType: command,
        PlanStatus: this.listQuery.PlanStatus,
        Customer: this.listQuery.Customer,
        CustomerOrderNum: this.listQuery.CustomerOrderNum,
        CONT: this.listQuery.CONT,
        PSStatus: this.listQuery.PSStatus
      };
      let title = '';
      const date = this.$moment(new Date()).format('YYYY-MM-DD');
      if (command === '导出') {
        title = date + '发运计划';
      } else if (command === '物流供应商') {
        title = date + '发运计划-按物流供应商导出';
      }
      if (command === '结算地址') {
        title = date + '发运计划-按结算地址导出';
      }
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, title);
        this.handleFilter();
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    // 打印
    handlePrint(command) {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      var docNums = selectRows.map(v => v.DocNum);
      if (this.checkSingleSelection(selectRows)) {
        printOrderToPDF({
          docNums: docNums,
          PrintType: command
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.ShippingPlanStatus === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步，请勿删除');
          switchBtn = false;
          return true
        }
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿删除');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const arrRowsID = selectRows.map(v => v.DocNum);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('SD.SD_ShippingPlanDetail');
    },
    handleUpdate() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.ShippingPlanStatus === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步，请勿编辑');
          switchBtn = false;
          return true
        }
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿编辑');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.routeTo('SD.SD_ShippingPlanDetail', Object.assign(selectRows[0]));
      }
    },
    handleUploadSRM() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.ShippingPlanStatus === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步，请勿重复同步');
          switchBtn = false;
          return true;
        }
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿进行同步操作');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        UploadSRM(selectRows).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', '同步成功！');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        })
      }
    },
    handleSuccessf() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿重复操作');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Finish({
          DocNums: arrRowsID
        }).then(res => {
          this.isProcessing = false;
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        })
          .catch(error => {
            this.isProcessing = false;
          });
      }
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancel() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    }
  }
};
</script>
