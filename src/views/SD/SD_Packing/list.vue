<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        class="filter-item"
        :clearable="false"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{name:'SD.SD_Packing.Edit'}"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}</el-button>
      <!--v-permission="{name:'SD.SD_Packing.Edit'}"-->
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_Packing.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_Packing.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_Packing.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-printer"
        :disabled="selective"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="getDetailList"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_Packing.PackingID')"
        prop="PackingID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PackingID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.CustomerName')"
        prop="CustomerName"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.CustomerAdd')"
        prop="CustomerAdd"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerAdd }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.Container')"
        prop="Container"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Container }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.Forwarder')"
        prop="Forwarder"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Forwarder }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.ShipmentNo')"
        prop="ShipmentNo"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ShipmentNo }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_Packing.CustomsNum')"
        prop="CustomsNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomsNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t('ui.SD.SD_PackingDetailed.title') }}</span>
    </p>

    <el-table
      v-loading="detailListLoading"
      :data="detailList"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_PackingDetailed.ScanNum')"
        prop="ScanNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.WaveNum')"
        prop="WaveNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.BarCode')"
        prop="BarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.ItemCode')"
        prop="ItemCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.ItemName')"
        prop="ItemName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_PackingDetailed.BatchNum')"
        prop="BatchNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import { formatDate, formatDateTime } from '../../../utils';
import {
  fetchList,
  batchDelete,
  printToPDF,
  exportExcelFile
} from '../../../api/SD/SD_Packing';
import { exportToExcel } from '@/utils/excel-export';
import {
  fetchDetailPage,
  fetchPurchaseOrderDetailList
} from '../../../api/SD/SD_PackingDetailed';
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'SD.SD_Packing',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      list: [],
      detailList: [],
      total: 0,
      listLoading: false,
      detailListLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        isDelivery: '',
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      multipleSelection: [],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      console.log(this.listQuery);
      fetchList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.detailList = [];
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    getDetailList(row) {
      this.detailListLoading = true;

      fetchDetailPage({ DocNum: row.DocNum }).then(response => {
        if (response.Code === 2000) {
          this.detailList = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
        this.detailListLoading = false;
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter(1);
    },
    detailSortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQueryDetail.sort = prop + ' asc';
        } else {
          this.listQueryDetail.sort = prop + ' desc';
        }
      } else {
        this.listQueryDetail.sort = '';
      }
      this.handleFilter(2);
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      this.routeTo('SD.SD_PackingDetailed', Object.assign(selectRows[0]));
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        var arrRowsID = selectRows.map(function(v) {
          return v.DocNum;
        });

        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.updateSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.handleFilter();
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      const printDocNum = selectRows[0].DocNum;

      printToPDF({ DocNum: printDocNum }).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
    },
    handleExport() {
      exportExcelFile({
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue
      }).then(res => exportToExcel(res.data, '销售装箱单'));
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>

