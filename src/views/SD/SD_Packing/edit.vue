<template>
  <div class="app-container">
    <p><label style="width:100%">{{ $t('ui.SD.SD_Packing.title') }}</label></p>
    <el-form ref="dataForm" :inline="false" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.DocNum')" prop="DocNum">
            <el-input v-model="temp.DocNum" readonly disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.CustomerCode')" prop="CustomerCode">
            <el-input v-model="temp.CustomerCode" readonly disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.CustomerName')" prop="CustomerName">
            <el-input v-model="temp.CustomerName" readonly disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.CustomerAdd')" prop="CustomerAdd">
            <el-input v-model="temp.CustomerAdd" readonly disabled />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.ShipmentID')" prop="ShipmentID">
            <el-input v-model="temp.ShipmentID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.Container')" prop="Container">
            <el-input v-model="temp.Container" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.Forwarder')" prop="Forwarder">
            <el-input v-model="temp.Forwarder" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_Packing.CustomsNum')" prop="CustomsNum">
            <el-input v-model="temp.CustomsNum" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="filter-container">
      <el-button v-waves class="filter-item" type="danger" icon="el-icon-delete" :disabled="deletable" @click="handleDelDetail">{{ $t('Common.delete') }}</el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-edit" @click="handleCommitDetail">{{ $t('Common.confirm') }}</el-button>
    </div>
    <p><label style="width:100%">{{ $t('ui.SD.SD_Packing.titlesub') }}</label></p>
    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleDetailSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.DocNum')" prop="DocNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.ScanNum')" prop="ScanNum" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.WaveNum')" prop="WaveNum" align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.WaveNum }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.BaseNum')" prop="BaseNum" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.BaseNum }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.BoxBarCode')" prop="BoxBarCode" align="center" width="100"> <template slot-scope="scope"> <span>{{ scope.row.BoxBarCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.BarCode')" prop="BarCode" align="center" width="100"> <template slot-scope="scope"> <span>{{ scope.row.BarCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.ItemCode')" prop="ItemCode" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItemCode }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.ItemName')" prop="ItemName" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItemName }}</span> </template> </el-table-column>
      <!--      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.Qty')" prop="Qty" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.Qty }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.Unit')" prop="Unit" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.Unit }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.BatchNum')" prop="BatchNum" align="center" width="100"> <template slot-scope="scope"> <span>{{ scope.row.BatchNum }}</span> </template> </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_PackingDetailed.BaseLine')" prop="BaseLine" align="center" width="100"> <template slot-scope="scope"> <span>{{ scope.row.BaseLine }}</span> </template> </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.IsDelete }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CUser')" prop="CUser" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.CUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CTime')" prop="CTime" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.CTime }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.MUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.MTime }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.DUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.DTime }}</span> </template> </el-table-column>
    </el-table>
  </div>
</template>

<script>
import waves from '@/directive/waves' // waves directive
import { fetchDetailPage } from '../../../api/SD/SD_PackingDetailed'
import { formatDate } from '../../../utils'
import { submitForAdd, submitForUpdate } from '../../../api/SD/SD_Packing'

export default {
  name: 'SD_PackingDetailed',
  components: {
  },
  directives: {
    waves
  },
  filters: {
  },
  data() {
    return {
      listDetail: [],
      listDetailLoading: false,
      temp: {
        PlanID: '',
        DocNum: '',
        CustomerName: '',
        CustomerCode: '',
        CustomerAdd: '',
        ShipmentID: '',
        Container: '',
        Forwarder: '',
        CustomsNum: ''
      },
      editStatus: '',
      rules: {
        DocNum: [{
          required: true,
          message: 'type is required',
          trigger: 'change'
        }]
      },
      deleteRows: [],
      multipleDetailSelection: []
    }
  },
  computed: {
    deletable() {
      return this.multipleDetailSelection.length === 0
    }
  },
  created() {
    this.getPageParams();
  },
  methods: {
    formatDate,
    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      this.getDetailList(this.temp.DocNum);
    },
    getDetailList(doc) {
      this.listDetailLoading = true;
      fetchDetailPage({ DocNum: doc }).then(res => {
        this.listDetail = res.Data;
        this.listDetailLoading = false;
      })
    },
    handleCommitDetail() { // 提交按钮，按editStatus的状态调用添加或是更新方法
      /* 这块写后台交互代码 */
      console.log('删除的数据', this.deleteRows);
      submitForUpdate({ MasterData: this.temp, DetailsData: this.listDetail }).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', 'Common.updateSuccess');
          this.backTo('SD.SD_Packing');
        } else {
          this.showNotify('error', res.Message);
        }
      }) // 返回上级页面语句写入then操作，成功写入数据库后返回上级页面
    },
    handleDelDetail() {
      // 只在该页面删除子表里面的信息，不会影响数据库操作
      const tableData = this.listDetail;
      const multDataLen = this.multipleDetailSelection.length;
      const tableDataLen = tableData.length;
      for (let i = 0;i < multDataLen;i++) {
        for (let j = 0;j < tableDataLen;j++) {
          if (JSON.stringify(tableData[j]) === JSON.stringify(this.multipleDetailSelection[i])) { // 判断是否相等，相等就删除
            this.deleteRows = this.listDetail[j];
            this.listDetail.splice(j, 1);
            break;
          }
        }
      }
    },
    handleDetailSelectionChange(val) {
      this.multipleDetailSelection = val;
    }
  }
}
</script>
