<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeCustomer.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="deletable"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeCustomer.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeCustomer.Print' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-printer"
        :disabled="deletable"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeCustomer.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_BarCodeCustomer.BarID')"
        prop="BarID"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.ShipmentID')"
        prop="ShipmentID"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ShipmentID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_BarCodeCustomer.BarCode')"
        prop="BarCode"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.ItemCode')"
        prop="ItemCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.ItemName')"
        prop="ItemName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.CustomerName')"
        prop="CustomerName"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.CustomerNum')"
        prop="CustomerNum"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerNum }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.SD.SD_BarCodeCustomer.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.Version')"
        prop="Version"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Version }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="ShipmentID"
        prop="Version"

        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ShipmentID }}</span>
        </template>
      </el-table-column>-->
      <!--
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeCustomer.PrintTemplate')"
        prop="PrintTemplate"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PrintTemplate }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
    <el-dialog :title="$t('Common.edit')" :visible.sync="dialogContainerVisible">
      <el-form
        ref="dataForm"
        :inline="false"
        :model="temp"
        label-position="right"
        label-width="100px"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_BarCodeCustomer.ShipmentID')" prop="ShipmentID">
              <el-input v-model="temp.ShipmentID" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_BarCodeCustomer.CustomerNum')" prop="CustomerNum">
              <el-input v-model="temp.CustomerNum" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_BarCodeCustomer.Version')" prop="Version">
              <el-input v-model="temp.Version" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item :label="ShipmentID" prop="ShipmentID">
              <el-input v-model="temp.ShipmentID" />
            </el-form-item>
          </el-col>-->
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogContainerVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleaffirmUpdate"
        >{{ $t('Common.affirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { formatDate, formatDateTime } from '../../../utils';
import permission from '@/directive/permission/index.js'; // 权限判断指令
import {
  fetchList,
  batchDelete,
  printToPDF,
  exportExcelFile,
  update
} from '../../../api/SD/SD_BarCodeCustomer';
import { exportToExcel } from '@/utils/excel-export';

export default {
  name: 'SD.SD_BarCodeCustomer',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        dateValue: [
          new Date(),
          new Date()
        ],
        keyword: ''
      },
      multipleSelection: [],
      isProcessing: false,
      temp: {
        ShipmentID: '',
        CustomerNum: '',
        Version: ''
      },
      dialogContainerVisible: false
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = false;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.BarID);
        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.getList();
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.dateValue[0], this.listQuery.dateValue[1]]
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '客户标签')
      );
    },
    handleUpdate() {
      this.dialogContainerVisible = true;
      var selectedRows = this.multipleSelection;
      this.temp.ShipmentID = selectedRows[0].ShipmentID;
      this.temp.CustomerNum = selectedRows[0].CustomerNum;
      this.temp.Version = selectedRows[0].Version;
    },
    handleaffirmUpdate() {
      var selectedRows = this.multipleSelection;
      selectedRows.forEach(element => {
        element.ShipmentID = this.temp.ShipmentID;
        element.CustomerNum = this.temp.CustomerNum;
        element.Version = this.temp.Version;
      });
      this.isProcessing = true;

      update(selectedRows).then(response => {
        if (response.Code === 2000) {
          this.showNotify('success', 'Common.updateSuccess');
        } else {
          this.showNotify('error', response.Message);
        }
        this.getList();
        this.dialogContainerVisible = false;
        this.isProcessing = false;
      });
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      const BoxBarCodes = selectRows.map(v => v.BoxBarCode);
      // console.log(BoxBarCodes);
      // var DoubleBoxBarCodes=[];
      //  DoubleBoxBarCodes =DoubleBoxBarCodes.concat (
      //   BoxBarCodes,
      //   BoxBarCodes
      // );
      // console.log(DoubleBoxBarCodes.sort());

      // printToPDF(DoubleBoxBarCodes.sort()).then(response => {
      printToPDF(BoxBarCodes).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
