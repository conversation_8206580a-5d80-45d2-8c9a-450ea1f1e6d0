<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />

      <el-select v-model="listQuery.Status" filterable style="width: 140px" class="filter-item" @change="handleFilter(0)">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Edit' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >
        {{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Print' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="primary"
        icon="el-icon-printer"
        :disabled="print"
        @click="handlePrint"
      >
        {{ $t('Common.print') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Successf' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="success"
        icon="el-icon-check"
        :disabled="succseeF"
        @click="handleComplete"
      >
        {{ $t('Common.successf') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Person' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="primary"
        icon="el-icon-service"
        :disabled="deletable"
        @click="handleAssign"
      >
        {{ $t('Common.Person') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryWave.Export' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="getDetailList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryWave.WaveID')"
        prop="WaveID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWave.DocNum')" prop="DocNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWave.CustomerCode')" prop="CustomerCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWave.CustomerName')" prop="CustomerName" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWave.CustomerAdd')" prop="CustomerAdd" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerAdd }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWave.ShipTime')"
        prop="ShipTime"
        align="center"
        width="120"
        :formatter="formatDate"
      />
      <el-table-column :label="$t('ui.SD.SD_DeliveryWave.PUser')" prop="PUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWave.Status')"
        prop="Status"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.Status===0" type="danger">{{ $t('Dictionary.SD_DeliveryWave_Status.Zero') }}</el-tag>
          <el-tag v-else-if="scope.row.Status===1" type="primary">{{ $t('Dictionary.SD_DeliveryWave_Status.One') }}
          </el-tag>
          <el-tag v-else type="success">{{ $t('Dictionary.SD_DeliveryWave_Status.Two') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWave.Forwarder')" prop="Forwarder" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Forwarder }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t('ui.SD.SD_DeliveryWaveDetailed.title') }}</span>
    </p>

    <el-table
      v-loading="detailListLoading"
      :data="detailList"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseNum')" prop="BaseNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemName')" prop="ItemName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.Boxes')" prop="Boxes" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Boxes }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.NetWeight')"
        prop="NetWeight"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.NetWeight }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.GrossWeight')"
        prop="GrossWeight"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.GrossWeight }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.Pallet')" prop="Pallet" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Pallet }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.SD.SD_DeliveryWaveDetailed.MetreSum')" prop="MetreSum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MetreSum }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CTime')" prop="CTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="$t('Common.Person')" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form>
        <el-form-item :label="$t('ui.Sys.Sys_User.UserName')" prop="UserName">
          <el-select v-model="value" filterable>
            <el-option v-for="item in user" :key="item.UserID" :value="item.LoginAccount" :label="item.UserName" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  batchDelete,
  fetchDetailList,
  fetchUser,
  Complete,
  designatePerson,
  printToPDF,
  exportExcelFile
} from '../../../api/SD/SD_DeliveryWave';
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import {
  exportToExcel
} from '@/utils/excel-export';
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'SD.SD_DeliveryWave',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      detailList: [],
      detailListLoading: false,
      total: 0,
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        Status: 1, // 状态
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      dialogVisible: false,
      multipleSelection: [],
      user: [],
      value: '',
      statusOptions: [{
        value: 0,
        label: this.$i18n.t('Dictionary.SD_DeliveryWave_Status.Zero')
      },
      {
        value: 1,
        label: this.$i18n.t('Dictionary.SD_DeliveryWave_Status.One')
      },
      {
        value: 2,
        label: this.$i18n.t('Dictionary.SD_DeliveryWave_Status.Two')
      }
      ],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      return (
        this.multipleSelection.length !== 1 ||
          this.multipleSelection[0].Status !== 0
      );
    },
    print() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status !== 0) {
          return true;
        }
      }
      return false;
    },
    succseeF() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status !== 1) {
          return true;
        }
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.detailList = [];
        this.listLoading = false;
      });
    },
    getDetailList(row) {
      this.detailListLoading = true;
      fetchDetailList({
        DocNum: row.DocNum
      }).then(response => {
        this.detailList = response.Data;
        this.detailListLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.DocNum);
        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.updateSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.getList();
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        const printDocNum = selectRows[0].DocNum;
        printToPDF({
          DocNum: printDocNum
        }).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
        });
      }
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.dateValue[0], this.listQuery.dateValue[1]],
        state: this.listQuery.Status
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '销售波次任务单')
      );
    },
    // 指定责任人模块 start
    handleAssign() {
      // 指定责任人功能,从Sys_User中取出用户数据，指定责任人
      this.dialogVisible = true;
      fetchUser().then(res => {
        this.user = res.Data;
        var selectRowPerson = this.multipleSelection[0].PUser;
        this.value = selectRowPerson;
      });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave() {
      // 传递this.userSelection[0].LoginAccount(责任人的登录名)以及主表的this.multipleSelection[0](某一字段)给后端，用于指定责任人
      this.dialogVisible = false;
      var selectedRows = this.multipleSelection;
      var DocNums = selectedRows.map(v => v.DocNum);
      this.startLoading();
      designatePerson({
        DocNum: DocNums,
        LoginAccount: this.value
      }).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
        } else {
          this.showNotify('error', res.Message);
        }
        this.endLoading();
      }).catch(err => {
        console.log(err);
        this.endLoading();
      });
    },
    // 指定责任人模块 end
    // 完成
    handleComplete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Complete({
          DocNum: arrRowsID
        }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    handleCreate() {
      this.routeTo('SD.SD_DeliveryWaveDetailed');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      this.routeTo('SD.SD_DeliveryWaveDetailed', Object.assign(selectRows[0]));
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
