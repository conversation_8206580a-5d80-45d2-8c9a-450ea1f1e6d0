<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <p>
      <label style="width:100%">{{ $t('ui.SD.SD_DeliveryWave.title') }}</label>
    </p>
    <el-form
      ref="dataForm"
      :inline="false"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.DocNum')" prop="DocNum">
            <el-input v-model="temp.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.CustomerCode')" prop="CustomerCode">
            <el-input
              v-model="temp.CustomerCode"
              :placeholder="$t('ui.SD.SD_DeliveryWave.CustomerCode')"
              readonly
            >
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.CustomerName')" prop="CustomerName">
            <el-input v-model="temp.CustomerName" :disabled="editStatus==='edit'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.Forwarder')" prop="Forwarder">
            <el-input v-model="temp.Forwarder" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.CustomerAdd')" prop="CustomerAdd">
            <el-input v-model="temp.CustomerAdd" :disabled="editStatus==='edit'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.ShipTime')" prop="DeliveryTime">
            <el-date-picker
              v-model="temp.ShipTime"
              type="date"
              :clearable="false"
              placeholder="选择日期"
              :disabled="editStatus==='edit'"
              style="width: 200px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.PUser')" prop="PUser">
            <el-select v-model="temp.PUser" :disabled="editStatus==='edit'" filterable>
              <el-option
                v-for="item in PUserOptions"
                :key="item.UserID"
                :label="item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWave.Remark')" prop="Remark">
            <el-input v-model="temp.Remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <p>
      <label style="width:100%">{{ $t('ui.SD.SD_DeliveryWaveDetailed.title') }}</label>
    </p>

    <el-form
      ref="detailDataForm"
      :inline="false"
      :model="detail"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseNum')" prop="BaseNum">
            <el-input v-model="detail.BaseNum" :placeholder="$t('ui.SD.saleOrder.select')" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectSaleOrder" />
            </el-input>
          </el-form-item>
        </el-col>
        <!--<el-col :span="16">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItmsGrpName')" prop="ItmsGrpName">
            <el-input v-model="detail.ItmsGrpName" disabled />
          </el-form-item>
        </el-col>-->
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemCode')" prop="ItemCode">
            <el-input v-model="detail.ItemCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemName')" prop="ItemName">
            <el-input v-model="detail.ItemName" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.Unit')" prop="Unit">
            <el-input v-model="detail.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.Qty')" prop="Qty">
            <el-input-number v-model="detail.Qty" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.Boxes')" prop="Boxes">
            <el-input-number v-model="detail.Boxes" :min="0" />
            <!-- v-model="detail.Boxes" -->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.NetWeight')" prop="NetWeight">
            <el-input-number v-model="detail.NetWeight" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.GrossWeight')" prop="GrossWeight">
            <el-input-number v-model="detail.GrossWeight" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.MetreSum')" prop="MetreSum">
            <el-input-number v-model="detail.MetreSum" :min="0" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <!-- <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.PalletNum')" prop="PalletNum">
            <el-input-number v-model="detail.PalletNum" :min="0" />
          </el-form-item>
        </el-col>-->
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.Metre')" prop="Metre">
            <el-input-number :value="handlQtyChange" :min="0" disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_DeliveryWaveDetailed.Pallet')" prop="Pallet">
            <el-input-number v-model="detail.Pallet" :min="0" disabled="disabled" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleUpdateDetail"
      >{{ $t('Common.update') }}</el-button>
      <!-- <el-button v-waves class="filter-item" type="primary" icon="el-icon-edit" @click="handleFormAdd">{{ $t('Common.add') }}</el-button> -->
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelDetail"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleCommit"
      >{{ $t('Common.confirm') }}</el-button>
    </div>
    <el-table
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @row-click="editDetail"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="140"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.BaseEntry }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.BaseNum }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseLine')"
        prop="BaseLine"
        align="center"
        width="140"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.BaseNum }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemCode')"
        prop="ItemCode"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.ItemCode }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemName')"
        prop="ItemName"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.ItemName }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.Qty }}</span> </template> -->
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.Qty')"
        prop="SaleQty"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.Qty }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.Unit }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.Boxes')"
        prop="Boxes"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.Boxes }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.NetWeight')"
        prop="NetWeight"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.NetWeight }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.GrossWeight')"
        prop="GrossWeight"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.GrossWeight }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.Pallet')"
        prop="Pallet"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.Pallet }}</span> </template> -->
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryWaveDetailed.MetreSum')"
        prop="MetreSum"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.MetreSum }}</span> </template> -->
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <!-- <template slot-scope="scope"> <span>{{ scope.row.CUser }}</span> </template> -->
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>

    <!-- 选择订单 -->
    <el-dialog
      ref="dlg"
      :title="$t('ui.SD.saleOrder.title')"
      :visible.sync="dialogSaleorderFormVisible"
    >
      <div class="filter-container">
        <el-input
          v-model="listSaleOrderQuery.keyword"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('Common.keyword')"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >{{ $t('Common.search') }}</el-button>
      </div>
      <el-table
        v-loading="listsapLoading"
        :data="saleorderList"
        border
        fit
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%;overflow-y:scroll;"
        @selection-change="handleSAPSelectionChange"
      >
        <el-table-column
          :label="$t('Common.select')"
          type="selection"
          align="center"
          width="40"
          fixed
        />

        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseNum')"
          prop="BaseNum"
          align="center"
          width="90"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BaseNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.BaseLine')"
          prop="BaseLine"
          align="center"
          width="70"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BaseLine }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemCode')"
          prop="ItemCode"
          align="center"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.ItemName')"
          prop="ItemName"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.Qty')"
          prop="Qty"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Qty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.Unit')"
          prop="Unit"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Unit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.Boxes')"
          prop="Boxes"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Boxes }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.NetWeight')"
          prop="NetWeight"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.NetWeight }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.GrossWeight')"
          prop="GrossWeight"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.GrossWeight }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.Pallet')"
          prop="Pallet"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Pallet }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.Metre')"
          prop="Metre"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Metre }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.SD.SD_DeliveryWaveDetailed.Remark')"
          prop="Remark"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Remark }}</span>
          </template>
        </el-table-column>
        <!--
        <el-table-column :label="$t('ui.SD.saleOrder.ID')" prop="ID"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.ID }}</span> </template> </el-table-column>
        <el-table-column :label="$t('ui.SD.saleOrder.LineID')" prop="LineID"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.LineID }}</span> </template> </el-table-column>
        <el-table-column :label="$t('ui.SD.saleOrder.ProductID')" prop="ProductID"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ProductID }}</span> </template> </el-table-column>
        <el-table-column :label="$t('ui.SD.saleOrder.Name')" prop="Name"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.Name }}</span> </template> </el-table-column>
        <el-table-column :label="$t('ui.SD.saleOrder.Quantity')" prop="Quantity"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.Quantity }}</span> </template> </el-table-column>
        <el-table-column v-if="false" :label="$t('ui.SD.saleOrder.UnitOfMeasure')" prop="UnitOfMeasure"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.UnitOfMeasure }}</span> </template> </el-table-column>
        <el-table-column v-if="false" :label="$t('ui.SD.saleOrder.NetWeight')" prop="NetWeight"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.NetWeight }}</span> </template> </el-table-column>
        <el-table-column v-if="false" :label="$t('ui.SD.saleOrder.GrossWeight')" prop="GrossWeight"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.GrossWeight }}</span> </template> </el-table-column>
        <el-table-column v-if="false" :label="$t('ui.SD.saleOrder.PalletNum')" prop="PalletNum"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.PalletNum }}</span> </template> </el-table-column>
        <el-table-column v-if="false" :label="$t('ui.SD.saleOrder.Metre')" prop="Metre"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.Metre }}</span> </template> </el-table-column>-->
      </el-table>
      <Pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listSaleOrderQuery.PageNumber"
        :limit.sync="listSaleOrderQuery.PageSize"
        @pagination="getSAPList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogSaleorderFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSelect"
        >{{ $t('Common.select') }}</el-button>
      </span>
    </el-dialog>

    <!-- 选择客户 -->
    <el-dialog
      :title="$t('ui.MD.Customer.title')"
      :visible.sync="dialogCustomerFormVisible"
      width="50%"
      top="5vh"
    >
      <div class="filter-container">
        <el-input
          v-model="listLocalCustomerQuery.keyword"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('ui.MD.Customer.searchCustomer')"
          @keyup.enter.native="handleCustomerFilter"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleCustomerFilter"
        >{{ $t('Common.search') }}</el-button>
      </div>
      <el-table
        ref="CustomerSelectTable"
        v-loading="listLoading"
        :data="CustomerDataList"
        border
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%"
        @selection-change="handleCustomerRowSelectEvent"
      >
        <el-table-column type="selection" align="center" width="40" fixed />
        <el-table-column
          :label="$t('ui.MD.Customer.CustomerCode')"
          prop="InternalID"
          align="center"
          width="140"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.InternalID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.CustomerName')"
          prop="FirstLineName"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.FirstLineName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.Email')"
          prop="EMailURI"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.EMailURI }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.City')"
          prop="CityName"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.CityName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.Phone')"
          prop="Phone"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Phone }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.Fax')"
          prop="Fax"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Fax }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.ContactPerson')"
          prop="ContactPerson"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ContactPerson }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Customer.Address')"
          prop="Qty"
          sortable="Address"
          align="center"
          width="300"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Address }}</span>
          </template>
        </el-table-column>
        <template slot="append">
          <infinite-loading force-use-infinite-wrapper=".el-table__body-wrapper" spinner="spiral" @infinite="handleInfiniteCustomer" />
        </template>
      </el-table>
      <Pagination
        v-show="totalcustomer > 0"
        :total="totalcustomer"
        :page.sync="listLocalCustomerQuery.PageNumber"
        :limit.sync="listLocalCustomerQuery.PageSize"
        @pagination="handleCustomerFilter"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          :disabled="CustomerSelectDisable"
          @click="handleSelectCustomer"
        >{{ $t('Common.select') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { formatDate, formatDateTime } from '../../../utils';
import {
  fetchDetailList,
  submitForUpdate,
  updateCheckSaleQty,
  fetchDocNum,
  fetchUser,
  fetchCustomerNextPage,
  fetchSaleOrderNextPage,
  fetchCustomerLocalPage
} from '../../../api/SD/SD_DeliveryWave';

export default {
  name: 'SD_DeliveryWaveDetailed',
  components: { Pagination },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      SAPList: [],
      total: 0,
      totalcustomer: 0,
      SAPListQuery: {
        keyword: ''
      },
      listCustomerQuery: {
        keyword: '',
        pageNumber: 1,
        pageSize: 10,
        firstID: ''
      },
      listLocalCustomerQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      listSaleOrderQuery: {
        PageNumber: 1,
        PageSize: 10,
        CardCode: '',
        keyword: '',
        firstID: ''
      },
      temp: {
        DocNum: '',
        CustomerCode: '',
        CustomerName: '',
        CustomerAdd: '',
        Forwarder: '',
        ShipTime: new Date(),
        PUser: '',
        Remark: ''
      },
      CustomerCodeOptions: '',
      PUserOptions: [],
      CustomerDataList: [],
      saleorderList: [],
      SelectedCustomerRowData: [],
      detail: {
        BaseNum: '',
        BaseLine: '',
        ItemCode: '',
        ItemName: '',
        ItmsGrpName: '',
        Unit: '',
        Qty: '',
        Boxes: '',
        NetWeight: '',
        GrossWeight: '',
        Pallet: '',
        PalletNum: '',
        Metre: '',
        MetreSum: ''
      },
      dialogFormVisible: false,
      dialogCustomerFormVisible: false,
      dialogSaleorderFormVisible: false,
      listLoading: false,
      listsapLoading: false,
      rules: {},
      editStatus: '',
      downloadLoading: false,
      selectRow: '',
      multipleSAPSelection: [],
      multipleSelection: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    CustomerSelectDisable() {
      return this.SelectedCustomerRowData.length !== 1;
    },
    itemSelectDisable() {
      return this.SelectedItemRowData.length !== 1;
    },
    handlQtyChange() {
      console.log('1', this.detail);
      var oldBoxes = this.detail.Boxes;
      if (this.detail) {
        if (this.detail.Qty && this.detail.Pallet) {
          this.detail.Boxes = this.detail.Qty / this.detail.Pallet;
        }
        if (this.detail.NetWeight) {
          this.detail.NetWeight =
            (this.detail.NetWeight / oldBoxes) * this.detail.Boxes;
        }
        if (this.detail.GrossWeight) {
          this.detail.GrossWeight =
            (this.detail.GrossWeight / oldBoxes) * this.detail.Boxes;
        }
        this.detail.MetreSum = this.detail.Metre * this.detail.Qty;
      }
      this.detail.Boxes = this.detail.Boxes;
      return this.detail.Metre;
    }
  },
  created() {
    this.getPageParams();
    if (this.editStatus === 'edit') {
      fetchDetailList({ DocNum: this.temp.DocNum }).then(res => {
        this.list = res.Data;
      });
    }
  },
  methods: {
    formatDate,
    formatDateTime,
    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      if (this.temp.WaveID) this.editStatus = 'edit';
      else {
        this.editStatus = 'create';
        fetchDocNum().then(res => {
          this.temp.DocNum = res.Data;
        });
      }
      // 指定责任人功能,从Sys_User中取出用户数据，指定责任人
      this.dialogVisible = true;
      fetchUser().then(res => {
        this.PUserOptions = res.Data;
      });
    },
    getSAPList() {
      // 从SAP获取列表信息
      this.listsapLoading = true;
      // this.saleorderList = undefined;
      // this.total = 0;
      this.listSaleOrderQuery.firstID = undefined;
      this.listSaleOrderQuery.CardCode = '';
      this.listSaleOrderQuery.CardCode = this.temp.CustomerCode;
      console.log(this.listSaleOrderQuery);
      fetchSaleOrderNextPage(this.listSaleOrderQuery)
        .then(response => {
          if (response.Data) {
            this.saleorderList = response.Data.SaleOrder;
            this.total = response.Data.total;
            this.listSaleOrderQuery.firstID = response.Data.LastReturnUUID;
          }
          this.listsapLoading = false;
        })
        .catch(error => {
          this.saleorderList = undefined;
          this.total = 0;
          this.listSaleOrderQuery.firstID = undefined;
          this.listsapLoading = false;
        });
    },
    getDetailList() {},
    handleFilter() {
      this.listSaleOrderQuery.PageNumber = 1;
      this.listSaleOrderQuery.PageSize = 10;
      this.getSAPList();
    },
    resetDetail() {
      this.detail = {
        BaseNum: '',
        BaseLine: '',
        ItemCode: '',
        ItemName: '',
        ItmsGrpName: '',
        Unit: '',
        Qty: '',
        Boxes: '',
        NetWeight: '',
        GrossWeight: '',
        Pallet: '',
        PalletNum: '',
        Metre: '',
        MetreSum: ''
      };
    },
    selectSaleOrder() {
      if (this.temp.CustomerCode == '') {
        this.showNotify('warning', 'Common.firstChooceCustomerNumber');
        return;
      }
      this.resetDetail();
      this.dialogSaleorderFormVisible = true;
      this.getSAPList();
    },
    handleFormAdd() {
      // 将表单数据添加到表格中的方法
      this.list.push(this.detail);
      this.resetDetail();
    },

    editDetail(row) {
      this.selectRow = row;
      Object.assign(this.detail, row);
    },
    handleUpdateDetail() {
      var SaleQty = this.selectRow.SaleQty;
      var UpdateQty = this.detail.Qty;
      if (UpdateQty == 0) {
        this.showNotify('warning', 'Common.inCorrectNotZero');
        return;
      }
      if (SaleQty < UpdateQty) {
        this.showNotify('warning', 'Common.inCorrectSaleQty');
        return;
      }
      var newSelectRow = this.detail;
      if (this.editStatus !== 'edit') {
        updateCheckSaleQty([newSelectRow])
          .then(res => {
            Object.assign(this.selectRow, this.detail);
          })
          .catch(error => {
            return;
          });
      } else {
        Object.assign(this.selectRow, this.detail);
      }
    },
    handleDelDetail() {
      // 删除名字表格的数据
      const tableData = this.list;
      const multDataLen = this.multipleSelection.length;
      const tableDataLen = tableData.length;
      for (let i = 0;i < multDataLen;i++) {
        for (let j = 0;j < tableDataLen;j++) {
          if (
            JSON.stringify(tableData[j]) ===
            JSON.stringify(this.multipleSelection[i])
          ) {
            // 判断是否相等，相等就删除
            this.list.splice(j, 1);
            break;
          }
        }
      }
    },
    handleSelect() {
      console.log(this.multipleSAPSelection);

      var isHaveRepeat = false;
      for (let i = 0;i < this.multipleSAPSelection.length;i++) {
        var detail = this.list.find(
          obj =>
            obj.BaseNum == this.multipleSAPSelection[i].BaseNum &&
            obj.ItemCode == this.multipleSAPSelection[i].ItemCode
        );
        if (detail) {
          isHaveRepeat = true;
        } else {
          var rowSapSelection = this.multipleSAPSelection[i];
          rowSapSelection.SaleQty = rowSapSelection.Qty;
          console.log(rowSapSelection);
          this.temp.CustomerAdd = rowSapSelection.Remark;
          this.multipleSAPSelection[i].Remark = '';
          this.list.push(rowSapSelection);
        }
      }
      if (isHaveRepeat) {
        this.showNotify('warning', 'Common.someDataHasRepeat');
      }
      // this.list=this.multipleSAPSelection
      // this.detail.BaseNum = this.saleorderList[0].ID;
      // this.detail.BaseLine = this.saleorderList[0].LineID;
      // this.detail.ItemCode = this.saleorderList[0].ProductID;
      // this.detail.ItemName = this.saleorderList[0].Name;
      // this.detail.Unit = this.saleorderList[0].UnitOfMeasure;
      // this.detail.Qty = this.saleorderList[0].Quantity;
      // this.detail.NetWeight = this.saleorderList[0].NetWeight;
      // this.detail.GrossWeight = this.saleorderList[0].GrossWeight;
      // this.detail.PalletNum = this.saleorderList[0].PalletNum;
      // this.detail.Metre = this.saleorderList[0].Metre;

      this.dialogSaleorderFormVisible = false;
    },
    handleCommit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (this.list.length == 0) {
            this.showNotify('error', 'Common.noConfirmData');
            return;
          }
          var detailsRow = this.list.find(x => x.Qty == 0);
          if (detailsRow) {
            this.showNotify('warning', 'Common.inCorrectNotZero');
            return;
          }

          this.isProcessing = true;
          if (this.editStatus !== 'edit') {
            updateCheckSaleQty(this.list)
              .then(res => {
                submitForUpdate({
                  MasterData: this.temp,
                  DetailsData: this.list
                }).then(res => {
                  if (res.Code === 2000) {
                    this.showNotify('success', 'Common.updateSuccess');
                    this.isProcessing = false;
                    this.backTo('SD.SD_DeliveryWave');
                  } else {
                    this.showNotify('error', res.Message);
                    this.isProcessing = false;
                  }
                }); // 返回上级页面语句写入then操作，成功写入数据库后返回上级页面
              })
              .catch(error => {
                this.isProcessing = false;
                // if (!res.Code == 2000) {
                //   this.showNotify("warning", res.Message);
                // }
                return;
              });
          } else {
            submitForUpdate({
              MasterData: this.temp,
              DetailsData: this.list
            })
              .then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', 'Common.updateSuccess');
                  this.isProcessing = false;
                  this.backTo('SD.SD_DeliveryWave');
                } else {
                  this.showNotify('error', res.Message);
                  this.isProcessing = false;
                }
              })
              .catch(error => {
                this.isProcessing = false;
                // if (!res.Code == 2000) {
                //   this.showNotify("warning", res.Message);
                // }
                return;
              }); // 返回上级页面语句写入then操作，成功写入数据库后返回上级页面
          }
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSAPSelectionChange(val) {
      this.multipleSAPSelection = val;
    },
    // 选择供应商
    selectCustomer() {
      if (this.list.length !== 0) {
        this.showNotify('warning', 'Common.onlyOneCustomerOnList');
        return false;
      }
      this.dialogCustomerFormVisible = true;
      // this.getCustomerList();
    },
    handleCustomerFilter() {
      this.getCustomerList();
    },
    handleCustomerRowSelectEvent(selection) {
      this.SelectedCustomerRowData = selection;
    },
    handleInfiniteCustomer($state) {
      this.listLoading = true; // fetchCustomerNextPage
      fetchCustomerNextPage(this.listCustomerQuery).then(response => {
        this.CustomerDataList = this.CustomerDataList.concat(
          response.Data.Suppliers
        );
        this.listCustomerQuery.firstID = response.Data.LastReturnUUID;
        $state.loaded();
        this.listLoading = false;
      });
    },
    handleSelectCustomer() {
      this.temp.CustomerCode = this.SelectedCustomerRowData[0].InternalID;
      this.temp.CustomerName = this.SelectedCustomerRowData[0].FirstLineName;
      this.temp.CustomerAdd = this.SelectedCustomerRowData[0].Address;
      this.dialogCustomerFormVisible = false;
    },
    getCustomerList() {
      // if (this.listCustomerQuery.keyword == "") {
      //   this.showNotify("warning", "Common.writeCustomerNameOrOrder");
      //   return;
      // }
      this.listLoading = true;
      // if (this.CustomerDataList.length == 1) {
      //   this.listCustomerQuery.firstID = "";
      // }
      fetchCustomerLocalPage(this.listLocalCustomerQuery).then(response => {
        if (response.Data) {
          this.CustomerDataList = response.Data.Customers;
          this.totalcustomer = response.Data.total;
        }
        this.listLoading = false;
      });
    }
  }
};
</script>
