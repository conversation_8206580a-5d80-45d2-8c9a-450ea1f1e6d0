<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('ui.SD.SD_DeliveryScan.IsPosted')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-select
        v-model="listQuery.isDelivery"
        filterable
        :placeholder="$t('ui.SD.SD_DeliveryScan.IsDelivery')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isDeliveryOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryScan.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryScan.Posting' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="deletable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryScan.Packing' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-check"
        :disabled="packable"
        @click="handlePacking"
      >{{ $t('Common.Packing') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_DeliveryScan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>
    <!-- 批次包装箱汇总显示列表 -->
    <el-table
      v-loading="listLoadinghz"
      :data="listhz"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="getDetailListhz"
      @selection-change="handleSelectionChangehz"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.OutRegionCode')"
        prop="OutRegionCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.OutBinLocationCode')"
        prop="OutBinLocationCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationCode }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.SumQty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.CustomerName')"
        prop="CustomerName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryScan.BatchNum')"
        prop="BatchNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.IsDelivery')"
        prop="IsDelivery"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelivery |yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.ERPDocNum')"
        prop="ERPDocNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.PostUser')"
        prop="PostUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.IsPosted')"
        prop="IsPosted"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalhz>0"
      :total="totalhz"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getListhz"
    />

    <!-- 明细显示列表 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />-->
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryScan.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.WaveNum')"
        prop="WaveNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.StockupDoc')"
        prop="StockupDoc"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.StockupDoc }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryScan.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryScan.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.BarCode')"
        prop="BarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.SD.SD_DeliveryScan.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="220"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <!-- <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>-->

      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.CustomerName')"
        prop="CustomerName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.BatchNum')"
        prop="BatchNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.SD.SD_DeliveryScan.OutWhsName')" prop="OutWhsName"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.OutWhsName }}</span> </template> </el-table-column>-->

      <!--      <el-table-column :label="$t('ui.SD.SD_DeliveryScan.InWhsName')" prop="InWhsName"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.InWhsName }}</span> </template> </el-table-column>-->
      <!-- <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryScan.InRegionName')"
        prop="InRegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_DeliveryScan.InBinLocationName')"
        prop="InBinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_DeliveryScan.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize" @pagination="getList"  /> -->
    <!-- 填写集装箱信息 -->
    <el-dialog :title="$t('ui.SD.SD_Packing.title')" :visible.sync="dialogContainerVisible">
      <el-form
        ref="dataForm"
        :inline="false"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_Packing.Container')" prop="Container">
              <el-input v-model="temp.Container" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_Packing.Forwarder')" prop="Forwarder">
              <el-input v-model="temp.Forwarder" />
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_Packing.ShipmentNo')" prop="ShipmentNo">
              <el-input v-model="temp.ShipmentNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_Packing.CustomsNum')" prop="CustomsNum">
              <el-input v-model="temp.CustomsNum" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item :label="$t('ui.SD.SD_Packing.CustomerNum')" prop="CustomerNum">
              <el-input v-model="temp.CustomerNum" />
            </el-form-item>
          </el-col>-->
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogContainerVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleaffirmPacking"
        >{{ $t('Common.affirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  fetchCountList,
  add,
  update,
  batchDelete,
  doPost,
  exportExcelFile,
  fetchDetailList
} from '../../../api/SD/SD_DeliveryScan';
import { convertToKeyValue } from '@/utils';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
import { addSDPacking } from '@/api/SD/SD_Packing';
import { exportToExcel } from '@/utils/excel-export';
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'SDDeliveryScan',
  components: { Pagination },
  directives: { waves, permission },
  filters: {
    statusFilter(status) {
      const statusMap = {
        true: '正常',
        false: '冻结'
      };
      return statusMap[status];
    }
  },
  data() {
    return {
      list: [],
      listhz: [],
      total: 0,
      totalhz: 0,
      listLoading: false,
      listLoadinghz: false,
      temp: {
        Container: '',
        Forwarder: '',
        CustomsNum: '',
        CustomerNum: '',
        ShipmentNo: ''
      },
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        isDelivery: '',
        isPosted: '',
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      isPostedOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.posted'),
          key: true
        },
        {
          label: this.$i18n.t('Common.notPosted'),
          key: false
        }
      ],
      isDeliveryOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.isDelivery.yes'),
          key: true
        },
        {
          label: this.$i18n.t('Common.isDelivery.no'),
          key: false
        }
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      formTitle: '', // 弹窗标题
      formMode: '',
      dialogFormVisible: false, // 表单窗口是否显示标志
      dialogContainerVisible: false,
      rules: {
        // 表单校验逻辑
        UserName: [
          { required: true, message: '用户名必须输入', trigger: 'change' }
        ]
      },
      downloadLoading: false,
      multipleSelection: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) {
        return true;
      }
      while (i--) {
        if (this.multipleSelection[i].IsPosted) {
          return true;
        }
      }
    },
    packable() {
      let i = this.multipleSelection.length;
      if (i === 0) {
        return true;
      }
      var isNoPosted = this.multipleSelection.find(x => x.IsPosted === false);
      while (i--) {
        if (
          this.multipleSelection[i].IsPosted &&
          !this.multipleSelection[i].IsDelivery &&
          !isNoPosted
        ) {
          return false;
        }
      }
      return true;
    }
  },
  created() {
    this.getListhz();
  },
  methods: {
    getListhz() {
      // 获取数据
      this.listLoadinghz = true;
      fetchCountList(this.listQuery).then(response => {
        this.listhz = response.Data.items;
        this.totalhz = response.Data.total;
        this.list = [];
        this.listLoadinghz = false;
      });
    },
    // getList() {
    //   // 获取数据
    //   this.listLoading = true
    //   fetchList(this.listQuery).then(response => {
    //     this.list = response.Data.items
    //     this.total = response.Data.total
    //     this.listLoading = false
    //   })
    // },
    getDetailListhz(row) {
      console.log(row);
      this.listLoading = true;
      fetchDetailList({
        BatchNum: row.BatchNum,
        BoxBarCode: row.BoxBarCode,
        DocNum: row.DocNum,
        CustomerCode: row.CustomerCode,
        ItemCode: row.ItemCode,
        BaseNum: row.BaseNum
      }).then(response => {
        this.list = response.Data;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getListhz();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        // let arrRowsID = selectRows.map(v => v.ScanID);
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(selectRows)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.handleFilter();
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handlePacking() {
      if (this.multipleSelection.length === 0) {
        this.showNotify('error', 'Common.noSelection');
      } else {
        var selectedRow = this.multipleSelection;
        var isCan = selectedRow.find(obj => obj.IsDelivery);
        if (isCan) {
          this.showNotify('warning', 'Common.isDelivery.noDelivery');
          return;
        }
        this.dialogContainerVisible = true;
        this.temp = {
          Container: '',
          Forwarder: '',
          CustomsNum: '',
          CustomerNum: '',
          ShipmentNo: ''
        };
      }
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.dateValue[0], this.listQuery.dateValue[1]],
        isDelivery: this.listQuery.isDelivery,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '销售发货单')
      );
    },
    handlePosting() {
      // 过账功能
      // const postData = {master:this.selectedRow,details:this.multipleSelection}
      var selectRows = this.multipleSelection;
      var row = selectRows.find(obj => obj.IsPosted);
      if (row) {
        this.showNotify('error', 'Common.handlingPostedDataNotPermitted');
        return false;
      }
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.postToSAPConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.showNotify('warning', 'Common.syncStart');
          this.isProcessing = true;
          doPost(this.multipleSelection)
            .then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.operationSuccess');
                this.handleFilter(1);
                this.isProcessing = false;
              } else {
                this.showNotify('error', response.Message);
                this.handleFilter(1);
                this.isProcessing = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        });
      }
      // if (this.multipleSelection) {
      //   doPost(this.multipleSelection).then(res => {
      //     if (res.Code === 2000) {
      //       this.showNotify("success", "Common.postSuccess");
      //     } else {
      //       this.showNotify("error", res.Message);
      //     }
      //   });
      // }
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSelectionChangehz(val) {
      this.multipleSelection = val;
    },
    handleaffirmPacking() {
      this.isProcessing = true;
      const selectRows = {
        listDeliveryScan: this.multipleSelection,
        Container: this.temp.Container,
        Forwarder: this.temp.Forwarder,
        CustomsNum: this.temp.CustomsNum,
        CustomerNum: this.temp.CustomerNum,
        ShipmentNo: this.temp.ShipmentNo
      };
      addSDPacking(selectRows)
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        })
        .catch(error => {
          this.isProcessing = false;
        });
      this.dialogContainerVisible = false;
      // this.$confirm(
      //   this.$i18n.t('Common.actionConfirm'),
      //   this.$i18n.t('Common.tip'), {
      //     confirmButtonText: this.$i18n.t('Common.affirm'),
      //     cancelButtonText: this.$i18n.t('Common.cancel'),
      //     type: 'warning'
      //   }).then(() => {

      // })
    }
  }
};
</script>

