<template>
  <el-dialog :title="$t('ui.MD.Customer.title')" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.BaseNum"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleSearchFilter">
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.RequisitionNum')"
        prop="RequisitionNum"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RequisitionNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.BaseLine')"
        prop="BaseLine"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
        prop="ItemCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.ItemName')"
        prop="ItemName"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.DepartmentPickingApplication.FactoryCode')" prop="FactoryCode" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.FactoryCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.MovementType')"
        prop="MovementType"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MovementType }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.ApplyQty')"
        prop="ApplyQty"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ApplyQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.DepartmentPickingApplication.Unit')" prop="Unit" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.WhsCode')"
        prop="WhsCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.WhsName')"
        prop="WhsName"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.OrderNum')"
        prop="OrderNum"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.SalesOrderNum')"
        prop="SalesOrderNum"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.SalesOrderPro')"
        prop="SalesOrderPro"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderPro }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.AssessmentType')"
        prop="AssessmentType"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.AssessmentType }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.Remark')"
        prop="Remark"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.Cuser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetPageListBySAP
} from '@/api/MM/MM_DepartmentPickingApplication';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false
    }
  },
  computed: {

  },
  created() {},
  methods: {
    formatDate,
    formatDateTime,
    add() {
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      this.multipleSelection = selection;
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetPageListBySAP(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      this.$emit('ok', this.multipleSelection);
      this.dialogCustomerFormVisible = false;
    }
  }
}
</script>

<style scoped>
</style>
