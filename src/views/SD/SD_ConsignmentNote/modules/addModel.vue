<template>
  <el-drawer
    :title="title"
    :visible.sync="drawer"
    custom-class="drawerBox"
    :direction="direction"
    :destroy-on-close="destroyOnClose"
  >
    <div class="demo-drawer__content">
      <el-form ref="ruleForm" :model="model" label-width="120px" label-position="right" :rules="rules">
        <el-form-item label="合同号">
          <el-input v-model="model.ContractNo" />
        </el-form-item>
        <el-form-item label="物料件号" prop="ItemCode">
          <el-input v-model="model.ItemCode" @blur="blurItemCode" @keyup.enter.native="blurItemCode" />
        </el-form-item>
        <el-form-item label="物料名称">
          <el-input v-model="model.ItemName" />
        </el-form-item>
        <el-form-item label="物料组">
          <el-input v-model="model.ItmsGrpCode" />
        </el-form-item>
        <el-form-item label="数量" prop="Qty">
          <el-input v-model="model.Qty" />
        </el-form-item>
        <el-form-item label="单位">
          <el-input v-model="model.Unit" />
        </el-form-item>
        <!-- <el-form-item label="发货时间">
          <el-input v-model="model.DeliverDate" />
        </el-form-item> -->
        <el-form-item label="联系人">
          <el-input v-model="model.Contact" />
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input v-model="model.Telephone" />
        </el-form-item>
        <el-form-item label="重量（kg）" prop="Weight">
          <el-input v-model="model.Weight" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZSAP_MARCForCondition
} from '@/api/SD/SD_ConsignmentNote';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      destroyOnClose: true,
      direction: 'rtl',
      model: {
        ContractNo: '',
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        Qty: '',
        Unit: '',
        DeliverDate: '',
        Contact: '',
        Telephone: '',
        Weight: ''
      },
      rules: {
        ItemCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Weight: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Qty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      type: 'add'
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {
      this.model = {
        ContractNo: '',
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        Qty: '',
        Unit: '',
        DeliverDate: '',
        Contact: '',
        Telephone: '',
        Weight: ''
      };
      this.edit();
    },
    edit(record) {
      if (record) {
        this.type = 'edit'
      } else {
        this.type = 'add'
      }
      this.drawer = true;
      Object.assign(this.model, record)
    },
    handleSave() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (this.model.Qty === '0' || this.model.Qty === 0) {
            this.showNotify('warning', '数量不能为零');
            return
          }
          if (this.model.Weight === '0' || this.model.Weight === 0) {
            this.showNotify('warning', '重量不能为零');
            return
          }
          this.model.onlyId = new Date().getTime();
          this.$emit('ok', this.model, this.type);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    blurItemCode() {
      if (this.model.ItemCode) {
        const query = {
          keyword: this.model.ItemCode
        };
        GetXZSAP_MARCForCondition(query).then(res => {
          if (res.Code === 2000) {
            if (res.Data) {
              this.$nextTick(() => {
                this.model.ItemName = res.Data.MAKTX; // 物料名称
                this.model.Unit = res.Data.MEINS; // 单位
                this.model.ItmsGrpCode = res.Data.MATKL; // 物料组
                this.model.Weight = res.Data.NTGEW; // 净重
              });
            }
          }
        })
      }
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;

  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
<style>
  .el-drawer__body {
    overflow-y: auto;
  }

</style>
