<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.Status"
        size="small"
        filterable
        placeholder="状态"
        class="filter-item"
        style="width: 140px"
      >
        <el-option v-for="item in StatusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.Type"
        size="small"
        filterable
        placeholder="类型"
        class="filter-item"
        style="width: 140px"
      >
        <el-option v-for="item in TypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ConsignmentNote.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ConsignmentNote.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'SD.SD_ConsignmentNote.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <!-- <el-button v-waves v-permission="{name:'SD.SD_ConsignmentNote.Posting'}" class="filter-item" type="success"
        icon="el-icon-edit" size="small" :disabled="postDisable" @click="handlePosting">{{ $t('Common.posting') }}</el-button> -->
      <!-- <el-button v-permission="{ name: 'SD.SD_ConsignmentNote.Print' }" v-waves class="filter-item"
        type="primary" size="small" icon="el-icon-document"  :disabled="canNotUpdate" @click="handlePrint">{{ $t('Common.print') }}</el-button> -->
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ConsignmentNote.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ConsignmentNote.Successf' }"
        class="filter-item"
        type="success"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleSuccessf"
      >完成</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <!--
      <el-table-column label="类型" prop="Type" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发货日期"
        prop="DeliveryDate"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="发运计划单号" prop="ShippingPlanNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingPlanNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" prop="CustomerName" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户地址" prop="CustomerAdd" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerAdd }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结算地址" prop="CustomerRegion" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerRegion }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物流供应商" prop="LogisticsSupplierName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.LogisticsSupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="托运人" prop="Shipper" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Shipper }}</span>
        </template>
      </el-table-column>
      <el-table-column label="托运部门" prop="ShippingDepar" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingDepar }}</span>
        </template>
      </el-table-column>
      <el-table-column label="运输方式" prop="ShippingType" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌" prop="CarNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CarNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="特殊费用" prop="SpecialExpenses" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SpecialExpenses }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总金额(不含税)" prop="TotalTheoreticalAmount" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TotalTheoreticalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总重量" prop="TotalWeight" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TotalWeight }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="Status" fixed="right" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Status === 0">未发货</span>
          <span v-else-if="scope.row.Status ===1">未发货</span>
          <span v-else-if="scope.row.Status ===2">已发货</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>托运单明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column v-if="type" label="销售订单号" prop="BaseNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column v-if="type" label="销售单行号" prop="BaseLine" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="销售订单类型" v-if="type" prop="BaseType" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="合同号" prop="ContractNo" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料组" prop="ItmsGrpCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="单位" prop="Unit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{scope.row.Unit}}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="type"
        label="发货时间"
        prop="DeliverDate"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="联系人" prop="Contact" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="联系方式" prop="Telephone" align="center" width="120" show-overflow-tooltip />
      <el-table-column v-if="type" label="里程" prop="Mileage" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="费率" v-if="type" prop="MileageRate" align="center" width="100" show-overflow-tooltip /> -->
      <el-table-column label="重量" prop="Weight" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="重量费率" v-if="type" prop="WeightRate" align="center" width="100" show-overflow-tooltip /> -->
      <el-table-column label="行费用(不含税)" prop="RowTheoreticalAmount" align="center" width="120" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  GetDetailedPageList,
  batchDelete,
  exportExcelFile,
  printToPDF,
  Finish
} from '@/api/SD/SD_ConsignmentNote';

export default {
  name: 'SD.SD_ConsignmentNote',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        Type: 0,
        Status: -1
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      type: false,
      StatusOptions: [{
        value: -1,
        label: '全部发货'
      }, {
        value: 1,
        label: '未发货'
      }, {
        value: 2,
        label: '已发货'
      }],
      TypeOptions: [{
        value: 0,
        label: '全部计划'
      }, {
        value: 1,
        label: '计划内'
      }, {
        value: 2,
        label: '计划外'
      }]
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return this.multipleSelection.length === 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ConsignmentNote') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, this.$moment(new Date()).format('YYYY-MM-DD') + '托运单');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', res.Message || 'Common.postSuccess');
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    // 打印
    handlePrint() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      const binlocationCodes = selectRows.map(v => v.BinLocationCode);
      console.log(binlocationCodes);
      printToPDF(binlocationCodes).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        console.log(v.Status, v.Type);
        console.log(v.Status !== 0 || v.Type === 1);
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.Status !== 0 || v.Type === 1) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已同步SRM，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const arrRowsID = selectRows.map(v => v.DocNum);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      if (this.currentRow.Type === 1) {
        this.type = true;
      } else {
        this.type = false;
      }
      const query = {
        keyword: this.currentRow.DocNum,
        PageNumber: this.listDetailQuery.PageNumber,
        PageSize: this.listDetailQuery.PageSize
      };
      GetDetailedPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('SD.SD_ConsignmentNoteDetail');
    },
    handleUpdate() {
      if (this.multipleSelection[0].Status === 2) {
        this.showNotify('warning', '当前数据已经发货');
      } else {
        const selectRows = this.multipleSelection;
        let switchBtn = true;
        selectRows.some(v => {
          console.log(v.Status, v.Type);
          console.log(v.Status !== 0 || v.Type === 1);
          if (this.$store.getters.userRole !== 1) {
            if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
              this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
              switchBtn = false;
              this.isProcessing = false;
              return true;
            }
          }
        });
        if (switchBtn) {
          this.routeTo('SD.SD_ConsignmentNoteDetail', this.multipleSelection[0]);
        }
      }
    },
    handleSuccessf() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.ShippingPlanStatus === 3) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已完成，请勿重复操作');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Finish({
          DocNums: arrRowsID
        }).then(res => {
          this.isProcessing = false;
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        })
          .catch(error => {
            this.isProcessing = false;
          });
      }
    }
  }
};
</script>
