<template>
  <div class="app-container">
    <p>
      <label style="width:100%">托运单明申请登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="托运单号">
            <el-input v-model="searchQuery.DocNum" disabled class="input" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="托运部门" prop="ShippingDepar">
            <!-- <el-input v-model="searchQuery.ShippingDepar" class="input" /> -->
            <el-select
              v-model="searchQuery.ShippingDepar"
              filterable
              placeholder="请选择"
              class="input"
              @change="changeShippingDepar"
            >
              <el-option v-for="item in ShippingDeparOptions" :key="item.KOSTL" :label="item.LTEXT" :value="item.KOSTL" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="物流供应商">
            <el-select
              v-model="searchQuery.LogisticsSupplierCode"
              filterable
              placeholder="请选择"
              class="input"
              :disabled="typeDisabled"
              @change="changeLogisticsSupplier"
            >
              <el-option
                v-for="item in LogisticsSupplierOptions"
                :key="item.SupplierCode"
                :label="item.SupplierName"
                :value="item.SupplierCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="运输方式">
            <el-input v-model="searchQuery.ShippingType" class="input" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="托运人">
            <el-input v-model="searchQuery.Shipper" class="input" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="车号">
            <el-input v-model="searchQuery.CarNum" class="input" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="特殊费用">
            <el-input v-model="searchQuery.SpecialExpenses" class="input" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="总金额(不含税)">
            <el-input v-model="searchQuery.TotalTheoreticalAmount" class="input" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="总重量（kg）" prop="TotalWeight">
            <el-input v-model="searchQuery.TotalWeight" class="input" :disabled="typeDisabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="客户">
            <el-select
              v-if="!typeDisabled"
              v-model="searchQuery.CustomerID"
              filterable
              placeholder="请选择"
              class="input"
              :disabled="typeDisabled"
              @change="changeCustomer"
            >
              <el-option
                v-for="item in CustomerOptions"
                :key="item.CustomerCode"
                :label="item.CustomerName"
                :value="item.CustomerId"
              />
            </el-select>
            <el-input v-else v-model="searchQuery.CustomerName" class="input" :disabled="typeDisabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="发运地址">
            <el-input v-model="searchQuery.CustomerAdd" type="textarea" :rows="2" class="input" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="结算地址">
            <el-input v-model="searchQuery.CustomerRegion" type="textarea" :rows="2" class="input" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="filter-item" label="发货日期" prop="DeliveryDate">
            <el-date-picker v-model="searchQuery.DeliveryDate" :clearable="false" type="date" placeholder="选择发货日期" format="yyyy-MM-dd" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" class="input" />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <p>
      <el-button v-if="!typeDisabled" class="filter-item" size="small" type="primary" icon="el-icon-plus" @click="handleCreate">
        {{ $t("Common.add") }}
      </el-button>
      <el-button
        v-if="!typeDisabled"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDeleteDetail"
      >{{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}</el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <!-- <el-table-column label="销售订单号" prop="BaseNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售单行号" prop="BaseLine" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售订单类型" prop="BaseType" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="合同号" prop="ContractNo" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料组" prop="ItmsGrpCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="发货时间" prop="DeliverDate" align="center" width="160" show-overflow-tooltip  :formatter="formatDate"/> -->
      <el-table-column label="联系人" prop="Contact" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="联系方式" prop="Telephone" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="重量" prop="Weight" align="center" width="160" show-overflow-tooltip />
      <el-table-column v-if="!typeDisabled" fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <add-select-model ref="modalForm" @ok="modalFormOk" /> -->
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  Update,
  GetSRM_WLSupplierInfo,
  GetAllList,
  GetXZ_SAP_CSKS
} from '@/api/SD/SD_ConsignmentNote';
export default {
  name: 'SD.SD_ConsignmentNoteDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        ShippingPlanNum: '',
        ShippingDepar: '',
        LogisticsSupplierName: '',
        LogisticsSupplierCode: '',
        ShippingType: '',
        Shipper: '',
        CarNum: '',
        SpecialExpenses: '',
        TotalTheoreticalAmount: '',
        TotalWeight: '',
        CustomerID: '',
        CustomerName: '',
        CustomerCode: '',
        CustomerAdd: '',
        CustomerRegion: '', // 结算地址
        DeliveryDate: new Date(new Date().setDate(new Date().getDate() + 1)), // 发货日期
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        LogisticsSupplierCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        ShippingDepar: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        TotalTheoreticalAmount: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Shipper: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        TotalWeight: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        DeliveryDate: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: '',
      CustomerOptions: [],
      LogisticsSupplierOptions: [],
      typeDisabled: false,
      delList: [],
      ShippingDeparOptions: []
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/SD/SD_ConsignmentNoteDetail') {
        this.getPageParams();
        this.GetSRMSupplierInfo();
        this.GetXZSAPCSKS();
        this.GetAllList();
      }
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.$refs.dataForm.clearValidate();
    // });
    this.getPageParams();
    this.GetSRMSupplierInfo();
    this.GetXZSAPCSKS();
    this.GetAllList();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      GetDetailListForTest(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ConsignmentNoteDetailID) {
            v.IsDelete = true;
            this.delList.push(v.ConsignmentNoteDetailID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (this.list.length === 0) {
            this.showNotify('warning', '请添加明细信息');
            return;
          }
          let switchBtn = true;
          this.list.some(res => {
            if (res.Qty === null || res.Qty === 0 || res.Qty === '0' || res.Qty === '') {
              this.showNotify('warning', '数量不能为空或者为零');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            this.startLoading();
            const query = {
              detailed: this.list,
              deletedetail: this.delList
            };
            Object.assign(query, this.searchQuery);
            if (this.editStatus === 'create') {
              SubmitScanInfo(query).then(res => {
                if (res.Code === 2000) {
                  this.backTo('SD.SD_ConsignmentNote');
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            } else {
              Update(query).then(res => {
                if (res.Code === 2000) {
                  this.backTo('SD.SD_ConsignmentNote');
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            }
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleCreate() {
      this.$refs.modalFormAdd.add();
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.add');
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record, type) {
      if (type === 'edit') {
        this.list.forEach((v, index) => {
          if (v.ConsignmentNoteDetailID) {
            if (v.ConsignmentNoteDetailID === record.ConsignmentNoteDetailID) {
              this.$set(this.list, index, record);
            }
          } else {
            if (v.onlyId === record.onlyId) {
              this.$set(this.list, index, record);
            }
          }
        });
      } else {
        this.list.push(record);
      }
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      this.list = record;
      let model = '';
      record.forEach(v => {
        model += v.ItemCode + ',';
        this.searchQuery.ItemCode = model.substr(0, model.length - 1);
      });
    },
    getDetailList() {
      const query = {
        keyword: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ConsignmentNoteID) {
        this.editStatus = 'edit';
        if (this.searchQuery.Type === 1) {
          this.typeDisabled = true;
        } else {
          this.typeDisabled = false;
        }
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    GetSRMSupplierInfo() {
      GetSRM_WLSupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.LogisticsSupplierOptions = res.Data;
        }
      })
    },
    changeLogisticsSupplier(e) {
      const obj = this.LogisticsSupplierOptions.find(v => v.SupplierCode === e);
      this.searchQuery.LogisticsSupplierName = obj.SupplierName;
    },
    GetXZSAPCSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.ShippingDeparOptions = res.Data;
        }
      })
    },
    changeShippingDepar(e) {
      const obj = this.ShippingDeparOptions.find(v => v.KOSTL === e);
      this.searchQuery.ShippingDepar = obj.LTEXT;
      this.searchQuery.CostCenter = obj.KOSTL;
    },
    GetAllList() {
      GetAllList().then(res => {
        if (res.Code === 2000) {
          this.CustomerOptions = res.Data;
        }
      })
    },
    changeCustomer(e) {
      const obj = this.CustomerOptions.find(v => v.CustomerId === e);
      this.searchQuery.CustomerName = obj.CustomerName;
      this.searchQuery.CustomerCode = obj.CustomerCode;
      this.searchQuery.CustomerAdd = obj.CustomerAdd;
      this.searchQuery.CustomerRegion = obj.SettlementAdd;
    }
  }
}
</script>

<style scoped>
  .input {
    width: 200px;
  }

</style>
