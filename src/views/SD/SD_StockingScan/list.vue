<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <!-- <el-select
        v-model="listQuery.isPosted"
        :placeholder="$t('ui.SD.SD_DeliveryScan.IsPosted')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>-->
      <el-select
        v-model="listQuery.isDelivery"
        filterable
        :placeholder="$t('ui.SD.SD_StockingScan.IsDeliveryState')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isDeliveryOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{name:'SD.SD_StockingScan.Delete'}"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <!--v-permission="{name:'SD.SD_StockingScan.Delete'}"-->
      <el-button
        v-waves
        v-permission="{name:'SD.SD_StockingScan.Affirmdy'}"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="success"
        icon="el-icon-check"
        @click="handleDelivery"
      >{{ $t('Common.affirmdy') }}</el-button>
      <!--      v-permission="{name:'SD.SD_StockingScan.CreateNote'}"-->
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_StockingScan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoadinghz"
      :data="listhz"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChangehz"
      @row-click="getDetailListhz"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.ScanID')"
        prop="ScanID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.DocNum')"
        prop="DocNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.BaseNum')"
        prop="BaseNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.SumQty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.CustomerName')"
        prop="CustomerName"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.BarCode')"
        prop="BarCode"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.BatchNum')"
        prop="BatchNum"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.IsDelivery')"
        prop="IsDelivery"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelivery | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalhz>0"
      :total="totalhz"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
    <!-- 子表 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />-->
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.ScanID')"
        prop="ScanID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.WaveNum')"
        prop="WaveNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WaveNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.SD.SD_StockingScan.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.CustomerName')"
        prop="CustomerName"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.BoxBarCode')"
        prop="BoxBarCode"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.BarCode')"
        prop="BarCode"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.BatchNum')"
        prop="BatchNum"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.SD.SD_StockingScan.OutWhsName')"
        prop="OutWhsName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.OutRegionName')"
        prop="OutRegionName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.OutBinLocationName')"
        prop="OutBinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.SD.SD_StockingScan.InWhsName')"
        prop="InWhsName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.InRegionName')"
        prop="InRegionName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.InBinLocationName')"
        prop="InBinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.IsDelivery')"
        prop="IsDelivery"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelivery | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.SD.SD_StockingScan.IsPosted')"
        prop="IsPosted"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.PostUser')"
        prop="PostUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_StockingScan.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
        :formatter="formatDateTime"
      />

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />-->
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import permission from '@/directive/permission/index.js'; // 权限判断指令
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { formatDate, formatDateTime } from '../../../utils';
import {
  fetchPage,
  fetchList,
  fetchCountList,
  fetchDetailList,
  batchDelete,
  exportExcelFile
} from '../../../api/SD/SD_StockingScan';
import { addDeliveryNote } from '../../../api/SD/SD_DeliveryScan';
import { exportToExcel } from '@/utils/excel-export';

export default {
  name: 'SDStockingScan',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      dateRangeValue: [
        new Date(),
        new Date()
      ],

      list: [],
      listhz: [],
      total: 0,
      totalhz: 0,
      listLoading: false,
      listLoadinghz: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: '',
        isPosted: '',
        isDelivery: false
      },
      isPostedOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.posted'),
          key: true
        },
        {
          label: this.$i18n.t('Common.notPosted'),
          key: false
        }
      ],
      isDeliveryOptions: [
        {
          label: this.$i18n.t('ui.SD.SD_StockingScan.DeliveryState.isDelivery'),
          key: true
        },
        {
          label: this.$i18n.t('ui.SD.SD_StockingScan.DeliveryState.unDelivery'),
          key: false
        }
      ],
      multipleSelection: [],
      selectRow: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IsDelivery) {
          return true;
        }
      }
      return false;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoadinghz = true;
      // this.listQuery.fromTime = "";
      // this.listQuery.toTime = "";
      // if (this.dateRangeValue) {
      //   this.listQuery.fromTime = this.dateRangeValue[0];
      //   this.listQuery.toTime = this.dateRangeValue[1];
      // }
      var newListQuery = {
        PageNumber: this.listQuery.PageNumber,
        PageSize: this.listQuery.PageSize,
        sort: this.listQuery.sort,
        keyword: this.listQuery.keyword,
        dateValue: [
          new Date(),
          new Date()
        ],
        isDelivery: this.listQuery.isDelivery
      };
      fetchCountList(newListQuery).then(response => {
        if (response.Code === 2000) {
          this.listhz = response.Data.items;
          this.totalhz = response.Data.total;
          this.list = [];
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoadinghz = false;
      });
    },
    getDetailListhz(row) {
      this.listLoading = true;
      this.selectRow = row;
      fetchDetailList({
        BatchNum: row.BatchNum,
        BoxBarCode: row.BoxBarCode,
        DocNum: row.DocNum,
        CustomerCode: row.CustomerCode,
        ItemCode: row.ItemCode,
        BaseNum: row.BaseNum
      }).then(response => {
        this.list = response.Data;
        this.listLoading = false;
      });
    },
    sortChangehz(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getDetailListhz(this.selectRow);
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        // let arrRowsID = selectRows.map(v => v.BoxBarCode);IsDelivery
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(selectRows)
          .then(res => {
            console.log(res);
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
              this.handleFilter();
            } else {
              this.showNotify('error', res.Message);
            }
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleDelivery() {
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.actionConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          addDeliveryNote(selectRows)
            .then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.operationSuccess');
                this.routeTo('SD.SD_DeliveryScan');
                this.handleFilter();
              } else {
                this.showNotify('error', res.Message);
              }
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.fromTime, this.listQuery.toTime],
        isDelivery: this.listQuery.isDelivery,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '销售备货单')
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>

