<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <!-- <el-date-picker
        v-model="dateRangeValue"
        class="filter-item"
        type="daterange"
        style="width: 300px"
        :picker-options="pickerOptions"
        range-separator="-" :unlink-panels="true"
        :start-placeholder="$t('ui.PP.SAP_SD_SaleOrder.CreatedDateStart')"
        :end-placeholder="$t('ui.PP.SAP_SD_SaleOrder.CreatedDateEnd')"
      />-->
      <el-input
        v-model="listQuery.SaleOrder"
        type="text"
        :placeholder="$t('ui.SD.SAP_SD_SaleOrder.SalesOrderID')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="from"
        filterable
        style="width: 140px"
        class="filter-item"
        :placeholder="$t('ui.SD.SAP_SD_SaleOrder.datasource')"
      >
        <el-option value="1" :label="$t('ui.SD.SAP_SD_SaleOrder.fromRemote')" />
        <el-option value="2" :label="$t('ui.SD.SAP_SD_SaleOrder.fromLocal')" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />

      <!-- v-permission="{ name: 'PP.PP_ProductionOrder.Sync' }" -->
      <el-button
        v-waves
        :disabled="isProcessing"
        class="filter-item"
        type="success"
        icon="el-icon-download"
        @click="handleSync"
      >{{ $t("Common.sync") }}</el-button>
      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :height="200"
      :data="list"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      @sort-change="sortChange"
      @row-click="handleRowClick"
      @selection-change="handleMultiSelection"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.SalesOrderID')"
        prop="SalesOrderID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.ReceivingPartyCode')"
        prop="ReceivingPartyCode"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReceivingPartyCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.ReceivingPartyName')"
        prop="ReceivingPartyName"

        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReceivingPartyName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.ReceivingPartyAdd')"
        prop="ReceivingPartyAdd"

        align="center"
        width="260"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ReceivingPartyAdd }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.OrderStatus')"
        prop="OrderStatus"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OrderStatus|SDOrderStates }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.CustomerCode')"
        prop="CustomerCode"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.CustomerName')"
        prop="CustomerName"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleFilter"
    />

    <p>{{ $t("ui.SD.SAP_SD_SaleOrder.title") }}</p>
    <el-table
      v-loading="listLoadingDetail"
      :height="300"
      :data="listDetail"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
    >
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.SalesOrderID')"
        prop="SalesOrderID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.ItemID')"
        prop="ItemID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.ProductID')"
        prop="ProductID"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.ProductDescription')"
        prop="ProductDescription"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.Quantity')"
        prop="Quantity"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SAP_SD_SaleOrder.UnitCode')"
        prop="UnitCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.UnitCode }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  syncSaleOrder,
  fetchList,
  fetchGetDetailList,
  fetchSAPList
} from '../../../api/SD/SAP_SD_SaleOrder';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import permission from '@/directive/permission/index.js'; // 权限判断指令
import { formatDate, formatDateTime } from '../../../utils';

export default {
  name: 'SD.SAP_SD_SaleOrder',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      list: [],
      listDetail: [],
      listSAPDetail: [],
      total: 0,
      listLoading: true,
      listLoadingDetail: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        dateValue: [
          new Date(),
          new Date()
        ],
        SaleOrder: ''
      },
      from: '2',
      multipleSelection: [],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listDetail = [];
      if (this.from == '2') {
        fetchList(this.listQuery).then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false;
        });
      } else {
        fetchSAPList(this.listQuery).then(response => {
          this.list = response.Data.CountData;
          this.listSAPDetail = response.Data.DetailData;
          this.listLoading = false;
        });
      }
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSync() {
      if (this.listQuery.SaleOrder == '') {
        this.showNotify('warning', 'ui.SD.SAP_SD_SaleOrder.PlaseInSaleOrder');
        return;
      }
      var syncQueryList = {
        SaleOrder: this.listQuery.SaleOrder
      };
      this.showNotify('warning', 'Common.syncStart');
      this.isProcessing = true;
      this.from = '2';
      syncSaleOrder(syncQueryList)
        .then(response => {
          this.isProcessing = false;
          if (response && response.Code === 2000) {
            this.showNotify('success', 'ui.SD.SAP_SD_SaleOrder.syncSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    handleRowClick(row) {
      this.listLoadingDetail = true;
      if (this.from == '1') {
        var listDatas = this.listSAPDetail.filter(
          x => x.SalesOrderID == row.SalesOrderID
        );
        this.listDetail = listDatas;

        this.listLoadingDetail = false;
        return;
      }
      fetchGetDetailList({ SaleOrder: row.SalesOrderID })
        .then(response => {
          this.listDetail = response.Data;
          this.listLoadingDetail = false;
        })
        .catch(error => {
          this.listLoadingDetail = false;
        });
    },
    handleMultiSelection() {},
    sortChange(data) {
      if (this.from == '1') {
        return;
      }
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
