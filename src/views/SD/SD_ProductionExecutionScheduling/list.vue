<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-date-picker
        v-model="listQuery.deliveryDateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.BaseType"
        size="small"
        filterable
        placeholder="订单类型"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in BaseTypeOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.BaseNum"
        size="small"
        class="filter-item"
        placeholder="销售订单号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.BaseLine"
        size="small"
        class="filter-item"
        placeholder="销售订单行号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CustomerName"
        size="small"
        class="filter-item"
        placeholder="客户名"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ItemCode"
        size="small"
        class="filter-item"
        placeholder="物料件号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.BarCode"
        size="small"
        class="filter-item"
        placeholder="出厂编号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CONT"
        size="small"
        class="filter-item"
        placeholder="合同号"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.CUser"
        size="small"
        class="filter-item"
        placeholder="创建人"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.SAPmark"
        size="small"
        filterable
        placeholder="错误消息"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isSapMessageOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{name:'SD.SD_ProductionExecutionScheduling.Delete'}"
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'SD.SD_ProductionExecutionScheduling.Posting'}"
        class="filter-item"
        type="success"
        size="small"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}
      </el-button>
      <el-dropdown
        v-waves
        v-permission="{ name: 'SD.SD_ProductionExecutionScheduling.Print' }"
        class="filter-item"
        split-button
        type="primary"
        size="small"
        style="margin-left: 10px;"
        @click="handlePrint"
        @command="handleCommand"
      >
        {{ $t('Common.print') }}
        <el-dropdown-menu>
          <el-dropdown-item command="1" :disabled="deletable">按单号打印</el-dropdown-item>
          <el-dropdown-item command="2" :disabled="deletable">合并地址打印</el-dropdown-item>
          <el-dropdown-item command="3" :disabled="deletable">雷登打印</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-permission="{ name: 'SD.SD_ProductionExecutionScheduling.PassPost' }"
        v-waves
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-document"
        :disabled="deletable"
        @click="handlePassPost"
      >
        冲销
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ProductionExecutionScheduling.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="dialogSetShippingToDeliveryVisible = !dialogSetShippingToDeliveryVisible"
      >同步已完工</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ProductionExecutionScheduling.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ProductionExecutionScheduling.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ProductionExecutionScheduling.DownLoadTemp' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModel"
      >下载模板</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="交货单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售单号" prop="BaseNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售单行号" prop="BaseLine" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="DeliveryScanQty" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryScanQty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单类型" prop="BaseType" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="客户编号" prop="CustomerCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" prop="CustomerName" align="center" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发货仓库" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="客户地址" prop="CustomerAdd" align="center" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerAdd }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结算地址" prop="SettlementAdd" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SettlementAdd }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="交货日期"
        prop="DeliveryDate"
        align="center"
        width="150"
        :formatter="formatDate"
      />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="150"
        :formatter="formatDateTime"
      />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      :page-sizes.sync="pageSizes"
      @pagination="getList"
    />

    <p>
      <span>销售交货明细表</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="行号" prop="Line" align="center" width="100" show-overflow-tooltip /> -->
      <el-table-column label="销售订单号" prop="BaseNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售行号" prop="BaseLine" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="销售订单类型" prop="BaseType" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="批次" prop="BatchNum" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="客户" prop="CustomerName" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="客户地址" prop="CustomerAdd" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="交货数量" prop="DeliveryScanQty" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="单位" prop="Unit" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="项目类别" prop="ProjectCategory" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="项目名称" prop="Project" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" show-overflow-tooltip />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="120"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="Sap物料凭证单号" prop="SapDocNum" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="Sap物料凭证行号" prop="SapLine" align="center"width="160" show-overflow-tooltip /> -->
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 同步完工日期设置 -->
    <el-dialog title="同步完工日期设置" :visible.sync="dialogSetShippingToDeliveryVisible" width="30%">
      <div>
        <el-form ref="setShipmentDate" label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="同步日期" prop="Package_ConfigerName" style="width:93%">
                <el-date-picker
                  v-model="setDeliveryDate"
                  class="filter-item"
                  :picker-options="pickerOptions"
                  range-separator="-"
                  :unlink-panels="true"
                  type="daterange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
                  :start-placeholder="$t('Common.startTime')"
                  :end-placeholder="$t('Common.endTime')"
                />

              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogSetShippingToDeliveryVisible = !dialogSetShippingToDeliveryVisible">取消</el-button>
          <el-button type="primary" @click="syncCompleted">设定发运日期并同步WMS</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime, parseTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  batchDelete,
  exportExcelFile,
  DoPost,
  ManualMakeConsignmentNote,
  printOrderToPDF,
  GetPageDetailList,
  improtExcelFile,
  exportExcelModel,
  PassPost,
  getPrintInfo
} from '@/api/SD/SD_ProductionExecutionScheduling';
import {
  SetShippingToDelivery
} from '@/api/PP/PP_WorkshopScheduling';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import includingPriceTemplateBox from './print/includingPrice.json';
import notIncludingPriceTemplateBox from './print/notIncludingPrice.json';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

const includingPriceTempBox = new hiprint.PrintTemplate({ template: includingPriceTemplateBox });
const notIncludingPriceTempBox = new hiprint.PrintTemplate({ template: notIncludingPriceTemplateBox });

export default {
  name: 'SD.SD_ProductionExecutionScheduling',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      dialogSetShippingToDeliveryVisible: false,
      setDeliveryDate: [
      ],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        deliveryDateValue: [
        ],
        isPosted: '',
        BaseNum: '', //  销售订单号
        BaseLine: '',
        CustomerName: '', //  客户名
        ItemCode: '', //   物料件号
        BarCode: '', //  出厂编号
        CONT: '', // 合同号
        SAPmark: '正常', // 错误消息
        CUser: '', // 创建人
        BaseType: '' // 订单类型
      },
      pageSizes: [10, 50, 100, 200, 500],
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      isSapMessageOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '正常',
        key: '正常'
      },
      {
        label: '错误',
        key: '错误'
      }
      ],
      BaseTypeOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '主机',
        key: '主机'
      },
      {
        label: '部件',
        key: '部件'
      },
      {
        label: '售后主机',
        key: '售后主机'
      },
      {
        label: '部件售后',
        key: '部件售后'
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    syncCompleted() {
      if (this.setDeliveryDate.length !== 2) {
        this.showNotify('error', '请选择日期范围');
        return;
      }
      this.$confirm(
        '此操作将同步完工的发运输入导入交货, 是否继续?',
        '同步确认', {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        this.dialogSetShippingToDeliveryVisible = false;
        SetShippingToDelivery({ dateValue: this.setDeliveryDate }).then(res => {
          this.isProcessing = false;
          if (res.Code === 2000) {
            this.showNotify('success', '同步成功');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
        }).catch(error => {
          this.isProcessing = false;
        });
      });
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        BaseNum: this.listQuery.BaseNum,
        BaseLine: this.listQuery.BaseLine,
        CustomerName: this.listQuery.CustomerName,
        ItemCode: this.listQuery.ItemCode,
        BarCode: this.listQuery.BarCode,
        CONT: this.listQuery.CONT,
        SAPmark: this.listQuery.SAPmark,
        CUser: this.listQuery.CUser,
        BaseType: this.listQuery.BaseType
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, this.$moment(new Date()).format('YYYY-MM-DD') + '销售交货');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          const arrRowsID = this.multipleSelection.map(v => v.DocNum);
          DoPost(arrRowsID)
            .then(res => {
              if (res.Code === 2000) {
                this.$alert(res.Message || 'Common.postSuccess', res.MessageParam === 2000 ? '成功'
                  : '失败', {
                  confirmButtonText: '确定',
                  closeOnClickModal: false,
                  showCancelButton: false,
                  callback: action => {
                    this.handleFilter();
                    this.isProcessing = false;
                  }
                });
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const arrRowsID = selectRows.map(v => v.DocNum);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        DocNum: this.currentRow.DocNum.trim()
      });
      GetPageDetailList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleConsignmentNote() {
      this.isProcessing = true;
      ManualMakeConsignmentNote(this.multipleSelection)
        .then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.postSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
          this.isProcessing = false;
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    // 打印
    handlePrint() {
      const selectRows = this.multipleSelection;
      console.log('打印：', selectRows);
      this.isProcessing = true;
      if (this.checkMultiSelection(selectRows)) {
        const docNums = selectRows.map(v => v.DocNum);
        console.log(docNums);
        printOrderToPDF({
          docNums: docNums
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      } else {
        this.isProcessing = false;
      }
    },
    handleCommand(command) {
      this.isProcessing = true;
      if (command === '1') {
        this.notMergePrint(notIncludingPriceTempBox);
      } else if (command === '2') {
        this.print(notIncludingPriceTempBox)
      } else if (command === '3') {
        const selectRows = this.multipleSelection;
        for (let i = 0;i < selectRows.length;i++) {
          if (selectRows[i].CustomerCode !== 'Z001201606') {
            this.isProcessing = false;
            this.showNotify('error', '只允许打印客户：Z001201606-雷登电梯有限公司');
            return;
          }
        }
        this.print(includingPriceTempBox)
      }
    },
    notMergePrint(tempBox) {
      const selectRows = this.multipleSelection;
      const docNums = selectRows.map(v => v.DocNum);
      getPrintInfo({
        DocNums: docNums
      }).then(response => {
        const printData = [];
        const res = response.Data;
        const detailMap = res.reduce((map, detail) => {
          // 如果没有这个 customerId，初始化一个空数组
          if (!map.has(detail.CustomerName)) {
            map.set(detail.CustomerName, []);
          }
          // 将当前客户对象推入对应 customerId 的数组中
          map.get(detail.CustomerName).push(detail);
          return map;
        }, new Map());
        for (const [key, details] of detailMap) {
          if (details.length > 0) {
            for (let j = 0;j < details.length;j++) {
              const detailList = []
              detailList.push({
                No: j + 1,
                CONT: details[j].CONT,
                ItemName: details[j].ItemName,
                EapSerialNo: details[j].EapSerialNo,
                Project: details[j].Project,
                DeliveryScanQty: details[j].DeliveryScanQty,
                Price: details[j].Price
              })
              printData.push({
                // 主表
                CustomerName: details[0].CustomerName,
                CustomerAdd: details[0].CustomerAdd,
                CustomerCode: details[0].CustomerCode,
                ContactTelephone: details[0].Contact + '/' + details[0].Telephone,
                Creator: details[0].CUserName,
                CreateTime: parseTime(details[0].CTime, '{y}-{m}-{d}'),
                DetailList: detailList
              })
            }
          }
        }
        // 设置模板并 打印
        tempBox.print(printData, {
          // 将覆盖面板偏移设置
          leftOffset: 0, // 左偏移
          topOffset: 0// 上偏移
        }, {
          callback: () => {
            // 关闭打印窗口回调
            this.isProcessing = false;
          }
        });
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    print(tempBox) {
      const selectRows = this.multipleSelection;
      const docNums = selectRows.map(v => v.DocNum);
      getPrintInfo({
        docNums: docNums
      }).then(response => {
        const printData = [];
        const res = response.Data;
        const detailMap = res.reduce((map, detail) => {
          // 如果没有这个 customerId，初始化一个空数组
          if (!map.has(detail.CustomerName + detail.CustomerAdd)) {
            map.set(detail.CustomerName + detail.CustomerAdd, []);
          }
          // 将当前客户对象推入对应 customerId 的数组中
          map.get(detail.CustomerName + detail.CustomerAdd).push(detail);
          return map;
        }, new Map());
        for (const [key, details] of detailMap) {
          if (details.length > 0) {
            const detailList = []
            for (let j = 0;j < details.length;j++) {
              detailList.push({
                No: j + 1,
                CONT: details[j].CONT,
                ItemName: details[j].ItemName,
                EapSerialNo: details[j].EapSerialNo,
                Project: details[j].Project,
                DeliveryScanQty: details[j].DeliveryScanQty,
                Price: details[j].Price
              })
            }
            printData.push({
              // 主表
              CustomerName: details[0].CustomerName,
              CustomerAdd: details[0].CustomerAdd,
              CustomerCode: details[0].CustomerCode,
              ContactTelephone: details[0].Contact + '/' + details[0].Telephone,
              Creator: details[0].CUserName,
              CreateTime: parseTime(details[0].CTime, '{y}-{m}-{d}'),
              DetailList: detailList
            })
          }
        }
        // 设置模板并 打印
        // 设置模板并 打印
        tempBox.print(printData, {
          // 将覆盖面板偏移设置
          leftOffset: 0, // 左偏移
          topOffset: 0// 上偏移
        }, {
          callback: () => {
            // 关闭打印窗口回调
            this.isProcessing = false;
          }
        });
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImportData(data) {
      this.uploadExcelData = data;
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          if (response.Code === 2000) {
            this.showNotify('success', response.Message);
          } else {
            this.showNotify('warning', '导入失败');
          }
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImportData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    handlePassPost() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === false) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，禁止冲销');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.isProcessing = true;
        const docNums = selectRows.map(v => v.DocNum);
        PassPost(docNums).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '操作成功！');
            } else {
              this.showNotify('warning', res.Message);
            }
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    }
  }
};
</script>
