<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        size="small"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('ui.SD.SD_DeliveryScan.IsPosted')"
        style="width: 140px"
        class="filter-item"
        size="small"
        @change="handleFilter"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ReturnScan.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ReturnScan.Posting' }"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="deletable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_ReturnScan.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :row-class-name="tableRowClassName"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :height="tableHeight"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="行号" prop="Line" align="center" width="140" show-overflow-tooltip/> -->
      <el-table-column label="退货订单号" prop="BaseNum" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退货订单行号" prop="BaseLine" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="批次" prop="BatchNum" align="center" width="160" show-overflow-tooltip/>
      <el-table-column label="客户名称" prop="CustomerName" align="center" width="160" show-overflow-tooltip/> -->
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.ItemName')"
        prop="ItemName"
        align="center"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料组名称" prop="ItmsGrpName" align="center" width="220" show-overflow-tooltip/> -->
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.Qty')"
        prop="Qty"
        align="center"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.Unit')"
        prop="Unit"
        align="center"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="区域编号" prop="RegionCode" align="center" width="120" show-overflow-tooltip >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="区域名称" prop="RegionName" align="center" width="120" show-overflow-tooltip >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库位编号" prop="BinLocationCode" align="center" width="120" show-overflow-tooltip >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库位名称" prop="BinLocationName" align="center" width="120" show-overflow-tooltip >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.IsPosted')"
        prop="IsPosted"
        align="center"
        width="100"
        show-overflow-tooltip
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.PostUser')"
        prop="PostUser"
        align="center"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_ReturnScan.PostTime')"
        prop="PostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="Sap销售凭证单号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Sap销售凭证行号" prop="SapLine" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="160"
        show-overflow-tooltip
      />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import permission from '@/directive/permission/index.js'; // 权限判断指令
import {
  fetchList,
  batchDelete,
  doPost,
  exportExcelFile
} from '../../../api/SD/SD_ReturnScan';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  convertToKeyValue
} from '@/utils';
import {
  MessageBox
} from 'element-ui'; // 提示框

export default {
  name: 'SD.SD_ReturnScan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        isPosted: '',
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      isProcessing: false,
      tableHeight: '300px'
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) {
        return true;
      }
      while (i--) {
        if (this.multipleSelection[i].IsPosted) {
          return true;
        }
      }
    }
  },
  created() {
    this.handleFilter();
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          const arrRowsID = selectRows.map(v => v.ReturnScanID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter();
              } else {
                this.showNotify('error', res.Message);
              }
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    // 过账SAP
    handlePosting() {
      // const postData = {master:this.selectedRow,details:this.multipleSelection}
      var selectRows = this.multipleSelection;
      // var row = selectRows.find(obj => obj.IsPosted);
      // if (row) {
      //   this.showNotify("error", "Common.handlingPostedDataNotPermitted");
      //   return false;
      // }
      if (this.checkMultiSelection(selectRows)) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          this.isProcessing = true;
          doPost(this.multipleSelection)
            .then(response => {
              if (response.Code === 2000) {
                if (response.MessageParam !== 2000) {
                  this.showNotify('warning', response.Message);
                } else {
                  this.showNotify('success', response.Message);
                }
                // this.showNotify("success", "Common.operationSuccess");
                this.handleFilter(1);
                this.isProcessing = false;
              } else {
                this.showNotify('error', response.Message);
                this.isProcessing = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    handleExport() {
      this.isProcessing = false;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, this.$moment(new Date()).format('YYYY-MM-DD') + '销售退货');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    }
  }
};
</script>
