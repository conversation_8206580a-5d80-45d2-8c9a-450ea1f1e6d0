<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('ui.SD.SD_BarCodeReturn.title') }}</label>
    </p>
    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-delete"
        @click="resetFormData"
      >{{ $t('Common.empty') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleCommit"
      >{{ $t('Common.confirm') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-back"
        @click="handleBack"
      >{{ $t('Common.cancel') }}</el-button>
    </div>
    <el-form
      ref="dataForm"
      :inline="false"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.BaseEntry')" prop="BaseEntry">
            <el-input
              v-model="temp.BaseEntry"
              :placeholder="$t('ui.SD.ReturnRequest.select')"
              readonly
            >
              <el-button slot="append" icon="el-icon-more" @click="selectsddelivergoodsOrder" />
            </el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.BatchNum')" prop="BatchNum">
            <el-input v-model="temp.BatchNum" disabled/>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.PTime')" prop="PTime">
            <el-date-picker
              v-model="temp.PTime"
              type="date"
              :clearable="false"
              :placeholder="$t('Common.pleaseSelectDate')"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.CustomerCode')" prop="CustomerCode">
            <el-input v-model="temp.CustomerCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.CustomerName')" prop="CustomerName">
            <el-input v-model="temp.CustomerName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.Qty')" prop="Qty">
            <el-input-number v-model="temp.Qty" :min="0" controls-position="right" />
            <el-input-number v-if="false" v-model="temp.ckQty" :min="0" controls-position="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.Unit')" prop="Unit">
            <el-input v-model="temp.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-form-item :label="$t('ui.SD.SD_BarCodeReturn.PrintTemplate')" prop="PrintTemplate">
          <el-select v-model="temp.PrintTemplate" filterable>
            <el-option
              v-for="item in printTemplateOptions"
              :key="item.TempleteDesc"
              :label="item.TempleteDesc"
              :value="item.TempleteFile"
            />
          </el-select>
        </el-form-item>
      </el-row>
    </el-form>

    <el-dialog :title="$t('ui.SD.ReturnRequest.title')" :visible.sync="dialogFormVisible">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('Common.keyword')"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >{{ $t('Common.search') }}</el-button>
      </div>
      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="list"
        border
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          :label="$t('Common.select')"
          type="selection"
          align="center"
          width="40"
          fixed
        />
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.BaseEntry')"
          prop="BaseEntry"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BaseEntry }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.BaseNum')"
          prop="BaseNum"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BaseNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.SD.ReturnRequest.BarCode')"
          prop="BarCode"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BarCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.SD.ReturnRequest.BatchNum')"
          prop="BatchNum"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BatchNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.CustomerCode')"
          prop="CustomerCode"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.CustomerCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.CustomerName')"
          prop="CustomerName"
          align="center"
          width="200"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.CustomerName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.ItemCode')"
          prop="ItemCode"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.ItemName')"
          prop="ItemName"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.Qty')"
          prop="Qty"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Qty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.SD.ReturnRequest.Unit')"
          prop="Unit"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Unit }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="handleSelect"
        >{{ $t('Common.select') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { add, update, GetTHRW } from '../../../api/SD/SD_BarCodeReturn';
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { fetchTemplate as fetchPrintTemplate } from '@/api/MD/MD_LabelTemplate';

_ = require('lodash');

export default {
  name: 'FTTP',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: 'ItemName asc',
        keyword: '',
        apiEventType: ''
      },
      listQueryDetail: {
        page: 1,
        limit: 20,
        title: undefined,
        type: undefined,
        sort: 'ItemName asc',
        keyword: '',
        waveNum: ''
      },
      temp: {
        BarCode: '',
        BatchNum: '',
        BaseEntry: '',
        BaseNum: '',
        BaseLine: '',
        PTime: '',
        CustomerCode: '',
        CustomerName: '',
        ItemCode: '',
        ItemName: '',
        Qty: '',
        ckQty: '',
        Unit: '',
        PrintTemplate: '', // 暂时写入数据，值不能为null
        SiteLogisticsTaskUUID: '',
        OperationActivityUUID: '',
        MaterialOutputUUID: '',
        ReferenceObjectUUID: ''
      },
      editStatus: '',
      dialogFormVisible: false,
      printTemplateOptions: [],
      dialogStatus: '',
      rules: {
        ItemCode: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ],
        Qty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ]
      },
      textMap: {
        select: this.$i18n.t('Common.select')
      },
      downloadLoading: false,
      multipleSelection: []
    };
  },
  created() {
    this.getPrintTemplateList();
    this.getPageParams();
  },
  methods: {
    // fetchPage,
    getPrintTemplateList() {
      fetchPrintTemplate({ templateType: 15 }).then(response => {
        if (response.Code === 2000) {
          this.printTemplateOptions = response.Data;
          this.temp.PrintTemplate = this.printTemplateOptions[0].TempleteDesc;
        }
      });
    },
    getList() {
      this.listLoading = true;
      GetTHRW({
        PageNumber: this.listQuery.PageNumber,
        PageSize: this.listQuery.PageSize,
        keyword: this.listQuery.keyword,
        deliveryNote: 1
      }).then(response => {
        // this.list = response.Data;
        this.list = response.Data;
        // Just to simulate the time of the request
        setTimeout(() => {
          this.listLoading = false;
        }, 1.5 * 1000);
      });
    },
    getDetailList() {},
    handleFilter() {
      // this.listQuery.PageNumber = 1;
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 20;
      this.getList();
    },
    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      if (this.temp.BarID) {
        this.editStatus = 'edit';
      } else {
        this.editStatus = 'create';
      }
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop === 'LogID') {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = 'LogID asc';
      } else {
        this.listQuery.sort = 'LogID desc';
      }
      this.handleFilter();
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    handleSelectionChange(val) {
      // if (val[0].PrintTemplate == "01") {
      //   val[0].PrintTemplate = "销售退货标签";
      // }
      val[0].PrintTemplate = this.printTemplateOptions[0].TempleteDesc;
      this.multipleSelection = val;
    },
    resetTemp() {
      this.temp.ItemCode = '';
      this.temp.Qty = '';
    },
    selectsddelivergoodsOrder() {
      this.resetTemp();
      this.dialogStatus = 'select';
      this.dialogFormVisible = true;
    },
    resetFormData() {
      // 清空表单数据
      this.temp = {
        BarCode: '',
        BatchNum: '',
        BaseEntry: '',
        BaseNum: '',
        BaseLine: '',
        PTime: '',
        CustomerCode: '',
        CustomerName: '',
        ItemCode: '',
        ItemName: '',
        Qty: '',
        ckQty: '',
        Unit: '',
        PrintTemplate: '', // 暂时写入数据，值不能为null
        SiteLogisticsTaskUUID: '',
        OperationActivityUUID: '',
        MaterialOutputUUID: '',
        ReferenceObjectUUID: ''
      };
    },
    handleCommit() {
      console.log('TempleteFile', this.temp);
      this.$refs['dataForm'].validate(val => {
        if (val) {
          var isChoose = this.printTemplateOptions.find(
            x => x.TempleteDesc == this.temp.PrintTemplate
          );
          if (isChoose) { // 判断是否是key值，如果是key值换成value值
            this.temp.PrintTemplate = isChoose.TempleteFile;
          }
          if (this.editStatus === 'create') {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.handleBack();
              } else {
                this.showNotify('error', res.Message);
              }
            });
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.handleBack();
              } else {
                this.showNotify('error', res.Message);
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    handleBack() {
      this.backTo('SD.SD_BarCodeReturn');
    },
    handleAddDetail() {},
    handleCommitDetail() {},
    handleDelDetail() {},
    handleSelect() {
      this.temp.BaseEntry = this.multipleSelection[0].BaseEntry;
      this.temp.PTime = this.multipleSelection[0].PTime;
      this.temp.CustomerCode = this.multipleSelection[0].CustomerCode;
      this.temp.CustomerName = this.multipleSelection[0].CustomerName;
      this.temp.ItemCode = this.multipleSelection[0].ItemCode;
      this.temp.ItemName = this.multipleSelection[0].ItemName;
      this.temp.Qty = this.multipleSelection[0].Qty;
      this.temp.ckQty = this.multipleSelection[0].Qty;
      this.temp.Unit = this.multipleSelection[0].Unit;
      this.temp.PrintTemplate = this.multipleSelection[0].PrintTemplate;
      this.temp.SiteLogisticsTaskUUID = this.multipleSelection[0].SiteLogisticsTaskUUID;
      this.temp.OperationActivityUUID = this.multipleSelection[0].OperationActivityUUID;
      this.temp.MaterialOutputUUID = this.multipleSelection[0].MaterialOutputUUID;
      this.temp.ReferenceObjectUUID = this.multipleSelection[0].ReferenceObjectUUID;
      this.temp.BaseNum = this.multipleSelection[0].BaseNum;
      this.temp.BaseLine = this.multipleSelection[0].BaseLine;
      this.dialogFormVisible = false;
    }
  }
};
</script>
