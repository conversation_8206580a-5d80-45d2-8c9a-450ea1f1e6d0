<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        class="filter-item"
        type="daterange"
        :clearable="false"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeReturn.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeReturn.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate1"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeReturn.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete1"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeReturn.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-printer"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'SD.SD_BarCodeReturn.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_BarCode.BarID')"
        prop="BarID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.BarCode')"
        prop="BarCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.ItemName')"
        prop="ItemName"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.Qty')"
        prop="Qty"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.Unit')"
        prop="Unit"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.BatchNum')"
        prop="BatchNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.CustomerCode')"
        prop="CustomerCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.CustomerName')"
        prop="CustomerName"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.PTime')"
        prop="PTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PTime |date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.PrintTemplate')"
        prop="PrintTemplate"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PrintTemplate }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.SD.SD_BarCodeReturn.InspectionScanStatus')"
        prop="InspectionStatus"

        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InspectionStatus|yesnoFilter }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { formatDate, formatDateTime } from '../../../utils';
import permission from '@/directive/permission/index.js'; // 权限判断指令
import {
  fetchList,
  add,
  update,
  batchDelete,
  printToPDF,
  exportExcelFile
} from '@/api/SD/SD_BarCodeReturn';
import { convertToKeyValue } from '@/utils';
import { MessageBox } from 'element-ui'; // 提示框
import { exportToExcel } from '@/utils/excel-export';

export default {
  name: 'SD.SD_BarCodeReturn',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},

  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        fromTime: '',
        toTime: ''
      },
      multipleSelection: [],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      const i = this.multipleSelection.length;
      return i > 1 || i === 0 || this.multipleSelection[0].InspectionStatus;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        console.log(this.multipleSelection[i].InspectionStatus);
        if (this.multipleSelection[i].InspectionStatus) return true;
      }
      return false;
    },
    canDelivery() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IsDelivery) return true;
      }
      return false;
    }
  },

  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      console.log(this.listQuery);
      fetchList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleExport() {
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.fromTime, this.listQuery.toTime]
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '销售退货标签')
      );
    },
    tableKey() {},
    // handleFilter() {
    //   this.getList()
    // },
    resetFormData() {
      // 重置表单数据
      this.formData = {
        BarID: '',
        BaseNum: '',
        BarCode: '',
        BatchNum: '',
        PTiem: '',
        CustomerCode: '',
        CustomerName: '',
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: '',
        Unit: '',
        PrintTemplate: '',
        Remark: ''
      };
    },
    routeTo(routeName, pms) {
      this.$router.push({
        name: routeName,
        params: pms
      });
    },
    handleCreate() {
      this.routeTo('SD.SD_BarCodeReturnDetailed');
    },
    handleUpdate1() {
      this.routeTo(
        'SD.SD_BarCodeReturnDetailed',
        Object.assign(this.multipleSelection[0])
      );
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      if (selectRows.length > 1) {
        this.showNotify('error', this.$i18n.t('Common.justSingleSelection'));
        return;
      } else if (selectRows.length == 0) {
        this.showNotify('error', this.$i18n.t('Common.noSelection'));
        return;
      } else {
        this.routeTo(
          'SD.SD_BarCodeReturnDetailed',
          Object.assign(selectRows[0])
        );
      }
    },
    createData() {
      // 添加
      add(this.formData).then(response => {
        // this.list.unshift(this.temp)
        this.dialogFormVisible = false;
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        });
        this.getList();
      });
    },
    handleUpdate() {
      var selectRows = this.$refs.multipleTable.store.states.selection;
      if (selectRows.length > 1) {
        this.$notify({
          title: '提示',
          message: '编辑时只能选中一行数据',
          type: 'info',
          duration: 2000
        });
        return;
      } else if (selectRows.length == 0) {
        this.$notify({
          title: '提示',
          message: '编辑时请先选中一行数据',
          type: 'info',
          duration: 2000
        });
        return;
      } else {
        this.resetFormData();
        this.formData = Object.assign({}, selectRows[0]); // 对象拷贝
        this.formTitle = '更新';
        this.formMode = 'Update';
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate(); // 清除校验
        });
      }
    },
    updateData() {
      update(this.formData).then(() => {
        this.getList();
        this.dialogFormVisible = false;
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        });
      });
    },
    handleDelete1() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.BarID);

        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        });
      });
    },
    handleDelete() {
      var selectRows = this.$refs.multipleTable.store.states.selection;

      if (selectRows.length < 1) {
        this.$notify({
          title: '提示',
          message: '请至少选择一行数据进行删除',
          type: 'info',
          duration: 2000
        });
        return;
      } else {
        console.log('batchDelete');
        this.$confirm('是否确认要删除该行数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var arrRowsID = selectRows.map(function(v) {
            return v.BarID;
          });

          // 删除逻辑处理
          batchDelete(arrRowsID).then(() => {
            this.$notify({
              title: '成功',
              message: '操作成功',
              type: 'success',
              duration: 2000
            });
            this.getList();
          });
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        const BarCodes = selectRows.map(v => v.BarCode);
        console.log(BarCodes);
        printToPDF(BarCodes).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
        });
      }
    }
  }
};
</script>

