<template>
  <div class="app-container">
    <!-- 检索以及工具条区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 300px;"
        clearable
        class="filter-item"
        size="small"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-search"
        @click="handleFilter"
      />

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加</el-button>

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        :disabled="canNotUpdate"
        icon="el-icon-edit"
        @click="handleUpdate"
      >编辑</el-button>

      <el-button
        v-waves
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-delete"
        @click="handleDelete"
      >删除</el-button>

      <el-button
        v-waves
        :loading="downloadLoading"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
      >导出</el-button>
    </div>

    <!-- 数据列表 -->
    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      fit
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectChange"
    >
      <el-table-column
        type="selection"
        width="40"
      />

      <el-table-column v-if="false" width="160px" align="center" prop="ResourceID" :label="$t('ui.Sys.Sys_Resource.ResourceID')" />
      <el-table-column width="160px" align="center" prop="FathResourceID" :label="$t('ui.Sys.Sys_Resource.FathResourceID')" />
      <el-table-column width="160px" align="center" prop="ResourceTitle" :label="$t('ui.Sys.Sys_Resource.ResourceTitle')" />
      <el-table-column width="160px" align="center" prop="ResourceName" :label="$t('ui.Sys.Sys_Resource.ResourceName')" />
      <el-table-column width="160px" align="center" prop="ResourceType" :label="$t('ui.Sys.Sys_Resource.ResourceType')" />
      <el-table-column width="160px" align="center" prop="RedirectUrl" :label="$t('ui.Sys.Sys_Resource.RedirectUrl')" />
      <el-table-column width="160px" align="center" prop="ResourcePath" :label="$t('ui.Sys.Sys_Resource.ResourcePath')" />
      <el-table-column width="160px" align="center" prop="ResourceLevel" :label="$t('ui.Sys.Sys_Resource.ResourceLevel')" />
      <el-table-column width="160px" align="center" prop="ResourceIcon" :label="$t('ui.Sys.Sys_Resource.ResourceIcon')" />
      <el-table-column width="160px" align="center" prop="IsCache" :label="$t('ui.Sys.Sys_Resource.IsCache')" />
      <el-table-column width="160px" align="center" prop="Component" :label="$t('ui.Sys.Sys_Resource.Component')" />
      <el-table-column width="160px" align="center" prop="Layout" :label="$t('ui.Sys.Sys_Resource.Layout')" />
      <el-table-column width="160px" align="center" prop="Role" :label="$t('ui.Sys.Sys_Resource.Role')" />
      <el-table-column width="160px" align="center" prop="AppID" :label="$t('ui.Sys.Sys_Resource.AppID')" />
      <el-table-column width="160px" align="center" prop="Remark" :label="$t('ui.Sys.Sys_Resource.Remark')" />
      <el-table-column width="160px" align="center" prop="CUser" :label="$t('ui.Sys.Sys_Resource.CUser')" />
      <el-table-column width="160px" align="center" prop="CTime" :label="$t('ui.Sys.Sys_Resource.CTime')" />
      <el-table-column width="160px" align="center" prop="MUser" :label="$t('ui.Sys.Sys_Resource.MUser')" />
      <el-table-column width="160px" align="center" prop="MTime" :label="$t('ui.Sys.Sys_Resource.MTime')" />

    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 表单对话框 -->
    <el-dialog :title="formTitle" :visible.sync="dialogFormVisible" top="10vh">
      <el-form ref="dataForm" :rules="rules" :model="formData" label-position="right" label-width="70px">

        <el-form-item :label="$t('ui.Sys.Sys_Resource.FathResourceID')">
          <el-input v-model="formData.FathResourceID" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.ResourceTitle')">
          <el-input v-model="formData.ResourceTitle" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.ResourceName')">
          <el-input v-model="formData.ResourceName" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.ResourceType')">
          <el-input v-model="formData.ResourceType" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.RedirectUrl')">
          <el-input v-model="formData.RedirectUrl" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.ResourcePath')">
          <el-input v-model="formData.ResourcePath" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.ResourceLevel')">
          <el-input v-model="formData.ResourceLevel" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.ResourceIcon')">
          <el-input v-model="formData.ResourceIcon" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.IsCache')">
          <el-input v-model="formData.IsCache" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.Component')">
          <el-input v-model="formData.Component" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.Layout')">
          <el-input v-model="formData.Layout" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.Role')">
          <el-input v-model="formData.Role" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.AppID')">
          <el-input v-model="formData.AppID" autocomplete="off" />
        </el-form-item>
        <el-form-item :label="$t('ui.Sys.Sys_Resource.Remark')">
          <el-input v-model="formData.Remark" :autosize="{minRows: 3, maxRows: 5}" type="textarea" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('Common.cancel') }}
        </el-button>
        <el-button type="primary" @click="formMode==='Create'?createData():updateData()">
          {{ $t('Common.confirm') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { fetchList, add, update, batchDelete } from '@/api/Sys/Sys_Resource';

import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页

export default {
  name: 'Sys_Resource',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        true: '正常',
        false: '冻结'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: [],
      selectedRowsData: [],
      total: 0,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10
      },
      formData: { // 表单数据
        ResourceID: '',
        FathResourceID: '',
        ResourceTitle: '',
        ResourceName: '',
        ResourceType: '',
        RedirectUrl: '',
        ResourcePath: '',
        ResourceLevel: '',
        ResourceIcon: '',
        IsCache: '',
        Component: '',
        Layout: '',
        Role: '',
        AppID: '',
        Remark: ''

      },
      formTitle: '', // 弹窗标题
      formMode: '',
      dialogFormVisible: false, // 表单窗口是否显示标志
      rules: { // 表单校验逻辑
        UserName: [
          { required: true, message: '用户名必须输入', trigger: 'change' }
        ]
      },
      downloadLoading: false
    }
  },
  computed: {
    canNotUpdate() {
      var selectRows = this.selectedRowsData || []
      return selectRows.length !== 1
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取数据
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.listQuery.PageSize = 10
      this.getList()
    },
    resetFormData() {
      // 重置表单数据
      this.formData = {
        ResourceID: '',
        FathResourceID: '',
        ResourceTitle: '',
        ResourceName: '',
        ResourceType: '',
        RedirectUrl: '',
        ResourcePath: '',
        ResourceLevel: '',
        ResourceIcon: '',
        IsCache: '',
        Component: '',
        Layout: '',
        Role: '',
        AppID: '',
        Remark: ''

      }
    },
    handleSelectChange(selection) {
      this.selectedRowsData = selection
    },
    handleCreate() {
      this.resetFormData()
      this.formTitle = '添加'
      this.formMode = 'Create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    createData() {
      // 添加
      add(this.formData).then((response) => {
        // this.list.unshift(this.temp)
        this.dialogFormVisible = false
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    handleUpdate() {
      this.resetFormData()
      Object.assign(this.formData, this.selectedRowsData[0]); // 对象拷贝
      this.formTitle = '更新'
      this.formMode = 'Update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    updateData() {
      update(this.formData).then(() => {
        this.getList()
        this.dialogFormVisible = false;
        this.$notify({
          title: '成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        });
      });
    },
    handleDelete() {
      var selectRows = this.$refs.multipleTable.store.states.selection

      if (selectRows.length < 1) {
        this.$notify({
          title: '提示',
          message: '请至少选择一行数据进行删除',
          type: 'info',
          duration: 2000
        })
        return
      } else {
        console.log('batchDelete')
        this.$confirm('是否确认要删除该行数据？', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
          var arrRowsID = selectRows.map(function(v) { return v.ResourceID })

          // 删除逻辑处理
          batchDelete(arrRowsID).then(() => {
            this.$notify({
              title: '成功',
              message: '操作成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        });
      }
    }
  }
}
</script>

