{"panels": [{"index": 0, "name": 1, "height": 297, "width": 210, "paperHeader": 7.5, "paperFooter": 827.5, "printElements": [{"options": {"left": 7.5, "top": 47.5, "height": 39, "width": 582, "coordinateSync": false, "widthHeightSync": false, "right": 590.25, "bottom": 85.74375915527344, "vCenter": 299.25, "hCenter": 66.24375915527344, "field": "ProductionList", "columns": [[{"width": 47.74369002355499, "title": "电扶货梯", "field": "ElectricCargoElevator", "checked": true, "columnId": "ElectricCargoElevator", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 71.08443504478436, "title": "工地模块", "field": "ConstructionModule", "checked": true, "columnId": "ConstructionModule", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 72.06785292189505, "title": "交货日期", "field": "DeliveryDate", "checked": true, "columnId": "DeliveryDate", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 102.15798344154375, "title": "客户件号", "field": "CustomerPartNo", "checked": true, "columnId": "CustomerPartNo", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 131.152051339538, "title": "物料描述", "field": "MaterialDes", "checked": true, "columnId": "MaterialDes", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 49.04131952342907, "title": "单根数量", "field": "SingleQuantity", "checked": true, "columnId": "SingleQuantity", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 37.042284357209695, "title": "数量", "field": "Quantity", "checked": true, "columnId": "Quantity", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 36.04799340175974, "title": "单位", "field": "Unit", "checked": true, "columnId": "Unit", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 35.662389946285316, "title": "备注", "field": "Remark", "checked": true, "columnId": "Remark", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "halign": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 175.43861198681964, "title": "规格型号", "field": "SpecificationModel", "checked": false, "columnId": "SpecificationModel", "fixed": false, "rowspan": 1, "colspan": 1}]]}, "printElementType": {"title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 237.5, "top": 15, "height": 18, "width": 135, "title": "部件汇总清单", "right": 360.75, "bottom": 33.24375915527344, "vCenter": 299.25, "hCenter": 24.243759155273438, "coordinateSync": false, "widthHeightSync": false, "fontSize": 16, "fontWeight": "bolder", "qrCodeLevel": 0}, "printElementType": {"title": "文本", "type": "text"}}], "paperNumberLeft": 565.5, "paperNumberTop": 573, "paperNumberContinue": true, "watermarkOptions": {"content": "", "rotate": 25, "timestamp": true, "format": "YYYY-MM-DD HH:mm", "fillStyle": "rgba(184, 184, 184, 0.3)", "fontSize": "14px", "width": 200, "height": 200}, "panelLayoutOptions": {"layoutType": "column", "layoutRowGap": 0, "layoutColumnGap": 0}}]}