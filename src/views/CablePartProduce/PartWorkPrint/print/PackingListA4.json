{"panels": [{"index": 0, "name": 1, "height": 296.6, "width": 210, "paperHeader": 6, "paperFooter": 832.5, "printElements": [{"options": {"left": 420, "top": 9, "height": 40.5, "width": 9}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 9, "height": 9, "width": 576, "borderWidth": 0.75, "right": 99.7430419921875, "bottom": 17.996532440185547, "vCenter": 54.7430419921875, "hCenter": 13.496532440185547}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 585, "top": 9, "height": 154.5, "width": 9, "right": 596.9999542236328, "bottom": 404.244140625, "vCenter": 592.4999542236328, "hCenter": 207.744140625}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 9, "height": 154.5, "width": 9, "right": 17.24219512939453, "bottom": 101.99218368530273, "vCenter": 12.742195129394531, "hCenter": 56.992183685302734}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 58.5, "top": 13.5, "height": 33, "width": 348, "title": "合同号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 179.24805450439453, "bottom": 46.496097564697266, "vCenter": 119.24805450439453, "hCenter": 29.996097564697266, "field": "ContractNo1", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 429, "top": 13.5, "height": 33, "width": 145.5, "title": "顺序号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 543.744140625, "bottom": 45.74608612060547, "vCenter": 486.744140625, "hCenter": 29.24608612060547, "field": "SequenceNo1", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 13.5, "top": 13.5, "height": 33, "width": 33, "title": "二维码", "qrcodeType": "qrcode", "testData": "qrcode", "qrCodeLevel": 0, "coordinateSync": false, "widthHeightSync": false, "hideTitle": true, "right": 47.24217224121094, "bottom": 48.75, "vCenter": 30.742172241210938, "hCenter": 32.25, "field": "qrcode1"}, "printElementType": {"title": "二维码", "type": "qrcode"}}, {"options": {"left": 180, "top": 49.5, "height": 114, "width": 9, "right": 189, "bottom": 139.5, "vCenter": 184.5, "hCenter": 94.5}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 307.5, "top": 49.5, "height": 22.5, "width": 9, "right": 317.24649810791016, "bottom": 73.4999942779541, "vCenter": 312.74649810791016, "hCenter": 62.2499942779541}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 49.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 585.2441482543945, "bottom": 57.99414253234863, "vCenter": 297.24414825439453, "hCenter": 53.49414253234863}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 504, "top": 49.5, "height": 22.5, "width": 9, "right": 515.25, "bottom": 72, "vCenter": 510.75, "hCenter": 60.75}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 49.5, "height": 114, "width": 171, "right": 242.99805450439453, "bottom": 85.49608612060547, "vCenter": 126.74805450439453, "hCenter": 67.49608612060547, "coordinateSync": false, "widthHeightSync": false, "fontSize": 10.5, "textAlign": "center", "tableHeaderRowHeight": 22.5, "tableBodyRowHeight": 22.5, "field": "table1", "tableHeaderFontSize": 14.25, "columns": [[{"width": 122.19075000000002, "title": "件号", "field": "CustomerPartNo", "checked": true, "columnId": "CustomerPartNo", "fixed": false, "rowspan": 1, "colspan": 1}, {"width": 48.809249999999984, "title": "数量", "field": "Quantity", "checked": true, "columnId": "Quantity", "fixed": false, "rowspan": 1, "colspan": 1}]]}, "printElementType": {"title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 186, "top": 51, "height": 21, "width": 114, "title": "完工批次", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 296.24803161621094, "bottom": 71.99609756469727, "vCenter": 241.49803161621094, "hCenter": 61.496097564697266, "field": "FinishBatch1", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 505, "top": 51, "height": 21, "width": 80, "title": "电梯", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 578.9921722412109, "bottom": 72.744140625, "vCenter": 551.9921722412109, "hCenter": 62.244140625, "field": "ElectricCargoElevator1", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 316.5, "top": 51, "height": 21, "width": 196.5, "title": "模块号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 429.74217224121094, "bottom": 71.99609756469727, "vCenter": 372.74217224121094, "hCenter": 61.496097564697266, "field": "ConstructionModule1", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 180, "top": 72, "height": 9, "width": 405, "borderWidth": 0.75, "right": 578.244140625, "bottom": 80.99218082427979, "vCenter": 379.494140625, "hCenter": 76.49218082427979}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 163.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.994140625, "bottom": 173.24218940734863, "vCenter": 296.994140625, "hCenter": 168.74218940734863}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 420, "top": 168, "height": 40.5, "width": 9, "right": 429.74610900878906, "bottom": 209.24219512939453, "vCenter": 425.24610900878906, "hCenter": 188.99219512939453}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 168, "height": 9, "width": 576, "borderWidth": 0.75, "right": 583.494140625, "bottom": 177.75, "vCenter": 295.494140625, "hCenter": 173.25}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 585, "top": 168, "height": 154.5, "width": 9, "right": 593.2440948486328, "bottom": 322.49804306030273, "vCenter": 588.7440948486328, "hCenter": 245.24804306030273}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 168, "height": 154.5, "width": 9, "right": 18.75, "bottom": 324.74608612060547, "vCenter": 14.25, "hCenter": 247.49608612060547}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 58.5, "top": 172.5, "height": 33, "width": 348, "title": "合同号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 407.24217224121094, "bottom": 202.494140625, "vCenter": 233.24217224121094, "hCenter": 185.994140625, "field": "ContractNo2", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 429, "top": 172.5, "height": 33, "width": 145.5, "title": "顺序号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 573.7421722412109, "bottom": 204, "vCenter": 500.99217224121094, "hCenter": 187.5, "field": "SequenceNo2", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 13.5, "top": 172.5, "height": 33, "width": 33, "title": "二维码", "qrcodeType": "qrcode", "testData": "qrcode", "qrCodeLevel": 0, "coordinateSync": false, "widthHeightSync": false, "hideTitle": true, "right": 47.244140625, "bottom": 205.494140625, "vCenter": 30.744140625, "hCenter": 188.994140625, "field": "qrcode2"}, "printElementType": {"title": "二维码", "type": "qrcode"}}, {"options": {"left": 180, "top": 208.5, "height": 114, "width": 9, "right": 188.99610900878906, "bottom": 320.99804878234863, "vCenter": 184.49610900878906, "hCenter": 263.99804878234863}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 307.5, "top": 208.5, "height": 22.5, "width": 9, "right": 317.244140625, "bottom": 233.2441291809082, "vCenter": 312.744140625, "hCenter": 221.9941291809082}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 208.5, "height": 114, "width": 171, "right": 217.5, "bottom": 245.24803161621094, "vCenter": 114, "hCenter": 227.24803161621094, "coordinateSync": false, "widthHeightSync": false, "fontSize": 10.5, "textAlign": "center", "tableHeaderRowHeight": 22.5, "tableBodyRowHeight": 22.5, "field": "table2", "tableHeaderFontSize": 14.25, "columns": [[{"width": 122.20050000000002, "title": "件号", "field": "CustomerPartNo", "checked": true, "columnId": "CustomerPartNo", "fixed": false, "rowspan": 1, "colspan": 1}, {"width": 48.79949999999999, "title": "数量", "field": "Quantity", "checked": true, "columnId": "Quantity", "fixed": false, "rowspan": 1, "colspan": 1}]]}, "printElementType": {"title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 504, "top": 208.5, "height": 22.5, "width": 9, "right": 515.25, "bottom": 230.25, "vCenter": 510.75, "hCenter": 219}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 205.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.994140625, "bottom": 217.49608612060547, "vCenter": 296.994140625, "hCenter": 212.99608612060547}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 316.5, "top": 210, "height": 21, "width": 196.5, "title": "模块号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 515.2480316162109, "bottom": 229.49803161621094, "vCenter": 416.99803161621094, "hCenter": 218.99803161621094, "field": "ConstructionModule2", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 505, "top": 210, "height": 21, "width": 80, "title": "电梯", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 581.994140625, "bottom": 229.49803161621094, "vCenter": 554.244140625, "hCenter": 218.99803161621094, "field": "ElectricCargoElevator2", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 186, "top": 210, "height": 21, "width": 114, "title": "完工批次", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 299.994140625, "bottom": 230.24803161621094, "vCenter": 242.994140625, "hCenter": 219.74803161621094, "field": "FinishBatch2", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 180, "top": 231, "height": 9, "width": 405, "borderWidth": 0.75, "right": 585, "bottom": 239.99218368530273, "vCenter": 382.5, "hCenter": 235.49218368530273}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 322.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 582.9941482543945, "bottom": 332.49609375, "vCenter": 294.99414825439453, "hCenter": 327.99609375}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 420, "top": 327, "height": 40.5, "width": 9, "right": 428.99610900878906, "bottom": 366.74804878234863, "vCenter": 424.49610900878906, "hCenter": 346.49804878234863}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 327, "height": 9, "width": 576, "borderWidth": 0.75, "right": 587.2480545043945, "bottom": 338.99217224121094, "vCenter": 299.24805450439453, "hCenter": 334.49217224121094}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 585, "top": 327, "height": 154.5, "width": 9, "right": 593.2440948486328, "bottom": 481.49217796325684, "vCenter": 588.7440948486328, "hCenter": 404.24217796325684}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 327, "height": 154.5, "width": 9, "right": 18, "bottom": 481.49217224121094, "vCenter": 13.5, "hCenter": 404.24217224121094}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 58.5, "top": 331.5, "height": 33, "width": 348, "title": "合同号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 407.244140625, "bottom": 362.24803161621094, "vCenter": 233.244140625, "hCenter": 345.74803161621094, "field": "ContractNo3", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 429, "top": 331.5, "height": 33, "width": 145.5, "title": "顺序号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 573.75, "bottom": 363, "vCenter": 501, "hCenter": 346.5, "field": "SequenceNo3", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 13.5, "top": 331.5, "height": 33, "width": 33, "title": "二维码", "qrcodeType": "qrcode", "testData": "qrcode", "qrCodeLevel": 0, "coordinateSync": false, "widthHeightSync": false, "hideTitle": true, "right": 47.24219512939453, "bottom": 365.99608612060547, "vCenter": 30.74219512939453, "hCenter": 349.49608612060547, "field": "qrcode3"}, "printElementType": {"title": "二维码", "type": "qrcode"}}, {"options": {"left": 180, "top": 367.5, "height": 114, "width": 9, "right": 189.744140625, "bottom": 481.49804306030273, "vCenter": 185.244140625, "hCenter": 424.49804306030273}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 307.5, "top": 367.5, "height": 22.5, "width": 9, "right": 316.4940948486328, "bottom": 391.4941349029541, "vCenter": 311.9940948486328, "hCenter": 380.2441349029541}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 367.5, "height": 114, "width": 171, "right": 559.7461090087891, "bottom": 404.25, "vCenter": 284.74610900878906, "hCenter": 386.25, "coordinateSync": false, "widthHeightSync": false, "fontSize": 10.5, "textAlign": "center", "tableHeaderRowHeight": 22.5, "tableBodyRowHeight": 22.5, "field": "table3", "tableHeaderFontSize": 14.25, "columns": [[{"width": 122.18025, "title": "件号", "field": "CustomerPartNo", "checked": true, "columnId": "CustomerPartNo", "fixed": false, "rowspan": 1, "colspan": 1}, {"width": 48.81975, "title": "数量", "field": "Quantity", "checked": true, "columnId": "Quantity", "fixed": false, "rowspan": 1, "colspan": 1}]]}, "printElementType": {"title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 504, "top": 367.5, "height": 22.5, "width": 9, "right": 515.25, "bottom": 390, "vCenter": 510.75, "hCenter": 378.75}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 367.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.994140625, "bottom": 377.24804878234863, "vCenter": 296.994140625, "hCenter": 372.74804878234863}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 186, "top": 369, "height": 21, "width": 114, "title": "完工批次", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 300.74217224121094, "bottom": 392.9960632324219, "vCenter": 243.74217224121094, "hCenter": 382.4960632324219, "field": "FinishBatch3", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 505, "top": 369, "height": 21, "width": 80, "title": "电梯", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 583.4921722412109, "bottom": 392.244140625, "vCenter": 555.7421722412109, "hCenter": 381.744140625, "field": "ElectricCargoElevator3", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 316.5, "top": 369, "height": 21, "width": 196.5, "title": "模块号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 514.4961090087891, "bottom": 388.49803161621094, "vCenter": 416.24610900878906, "hCenter": 377.99803161621094, "field": "ConstructionModule3", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 180, "top": 390, "height": 9, "width": 405, "borderWidth": 0.75, "right": 585.75, "bottom": 399.74609184265137, "vCenter": 383.25, "hCenter": 395.24609184265137}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 481.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.9980316162109, "bottom": 491.2441234588623, "vCenter": 296.99803161621094, "hCenter": 486.7441234588623}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 486, "height": 154.5, "width": 9, "right": 19.494140625, "bottom": 638.9921493530273, "vCenter": 14.994140625, "hCenter": 561.7421493530273}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 420, "top": 486, "height": 40.5, "width": 9, "right": 429.74217224121094, "bottom": 528.7441062927246, "vCenter": 425.24217224121094, "hCenter": 508.4941062927246}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 585, "top": 486, "height": 154.5, "width": 9, "right": 593.2440948486328, "bottom": 481.49217796325684, "vCenter": 588.7440948486328, "hCenter": 404.24217796325684}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 486, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.994140625, "bottom": 494.9999771118164, "vCenter": 296.994140625, "hCenter": 490.4999771118164}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 58.5, "top": 490.5, "height": 33, "width": 348, "title": "合同号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 406.49610900878906, "bottom": 521.244140625, "vCenter": 232.49610900878906, "hCenter": 504.744140625, "field": "ContractNo4", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 429, "top": 490.5, "height": 33, "width": 145.5, "title": "顺序号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 574.4921722412109, "bottom": 521.9960861206055, "vCenter": 501.74217224121094, "hCenter": 505.49608612060547, "field": "SequenceNo4", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 13.5, "top": 490.5, "height": 33, "width": 33, "title": "二维码", "qrcodeType": "qrcode", "testData": "qrcode", "qrCodeLevel": 0, "coordinateSync": false, "widthHeightSync": false, "hideTitle": true, "right": 47.244140625, "bottom": 522.7480316162109, "vCenter": 30.744140625, "hCenter": 506.24803161621094, "field": "qrcode4"}, "printElementType": {"title": "二维码", "type": "qrcode"}}, {"options": {"left": 180, "top": 526.5, "height": 114, "width": 9, "right": 188.994140625, "bottom": 641.2480316162109, "vCenter": 184.494140625, "hCenter": 584.2480316162109}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 307.5, "top": 526.5, "height": 22.5, "width": 9, "right": 317.244140625, "bottom": 550.4980316162109, "vCenter": 312.744140625, "hCenter": 539.2480316162109}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 526.5, "height": 114, "width": 171, "right": 559.75, "bottom": 564.744140625, "vCenter": 284.75, "hCenter": 546.744140625, "coordinateSync": false, "widthHeightSync": false, "fontSize": 10.5, "textAlign": "center", "tableHeaderRowHeight": 22.5, "tableBodyRowHeight": 22.5, "field": "table4", "tableHeaderFontSize": 14.25, "columns": [[{"width": 122.19300000000001, "title": "件号", "field": "CustomerPartNo", "checked": true, "columnId": "CustomerPartNo", "fixed": false, "rowspan": 1, "colspan": 1}, {"width": 48.807, "title": "数量", "field": "Quantity", "checked": true, "columnId": "Quantity", "fixed": false, "rowspan": 1, "colspan": 1}]]}, "printElementType": {"title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 504, "top": 526.5, "height": 22.5, "width": 9, "right": 514.5, "bottom": 549, "vCenter": 510, "hCenter": 537.75}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 526.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 586.494140625, "bottom": 536.2499885559082, "vCenter": 298.494140625, "hCenter": 531.7499885559082}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 186, "top": 528, "height": 21, "width": 114, "title": "完工批次", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 300.74217224121094, "bottom": 548.2421722412109, "vCenter": 243.74217224121094, "hCenter": 537.7421722412109, "field": "FinishBatch4", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 505, "top": 528, "height": 21, "width": 80, "title": "电梯", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 581.9921722412109, "bottom": 550.4980316162109, "vCenter": 554.2421722412109, "hCenter": 539.9980316162109, "field": "ElectricCargoElevator4", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 316.5, "top": 528, "height": 21, "width": 196.5, "title": "模块号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 514.494140625, "bottom": 550.4960861206055, "vCenter": 416.244140625, "hCenter": 539.9960861206055, "field": "ConstructionModule4", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 180, "top": 549, "height": 9, "width": 405, "borderWidth": 0.75, "right": 586.5, "bottom": 557.9999885559082, "vCenter": 384, "hCenter": 553.4999885559082}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 640.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 587.244140625, "bottom": 650.244140625, "vCenter": 299.244140625, "hCenter": 645.744140625}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 645, "height": 9, "width": 576, "borderWidth": 0.75, "right": 583.494140625, "bottom": 656.9960632324219, "vCenter": 295.494140625, "hCenter": 652.4960632324219}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 420, "top": 645, "height": 40.5, "width": 9, "right": 429.744140625, "bottom": 686.2421722412109, "vCenter": 425.244140625, "hCenter": 665.9921722412109}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 585, "top": 645, "height": 154.5, "width": 9, "right": 595.494140625, "bottom": 800.2460632324219, "vCenter": 590.994140625, "hCenter": 722.9960632324219}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 645, "height": 154.5, "width": 9, "right": 17.246109008789062, "bottom": 800.2480316162109, "vCenter": 12.746109008789062, "hCenter": 722.9980316162109}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 58.5, "top": 649.5, "height": 33, "width": 348, "title": "合同号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 406.49803161621094, "bottom": 682.4960632324219, "vCenter": 232.49803161621094, "hCenter": 665.9960632324219, "field": "ContractNo5", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 429, "top": 649.5, "height": 33, "width": 145.5, "title": "顺序号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 21.75, "fontWeight": "bold", "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 576.744140625, "bottom": 680.9960632324219, "vCenter": 503.994140625, "hCenter": 664.4960632324219, "field": "SequenceNo5", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 13.5, "top": 649.5, "height": 33, "width": 33, "title": "二维码", "qrcodeType": "qrcode", "testData": "qrcode", "qrCodeLevel": 0, "coordinateSync": false, "widthHeightSync": false, "hideTitle": true, "right": 47.24610900878906, "bottom": 681.7421722412109, "vCenter": 30.746109008789062, "hCenter": 665.2421722412109, "field": "qrcode5"}, "printElementType": {"title": "二维码", "type": "qrcode"}}, {"options": {"left": 180, "top": 685.5, "height": 114, "width": 9, "right": 189.74803161621094, "bottom": 800.2480316162109, "vCenter": 185.24803161621094, "hCenter": 743.2480316162109}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 307.5, "top": 685.5, "height": 22.5, "width": 9, "right": 316.5, "bottom": 710.2421493530273, "vCenter": 312, "hCenter": 698.9921493530273}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 685.5, "height": 114, "width": 171, "right": 558.2461090087891, "bottom": 723.7421836853027, "vCenter": 283.24610900878906, "hCenter": 705.7421836853027, "coordinateSync": false, "widthHeightSync": false, "fontSize": 10.5, "textAlign": "center", "tableHeaderRowHeight": 22.5, "tableBodyRowHeight": 22.5, "field": "table5", "tableHeaderFontSize": 14.25, "columns": [[{"width": 122.19536181818182, "title": "件号", "field": "CustomerPartNo", "checked": true, "columnId": "CustomerPartNo", "fixed": false, "rowspan": 1, "colspan": 1}, {"width": 48.80463818181817, "title": "数量", "field": "Quantity", "checked": true, "columnId": "Quantity", "fixed": false, "rowspan": 1, "colspan": 1}]]}, "printElementType": {"title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 504, "top": 685.5, "height": 22.5, "width": 9, "right": 514.5, "bottom": 708.75, "vCenter": 510, "hCenter": 697.5}, "printElementType": {"title": "竖线", "type": "vline"}}, {"options": {"left": 9, "top": 685.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.994140625, "bottom": 695.2460632324219, "vCenter": 296.994140625, "hCenter": 690.7460632324219}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 186, "top": 687, "height": 21, "width": 114, "title": "完工批次", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 299.994140625, "bottom": 708, "vCenter": 242.994140625, "hCenter": 697.5, "field": "FinishBatch5", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 505, "top": 687, "height": 21, "width": 80, "title": "电梯", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 581.25, "bottom": 709.5, "vCenter": 553.5, "hCenter": 699, "field": "ElectricCargoElevator5", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 316.5, "top": 687, "height": 21, "width": 196.5, "title": "模块号", "coordinateSync": false, "widthHeightSync": false, "fontSize": 14.25, "textAlign": "center", "textContentVerticalAlign": "middle", "qrCodeLevel": 0, "right": 515.2461090087891, "bottom": 708, "vCenter": 416.99610900878906, "hCenter": 697.5, "field": "ConstructionModule5", "hideTitle": true}, "printElementType": {"title": "文本", "type": "text"}}, {"options": {"left": 180, "top": 708, "height": 9, "width": 405, "borderWidth": 0.75, "right": 585, "bottom": 716.9940948486328, "vCenter": 382.5, "hCenter": 712.4940948486328}, "printElementType": {"title": "横线", "type": "hline"}}, {"options": {"left": 9, "top": 799.5, "height": 9, "width": 576, "borderWidth": 0.75, "right": 584.9980316162109, "bottom": 807.7480316162109, "vCenter": 296.99803161621094, "hCenter": 803.2480316162109}, "printElementType": {"title": "横线", "type": "hline"}}], "paperNumberLeft": 564, "paperNumberTop": 813, "paperNumberContinue": true, "watermarkOptions": {"content": "", "rotate": 25, "timestamp": true, "format": "YYYY-MM-DD HH:mm", "fillStyle": "rgba(184, 184, 184, 0.3)", "fontSize": "14px", "width": 200, "height": 200}, "panelLayoutOptions": {"layoutType": "column", "layoutRowGap": 0, "layoutColumnGap": 0}}]}