{"panels": [{"index": 0, "name": 1, "height": 210, "width": 297, "paperHeader": 49.5, "paperFooter": 551.5151515151515, "printElements": [{"options": {"left": 250, "top": 21, "height": 27, "width": 259, "title": "电缆部件发货清单", "fontSize": 19, "fontWeight": "600", "textAlign": "center", "lineHeight": 26, "coordinateSync": false, "widthHeightSync": true, "draggable": false, "qrCodeLevel": 0}, "printElementType": {"title": "自定义文本", "type": "text"}}, {"options": {"left": 25.5, "top": 57, "height": 705, "width": 9, "fixed": true, "borderStyle": "dotted"}, "printElementType": {"type": "vline"}}, {"options": {"left": 60, "top": 100, "height": 195, "width": 681, "tableFooterRepeat": "no", "tableBorder": "border", "tableHeaderBorder": "topBorder", "tableHeaderCellBorder": "border", "tableBodyRowBorder": "topBorder", "tableFooterBorder": "topBorder", "coordinateSync": false, "widthHeightSync": false, "field": "PartShipmentList", "columns": [[{"width": 23.6791988753611, "title": "序号", "field": "rownum", "checked": true, "columnId": "rownum", "fixed": false, "rowspan": 1, "colspan": 1, "tableTextType": "sequence", "tableQRCodeLevel": 0}, {"width": 59.00779097233582, "title": "采购订单号\n", "field": "CustomerOrderNum", "checked": true, "columnId": "CustomerOrderNum", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 48.564468855449796, "title": "电扶货梯\n", "field": "ElectricCargoElevator", "checked": true, "columnId": "ElectricCargoElevator", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 43.98178905283137, "title": "工地模块", "field": "ConstructionModule", "checked": true, "columnId": "ConstructionModule", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 41.7502313207678, "title": "合同号", "field": "ContractNo", "checked": true, "columnId": "ContractNo", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 47.122693339732805, "title": "交货日期", "field": "DeliveryDate", "checked": true, "columnId": "DeliveryDate", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 50.23979436245059, "title": "完工批次\n", "field": "FinishBatch", "checked": true, "columnId": "FinishBatch", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 36.925853598330505, "title": "顺序号", "field": "SequenceNo", "checked": true, "columnId": "SequenceNo", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 36.0510484960798, "title": "件号1", "field": "PartNo1", "checked": true, "columnId": "PartNo1", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 35.537969444179915, "title": "件号2\n", "field": "PartNo2", "checked": true, "columnId": "PartNo2", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 37.55774426769531, "title": "件号3\n", "field": "PartNo3", "checked": true, "columnId": "PartNo3", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 34.17693541968028, "title": "件号4\n", "field": "PartNo4", "checked": true, "columnId": "PartNo4", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 35.11782283050228, "title": "件号5", "field": "PartNo5", "checked": true, "columnId": "PartNo5", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}, {"width": 37.68009478209015, "title": "件号6", "field": "PartNo6", "checked": true, "columnId": "PartNo6", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": ""}, {"width": 33.106564382512595, "title": "件号7", "field": "PartNo7", "checked": true, "columnId": "PartNo7", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0}]]}, "printElementType": {"title": "表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true}}, {"options": {"left": 12, "top": 786, "height": 49, "width": 49}, "printElementType": {"title": "html", "type": "html"}}], "paperNumberLeft": 565.5, "paperNumberTop": 573, "paperNumberContinue": true, "rotate": true, "watermarkOptions": {}, "panelLayoutOptions": {}}]}