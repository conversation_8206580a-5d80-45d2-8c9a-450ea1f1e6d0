<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.CreateDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单交期">
              <el-date-picker
                v-model="listQuery.DeliveryDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="客户名称">
              <el-input
                v-model="listQuery.CustomerName"
                size="small"
                class="filter-item"
                placeholder="客户名称"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单号">
              <el-input
                v-model="listQuery.SapNo"
                size="small"
                class="filter-item"
                placeholder="销售订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="电扶货梯">
              <el-input
                v-model="listQuery.ElectricCargoElevator"
                size="small"
                class="filter-item"
                placeholder="电扶货梯"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工地模块">
              <el-input
                v-model="listQuery.ConstructionModule"
                size="small"
                class="filter-item"
                placeholder="工地模块"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="明细数">
              <el-select v-model="listQuery.DetailNo" clearable>
                <el-option
                  v-for="item in DetailNoOption"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-button
            v-waves
            size="small"
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            style="margin-left: 10px;"
            @click="handleFilter"
          >查询
          </el-button>
        </el-row>
      </el-form>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="handlePrintPartNo"
      >部件件号清单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="handlePrintPartSummary"
      >汇总清单导出
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="handlePrintPartShipment"
      >部件发货清单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="handlePrintCountSummary"
      >总数汇总清单导出
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column align="center" prop="CustomerCode" width="120px" show-overflow-tooltip label="客户编码">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerName" width="180px" show-overflow-tooltip label="客户名称">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="CustomerOrderN-um"
        width="180px"
        show-overflow-tooltip
        label="客户定单号"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ContractNo" width="180px" show-overflow-tooltip label="合同号">
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ElectricCargoElevator" width="180px" show-overflow-tooltip label="电扶货梯">
        <template slot-scope="scope">
          <span>{{ scope.row.ElectricCargoElevator }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ConstructionModule" width="180px" show-overflow-tooltip label="工地模块">
        <template slot-scope="scope">
          <span>{{ scope.row.ConstructionModule }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="DeliveryDate" width="90px" show-overflow-tooltip label="订单交期">
        <template slot-scope="scope">
          <span>{{ formatDate('', '', scope.row.DeliveryDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CUser" width="110px" show-overflow-tooltip label="制单人">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderDate" width="110px" show-overflow-tooltip label="制单日期">
        <template slot-scope="scope">
          <span>{{ formatDate('', '', scope.row.OrderDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderNum" width="120px" show-overflow-tooltip label="订单编号">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="LineNum" width="90px" show-overflow-tooltip label="订单行">
        <template slot-scope="scope">
          <span>{{ scope.row.LineNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapNo" width="130px" show-overflow-tooltip label="SAP销售单号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapLine" width="130px" show-overflow-tooltip label="SAP销售行号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime,
  formatElevatorType
} from '@/utils';
import {
  getPageList
} from '@/api/CablePartProduce/PartWorkOrder';

import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import templatePartCountList from './print/PartCountList.json';
import templatePartNoList from './print/PartNoList.json';
import templatePartShipmentList from './print/PartShipmentList.json';
import templatePartSummaryList from './print/PartSummaryList.json';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

export default {
  name: 'PP.PP_ProductionSerialNumberAssignment',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      OrderTypeList: [{
        label: '电缆箱',
        value: 1
      }, {
        label: '电器箱',
        value: 2
      }],
      printStatus: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ],
      DetailNoOption: [
        {
          label: '全部',
          value: null
        },
        {
          label: '>2',
          value: 1
        },
        {
          label: '≤2',
          value: 2
        }
      ],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        ProductionLineDes: '',
        ProductionScheduler: '',
        ElectricCargoElevator: '',
        ConstructionModule: '',
        PageNumber: 1,
        PageSize: 10,
        DetailNo: null,
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        OrderNo: '',
        OrderTypeName: null,
        ContractNo: '',
        CustomerName: '',
        SapNo: '',
        WorkOrderPrintStatus: null,
        DeliveryDate: [],
        CreateDate: [
          new Date(),
          new Date()
        ]
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      tableHeight: '300px',
      CodeCompare: {
        'K': 'TopHeight',
        'R': 'LiftingHeight',
        'S': 'PitDepth',
        'LES': 'CabinetMovementDistance',
        'HW': 'HoistwayWide',
        'HD': 'HoistwayDepth'
      },
      CodeCompareName: {
        'K': '顶层高度',
        'R': '提升高度',
        'S': '底坑深度',
        'LES': '控制柜位移距离或控制柜侧加长距离',
        'HW': '井道净宽',
        'HD': '井道净深'
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
  },
  methods: {
    getNthValue(str, n) {
      const character = str.charAt(n - 1);
      // 将值转换为整数，如果为空，返回 0
      return parseInt(character) || 0;
    },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      getPageList(query).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      const postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    // 打印 件号清单
   
    handlePrintPartNo() {
      const printData = [];
      this.multipleSelection = this.sortObjectsByProperty(this.multipleSelection, 'SequenceNo')
      for (let i = 0;i < this.multipleSelection.length;i++) {
        if (this.multipleSelection[i].OrderDetailList != null) {
          this.addDataPartNoList(printData, this.multipleSelection[i], this.multipleSelection[i].OrderDetailList)
        }
      }
      // 准备打印数据
      const printDataList = [{
        PartNoList: printData
      }];

      // 根据排产序号排序
      const templateDef = new hiprint.PrintTemplate({ template: templatePartNoList });
      // 打印
      templateDef.print(printDataList);
    },

    // 打印 总数汇总清单
    handlePrintCountSummary() {
      const groupedByPartNo = {};

      // 收集所有订单的明细
      for (let i = 0;i < this.multipleSelection.length;i++) {
        const order = this.multipleSelection[i];
        if (order.OrderDetailList != null) {
          const details = JSON.parse(JSON.stringify(order.OrderDetailList));
          details.forEach(detail => {
            // 按部件件号分组并汇总
            const key = detail.CustomerPartNo;
            if (!groupedByPartNo[key]) {
              groupedByPartNo[key] = { PartNo: key, Quantity: 0, Unit: detail.Unit };
            }
            groupedByPartNo[key].Quantity += detail.Quantity;
          });
        }
      }

      // 转换为数组
      const summaryData = Object.values(groupedByPartNo);

      // 准备打印数据
      const printData = [{
        PartCountList: summaryData
      }];

      // 使用总数汇总模板
      const templateDef = new hiprint.PrintTemplate({ template: templatePartCountList });
      // 打印
      templateDef.print(printData);
    },

    addDataPartNoList(printData, order, details) {
      details.forEach(detail => {
        printData.push({

          ElectricCargoElevator: formatElevatorType(order.ElectricCargoElevator),
          ConstructionModule: order.ConstructionModule,
          ContractNo: order.ContractNo,
          DeliveryDate: formatDate('', '', order.DeliveryDate),
          FinishBatch: order.FinishBatch,
          SequenceNo: order.SequenceNo,
          ProductionDate: formatDate('', '', order.ProductionDate),
          CustomOrderNo: detail.CustomOrderNo,
          PartNo: detail.CustomerPartNo,
          MaterialDes: detail.MaterialDes,
          SingleQuantity: detail.Quantity,
          Unit: detail.Unit,
          Remark: order.ProductionRemark
        })
      });
    },

    sortObjectsByProperty(arr, property) {
      // 排序数组，根据指定属性
      return arr.sort((a, b) => {
        let aValue, bValue;
        // 尝试将两个值转换为浮点数，并处理异常
        try {
          aValue = parseFloat(a[property]);
        } catch (e) {
          aValue = NaN; // 转换失败时设置为 NaN
        }

        try {
          bValue = parseFloat(b[property]);
        } catch (e) {
          bValue = NaN; // 转换失败时设置为 NaN
        }

        // 将 NaN 排到最后
        if (isNaN(aValue) && isNaN(bValue)) return 0; // 两个都是 NaN
        if (isNaN(aValue)) return 1; // a 是 NaN，排到最后
        if (isNaN(bValue)) return -1; // b 是 NaN，排到最后

        // 正常比较
        return aValue - bValue;
      });
    },

    // 打印部件发货清单
    handlePrintPartShipment() {
      const groupedByOrderNo = {};
      // 按排产序号排序
      this.multipleSelection = this.sortObjectsByProperty(this.multipleSelection, 'SequenceNo');

      // 收集所有订单的明细
      for (let i = 0;i < this.multipleSelection.length;i++) {
        const order = this.multipleSelection[i];
        if (order.OrderDetailList != null) {
          const details = JSON.parse(JSON.stringify(order.OrderDetailList));
          details.forEach(detail => {
            // 按部件件号分组并汇总
            const key = order.ContractNo;
            if (!groupedByOrderNo[key]) {
              groupedByOrderNo[key] =
                   { CustomerOrderNum: '',
                     ElectricCargoElevator: formatElevatorType(order.ElectricCargoElevator),
                     ConstructionModule: order.ConstructionModule,
                     ContractNo: order.ContractNo,
                     DeliveryDate: formatDate('', '', order.DeliveryDate),
                     FinishBatch: order.FinishBatch,
                     SequenceNo: order.SequenceNo,
                     PartNo1: '',
                     PartNo2: '',
                     PartNo3: '',
                     PartNo4: '',
                     PartNo5: '',
                     PartNo6: '',
                     PartNo7: ''
                   }
            }

            if (groupedByOrderNo[key].PartNo1 === '') { groupedByOrderNo[key].PartNo1 = detail.CustomerPartNo } else if (groupedByOrderNo[key].PartNo2 === '') { groupedByOrderNo[key].PartNo2 = detail.CustomerPartNo } else if (groupedByOrderNo[key].PartNo3 === '') { groupedByOrderNo[key].PartNo3 = detail.CustomerPartNo } else if (groupedByOrderNo[key].PartNo4 === '') { groupedByOrderNo[key].PartNo4 = detail.CustomerPartNo } else if (groupedByOrderNo[key].PartNo5 === '') { groupedByOrderNo[key].PartNo5 = detail.CustomerPartNo } else if (groupedByOrderNo[key].PartNo6 === '') { groupedByOrderNo[key].PartNo6 = detail.CustomerPartNo } else if (groupedByOrderNo[key].PartNo7 === '') { groupedByOrderNo[key].PartNo7 = detail.CustomerPartNo }
          });
        }
        if (!groupedByOrderNo[order.ContractNo].CustomerOrderNum.includes(order.CustomerOrderNum)) {
          if (groupedByOrderNo[order.ContractNo].CustomerOrderNum === '') { groupedByOrderNo[order.ContractNo].CustomerOrderNum = order.CustomerOrderNum } else { groupedByOrderNo[order.ContractNo].CustomerOrderNum = '\r\n' + order.CustomerOrderNum }
        }
      }

      // 转换为数组
      const summaryData = Object.values(groupedByOrderNo);

      // 按交货日期排序
      summaryData.sort((a, b) => {
        return new Date(a.DeliveryDate) - new Date(b.DeliveryDate);
      });

      // 准备打印数据
      const printData = [{
        PartShipmentList: summaryData
      }];

      // 使用发货清单模板
      const templateDef = new hiprint.PrintTemplate({ template: templatePartShipmentList });
      // 打印
      templateDef.print(printData);
      this.isProcessing = false;
    },

    // 打印部件汇总清单
    handlePrintPartSummary() {
      const groupedByDate = {};

      let allDetails = [];

      // 收集所有订单的明细
      for (let i = 0;i < this.multipleSelection.length;i++) {
        const order = this.multipleSelection[i];
        if (order.OrderDetailList != null) {
          const details = JSON.parse(JSON.stringify(order.OrderDetailList));
          details.forEach(detail => {
            // 添加订单相关信息到明细
            detail.ElectricCargoElevator = formatElevatorType(order.ElectricCargoElevator);
            detail.ConstructionModule = order.ConstructionModule;
            detail.DeliveryDate = formatDate('', '', order.DeliveryDate);

            // 按部件件号分组并汇总
            const key = detail.CustomerPartNo;
            if (!groupedByDate[key]) {
              groupedByDate[key] = { ...detail, Quantity: 0, FinishDate: formatDate('', '', order.FinishDate), FinishBatch: order.FinishBatch };
            }
            groupedByDate[key].Quantity += detail.Quantity;
          });
          allDetails = allDetails.concat(details);
        }
      }

      // 转换为数组
      const summaryData = Object.values(groupedByDate);

      // 按交货日期排序
      summaryData.sort((a, b) => {
        return new Date(a.DeliveryDate) - new Date(b.DeliveryDate);
      });

      // 准备打印数据
      const printData = [{
        PartSummaryList: summaryData
      }];

      // 使用汇总清单模板
      const templateDef = new hiprint.PrintTemplate({ template: templatePartSummaryList });
      // 打印
      templateDef.print(printData);
      this.isProcessing = false;
    }
  }
};
</script>
