<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="7">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.CreateDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单交期">
              <el-date-picker
                v-model="listQuery.DeliveryDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="发运日期">
              <el-date-picker
                v-model="listQuery.ShipmentDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="客户名称">
              <el-input
                v-model="listQuery.CustomerName"
                size="small"
                class="filter-item"
                placeholder="客户名称"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单号">
              <el-input
                v-model="listQuery.SalesOrderNo"
                size="small"
                class="filter-item"
                placeholder="销售订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="顺序号">
              <el-input
                v-model="listQuery.SerialNo"
                size="small"
                class="filter-item"
                placeholder="顺序号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="标签打印状态">
              <el-select v-model="listQuery.LabelPrintStatus" clearable style="width: 100%">
                <el-option
                  v-for="item in printStatus"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="唛头打印状态">
              <el-select v-model="listQuery.ShippingMarkPrintStatus" clearable style="width: 100%">
                <el-option
                  v-for="item in printStatus"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="订单类型">
              <el-select v-model="listQuery.OrderTypeName" clearable style="width: 100%">
                <el-option
                  v-for="item in OrderTypeList"
                  :key="item.EnumValue"
                  :value="item.EnumValue"
                  :label="item.EnumValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="电梯类型">
              <el-input
                v-model="listQuery.ElevatorType"
                size="small"
                class="filter-item"
                placeholder="电梯类型"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="handlePrint(1)"
      >标签打印
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="handlePrint(2)"
      >附加标签打印
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="listPrintA4"
      >清单打印A4
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="listPrintA5"
      >清单打印A5
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="cpListPrintA5"
      >打包CP件清单A5
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="handleShippingMarkPrint"
      >唛头打印A5
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.PackLabel.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="this.multipleSelection.length === 0"
        @click="handleShippingMarkPrint(2)"
      >唛头打印A6
      </el-button>
      <el-dropdown v-permission="{name:'Cable.PackLabel.Print'}" class="filter-item" style="margin-left: 10px;" @command="handleYidaCommand">
        <el-button type="warning" icon="el-icon-printer" size="small" :disabled="this.multipleSelection.length === 0">
          怡达打印<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="yidaHandlePrint">怡达标签打印</el-dropdown-item>
          <el-dropdown-item command="yidaPackHandlePrint">怡达装箱标签打印</el-dropdown-item>
          <el-dropdown-item command="yidaFloorHandlePrint">怡达标签打印-楼层</el-dropdown-item>
          <el-dropdown-item command="yidaListPrintA4">怡达装箱清单打印A4</el-dropdown-item>
          <el-dropdown-item command="handleYidaShippingMarkPrintA5">怡达唛头打印A5</el-dropdown-item>
          <el-dropdown-item command="handleYidaShippingMarkPrintA6">怡达唛头打印A6</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-left: 10px;"
        @click="handleFilter"
      >查询
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column align="center" prop="LabelPrintStatus" width="130px" show-overflow-tooltip label="标签打印">
        <template slot-scope="scope">
          <span>{{ scope.row.LabelPrintStatus === 1 ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ShippingMarkPrintStatus" width="130px" show-overflow-tooltip label="唛头打印">
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPrintStatus === 1 ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderTypeName" width="120px" show-overflow-tooltip label="订单类型">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ElevatorType" width="120px" show-overflow-tooltip label="电梯类型">
        <template slot-scope="scope">
          <span>{{ scope.row.ElevatorType }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerCode" width="120px" show-overflow-tooltip label="客户编码">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerName" width="180px" show-overflow-tooltip label="客户名称">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="CustomerOrderNum"
        width="180px"
        show-overflow-tooltip
        label="客户定单号"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ContractNo" width="180px" show-overflow-tooltip label="合同号">
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="DeliveryDate" width="90px" show-overflow-tooltip label="订单交期">
        <template slot-scope="scope">
          <span>{{ formatDate('', '', scope.row.DeliveryDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SetShipmentDate" width="90px" show-overflow-tooltip label="发运日期">
        <template slot-scope="scope">
          <span>{{ formatDate('', '', scope.row.SetShipmentDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="BatchNum" width="180px" show-overflow-tooltip label="批次">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SerialNo" width="180px" show-overflow-tooltip label="顺序号">
        <template slot-scope="scope">
          <span>{{ scope.row.SerialNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CUser" width="110px" show-overflow-tooltip label="制单人">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderDate" width="110px" show-overflow-tooltip label="制单日期">
        <template slot-scope="scope">
          <span>{{ formatDate('','',scope.row.OrderDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderNum" width="120px" show-overflow-tooltip label="订单编号">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="LineNum" width="90px" show-overflow-tooltip label="订单行">
        <template slot-scope="scope">
          <span>{{ scope.row.LineNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapNo" width="130px" show-overflow-tooltip label="SAP销售单号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapLine" width="130px" show-overflow-tooltip label="SAP销售行号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="SetShipmentUser" align="center" width="100" />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="SetShipmentDate"
        align="center"
        width="150"
        :formatter="formatDateTime"
      />

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  getPageList, updatePrint
} from '@/api/CableProduce/WorkOrder';
import {
  parseTime
} from '@/utils';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import labelTemplate from './print/label.json';
import listTemplate from './print/list.json';
import listTemplateA5 from './print/listA5.json';
import shippingMarkTemplateA5 from './print/shippingMark.json';
import shippingMarkTemplateA6 from './print/shippingMark.json';
import yidaBranchLineLabel from './print/yidaBranchLineLabel.json';
import yidaBranchLineFloorLabel from './print/yidaBranchLineFloorLabel.json';
import yidaShippingMarkA5 from './print/yidaShippingMarkA5.json';
import yidaShippingMarkA6 from './print/yidaShippingMarkA6.json';
import yidaListA4 from './print/yidaListA4.json';
import yidaLabel from './print/yidaLabel.json';
import cpPackList from './print/cpPackList.json';
import { getAllList } from '@/api/CableBasic/CustomerPart';
import { fetchList as dictionaryFetchList } from '../../../api/Sys/Sys_Dictionary.js';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

const templateDef = new hiprint.PrintTemplate({ template: labelTemplate });
const listTemplateDef = new hiprint.PrintTemplate({ template: listTemplate });
const listTemplateA5Def = new hiprint.PrintTemplate({ template: listTemplateA5 });
const shippingMarkTemplateA5Def = new hiprint.PrintTemplate({ template: shippingMarkTemplateA5 });
const shippingMarkTemplateA6Def = new hiprint.PrintTemplate({ template: shippingMarkTemplateA6 });
const yidaBranchLineLabelDef = new hiprint.PrintTemplate({ template: yidaBranchLineLabel });
const yidaBranchLineFloorLabelDef = new hiprint.PrintTemplate({ template: yidaBranchLineFloorLabel });
const yidaShippingMarkA5Def = new hiprint.PrintTemplate({ template: yidaShippingMarkA5 });
const yidaShippingMarkA6Def = new hiprint.PrintTemplate({ template: yidaShippingMarkA6 });
const yidaLabelDef = new hiprint.PrintTemplate({ template: yidaLabel });
const yidaListA4Def = new hiprint.PrintTemplate({ template: yidaListA4 });
const cpPackListDef = new hiprint.PrintTemplate({ template: cpPackList });

export default {
  name: 'PP.PP_ProductionSerialNumberAssignment',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      printStatus: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ],
      OrderTypeList: [],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        ProductionLineDes: '',
        ProductionScheduler: '',
        PageNumber: 1,
        PageSize: 10,
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        OrderNo: '',
        ContractNo: '',
        OrderTypeName: '',
        DeliveryDate: [],
        LabelPrintStatus: null,
        ShippingMarkPrintStatus: null,
        CreateDate: [
          new Date(),
          new Date()
        ]
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      tableHeight: '300px'
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    // templateDef.addPrintPanel({ width: 95, height: 25, paperFooter: 0, paperHeader: 0, paperNumberDisabled: true })
    this.initOrderType();
  },
  methods: {
    formatDate,
    formatDateTime,
    initOrderType() {
      // 订单类型
      dictionaryFetchList({
        typeCode: 'CableOrderType'
      }).then(response => {
        this.OrderTypeList = response.Data;
        this.OrderTypeList = this.OrderTypeList.sort((a, b) => a.EnumKey - b.EnumKey);
        this.fullscreenLoading = false;
      }).catch((c) => {
        this.fullscreenLoading = false;
      });
    },
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      getPageList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    handleYidaCommand(command) {
      switch (command) {
        case 'yidaHandlePrint':
          this.yidaHandlePrint();
          break;
        case 'yidaPackHandlePrint':
          this.yidaPackHandlePrint();
          break;
        case 'yidaFloorHandlePrint':
          this.yidaFloorHandlePrint();
          break;
        case 'yidaListPrintA4':
          this.yidaListPrintA4();
          break;
        case 'handleYidaShippingMarkPrintA5':
          this.handleYidaShippingMarkPrintA5();
          break;
        case 'handleYidaShippingMarkPrintA6':
          this.handleYidaShippingMarkPrintA6();
          break;
      }
    },
    // 打印
    handlePrint(flag) {
      getAllList().then(response => {
        const partList = response.Data;
        const ids = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          ids.push(this.multipleSelection[i].Id)
        }
        updatePrint({
          Ids: ids,
          Identification: 2
        }).then((res) => {
          this.getList();
        })
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          const materialCodes = [];
          for (let j = 0;j < details.length;j++) {
            const part = partList.find(item => {
              return item.PartNo === details[j].CustomerPartNo
            })
            let no = 1
            // 检查包含几个
            for (let k = 0;k < materialCodes.length;k++) {
              if (details[j].CustomerPartNo === materialCodes[k]) {
                no++
              }
            }
            if (part != null && part.IsBoxedItem === '是' && details[j].Classify !== '通用' && details[j].Classify !== '井道1') {
              if (flag === 1) {
                printData.push({
                  ScanNo: this.multipleSelection[i].ContractNo + ' & ' + details[j].CustomerPartNo + ' - ' + no,
                  ContractNo: this.multipleSelection[i].ContractNo,
                  ProductionNo: this.multipleSelection[i].ProductionNo,
                  DeliveryDate: parseTime(this.multipleSelection[i].DeliveryDate, '{y}-{m}-{d}'),
                  MaterialDes: details[j].MaterialDes,
                  MaterialCode: details[j].CustomerPartNo,
                  Classify: details[j].Classify,
                  Sort: details[j].Sort,
                  Quantity: details[j].Quantity
                })
              } else if (flag === 2) {
                if (details[j].Classify === '井道-附加') {
                  printData.push({
                    ScanNo: this.multipleSelection[i].ContractNo + ' & ' + details[j].CustomerPartNo + ' - ' + no,
                    ContractNo: this.multipleSelection[i].ContractNo,
                    ProductionNo: this.multipleSelection[i].ProductionNo,
                    DeliveryDate: parseTime(this.multipleSelection[i].DeliveryDate, '{y}-{m}-{d}'),
                    MaterialDes: details[j].MaterialDes,
                    MaterialCode: details[j].CustomerPartNo,
                    Classify: details[j].Classify,
                    Sort: details[j].Sort,
                    Quantity: details[j].Quantity
                  })
                }
              }
            }
            materialCodes.push(details[j].CustomerPartNo)
          }
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 按Classify字段进行分组
        const groupedData = printData.reduce((acc, item) => {
          if (!acc[item.Classify]) {
            acc[item.Classify] = [];
          }
          acc[item.Classify].push(item);
          return acc;
        }, {});

        // 对每个分组内的数据进行Sort排序
        // eslint-disable-next-line no-unused-vars
        for (const classify in groupedData) {
          groupedData[classify].sort((a, b) => a.Sort - b.Sort);
        }
        // 将分组后的数据合并为一个数组
        const sortedData = Object.values(groupedData).flat();
        // 打印
        templateDef.print(sortedData);
      });
    },
    // 打印
    yidaHandlePrint() {
      getAllList().then(response => {
        const partList = response.Data;
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          for (let j = 0;j < details.length;j++) {
            const part = partList.find(item => {
              return item.PartNo === details[j].CustomerPartNo
            })
            if (part != null && details[j].Classify === '随行1') {
              printData.push({
                ContractNo: this.multipleSelection[i].ContractNo,
                ProductionNo: this.multipleSelection[i].ProductionNo,
                MaterialDes: details[j].MaterialDes,
                MaterialCode: details[j].CustomerPartNo,
                Sort: details[j].Sort,
                Quantity: details[j].Quantity,
                SpecificationModel: details[j].SpecificationModel,
                DeliveryDate: parseTime(this.multipleSelection[i].DeliveryDate, '{y}-{m}-{d}'),
                QrCode: this.multipleSelection[i].ContractNo + '&L2'
              })
            }
          }
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 打印
        yidaBranchLineLabelDef.print(printData);
      });
    }, // 打印
    yidaPackHandlePrint() {
      let printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        printData.push({
          ContractNo: this.multipleSelection[i].ContractNo,
          SerialNo: this.multipleSelection[i].SerialNo,
          QrCode: this.multipleSelection[i].ContractNo + '&L1'
        })
      }
      printData = this.sortObjectsByProperty(printData, 'SerialNo')
      // 打印
      yidaLabelDef.print(printData);
    },
    // 打印
    yidaFloorHandlePrint() {
      getAllList().then(response => {
        const printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          console.log('this.multipleSelection[i]', this.multipleSelection[i])
          const serverLayer = this.multipleSelection[i].SaleParameter.ServerLayer;
          const details = this.multipleSelection[i].OrderDetailList;
          for (let j = 0;j < details.length;j++) {
            if (details[j].Classify === '分支') {
              console.log('serverLayer', serverLayer)
              if (serverLayer !== null && serverLayer !== undefined && serverLayer !== '') {
                const layers = serverLayer.split(',');
                for (let k = 0;k < layers.length;k++) {
                  printData.push({
                    ProductionNo: this.multipleSelection[i].ProductionNo,
                    MaterialDes: details[j].MaterialDes,
                    MaterialCode: details[j].CustomerPartNo,
                    Sort: details[j].Sort,
                    Quantity: details[j].Quantity,
                    FloorNo: layers[k]
                  })
                }
              }
            }
          }
        }
        // printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 打印
        yidaBranchLineFloorLabelDef.print(printData);
      });
    },
    // 清单打印A4
    listPrintA4() {
      getAllList().then(response => {
        // let partList = response.Data;
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          let saleParameter = {};
          if (this.multipleSelection[i].SaleParameter != null) {
            saleParameter = this.multipleSelection[i].SaleParameter;
          }
          const hoistwayDetails = details.filter(detail => detail.Classify !== '随行' && detail.Classify !== '随行1' && detail.Classify !== '通用' && detail.Classify !== '通用1');
          if (hoistwayDetails.length > 0) {
            printData.push({
              FloorStationStopOpen: saleParameter.FloorNo + '/' + saleParameter.StationStopNo + '/' + saleParameter.OpenNo,
              ContractNo: this.multipleSelection[i].ContractNo,
              ProjectDisc: this.multipleSelection[i].ProjectDisc,
              ProductionNo: this.multipleSelection[i].ProductionNo,
              DeliveryDate: parseTime(this.multipleSelection[i].DeliveryDate, '{y}-{m}-{d}'),
              SerialNo: this.multipleSelection[i].SerialNo,
              BinNo: this.getBinNo(details, 2),
              ScanNo: this.multipleSelection[i].ContractNo + '&' + this.multipleSelection[i].CustomerOrderNum + '&' + this.multipleSelection[i].CustomerOrderLine + '&JD',
              Details: hoistwayDetails
            })
          }
          const currencyDetails = details.filter(detail => detail.Classify === '通用' || detail.Classify === '通用1')
          if (currencyDetails.length > 0) {
            printData.push({
              FloorStationStopOpen: saleParameter.FloorNo + '/' + saleParameter.StationStopNo + '/' + saleParameter.OpenNo,
              ContractNo: this.multipleSelection[i].ContractNo,
              ProjectDisc: this.multipleSelection[i].ProjectDisc,
              ProductionNo: this.multipleSelection[i].ProductionNo,
              DeliveryDate: parseTime(this.multipleSelection[i].DeliveryDate, '{y}-{m}-{d}'),
              SerialNo: this.multipleSelection[i].SerialNo,
              BinNo: this.getBinNo(details, 3),
              ScanNo: this.multipleSelection[i].ContractNo + '&' + this.multipleSelection[i].CustomerOrderNum + '&' + this.multipleSelection[i].CustomerOrderLine + '&TY',
              Details: currencyDetails
            })
          }
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 打印
        listTemplateDef.print(printData);
      });
    },
    // 清单打印A5
    listPrintA5() {
      getAllList().then(response => {
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          let saleParameter = {};
          if (this.multipleSelection[i].SaleParameter != null) {
            saleParameter = this.multipleSelection[i].SaleParameter;
          }
          const followDetails = details.filter(detail => detail.Classify === '随行' || detail.Classify === '随行1');
          if (followDetails.length > 0) {
            printData.push({
              FloorStationStopOpen: saleParameter.FloorNo + '/' + saleParameter.StationStopNo + '/' + saleParameter.OpenNo,
              ContractNo: this.multipleSelection[i].ContractNo,
              ProjectDisc: this.multipleSelection[i].ProjectDisc,
              ProductionNo: this.multipleSelection[i].ProductionNo,
              DeliveryDate: parseTime(this.multipleSelection[i].DeliveryDate, '{y}-{m}-{d}'),
              SerialNo: this.multipleSelection[i].SerialNo,
              BinNo: this.getBinNo(details, 1),
              ScanNo: this.multipleSelection[i].ContractNo + '&' + this.multipleSelection[i].CustomerOrderNum + '&' + this.multipleSelection[i].CustomerOrderLine + '&SX',
              Details: followDetails
            })
          }
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 打印
        listTemplateA5Def.print(printData);
      });
    },
    // 清单打印A5
    cpListPrintA5() {
      getAllList().then(response => {
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          if (details.length > 0) {
            printData.push({
              ContractNo: this.multipleSelection[i].ContractNo,
              BatchNum: this.multipleSelection[i].BatchNum,
              SerialNo: this.multipleSelection[i].SerialNo,
              ElevatorType: this.multipleSelection[i].ElevatorType,
              table: details
            })
          }
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 打印
        cpPackListDef.print(printData);
      });
    },
    // 清单打印A4
    yidaListPrintA4() {
      getAllList().then(response => {
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          let saleParameter = {};
          if (this.multipleSelection[i].SaleParameter != null) {
            saleParameter = this.multipleSelection[i].SaleParameter;
          }
          printData.push({
            FloorStationStopOpen: saleParameter.FloorNo + '/' + saleParameter.StationStopNo + '/' + saleParameter.OpenNo,
            TopHeight: this.multipleSelection[i].SaleParameter.TopHeight,
            LiftingHeight: this.multipleSelection[i].SaleParameter.LiftingHeight,
            PitDepth: this.multipleSelection[i].SaleParameter.PitDepth,
            Trapezium: this.multipleSelection[i].SaleParameter.Trapezium,
            ContractNo: this.multipleSelection[i].ContractNo,
            ProjectDisc: this.multipleSelection[i].ProjectDisc,
            ProductionNo: this.multipleSelection[i].ProductionNo,
            ScanNo: this.multipleSelection[i].ContractNo,
            Details: details
          })
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 打印
        yidaListA4Def.print(printData);
      });
    },
    getBinNo(details, classify) {
      let count = 0;
      let sx = false;
      if (details.filter(detail => detail.Classify === '随行' || detail.Classify === '随行1').length > 0) {
        count++
        sx = true;
      }
      if (details.filter(detail => detail.Classify !== '随行' && detail.Classify !== '随行1' && detail.Classify !== '通用').length > 0) {
        count++
      }
      if (details.filter(detail => detail.Classify === '通用').length > 0) {
        count++
      }
      if (classify === 1) return count + '-' + 1;
      if (classify === 2) {
        if (sx) {
          return count + '-' + 2
        } else {
          return count + '-' + 1
        }
      }
      if (classify === 3) return count + '-' + count;
    },
    // 打印唛头
    handleShippingMarkPrint(version) {
      getAllList().then(response => {
        const partList = response.Data;
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          let saleParameter = {};
          if (this.multipleSelection[i].SaleParameter != null) {
            saleParameter = this.multipleSelection[i].SaleParameter;
          }
          let floor = {};
          if (this.multipleSelection[i].SaleParameter != null && this.multipleSelection[i].SaleParameter.OrderFloorList != null) {
            floor = this.multipleSelection[i].SaleParameter.OrderFloorList;
          }
          const followDetails = details.filter(detail => detail.Classify === '随行' || detail.Classify === '随行1');
          const handleContract = this.multipleSelection[i].ContractNo.replace('@Q', ' ');
          const ymNo = handleContract + '《14#电缆打包件》' + this.getBinNo(details, 1).split('-')[1] + '/' + this.getBinNo(details, 1).split('-')[0];
          console.log('ymNo', ymNo)
          if (followDetails.length > 0) {
            printData.push({
              ContractNo: this.multipleSelection[i].ContractNo,
              ProductionNo: this.multipleSelection[i].ProductionNo,
              ProjectDisc: this.multipleSelection[i].ProjectDisc,
              DeliveryDate: parseTime(this.multipleSelection[i].CompletionDate, '{y}-{m}-{d}'),
              SerialNo: this.multipleSelection[i].SerialNo,
              BinNo: this.getBinNo(details, 1),
              YmNo: ymNo,
              ProductName: '随行',
              OrderingCustom: saleParameter.OrderingCustom,
              Weight: this.getWeight(followDetails, partList) + 'KG',
              Size: this.getSize(floor, this.getBinNo(details, 1)),
              ScanNo: this.multipleSelection[i].ContractNo + '&SX',
              Details: followDetails
            })
          }
          const hoistwayDetails = details.filter(detail => detail.Classify !== '随行' && detail.Classify !== '随行1' && detail.Classify !== '通用' && detail.Classify !== '通用1');
          if (hoistwayDetails.length > 0) {
            printData.push({
              ContractNo: this.multipleSelection[i].ContractNo,
              ProductionNo: this.multipleSelection[i].ProductionNo,
              ProjectDisc: this.multipleSelection[i].ProjectDisc,
              DeliveryDate: parseTime(this.multipleSelection[i].CompletionDate, '{y}-{m}-{d}'),
              SerialNo: this.multipleSelection[i].SerialNo,
              BinNo: this.getBinNo(details, 2),
              YmNo: ymNo,
              ProductName: '井道',
              OrderingCustom: saleParameter.OrderingCustom,
              Weight: this.getWeight(hoistwayDetails, partList) + 'KG',
              Size: this.getSize(floor, this.getBinNo(details, 2)),
              ScanNo: this.multipleSelection[i].ContractNo + '&JD',
              Details: hoistwayDetails
            })
          }
          const currencyDetails = details.filter(detail => detail.Classify === '通用' || detail.Classify === '通用1');
          if (currencyDetails.length > 0) {
            printData.push({
              ContractNo: this.multipleSelection[i].ContractNo,
              ProductionNo: this.multipleSelection[i].ProductionNo,
              ProjectDisc: this.multipleSelection[i].ProjectDisc,
              DeliveryDate: parseTime(this.multipleSelection[i].CompletionDate, '{y}-{m}-{d}'),
              SerialNo: this.multipleSelection[i].SerialNo,
              BinNo: this.getBinNo(details, 3),
              YmNo: ymNo,
              ProductName: '通用',
              OrderingCustom: saleParameter.OrderingCustom,
              Weight: this.getWeight(currencyDetails, partList) + 'KG',
              Size: this.getSize(floor, this.getBinNo(details, 3)),
              ScanNo: this.multipleSelection[i].ContractNo + '&TY',
              Details: currencyDetails
            })
          }
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 更新打印状态
        const ids = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          ids.push(this.multipleSelection[i].Id)
        }
        updatePrint({
          Ids: ids,
          Identification: 3
        }).then((res) => {
          this.getList();
        })
        console.log('printData', printData)
        // 打印
        if (version === 2) {
          shippingMarkTemplateA6Def.print(printData);
        } else {
          shippingMarkTemplateA5Def.print(printData);
        }
      });
    },
    // 怡达唛头A5
    handleYidaShippingMarkPrintA5() {
      getAllList().then(response => {
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const shipmentDate = this.multipleSelection[i].ShipmentDate === null || this.multipleSelection[i].ShipmentDate === undefined ? '' : parseTime(this.multipleSelection[i].ShipmentDate, '{y}-{m}-{d}')
          printData.push({
            BatchSerial: shipmentDate + '-' + this.multipleSelection[i].SerialNo,
            ProductionNo: this.multipleSelection[i].ProductionNo,
            ContractNo: this.multipleSelection[i].ContractNo,
            ScanNo: this.multipleSelection[i].ContractNo + '&JD'
          })
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 更新打印状态
        const ids = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          ids.push(this.multipleSelection[i].Id)
        }
        updatePrint({
          Ids: ids,
          Identification: 3
        }).then((res) => {
          this.getList();
        })
        yidaShippingMarkA5Def.print(printData);
      });
    },
    // 怡达唛头A6
    handleYidaShippingMarkPrintA6() {
      getAllList().then(response => {
        let printData = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const shipmentDate = this.multipleSelection[i].ShipmentDate === null || this.multipleSelection[i].ShipmentDate === undefined ? '' : parseTime(this.multipleSelection[i].ShipmentDate, '{y}-{m}-{d}')
          printData.push({
            BatchSerial: shipmentDate + '-' + this.multipleSelection[i].SerialNo,
            ProductionNo: this.multipleSelection[i].ProductionNo,
            ContractNo: this.multipleSelection[i].ContractNo,
            ScanNo: this.multipleSelection[i].ContractNo
          })
        }
        printData = this.sortObjectsByProperty(printData, 'ProductionNo')
        // 更新打印状态
        const ids = [];
        for (let i = 0;i < this.multipleSelection.length;i++) {
          ids.push(this.multipleSelection[i].Id)
        }
        updatePrint({
          Ids: ids,
          Identification: 3
        }).then((res) => {
          this.getList();
        })
        yidaShippingMarkA6Def.print(printData);
      });
    },
    getWeight(followDetails, partList) {
      let weight = 0;
      for (let i = 0;i < followDetails.length;i++) {
        console.log('followDetails[i]', followDetails[i])
        const part = partList.find(item => {
          return item.PartNo === followDetails[i].CustomerPartNo
        })
        if (part === undefined) {
          this.showNotify('error', followDetails[i].CustomerPartNo + '缺少维护信息')
          return;
        }
        console.log('part', part)
        if (part !== null && part['Weight'] !== null && followDetails[i]['Quantity'] !== null) {
          weight += Math.round(parseFloat(followDetails[i]['Quantity']) * parseFloat(part['Weight']) * 1000) / 1000;
        }
      }
      return weight.toFixed(3);
    },
    getSize(floor, boxNo) {
      let size = '420*320*250';
      if (floor.length <= 12 && boxNo.split('-')[1] === '1') {
        size = '600*600*150';
      }
      if (floor.length <= 12 && boxNo.split('-')[1] === '2') {
        size = '600*600*200';
      }
      if (floor.length > 12 && boxNo.split('-')[1] === '1') {
        size = '600*600*200';
      }
      if (floor.length > 12 && boxNo.split('-')[1] === '2') {
        size = '600*600*300';
      }
      return size;
    },
    sortObjectsByProperty(arr, property) {
      // 排序数组，根据指定属性
      return arr.sort((a, b) => {
        let aValue, bValue;

        // 尝试将两个值转换为浮点数，并处理异常
        try {
          aValue = parseFloat(a[property]);
        } catch (e) {
          aValue = NaN; // 转换失败时设置为 NaN
        }

        try {
          bValue = parseFloat(b[property]);
        } catch (e) {
          bValue = NaN; // 转换失败时设置为 NaN
        }

        // 将 NaN 排到最后
        if (isNaN(aValue) && isNaN(bValue)) return 0; // 两个都是 NaN
        if (isNaN(aValue)) return 1; // a 是 NaN，排到最后
        if (isNaN(bValue)) return -1; // b 是 NaN，排到最后

        // 正常比较
        return aValue - bValue;
      });
    }
  }
};
</script>
