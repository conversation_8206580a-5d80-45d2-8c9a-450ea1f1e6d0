<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="装配日期">
              <el-date-picker
                v-model="listQuery.dateValue"
                size="small"
                :clearable="false"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.createDateValue"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单号">
              <el-input
                v-model="listQuery.SalesOrderNo"
                size="small"
                class="filter-item"
                placeholder="销售订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单行号">
              <el-input
                v-model="listQuery.SalesOrderLineNo"
                size="small"
                class="filter-item"
                placeholder="销售订单行号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_ProductionSerialNumberAssignment.distribution'}"
        class="filter-item"
        type="success"
        icon="el-icon-refresh"
        size="small"
        @click="handleDistribution"
      >分配
      </el-button>
      <!--      <el-button v-waves v-permission="{name:'PP.PP_ProductionSerialNumberAssignment.Email'}" class="filter-item"-->
      <!--        type="primary" icon="el-icon-circle-check" size="small" :disabled="postDisable" @click="handleEmail">下达-->
      <!--      </el-button>-->
      <!--  <el-button v-waves v-permission="{ name: 'PP.PP_ProductionSerialNumberAssignment.Export' }" class="filter-item"
        type="primary" icon="el-icon-document" size="small" @click="handleExport">{{ $t("Common.export") }}</el-button>-->
      <el-dropdown
        v-waves
        v-permission="{ name: 'PP.PP_ProductionSerialNumberAssignment.Export' }"
        class="filter-item"
        split-button
        type="primary"
        size="small"
        style="margin-left: 10px;"
        @click="handleExport"
        @command="handleCommand"
      >
        {{ $t("Common.export") }}
        <el-dropdown-menu slot="dropdown">
          <!--          <el-dropdown-item command="1">导出装配指令单</el-dropdown-item>-->
          <el-dropdown-item command="2">导出序列号</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-waves
        v-permission="{name:'PP.PP_ProductionSerialNumberAssignment.Delete'}"
        class="filter-item"
        style="margin-left: 10px;"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >
        取消分配
      </el-button>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="排产状态" prop="IsNoticed" align="center" width="80px" fixed="right" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.IsNoticed === 1 || scope.row.IsNoticed === '1' ">已通知</span>
          <span v-if="scope.row.IsNoticed === 0 || scope.row.IsNoticed === '0' ">未通知</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP生产订单" prop="ProductionOrderNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionOrderNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出厂编号" prop="SerialNo" align="center" width="140px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SerialNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发货单位" prop="Shippers" align="center" width="140px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Shippers }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="交货日期"
        prop="DeliveryTime"
        align="center"
        width="100px"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column
        label="装配日期"
        prop="StartTime"
        align="center"
        width="100px"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="生产批次" prop="ProductionBatch" align="center" width="80px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户订单号" prop="OrderNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合同号" prop="ContractNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" prop="MaterialNo" align="center" width="160px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.MaterialCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户件号" prop="MaterialName" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerPartNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售订单号" prop="SalesOrderNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售订单行号" prop="SalesOrderLineNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderLineNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单类型" prop="OrderType" align="center" width="80px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.OrderType }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="订单状态" prop="OrderStatus" align="center" width="80px" show-overflow-tooltip /> -->
      <el-table-column label="工厂" prop="FactoryCode" align="center" width="60px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.FactoryCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存地点" prop="ReceivingLocation" align="center" width="80px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.StoreCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100px"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  dispenseSerialNo,
  unDispenseSerialNo
} from '@/api/CableProduce/ProductionOrder';

export default {
  name: 'PP.PP_ProductionSerialNumberAssignment',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        ProductionLineDes: '',
        ProductionScheduler: '',
        PageNumber: 1,
        PageSize: 10,
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        OrderNo: '',
        ContractNo: '',
        dateValue: [
          new Date(),
          new Date()
        ],
        createDateValue: [
        ]
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      tableHeight: '300px'
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    // this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        createDateValue: this.listQuery.createDateValue,
        ProductionLineDes: this.listQuery.ProductionLineDes,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        SalesOrderNo: this.listQuery.SalesOrderNo,
        SalesOrderLineNo: this.listQuery.SalesOrderLineNo,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, res.fileName);
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleCommand(command) {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        createDateValue: this.listQuery.createDateValue,
        ProductionLineDes: this.listQuery.ProductionLineDes,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        SalesOrderNo: this.listQuery.SalesOrderNo,
        SalesOrderLineNo: this.listQuery.SalesOrderLineNo,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      if (command === '1') {
        this.handleExport()
      } else if (command === '2') {
        exportExcelFileSerialNumber(exportQuery).then(res => {
          exportToExcel(res.data, res.fileName);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    // 分配
    handleDistribution() {
      this.isProcessing = true;
      // if (this.listQuery.dateValue) {
      //   this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
      //   this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      // }
      const ids = this.multipleSelection.map(t => t.Id);
      const query = {
        ids: ids
      };
      dispenseSerialNo(query).then(res => {
        if (res.Code === 2000) {
          this.handleFilter();
          this.showNotify('success', '操作成功');
          this.isProcessing = false;
        }
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 邮件
    handleEmail() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      const arrRowsID = selectRows.map(v => v.ProductionOrderNo);
      IssueNotice(arrRowsID).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', res.Message);
        } else {
          this.showNotify('error', res.Message);
        }
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', 'SAP生产订单为：' + v.ProductionOrderNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          const ids = selectRows.map(v => v.Id);
          // 删除逻辑处理
          const query = {
            ids: ids
          };
          unDispenseSerialNo(query)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', res.Message || 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    }
  }
};
</script>
