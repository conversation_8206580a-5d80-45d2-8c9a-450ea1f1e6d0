<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="7">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.CreateDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单交期">
              <el-date-picker
                v-model="listQuery.DeliveryDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户名称">
              <el-input
                v-model="listQuery.CustomerName"
                size="small"
                class="filter-item"
                placeholder="客户名称"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售订单号">
              <el-input
                v-model="listQuery.SapNo"
                size="small"
                class="filter-item"
                placeholder="销售订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="工单打印状态">
              <el-select
                v-model="listQuery.WorkOrderPrintStatus"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in printStatus"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="订单类型">
              <el-select
                v-model="listQuery.OrderTypeName"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in OrderTypeList"
                  :key="item.EnumValue"
                  :value="item.EnumValue"
                  :label="item.EnumValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="电梯类型">
              <el-input
                v-model="listQuery.ElevatorType"
                size="small"
                class="filter-item"
                placeholder="电梯类型"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="扫描状态">
              <el-select
                v-model="listQuery.ScanStatus"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in ScanStatusList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="井道扫描状态">
              <el-select
                v-model="listQuery.HoistwayStatus"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in ScanStatusList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="primary"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="generateProductNo"
      >生成排序
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="handlePrint"
      >打印工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="printProductionList"
      >打印电缆箱排产清单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="printBranchLineProductionWorkOrder"
      >分支线排产工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="printAdditionalLineProductionWorkOrder"
      >附加线排产工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="printAccompanyingCableCuttingWorkOrder"
      >随行电缆裁线工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="generalCableProductionWorkOrder"
      >通用电缆排产工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="generalOneCableProductionWorkOrder"
      >通用1电缆排产工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="warning"
        icon="el-icon-printer"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="yidaGeneralCableProductionWorkOrder"
      >怡达通用电缆排产工单
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="exportProductionFormula(false)"
      >导出生产配方
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        :disabled="multipleSelection.length === 0"
        @click="exportProductionFormula(true)"
      >导出生产配方-反楼层
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'Cable.WorkPrint.Print'}"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="contractDetailExport()"
      >合同明细导出
      </el-button>
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-left: 10px;"
        @click="handleFilter"
      >查询
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        align="center"
        prop="WorkOrderPrintStatus"
        width="130px"
        show-overflow-tooltip
        label="工单打印状态"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WorkOrderPrintStatus === 1 ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderTypeName" width="120px" show-overflow-tooltip label="订单类型">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ElevatorType" width="120px" show-overflow-tooltip label="电梯类型">
        <template slot-scope="scope">
          <span>{{ scope.row.ElevatorType }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ScanStatus" width="120px" show-overflow-tooltip label="扫描状态">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanStatus === 1 ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="HoistwayStatus" width="120px" show-overflow-tooltip label="井道扫描状态">
        <template slot-scope="scope">
          <span>{{ scope.row.HoistwayStatus === 1 ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerCode" width="120px" show-overflow-tooltip label="客户编码">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ProductionNo" width="120px" show-overflow-tooltip label="排产序号">
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CustomerName" width="180px" show-overflow-tooltip label="客户名称">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="CustomerOrderN-um"
        width="180px"
        show-overflow-tooltip
        label="客户定单号"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ContractNo" width="180px" show-overflow-tooltip label="合同号">
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="DeliveryDate" width="90px" show-overflow-tooltip label="订单交期">
        <template slot-scope="scope">
          <span>{{ formatDate('', '', scope.row.DeliveryDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="CUser" width="110px" show-overflow-tooltip label="制单人">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderDate" width="110px" show-overflow-tooltip label="制单日期">
        <template slot-scope="scope">
          <span>{{ formatDate('', '', scope.row.OrderDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="OrderNum" width="120px" show-overflow-tooltip label="订单编号">
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="LineNum" width="90px" show-overflow-tooltip label="订单行">
        <template slot-scope="scope">
          <span>{{ scope.row.LineNum }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapNo" width="130px" show-overflow-tooltip label="SAP销售单号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapNo }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="SapLine" width="130px" show-overflow-tooltip label="SAP销售行号">
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  getPageList,
  updatePrint,
  contractDetailExport,
  generateProductNo
} from '@/api/CableProduce/WorkOrder';
import {
  getAllList
} from '@/api/CableBasic/CustomerPart';
import {
  parseTime
} from '@/utils';
import {
  exportToExcel
} from '@/utils/excel-export';
import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import templateConvention from './print/printConvention.json';
import templateProductionList from './print/productionList.json';
import templateBranchLineProductionWorkOrder from './print/branchLineProductionWorkOrder.json';
import templateAdditionalLineProductionWorkOrder from './print/AdditionalLineProductionWorkOrder.json';
import templateAccompanyingCableCuttingWorkOrder from './print/accompanyingCableCuttingWorkOrder.json';
import templateYidaGeneralCableProductionWorkOrder from './print/yidaGeneralCableProductionWorkOrder.json';
import templateGeneralCableProductionWorkOrder from './print/generalCableProductionWorkOrder.json';
import templateGeneralOneCableProductionWorkOrder from './print/generalOneCableProductionWorkOrder.json';
import XLSX from 'xlsx';
import { fetchList as dictionaryFetchList } from '@/api/Sys/Sys_Dictionary';

disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

const tempProductionList = new hiprint.PrintTemplate({ template: templateProductionList });
const tempBranchLineProductionWorkOrder = new hiprint.PrintTemplate({ template: templateBranchLineProductionWorkOrder });
const tempAdditionalLineProductionWorkOrder = new hiprint.PrintTemplate({ template: templateAdditionalLineProductionWorkOrder });
const tempAccompanyingCableCuttingWorkOrder = new hiprint.PrintTemplate({ template: templateAccompanyingCableCuttingWorkOrder });
const tempYidaGeneralCableProductionWorkOrder = new hiprint.PrintTemplate({ template: templateYidaGeneralCableProductionWorkOrder });
const tempGeneralCableProductionWorkOrder = new hiprint.PrintTemplate({ template: templateGeneralCableProductionWorkOrder });
const tempGeneralOneCableProductionWorkOrder = new hiprint.PrintTemplate({ template: templateGeneralOneCableProductionWorkOrder });

export default {
  name: 'PP.PP_ProductionSerialNumberAssignment',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      OrderTypeList: [{
        label: '电缆箱',
        value: 1
      }, {
        label: '电器箱',
        value: 2
      }, {
        label: '打包CP件(XO-0814)',
        value: 3
      }, {
        label: '打包CP件(XO-0813)',
        value: 4
      }],
      ScanStatusList: [{
        label: '是',
        value: 1
      }, {
        label: '否',
        value: 0
      }],
      printStatus: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ],
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        ProductionLineDes: '',
        ProductionScheduler: '',
        PageNumber: 1,
        PageSize: 10,
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        OrderNo: '',
        OrderTypeName: null,
        ContractNo: '',
        CustomerName: '',
        SapNo: '',
        WorkOrderPrintStatus: null,
        ElevatorType: null,
        DeliveryDate: [],
        CreateDate: [
          new Date(),
          new Date()
        ]
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      tableHeight: '300px',
      CodeCompare: {
        'K': 'TopHeight',
        'R': 'LiftingHeight',
        'S': 'PitDepth',
        'LES': 'CabinetMovementDistance',
        'HW': 'HoistwayWide',
        'NS': 'StationStopNo',
        'HD': 'HoistwayDepth'
      },
      CodeCompareName: {
        'K': '顶层高度',
        'R': '提升高度',
        'S': '底坑深度',
        'LES': '控制柜位移距离或控制柜侧加长距离',
        'HW': '井道净宽',
        'NS': '停站数',
        'HD': '井道净深'
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.initOrderType();
  },
  methods: {
    getNthValue(str, n) {
      const character = str.charAt(n - 1);
      // 将值转换为整数，如果为空，返回 0
      return parseInt(character) || 0;
    },
    initOrderType() {
      // 订单类型
      dictionaryFetchList({
        typeCode: 'CableOrderType'
      }).then(response => {
        this.OrderTypeList = response.Data;
        this.OrderTypeList = this.OrderTypeList.sort((a, b) => a.EnumKey - b.EnumKey);
        this.fullscreenLoading = false;
      }).catch((c) => {
        this.fullscreenLoading = false;
      });
    },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      getPageList(query).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      const postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    generateProductNo() {
      const ids = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        ids.push(this.multipleSelection[i].Id)
      }
      generateProductNo(ids).then(() => {
        this.$message({
          message: '生成完成',
          type: 'success'
        });
        this.listLoading = false;
        this.getList();
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    // 打印
    handlePrint() {
      const ids = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        ids.push(this.multipleSelection[i].Id)
      }
      updatePrint({
        Ids: ids,
        Identification: 1
      }).then((res) => {
        this.getList();
      })
      let printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        let floors = [];
        let saleParameter = {};
        let details = [];
        if (this.multipleSelection[i].SaleParameter != null) {
          saleParameter = this.multipleSelection[i].SaleParameter;
          floors = this.multipleSelection[i].SaleParameter.OrderFloorList;
        }
        if (this.multipleSelection[i].OrderDetailList != null) {
          details = this.multipleSelection[i].OrderDetailList;
        }
        const order = this.multipleSelection[i];
        const currencyDetails = details.filter(detail => detail.Classify === '通用' || detail.Classify === '通用1');
        if (currencyDetails.length > 0) {
          this.addData(printData, order, saleParameter, floors, currencyDetails)
        }
        const unCurrencyDetails = details.filter(detail => detail.Classify !== '通用' && detail.Classify !== '通用1');
        if (unCurrencyDetails.length > 0) {
          this.addData(printData, order, saleParameter, floors, unCurrencyDetails)
        }
      }
      for (let i = 0;i < printData.length;i++) {
        for (let j = 0;j < printData[i].table.length;j++) {
          if (printData[i].table[j].SingleQuantity === 0) {
            printData[i].table[j].SingleQuantity = ''
          }
        }
      }
      // 根据排产序号排序
      printData = this.sortObjectsByProperty(printData, 'ProductionNo')
      // printData 根据
      const templateDef = new hiprint.PrintTemplate({ template: templateConvention });
      // 打印
      templateDef.print(printData);
    },
    addData(printData, order, saleParameter, floors, details) {
      const orderType = this.OrderTypeList.find(t => t.EnumValue === order.OrderTypeName);
      printData.push({
        // 主表
        ContractNo: order.ContractNo,
        OrderType: orderType == null ? '' : orderType.EnumValue1,
        PackingCode: order.ContractNo + '&' + order.CustomerOrderNum + '&' + (order.CustomerOrderLine === undefined || order.CustomerOrderLine === null ? '' : order.CustomerOrderLine),
        CustomOrderNo: order.CustomerOrderNum,
        projectDes: order.ProjectDisc,
        CustomName: order.CustomerName,
        ProductionNo: order.ProductionNo,
        LineRemark: order.LineRemark,
        Remark: order.Remark,
        BatchNum: order.BatchNum,
        ProductionTime: parseTime(order.ProductionTime, '{y}-{m}-{d}'),
        DeliveryDate: parseTime(order.DeliveryDate, '{y}-{m}-{d}'),
        // 子表
        table: details,
        // 参数
        FloorStationStopOpen: saleParameter.FloorNo + '/' + saleParameter.StationStopNo + '/' + saleParameter.OpenNo,
        LiftingHeight: saleParameter.LiftingHeight,
        Load: saleParameter.Load,
        TopHeight: saleParameter.TopHeight,
        PitDepth: saleParameter.PitDepth,
        MachineRoomCable: saleParameter.MachineRoomCable,
        ServerLayer: saleParameter.ServerLayer,
        AutoReLeveling: (saleParameter.AdvanceDoorOpen == null ? ((saleParameter.AutoReLeveling == null ? '' : saleParameter.AutoReLeveling) + ' ' + (saleParameter.AdvanceOpen == null ? '' : saleParameter.AdvanceOpen)) : saleParameter.AdvanceDoorOpen),
        Trapezium: saleParameter.Trapezium,
        ControlSystem: saleParameter.ControlSystem,
        ControlMethod: saleParameter.ControlMethod,
        MachineType: saleParameter.MachineType,
        PassageType: saleParameter.PassageType,
        DoubleThroughLayer: saleParameter.DoubleThroughLayer,
        NonStopLayer: saleParameter.NonStopLayer,
        BaseStation: saleParameter.BaseStation,
        EstimatedWeight: saleParameter.EstimatedWeight,
        FrontDoorOpenFloor: saleParameter.FrontDoorOpenFloor,
        Print: saleParameter.Print,
        PackageBoxStatus: saleParameter.PackageBoxStatus,
        Speed: saleParameter.Speed,
        IsSightseeingLift: saleParameter.IsSightseeingLift,
        RearDoorOpenFloor: saleParameter.RearDoorOpenFloor,
        HoistwayWide: saleParameter.HoistwayWide,
        HoistwayDepth: saleParameter.HoistwayDepth,
        SystemName: saleParameter.SystemName,
        WoodenBoxSize: saleParameter.WoodenBoxSize,
        WoodenTraySize: saleParameter.WoodenTraySize,
        BatchNo: saleParameter.BatchNo,
        SequenceNo: saleParameter.SequenceNo,
        EntryName: saleParameter.EntryName,
        OrderNum: saleParameter.OrderNum,
        PowerOutEmergencyEvacuationDevice: saleParameter.PowerOutEmergencyEvacuationDevice,
        ControlCabinetDisplacementExtension: saleParameter.ControlCabinetDisplacementExtension,
        ExternalCustomers: saleParameter.ExternalCustomers,
        CabinetMovementDistance: saleParameter.CabinetMovementDistance,
        OutboundCallBoardInterface: saleParameter.OutboundCallBoardInterface,
        ElevatorType: order.ElevatorType,
        // 楼层
        floors: floors
      })
    },
    // 排产清单打印
    printProductionList() {
      const templates = [];
      let printData = [];
      let sn = 1
      for (let i = 0;i < this.multipleSelection.length;i++) {
        let saleParameter = {};
        if (this.multipleSelection[i].SaleParameter != null) {
          saleParameter = this.multipleSelection[i].SaleParameter;
        }
        let WoodenBoxSize = '';
        let WoodenTraySize = '';
        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          if (this.multipleSelection[i].OrderDetailList[j].Classify === '包装箱1') {
            WoodenBoxSize = this.multipleSelection[i].OrderDetailList[j].SpecificationModel;
          }
          if (this.multipleSelection[i].OrderDetailList[j].Classify === '包装箱2') {
            WoodenTraySize = this.multipleSelection[i].OrderDetailList[j].SpecificationModel;
          }
        }
        printData.push({
          // 主表
          SerialNo: sn,
          DeliveryDate: this.multipleSelection[i].DeliveryDate.split('T')[0],
          ContractNo: this.multipleSelection[i].ContractNo,
          ProductionNo: this.multipleSelection[i].ProductionNo,
          FloorStationStopOpen: saleParameter.FloorNo + '/' + saleParameter.StationStopNo + '/' + saleParameter.OpenNo,
          MachineType: saleParameter.MachineType,
          ProjectDisc: saleParameter.ProjectDisc,
          WoodenBoxSize: WoodenBoxSize,
          WoodenTraySize: WoodenTraySize,
          Trapezium: saleParameter.Trapezium,
          Print: (saleParameter.PackageBoxStatus !== null ? saleParameter.PackageBoxStatus : '') + (saleParameter.Print !== null ? (saleParameter.PackageBoxStatus != null ? '&' : '') + saleParameter.Print : ''),
          BatchNum: this.multipleSelection[i].BatchNum
        })
        sn++
      }
      // 根据排产序号排序
      printData = this.sortObjectsByProperty(printData, 'ProductionNo')
      templates.push({ template: tempProductionList, data: { ProductionList: printData, PrintDate: parseTime(new Date(), null) }});
      // 打印
      hiprint.print({ templates: templates, paper: { orientation: 'landscape' }});
    },
    // 分支线排产工单
    printBranchLineProductionWorkOrder() {
      const templates = [];
      let printData = [];
      let sn = 1
      console.log('this.multipleSelection[i].OrderDetailList', this.multipleSelection)
      getAllList().then(response => {
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const details = this.multipleSelection[i].OrderDetailList;
          for (let j = 0;j < details.length;j++) {
            details[j].DeliveryDate = this.multipleSelection[i].DeliveryDate
            if (details[j].Classify === '分支' || details[j].Classify === '分支2') {
              const part = response.Data.find(item => {
                return item.PartNo === details[j].CustomerPartNo
              })
              details[j].MainPart = ''
              console.log('part', part)
              // 主件号不为空 检索当前订单所有明细
              if (part.MainPart !== null && part.MainPart !== undefined && part.MainPart !== '') {
                const mainParts = part.MainPart.split(',')
                console.log('mainParts', mainParts)
                for (let k = 0;k < details.length;k++) {
                  if (mainParts.includes(details[k].CustomerPartNo)) {
                    details[j].MainPart = details[k].CustomerPartNo
                    console.log('details[j]', details[j])
                    console.log('details[k]', details[k])
                  }
                }
              }
              printData.push(details[j])
            }
          }
        }
        const summary = printData.reduce((acc, item) => {
          const key = `${item.CustomerPartNo}_${item.SingleQuantity}_${item.DeliveryDate}_${item.MainPart}`;
          if (!acc[key]) {
            acc[key] = {
              CustomerPartNo: item.CustomerPartNo,
              MaterialDes: item.MaterialDes,
              SpecificationModel: item.SpecificationModel,
              SingleQuantity: item.SingleQuantity,
              MainPart: item.MainPart,
              DeliveryDate: item.DeliveryDate.split('T')[0],
              Quantity: 0
            };
          }
          acc[key].Quantity += item.Quantity;
          return acc;
        }, {});
        printData = Object.keys(summary).map(key => {
          summary[key].SerialNo = sn++
          return summary[key];
        });
        // 使用多字段排序，一次性完成
        printData = this.sortObjectsByProperty(printData, ['DeliveryDate', 'CustomerPartNo', 'SingleQuantity']);
        templates.push({ template: tempBranchLineProductionWorkOrder, data: { ProductionList: printData }});
        // 打印
        hiprint.print({ templates: templates });
      });
    },
    // 附加线排产工单
    printAdditionalLineProductionWorkOrder() {
      const templates = [];
      let printData = [];
      console.log('this.multipleSelection[i].OrderDetailList', this.multipleSelection)

      // 检查所有选中订单的客户编码是否都是Z001410002
      const allCustomerCodeIsZ001410002 = this.multipleSelection.every(item => item.CustomerCode === 'Z001410002');

      for (let i = 0;i < this.multipleSelection.length;i++) {
        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          this.multipleSelection[i].OrderDetailList[j].DeliveryDate = this.multipleSelection[i].DeliveryDate
          if (this.multipleSelection[i].OrderDetailList[j].Classify === '其他' || this.multipleSelection[i].OrderDetailList[j].Classify === '附加') {
            this.multipleSelection[i].OrderDetailList[j].ContractNo = this.multipleSelection[i].ContractNo;
            this.multipleSelection[i].OrderDetailList[j].DeliveryDate = this.multipleSelection[i].OrderDetailList[j].DeliveryDate.split('T')[0];
            this.multipleSelection[i].OrderDetailList[j].ProductionNo = this.multipleSelection[i].ProductionNo
            printData.push(this.multipleSelection[i].OrderDetailList[j])
          }
        }
      }
      console.log('printData', printData)

      // 根据客户编码判断使用不同的排序规则
      if (allCustomerCodeIsZ001410002) {
        // 如果都是Z001410002，使用现有的多字段排序
        printData = this.sortObjectsByProperty(printData, ['SpecificationModel', 'ProductionNo']);
      } else {
        // 如果不是，只按ProductionNo排序
        printData = this.sortObjectsByProperty(printData, 'ProductionNo');
      }

      templates.push({ template: tempAdditionalLineProductionWorkOrder, data: { ProductionList: printData }});
      // 打印
      hiprint.print({ templates: templates });
    },
    // 随行电缆裁线工单
    printAccompanyingCableCuttingWorkOrder() {
      const templates = [];
      let printData = [];

      // 检查所有选中订单的客户编码是否都是Z001410002
      const allCustomerCodeIsZ001410002 = this.multipleSelection.every(item => item.CustomerCode === 'Z001410002');

      for (let i = 0;i < this.multipleSelection.length;i++) {
        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          const detail = this.multipleSelection[i].OrderDetailList[j]
          if (detail.Classify === '随行1') {
            printData.push({
              DeliveryDate: this.multipleSelection[i].DeliveryDate.split('T')[0],
              ProductionNo: this.multipleSelection[i].ProductionNo,
              ContractNo: this.multipleSelection[i].ContractNo,
              MaterialCode: detail.MaterialCode,
              CustomerPartNo: detail.CustomerPartNo,
              SpecificationModel: detail.SpecificationModel,
              Quantity: detail.Quantity
            })
          }
        }
      }
      getAllList().then(response => {
        const partList = response.Data;
        console.log('partList', partList)
        for (let i = 0;i < printData.length;i++) {
          const part = partList.find(item => {
            return item.PartNo === printData[i].CustomerPartNo
          })
          console.log('part', part)
          // CustomerPartNo 匹配PartNo 的序号进行排序
          if (part != null) {
            printData[i].SerialNo = part.Sort
          } else {
            printData[i].SerialNo = 0
          }
        }
        console.log('printData', printData);

        // 根据客户编码判断使用不同的排序规则
        if (allCustomerCodeIsZ001410002) {
          // 如果都是Z001410002，使用现有的规则排序
          printData = this.sortObjectsByProperty(printData, ['SpecificationModel', 'ProductionNo']);
        } else {
          // 如果不是，只按ProductionNo排序
          printData = this.sortObjectsByProperty(printData, 'ProductionNo');
        }

        templates.push({ template: tempAccompanyingCableCuttingWorkOrder, data: { ProductionList: printData }});
        // 打印
        hiprint.print({ templates: templates });
      })
    },
    // 怡达通用电缆排产工单
    yidaGeneralCableProductionWorkOrder() {
      const templates = [];
      let printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        const deliveryDate = this.multipleSelection[i].DeliveryDate.split('T')[0];
        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          const detail = this.multipleSelection[i].OrderDetailList[j];
          if (detail.Classify === '通用') {
            console.log('detail.DeliveryDate', detail.DeliveryDate)
            // 在历史记录中查找是否存在相同记录
            const existingRecord = printData.find(item =>
              item.CustomerPartNo === detail.CustomerPartNo &&
              item.SingleQuantity === detail.Quantity &&
              item.DeliveryDate === deliveryDate
            );

            if (existingRecord) {
              // 存在则数量 +1
              existingRecord.Quantity += 1;
            } else {
              // 不存在则添加新记录
              printData.push({
                DeliveryDate: deliveryDate,
                CustomerPartNo: detail.CustomerPartNo,
                MaterialDes: detail.MaterialDes,
                SpecificationModel: detail.SpecificationModel,
                SingleQuantity: detail.Quantity,
                Quantity: 1
              });
            }
          }
        }
      }
      getAllList().then(response => {
        const partList = response.Data;
        console.log('partList', partList)
        for (let i = 0;i < printData.length;i++) {
          const part = partList.find(item => {
            return item.PartNo === printData[i].CustomerPartNo
          })
          console.log('part', part)
          // CustomerPartNo 匹配PartNo 的序号进行排序
          if (part != null) {
            printData[i].SerialNo = part.Sort
          } else {
            printData[i].SerialNo = 0
          }
        }
        printData = this.sortObjectsByProperty(printData, 'SerialNo')

        templates.push({ template: tempYidaGeneralCableProductionWorkOrder, data: { ProductionList: printData }});
        // 打印
        hiprint.print({ templates: templates });
      })
    },

    // 通用电缆排产工单
    generalCableProductionWorkOrder() {
      const templates = [];
      let printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        const deliveryDate = this.multipleSelection[i].DeliveryDate.split('T')[0];
        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          const detail = this.multipleSelection[i].OrderDetailList[j];
          if (detail.Classify === '通用') {
            console.log('detail.DeliveryDate', detail.DeliveryDate)
            // 在历史记录中查找是否存在相同记录
            const existingRecord = printData.find(item =>
              item.CustomerPartNo === detail.CustomerPartNo &&
              item.SingleQuantity === detail.SingleQuantity &&
              item.DeliveryDate === deliveryDate
            );

            if (existingRecord) {
              // 存在则数量 +1
              existingRecord.Quantity += detail.Quantity;
            } else {
              // 不存在则添加新记录
              printData.push({
                DeliveryDate: deliveryDate,
                CustomerPartNo: detail.CustomerPartNo,
                MaterialDes: detail.MaterialDes,
                SpecificationModel: detail.SpecificationModel,
                SingleQuantity: detail.SingleQuantity,
                Quantity: detail.Quantity
              });
            }
          }
        }
      }
      getAllList().then(response => {
        const partList = response.Data;
        console.log('partList', partList)
        for (let i = 0;i < printData.length;i++) {
          const part = partList.find(item => {
            return item.PartNo === printData[i].CustomerPartNo
          })
          console.log('part', part)
          // CustomerPartNo 匹配PartNo 的序号进行排序
          if (part != null) {
            printData[i].SerialNo = part.Sort
          } else {
            printData[i].SerialNo = 0
          }
        }
        printData = this.sortObjectsByProperty(printData, 'SerialNo')

        templates.push({ template: tempGeneralCableProductionWorkOrder, data: { ProductionList: printData }});
        // 打印
        hiprint.print({ templates: templates });
      })
    },

    // 通用1电缆排产工单
    generalOneCableProductionWorkOrder() {
      const templates = [];
      let printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        const deliveryDate = this.multipleSelection[i].DeliveryDate.split('T')[0];
        const contractNo = this.multipleSelection[i].ContractNo;

        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          const detail = this.multipleSelection[i].OrderDetailList[j];
          if (detail.Classify === '通用1') {
            // 在历史记录中查找是否存在相同记录
            const existingRecord = printData.find(item =>
              item.ContractNo === contractNo &&
              item.CustomerPartNo === detail.CustomerPartNo &&
              item.SingleQuantity === detail.SingleQuantity &&
              item.DeliveryDate === deliveryDate
            );

            if (existingRecord) {
              // 存在则数量 +1
              existingRecord.Quantity += detail.Quantity;
            } else {
              // 不存在则添加新记录
              printData.push({
                ContractNo: contractNo,
                DeliveryDate: deliveryDate,
                CustomerPartNo: detail.CustomerPartNo,
                MaterialDes: detail.MaterialDes,
                SpecificationModel: detail.SpecificationModel,
                SingleQuantity: detail.SingleQuantity,
                Quantity: detail.Quantity
              });
            }
          }
        }
      }
      printData = this.sortObjectsByProperty(printData, 'SerialNo')

      templates.push({ template: tempGeneralOneCableProductionWorkOrder, data: { ProductionList: printData }});
      // 打印
      hiprint.print({ templates: templates });
      // getAllList().then(response => {
      // const partList = response.Data;
      // console.log('partList', partList)
      // for (let i = 0;i < printData.length;i++) {
      //   const part = partList.find(item => {
      //     return item.PartNo === printData[i].CustomerPartNo
      //   })
      //   console.log('part', part)
      //   // CustomerPartNo 匹配PartNo 的序号进行排序
      //   if (part != null) {
      //     printData[i].SerialNo = part.Sort
      //   } else {
      //     printData[i].SerialNo = 0
      //   }
      // }
      // })
    },

    sortObjectsByProperty(arr, properties) {
      // 兼容单属性参数情况
      const propArray = Array.isArray(properties) ? properties : [properties];

      // 排序数组，根据指定的多个属性
      return arr.sort((a, b) => {
        // 依次比较每个属性
        for (let i = 0;i < propArray.length;i++) {
          const property = propArray[i];
          let aValue, bValue;

          // 尝试将两个值转换为浮点数，并处理异常
          try {
            aValue = parseFloat(a[property]);
          } catch (e) {
            aValue = NaN; // 转换失败时设置为 NaN
          }

          try {
            bValue = parseFloat(b[property]);
          } catch (e) {
            bValue = NaN; // 转换失败时设置为 NaN
          }

          // 处理NaN情况
          const aIsNaN = isNaN(aValue);
          const bIsNaN = isNaN(bValue);

          if (aIsNaN && bIsNaN) {
            // 两个都是NaN，比较原始字符串值
            if (a[property] !== b[property]) {
              return a[property] < b[property] ? -1 : 1;
            }
          } else if (aIsNaN) {
            return 1; // a是NaN，排到后面
          } else if (bIsNaN) {
            return -1; // b是NaN，排到后面
          } else if (aValue !== bValue) {
            // 如果不相等，直接返回比较结果
            return aValue - bValue;
          }

          // 如果当前属性相等，继续比较下一个属性
        }

        // 所有属性都相等
        return 0;
      });
    },
    /**
     * 导出生产配方 - 将选中订单的生产配方导出为Excel文件
     * @param {boolean} floorTurn - 是否需要反转楼层顺序
     */
    exportProductionFormula(floorTurn) {
      // 创建一个新的Excel工作簿
      const wb = XLSX.utils.book_new();

      // 定义Excel表头字段
      // 包括项目信息、电缆型号、开关状态、总长度和各层数据
      const fields = [
        // 基本信息字段
        '项目编号/合同号', 'A组电缆型号', 'B组电缆型号', 'C组电缆型号', 'D组电缆型号',
        '生产模式/客户', 'A组开/关', 'B组开/关', 'C组开/关', 'D组开/关',
        'A组总长', 'B组总长', 'C组总长', 'D组总长', '中间开线长度mm',

        // A组楼层数据 (1-60层)
        'A第1层', 'A第2层', 'A第3层', 'A第4层', 'A第5层', 'A第6层', 'A第7层', 'A第8层', 'A第9层', 'A第10层',
        'A第11层', 'A第12层', 'A第13层', 'A第14层', 'A第15层', 'A第16层', 'A第17层', 'A第18层', 'A第19层', 'A第20层',
        'A第21层', 'A第22层', 'A第23层', 'A第24层', 'A第25层', 'A第26层', 'A第27层', 'A第28层', 'A第29层', 'A第30层',
        'A第31层', 'A第32层', 'A第33层', 'A第34层', 'A第35层', 'A第36层', 'A第37层', 'A第38层', 'A第39层', 'A第40层',
        'A第41层', 'A第42层', 'A第43层', 'A第44层', 'A第45层', 'A第46层', 'A第47层', 'A第48层', 'A第49层', 'A第50层',
        'A第51层', 'A第52层', 'A第53层', 'A第54层', 'A第55层', 'A第56层', 'A第57层', 'A第58层', 'A第59层', 'A第60层',

        // B组楼层数据 (1-60层)
        'B第1层', 'B第2层', 'B第3层', 'B第4层', 'B第5层', 'B第6层', 'B第7层', 'B第8层', 'B第9层', 'B第10层',
        'B第11层', 'B第12层', 'B第13层', 'B第14层', 'B第15层', 'B第16层', 'B第17层', 'B第18层', 'B第19层', 'B第20层',
        'B第21层', 'B第22层', 'B第23层', 'B第24层', 'B第25层', 'B第26层', 'B第27层', 'B第28层', 'B第29层', 'B第30层',
        'B第31层', 'B第32层', 'B第33层', 'B第34层', 'B第35层', 'B第36层', 'B第37层', 'B第38层', 'B第39层', 'B第40层',
        'B第41层', 'B第42层', 'B第43层', 'B第44层', 'B第45层', 'B第46层', 'B第47层', 'B第48层', 'B第49层', 'B第50层',
        'B第51层', 'B第52层', 'B第53层', 'B第54层', 'B第55层', 'B第56层', 'B第57层', 'B第58层', 'B第59层', 'B第60层',

        // C组楼层数据 (1-60层)
        'C第1层', 'C第2层', 'C第3层', 'C第4层', 'C第5层', 'C第6层', 'C第7层', 'C第8层', 'C第9层', 'C第10层',
        'C第11层', 'C第12层', 'C第13层', 'C第14层', 'C第15层', 'C第16层', 'C第17层', 'C第18层', 'C第19层', 'C第20层',
        'C第21层', 'C第22层', 'C第23层', 'C第24层', 'C第25层', 'C第26层', 'C第27层', 'C第28层', 'C第29层', 'C第30层',
        'C第31层', 'C第32层', 'C第33层', 'C第34层', 'C第35层', 'C第36层', 'C第37层', 'C第38层', 'C第39层', 'C第40层',
        'C第41层', 'C第42层', 'C第43层', 'C第44层', 'C第45层', 'C第46层', 'C第47层', 'C第48层', 'C第49层', 'C第50层',
        'C第51层', 'C第52层', 'C第53层', 'C第54层', 'C第55层', 'C第56层', 'C第57层', 'C第58层', 'C第59层', 'C第60层',

        // D组楼层数据 (1-60层)
        'D第1层', 'D第2层', 'D第3层', 'D第4层', 'D第5层', 'D第6层', 'D第7层', 'D第8层', 'D第9层', 'D第10层',
        'D第11层', 'D第12层', 'D第13层', 'D第14层', 'D第15层', 'D第16层', 'D第17层', 'D第18层', 'D第19层', 'D第20层',
        'D第21层', 'D第22层', 'D第23层', 'D第24层', 'D第25层', 'D第26层', 'D第27层', 'D第28层', 'D第29层', 'D第30层',
        'D第31层', 'D第32层', 'D第33层', 'D第34层', 'D第35层', 'D第36层', 'D第37层', 'D第38层', 'D第39层', 'D第40层',
        'D第41层', 'D第42层', 'D第43层', 'D第44层', 'D第45层', 'D第46层', 'D第47层', 'D第48层', 'D第49层', 'D第50层',
        'D第51层', 'D第52层', 'D第53层', 'D第54层', 'D第55层', 'D第56层', 'D第57层', 'D第58层', 'D第59层', 'D第60层'
      ];

      // 初始化工作表数据，每个字段作为一行的第一列
      let ws_data = [];
      for (let i = 0;i < fields.length;i++) {
        ws_data.push([fields[i]]);
      }

      // 收集所有选中订单的零件编号
      const partNos = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        for (let j = 0;j < this.multipleSelection[i].OrderDetailList.length;j++) {
          partNos.push(this.multipleSelection[i].OrderDetailList[j].CustomerPartNo)
        }
      }

      // 校验是否有楼层为空的
      for (let i = 0;i < this.multipleSelection.length;i++) {
        if (this.multipleSelection[i].SaleParameter.OrderFloorList.length === 0) {
          this.$message({
            message: '合同号：' + this.multipleSelection[i].ContractNo + ' 楼层为空',
            type: 'error'
          });
          return;
        }
      }

      // 获取所有客户零件信息
      getAllList().then(response => {
        const partList = response.Data;

        // 遍历所有选中的订单
        for (let i = 0;i < this.multipleSelection.length;i++) {
          const order = this.multipleSelection[i];
          const details = this.multipleSelection[i].OrderDetailList;

          // 添加合同号和客户名称到工作表
          ws_data[0].push(order.ContractNo);
          ws_data[5].push(order.CustomerName);

          // 遍历订单的所有明细行
          for (let j = 0;j < details.length;j++) {
            // 查找对应的客户零件信息
            const part = partList.find(item => {
              return item.PartNo === details[j].CustomerPartNo
            });

            if (part !== undefined) {
              // 遍历零件的所有参数列表
              for (let k = 0;k < part.CustomerPartParamsList.length;k++) {
                // 检查机房属性是否匹配
                if (part.CustomerPartParamsList[k].ComputerRoomProp != null &&
                  (this.multipleSelection[i].SaleParameter.MachineType === '无机房' ? '无机房' : '有机房') !==
                  part.CustomerPartParamsList[k].ComputerRoomProp) {
                  continue; // 不匹配则跳过当前参数
                }

                // 检查观光电梯属性是否匹配
                if ((this.multipleSelection[i].SaleParameter.IsSightseeingLift === '是' ? '是' : '') !==
                  (part.CustomerPartParamsList[k].SightseeingLift === null ? '' : part.CustomerPartParamsList[k].SightseeingLift)) {
                  continue; // 不匹配则跳过当前参数
                }

                // 检查载重条件是否满足
                if (!this.checkCondition(part.CustomerPartParamsList[k].Load, this.multipleSelection[i].SaleParameter.Load)) {
                  continue; // 不满足则跳过当前参数
                }

                // 检查控制系统是否匹配
                if (part.CustomerPartParamsList[k].ControlSystem !== null &&
                  part.CustomerPartParamsList[k].ControlSystem !== undefined &&
                  part.CustomerPartParamsList[k].ControlSystem !== '' &&
                  this.multipleSelection[i].SaleParameter.ControlSystem !== part.CustomerPartParamsList[k].ControlSystem) {
                  continue; // 不匹配则跳过当前参数
                }

                // 检查电梯类型是否匹配
                if (part.CustomerPartParamsList[k].ElevatorType !== null &&
                  part.CustomerPartParamsList[k].ElevatorType !== undefined &&
                  part.CustomerPartParamsList[k].ElevatorType !== '' &&
                  this.multipleSelection[i].ElevatorType !== null &&
                  this.multipleSelection[i].ElevatorType !== undefined &&
                  this.multipleSelection[i].ElevatorType !== '' &&
                  this.multipleSelection[i].ElevatorType !== part.CustomerPartParamsList[k].ElevatorType) {
                  continue; // 不匹配则跳过当前参数
                }
                // 获取当前零件的参数
                const customerPartParams = part.CustomerPartParamsList[k];

                // 根据组别添加电缆型号到对应列
                const quadIndex = this.getIndexByValue(customerPartParams.Quad);
                ws_data[quadIndex].push(customerPartParams.CableType);

                // 设置开关状态为"开"
                ws_data[quadIndex + 5].push('开');

                // 初始化总长度和中间开线长度字段
                ws_data[10].push(''); // A组总长
                ws_data[11].push(''); // B组总长
                ws_data[12].push(''); // C组总长
                ws_data[13].push(''); // D组总长
                ws_data[14].push(''); // 中间开线长度

                // 获取当前组别的楼层起始索引
                const floorIndex = this.getFloorIndexByValue(customerPartParams.Quad);
                let additionalIndex = 0; // 额外索引偏移量
                let continueNoneStopTop = 0; // 标记顶层不停层状态

                // 处理底坑侧数据
                let pitSide = 0;
                if (customerPartParams.PitSide === '' || customerPartParams.PitSide === null || customerPartParams.PitSide === undefined) {
                  // 如果底坑侧数据为空，则设置为0
                  pitSide = 0;
                } else {
                  // 使用新的公式计算方法，支持加减乘除运算并遵循运算符优先级
                  // 结果保留三位小数
                  pitSide = this.evaluateFormula(customerPartParams.PitSide, order, customerPartParams);
                  console.log('pitSide', pitSide, customerPartParams)
                }

                // 将计算出的底坑侧长度添加到工作表中
                if (pitSide !== 0) {
                  ws_data[floorIndex].push(pitSide);
                  additionalIndex = additionalIndex + 1; // 只有当底坑侧有数据时才增加偏移量
                }
                // 处理机房侧数据
                let machineRoomSide = 0;
                if (customerPartParams.MachineRoomSide === '' || customerPartParams.MachineRoomSide === null || customerPartParams.MachineRoomSide === undefined) {
                  // 如果机房侧数据为空，则设置为0
                  machineRoomSide = 0;
                } else {
                  // 使用新的公式计算方法，支持加减乘除运算并遵循运算符优先级
                  // 结果保留三位小数
                  machineRoomSide = this.evaluateFormula(customerPartParams.MachineRoomSide, order, customerPartParams);
                }
                // 如果底坑侧为0，则不增加偏移量，使机房侧数据覆盖底坑侧位置
                if (pitSide === 0) {
                  additionalIndex = 0;

                  // 如果底坑侧为0，机房侧也为0，并且没有中间段，则跳过当前循环
                  if (machineRoomSide === 0 &&
                    (part.CustomerPartParamsList[k].IntermediateSection === '' ||
                      part.CustomerPartParamsList[k].IntermediateSection === null ||
                      part.CustomerPartParamsList[k].IntermediateSection === undefined)) {
                    continue;
                  }
                }
                // 处理中间段数据
                let floorLength = 0;

                // 如果中间段数据为空，不进行处理
                if (part.CustomerPartParamsList[k].IntermediateSection === '' ||
                  part.CustomerPartParamsList[k].IntermediateSection === null ||
                  part.CustomerPartParamsList[k].IntermediateSection === undefined) {
                  // 中间段为空，不做处理
                } else if (part.CustomerPartParamsList[k].IntermediateSection.includes('楼层')) {
                  // 如果中间段包含楼层关键字，按楼层高度处理
                  // 复制楼层列表，避免修改原始数据
                  const floors = JSON.parse(JSON.stringify(order.SaleParameter.OrderFloorList));

                  // 处理不停层情况
                  if (order.SaleParameter.NonStopLayer != null && order.SaleParameter.NonStopLayer !== '') {
                    const nonStopLayers = order.SaleParameter.NonStopLayer.split(',');

                    // 遍历所有不停层
                    for (let l = 0;l < nonStopLayers.length;l++) {
                      // 在楼层列表中查找不停层
                      const index = floors.findIndex(item => {
                        return item.FloorNum === nonStopLayers[l]
                      });

                      if (index !== -1) {
                        // 检查不停层高度是否有效
                        if (floors[index] === null || floors[index] === undefined || floors[index] === '') {
                          this.showNotify('error', '合同号：' + order.ContractNo + ',不停层：' + nonStopLayers[l] + ' 高度为空');
                          return;
                        }

                        // 如果前一层不存在，则将高度加到后一层
                        if (floors[index - 1] == null) {
                          floors[index + 1].Height = parseFloat(floors[index + 1].Height) + parseFloat(floors[index].Height);
                        } else {
                          // 否则将高度加到前一层
                          floors[index - 1].Height += floors[index].Height;
                        }

                        // 从楼层列表中移除不停层
                        floors.splice(index, 1);
                      } else {
                        // 标记顶层不停层状态
                        continueNoneStopTop = 1;
                      }
                    }
                  }

                  // 计算楼层总数
                  floorLength = floors.length;

                  // 处理中间段计算公式，如“楼层+5”
                  const computeList = part.CustomerPartParamsList[k].IntermediateSection.split('+');
                  let num = 0;

                  // 遍历计算公式中的每一部分
                  for (let o = 0;o < computeList.length;o++) {
                    if (computeList[o] === '楼层') {
                      // 如果是楼层关键字，则添加所有楼层高度
                      for (let l = 0;l < floorLength;l++) {
                        const floor = floors[l];

                        // 检查楼层高度是否有效
                        if (floor.Height === null || floor.Height === undefined || floor.Height === '') {
                          this.showNotify('error', '合同号：' + order.ContractNo + ',楼层：' + floor.FloorNum + ' 高度为空');
                          return;
                        }

                        // 将楼层高度添加到工作表中
                        ws_data[floorIndex + additionalIndex + num].push(floor.Height);
                        num++;
                      }
                    } else {
                      // 如果是其他值，直接添加
                      ws_data[floorIndex + additionalIndex + num].push(computeList[o]);
                      num++;
                    }
                  }

                  // 更新楼层总数
                  floorLength = num;
                } else {
                  // 如果中间段是数字，按固定长度分段处理
                  // 计算总长度，使用新的公式计算方法
                  // 结果保留三位小数
                  const quantity = this.evaluateFormula('R+K+S-1', order, customerPartParams);

                  // 将总长度按中间段长度分段
                  const intermediateSection = parseFloat(part.CustomerPartParamsList[k].IntermediateSection);
                  const len = parseInt(quantity / intermediateSection); // 完整段数
                  const remainder = quantity % intermediateSection; // 余数
                  floorLength = len;

                  // 添加完整段长度
                  for (let l = 0;l < len;l++) {
                    if (l === (len - 1) && remainder <= 0.5) {
                      // 如果是最后一段且余数很小，则将余数加到最后一段
                      ws_data[floorIndex + additionalIndex + l].push(intermediateSection + remainder);
                    } else {
                      // 否则添加标准长度
                      ws_data[floorIndex + additionalIndex + l].push(intermediateSection);
                    }
                  }

                  // 如果余数较大，则单独添加一段
                  if (remainder > 0.5) {
                    // 当中间段最后一个值小于2的时候，直接加到前面的值上面
                    if (remainder < 2 && len > 0 && order.CustomerCode === 'Z001200101') {
                      // 修改前一个值，将余数加到前一个值上
                      const lastIndex = ws_data[floorIndex + additionalIndex + len - 1].length - 1;
                      ws_data[floorIndex + additionalIndex + len - 1][lastIndex] += remainder;
                    } else {
                      // 否则创建新的段
                      ws_data[floorIndex + additionalIndex + len].push(remainder);
                      floorLength++; // 增加段数
                    }
                  }
                }
                // 添加机房侧长度到工作表
                if (machineRoomSide !== 0) {
                  if (continueNoneStopTop === 1) {
                    // 如果有顶层不停层，将机房侧长度加到最后一层
                    const arr = ws_data[floorIndex + floorLength + additionalIndex - 1];
                    arr[arr.length - 1] = arr[arr.length - 1] + machineRoomSide;
                  } else {
                    // 否则单独添加机房侧长度
                    ws_data[floorIndex + floorLength + additionalIndex].push(machineRoomSide);
                  }
                }

                // 填充剩余空行，确保所有列数据完整
                const exLen = 60 - floorLength - additionalIndex - 1; // 计算剩余行数
                for (let l = 2;l <= exLen;l++) {
                  ws_data[floorIndex + additionalIndex + floorLength + l].push('');
                }
              }
            }
          }

          // 处理缺失的电缆型号和开关状态
          for (let k = 1;k < 5;k++) {
            // 如果电缆型号缺失，填充为"无"
            if (ws_data[k].length === i + 1) {
              ws_data[k].push('无');
            }
            // 如果开关状态缺失，填充为"关"
            if (ws_data[k + 5].length === i + 1) {
              ws_data[k + 5].push('关');
            }
          }

          // 填充其他缺失的数据单元格
          for (let j = 0;j < 241;j++) {
            if (ws_data[j + 13].length === i + 1) {
              ws_data[j + 13].push('');
            }
          }

          // 如果需要反转楼层顺序
          if (floorTurn) {
            // 对每个组别(A/B/C/D)处理
            const floorHandle = ['A', 'B', 'C', 'D'];
            for (let j = 0;j < floorHandle.length;j++) {
              // 获取当前组别的楼层起始索引
              const floorIndex = this.getFloorIndexByValue(floorHandle[j]);

              // 查找第一个空行，作为反转的终点
              for (let k = 0;k < 60;k++) {
                if (ws_data[floorIndex + k][ws_data[0].length - 1] === '') {
                  // 反转当前列的楼层数据
                  ws_data = this.reverseColumnSegment(ws_data, ws_data[0].length - 1, floorIndex - 1, floorIndex + k);
                  break;
                }
              }
            }
          }
        }

        // 将工作表数据导出为Excel文件
        this.downExcel(wb, ws_data);
      });
    },
    /**
     * 检查比较值是否满足指定条件
     * @param {string} condition - 比较条件，如 "<1000", ">500", "=750" 等
     * @param {number} compareValue - 要比较的值
     * @returns {boolean} 是否满足条件
     */
    checkCondition(condition, compareValue) {
      // 如果条件为空，则返回真（不限制）
      if (condition === null || condition === undefined || condition === '') {
        return true;
      }

      // 提取条件中的运算符和数字
      const operator = condition.match(/[<>＞≤≥=!]/)[0]; // 匹配比较运算符（包括中文全角符号）
      const number = parseInt(condition.match(/\d+/)[0], 10); // 匹配数字部分

      // 根据不同的运算符进行比较
      const operatorMap = {
        '<': (a, b) => a < b, // 小于
        '＞': (a, b) => a > b, // 全角大于
        '>': (a, b) => a > b, // 大于
        '≤': (a, b) => a <= b, // 小于等于
        '≥': (a, b) => a >= b, // 大于等于
        '=': (a, b) => a === b, // 等于
        '!': (a, b) => a !== b // 不等于
      };

      // 如果运算符存在于映射中，执行相应的比较操作
      if (operatorMap[operator]) {
        return operatorMap[operator](compareValue, number);
      }

      // 如果运算符不支持，返回假
      return false;
    },
    contractDetailExport() {
      const loading = this.$loading({
        lock: true,
        text: '正在进行长耗时操作，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      contractDetailExport(query).then((res) => {
        exportToExcel(res.data, res.fileName)
        loading.close();
      }).catch((error) => {
        console.log(error);
        loading.close();
      });
    },
    /**
     * 反转数组中指定列的一段元素顺序
     * @param {Array} arr - 要操作的二维数组
     * @param {number} colIndex - 要反转的列索引
     * @param {number} startRow - 要反转的起始行索引
     * @param {number} endRow - 要反转的结束行索引
     * @returns {Array} 反转后的数组
     */
    reverseColumnSegment(arr, colIndex, startRow, endRow) {
      // 1. 提取需要反转的段落
      const segment = [];
      for (let i = startRow;i <= endRow;i++) {
        segment.push(arr[i][colIndex]);
      }

      // 2. 反转段落元素顺序
      segment.reverse();

      // 3. 将反转后的元素放回原数组
      for (let i = startRow;i <= endRow;i++) {
        arr[i][colIndex] = segment[i - startRow];
      }

      return arr;
    },
    /**
     * 检查值是否为数字或可转换为数字
     * @param {*} value - 要检查的值
     * @returns {boolean} 是否为数字或可转换为数字
     */
    isNumeric(value) {
      // 检查值是否为数字或可转换为数字
      // 1. !isNaN(value) 检查值本身是否为数字
      // 2. !isNaN(parseFloat(value)) 检查值是否可转换为数字
      return !isNaN(value) && !isNaN(parseFloat(value));
    },

    /**
     * 将数字四舍五入到指定的小数位数
     * @param {number} num - 要四舍五入的数字
     * @param {number} decimalPlaces - 小数位数，默认为3
     * @returns {number} 四舍五入后的数字
     */
    roundNumber(num, decimalPlaces = 3) {
      // 使用toFixed方法将数字四舍五入到指定的小数位数，然后转换回数字
      return parseFloat(parseFloat(num).toFixed(decimalPlaces));
    },

    /**
     * 计算公式的值，支持加减乘除运算，并遵循先乘除后加减的运算顺序
     * 所有浮点型计算结果保留三位小数
     * @param {string} formula - 要计算的公式，如 "K+S*2-1"
     * @param {Object} order - 订单对象，用于获取参数值
     * @param {Object} customerPartParams - 客户零件参数，用于获取电缆件号
     * @returns {number} 计算结果，保留三位小数
     */
    evaluateFormula(formula, order, customerPartParams) {
      // 分割公式，包含加减乘除运算符
      const tokens = formula.split(/([+\-*/])/).map(item => item.trim()).filter(item => item !== '');

      // 先处理乘除运算，生成新的标记数组
      const processedTokens = [];
      let i = 0;

      while (i < tokens.length) {
        // 如果当前标记不是乘除运算符，或者是数组的最后一个元素，直接添加
        if (i === tokens.length - 1 || (tokens[i + 1] !== '*' && tokens[i + 1] !== '/')) {
          processedTokens.push(tokens[i]);
          i++;
          continue;
        }
        // 处理乘除运算
        if (tokens[i + 1] === '*' || tokens[i + 1] === '/') {
          const leftOperand = tokens[i];
          const operator = tokens[i + 1];
          const rightOperand = tokens[i + 2];

          // 获取左操作数的值
          let leftValue;
          if (this.isNumeric(leftOperand)) {
            leftValue = parseFloat(leftOperand);
          } else if (leftOperand === 'L' && customerPartParams.CablePartCode) {
            // 处理L参数，检查电缆件号
            const cablePartDetail = order.OrderDetailList.find(detail =>
              detail.CustomerPartNo === customerPartParams.CablePartCode
            );
            if (cablePartDetail) {
              leftValue = parseFloat(cablePartDetail.Quantity || 0);
              console.log('leftValue', leftValue, customerPartParams)
            } else {
              this.showNotify('error', '合同号：' + order.ContractNo + ',电缆件号：' + customerPartParams.CablePartCode + ' 在订单中不存在');
              return 0;
            }
          } else {
            // 如果是参数代码（如K、S等），需要查找对应的实际值
            const val = order.SaleParameter[this.CodeCompare[leftOperand]];
            console.log('val', val, customerPartParams)
            if (val === undefined || val === '') {
              this.showNotify('error', '合同号：' + order.ContractNo + ',参数：' + leftOperand + '，值：' + this.CodeCompareName[leftOperand] + ' 为空');
              return 0;
            } else if (val != null && !this.isNumeric(val)) {
              this.showNotify('error', '参数：' + leftOperand + '，值：' + val + ' 格式错误');
              return 0;
            }
            leftValue = val === null ? 0 : parseFloat(val);
          }

          // 获取右操作数的值
          let rightValue;
          if (this.isNumeric(rightOperand)) {
            rightValue = parseFloat(rightOperand);
          } else if (rightOperand === 'L' && customerPartParams.CablePartCode) {
            // 处理L参数，检查电缆件号
            const cablePartDetail = order.OrderDetailList.find(detail =>
              detail.CustomerPartNo === customerPartParams.CablePartCode
            );
            if (cablePartDetail) {
              rightValue = parseFloat(cablePartDetail.Quantity || 0);
            } else {
              this.showNotify('error', '合同号：' + order.ContractNo + ',电缆件号：' + customerPartParams.CablePartCode + ' 在订单中不存在');
              return 0;
            }
          } else {
            // 如果是参数代码（如K、S等），需要查找对应的实际值
            const val = order.SaleParameter[this.CodeCompare[rightOperand]];
            if (val === undefined || val === '') {
              this.showNotify('error', '合同号：' + order.ContractNo + ',参数：' + rightOperand + '，值：' + this.CodeCompareName[rightOperand] + ' 为空');
              return 0;
            } else if (val != null && !this.isNumeric(val)) {
              this.showNotify('error', '参数：' + rightOperand + '，值：' + val + ' 格式错误');
              return 0;
            }
            rightValue = val === null ? 0 : parseFloat(val);
          }

          // 根据运算符计算结果并保留三位小数
          let result;
          if (operator === '*') {
            result = this.roundNumber(leftValue * rightValue);
          } else if (operator === '/') {
            if (rightValue === 0) {
              this.showNotify('error', '除数不能为零');
              return 0;
            }
            result = this.roundNumber(leftValue / rightValue);
          }

          // 将计算结果作为一个数字添加到处理后的标记数组
          processedTokens.push(result.toString());

          // 跳过已处理的三个标记
          i += 3;
        } else {
          processedTokens.push(tokens[i]);
          i++;
        }
      }

      // 处理加减运算
      let result = 0;
      let currentOperator = '+';

      // eslint-disable-next-line no-unused-vars
      for (const token of processedTokens) {
        if (token === '+' || token === '-') {
          currentOperator = token;
        } else {
          // 如果是数字，直接计算并保留三位小数
          if (this.isNumeric(token)) {
            if (currentOperator === '+') {
              result = this.roundNumber(result + parseFloat(token));
            } else {
              result = this.roundNumber(result - parseFloat(token));
            }
          } else if (token === 'L' && customerPartParams.CablePartCode) {
            // 如果是L参数，处理电缆件号
            const cablePartDetail = order.OrderDetailList.find(detail =>
              detail.CustomerPartNo === customerPartParams.CablePartCode
            );
            if (cablePartDetail) {
              const value = parseFloat(cablePartDetail.Quantity || 0);
              console.log('value', value, customerPartParams, token)
              if (currentOperator === '+') {
                result = this.roundNumber(result + value);
              } else {
                result = this.roundNumber(result - value);
              }
            } else {
              this.showNotify('error', '合同号：' + order.ContractNo + ',电缆件号：' + customerPartParams.CablePartCode + ' 在订单中不存在');
            }
          } else {
            // 如果是参数代码，查找实际值
            const val = order.SaleParameter[this.CodeCompare[token]];
            console.log('val', val, customerPartParams, token)
            if (val === undefined || val === '') {
              this.showNotify('error', '合同号：' + order.ContractNo + ',参数：' + token + '，值：' + this.CodeCompareName[token] + ' 为空');
              return 0;
            } else if (val != null && !this.isNumeric(val)) {
              this.showNotify('error', '参数：' + token + '，值：' + val + ' 格式错误');
              return 0;
            }

            const value = val === null ? 0 : parseFloat(val);
            if (currentOperator === '+') {
              result = this.roundNumber(result + value);
            } else {
              result = this.roundNumber(result - value);
            }
            console.log('result', result, customerPartParams, token, value, order)
          }
        }
      }

      // 返回最终结果，确保保留三位小数
      return this.roundNumber(result);
    },
    /**
     * 将数据导出为Excel文件并触发下载
     * @param {Object} wb - XLSX工作簿对象
     * @param {Array} ws_data - 工作表数据数组
     */
    downExcel(wb, ws_data) {
      // 将数据转换为工作表并添加到工作簿
      const ws = XLSX.utils.aoa_to_sheet(ws_data);
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      // 生成二进制字符串
      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

      // 将二进制字符串转换为ArrayBuffer
      function s2ab(s) {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0;i < s.length;i++) {
          view[i] = s.charCodeAt(i) & 0xFF;
        }
        return buf;
      }

      // 创建并触发下载
      const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);

      // 生成带日期的文件名
      const timeStr = parseTime(new Date(), '{y}-{m}-{d}');
      link.download = '四线组四划刀生产配方' + timeStr + '.xlsx';

      // 触发下载并释放资源
      link.click();
      URL.revokeObjectURL(link.href); // 释放URL对象以避免内存泄漏
    },
    /**
     * 根据组别获取对应的索引值
     * @param {string} value - 组别值（A/B/C/D）
     * @returns {number} 索引值
     */
    getIndexByValue(value) {
      // 将组别映射到对应的索引
      const indexMap = {
        'A': 1,
        'B': 2,
        'C': 3,
        'D': 4
      };
      return indexMap[value];
    },

    /**
     * 根据组别获取楼层数据的起始索引
     * @param {string} value - 组别值（A/B/C/D）
     * @returns {number} 楼层数据起始索引
     */
    getFloorIndexByValue(value) {
      // 将组别映射到对应的楼层起始索引
      const floorIndexMap = {
        'A': 15, // A组楼层数据从第15行开始
        'B': 75, // B组楼层数据从第75行开始
        'C': 135, // C组楼层数据从第135行开始
        'D': 195 // D组楼层数据从第195行开始
      };
      return floorIndexMap[value];
    }
  }
};
</script>
