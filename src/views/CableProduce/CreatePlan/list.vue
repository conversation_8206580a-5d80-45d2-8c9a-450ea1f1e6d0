<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="80px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.CreateDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装配日期">
              <el-date-picker
                v-model="listQuery.AssembleDate"
                size="small"
                :clearable="true"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单号">
              <el-input
                v-model="listQuery.SalesOrderNo"
                size="small"
                class="filter-item"
                placeholder="销售订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="销售订单行号">
              <el-input
                v-model="listQuery.SalesOrderLineNo"
                size="small"
                class="filter-item"
                placeholder="销售订单行号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="合同号">
              <el-input
                v-model="listQuery.ContractNo"
                size="small"
                class="filter-item"
                placeholder="合同号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-button
        v-waves
        v-permission="{name:'Cable.CreatePlan.Add'}"
        class="filter-item"
        type="success"
        size="small"
        @click="handleDistribution"
      >创建计划订单
      </el-button>
      <!--      <el-dropdown v-waves v-permission="{ name: 'PP.PP_ProductionSerialNumberAssignment.Export' }" class="filter-item"-->
      <!--                   split-button-->
      <!--                   type="primary" size="small" @click="handleExport" @command="handleCommand"-->
      <!--                   style="margin-left: 10px;">-->
      <!--        {{ $t('Common.export') }}-->
      <!--        <el-dropdown-menu slot="dropdown">-->
      <!--          <el-dropdown-item command="2">导出序列号</el-dropdown-item>-->
      <!--        </el-dropdown-menu>-->
      <!--      </el-dropdown>-->
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-left: 10px;"
        @click="handleFilter"
      >查询
      </el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        label="状态"
        prop="Status"
        align="center"
        width="110px"
        fixed="right"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="scope.row.Status === 1 || scope.row.Status === '1' ">待创建计划订单</span>
          <span v-if="scope.row.Status === 2 || scope.row.Status === '2' ">待创建生产订单</span>
          <span v-if="scope.row.Status === 3 || scope.row.Status === '3' ">待确认生产订单</span>
          <span v-if="scope.row.Status === 4 || scope.row.Status === '4' ">待报工</span>
          <span v-if="scope.row.Status === 5 || scope.row.Status === '5' ">已报工</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP计划单号" prop="PlanNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PlanNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合同号" prop="Shippers" align="center" width="140px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ContractNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发货单位" prop="Shippers" align="center" width="140px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="装配日期"
        prop="AssembleDate"
        align="center"
        width="100px"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="生产批次" prop="ProductionBatch" align="center" width="80px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProductionBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户订单号" prop="OrderNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主件号" prop="PartCode" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PartCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户件号" prop="CustomerPartNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerPartNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="子件编号" prop="ComponentCode" align="center" width="160px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ComponentCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="Quantity" align="center" width="160px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售订单号" prop="SalesOrderNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售订单行号" prop="SalesOrderLineNo" align="center" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单类型" prop="OrderType" align="center" width="80px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.OrderType === 1 || scope.row.OrderType === '1' ">电缆箱</span>
          <span v-if="scope.row.OrderType === 2 || scope.row.OrderType === '2' ">其他</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100px"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />

    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 装配日期设置 -->
    <el-dialog title="装配日期设置" :visible.sync="dialogSetAssembleDateVisable" width="30%">
      <div>
        <el-form ref="setShipmentDate" label-position="right" label-width="90px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="装配日期" prop="Package_ConfigerName" style="width:93%">
                <el-date-picker
                  v-model="assembleDateSet.assembleDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm"
                  style="width: 100%;"
                  placeholder="装配日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="onClickCancelSetAssembleDate">取消</el-button>
          <el-button type="primary" @click="onClickConfirmSetAssembleDate">设定装配日期</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  exportExcelFile,
  createPlanOrder
} from '@/api/CableProduce/ProductionOrder';

export default {
  name: 'PP.PP_ProductionSerialNumberAssignment',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: false,
      dialogSetAssembleDateVisable: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        status: 1, // 用户姓名 | 登录账号
        ProductionLineDes: '',
        ProductionScheduler: '',
        PageNumber: 1,
        PageSize: 10,
        SalesOrderNo: '',
        SalesOrderLineNo: '',
        OrderNo: '',
        ContractNo: '',
        AssembleDate: [

        ],
        CreateDate: [
          new Date(),
          new Date()
        ]
      },
      assembleDateSet: {
        assembleDate: new Date(),
        ids: []
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      tableHeight: '300px'
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    // this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    onClickCancelSetAssembleDate() {
      this.dialogSetAssembleDateVisable = false;
    },
    onClickConfirmSetAssembleDate() {
      this.listLoading = true;
      const ids = this.multipleSelection.map(data => data.Id);
      this.assembleDateSet.ids = ids;
      this.dialogSetAssembleDateVisable = false;
      createPlanOrder(this.assembleDateSet).then(response => {
        this.listLoading = false;
        this.$alert(ids.length + '行数据已创建计划订单！', '计划订单');
        this.getList();
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    getList() {
      this.listLoading = true;
      if (this.listQuery.CreateDate) {
        this.listQuery.CreateDate[0] = this.$moment(this.listQuery.CreateDate[0]).format('YYYY-MM-DD');
        this.listQuery.CreateDate[1] = this.$moment(this.listQuery.CreateDate[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      }).catch(err => {
        console.log(err);
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.listQuery.Status = 1;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        createDateValue: this.listQuery.createDateValue,
        ProductionLineDes: this.listQuery.ProductionLineDes,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        SalesOrderNo: this.listQuery.SalesOrderNo,
        SalesOrderLineNo: this.listQuery.SalesOrderLineNo,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, res.fileName);
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleCommand(command) {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        createDateValue: this.listQuery.createDateValue,
        ProductionLineDes: this.listQuery.ProductionLineDes,
        ProductionScheduler: this.listQuery.ProductionScheduler,
        SalesOrderNo: this.listQuery.SalesOrderNo,
        SalesOrderLineNo: this.listQuery.SalesOrderLineNo,
        OrderNo: this.listQuery.OrderNo,
        ContractNo: this.listQuery.ContractNo
      };
      if (command === '1') {
        this.handleExport()
      } else if (command === '2') {
        exportExcelFileSerialNumber(exportQuery).then(res => {
          exportToExcel(res.data, res.fileName);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    // 分配
    handleDistribution() {
      this.dialogSetAssembleDateVisable = true;
    },
    // 邮件
    handleEmail() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      const arrRowsID = selectRows.map(v => v.ProductionOrderNo);
      IssueNotice(arrRowsID).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', res.Message);
        } else {
          this.showNotify('error', res.Message);
        }
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', 'SAP生产订单为：' + v.ProductionOrderNo + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (!v.SerialNo || v.SerialNo.replace(/\s*/g, '') === '') {
          this.showNotify('warning', 'SAP生产订单为：' + v.ProductionOrderNo + '的出厂编号为空，请勿删除');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          const arrRowsID = selectRows.map(v => v.SerialNo);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', res.Message || 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    }
  }
};
</script>
