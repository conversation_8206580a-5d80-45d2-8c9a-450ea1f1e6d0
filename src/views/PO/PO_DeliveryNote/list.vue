<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isInspection"
        filterable
        :placeholder="$t('ui.PO.PO_DeliveryNote.IsInspection')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in isInspectionOptions"
          :key="item.EnumKey"
          :label="item.EnumValue"
          :value="item.EnumKey"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryNote.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryNote.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-printer"
        :disabled="printDisable"
        @click="handlePrint"
      >{{ $t("Common.print") }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryNote.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="300"
      @sort-change="sortChange"
      @row-click="showRowDetail"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_DeliveryNote.NoteID')"
        prop="NoteID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.NoteID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_DeliveryNote.DocNum')" prop="DocNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_DeliveryNote.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNote.DeliveryTime')"
        prop="DeliveryTime"
        align="center"
        width="120"
        :formatter="formatDate"
      >
        <!--<template slot-scope="scope"> <span>{{ scope.row.DeliveryTime }}</span> </template>-->
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNote.SendInspectionStatus')"
        prop="SendInspectionStatus"
        align="center"
        width="130"
        :formatter="formatInspectionStatus"
      >
        <!-- <template slot-scope="scope">
          <el-tag v-if="scope.row.IsInspection" type="primary">{{
            $t("Common.yes")
          }}</el-tag>
          <el-tag v-else type="danger">{{ $t("Common.no") }}</el-tag>
        </template>-->
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160" :formatter="formatDateTime">
        <!--<template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>-->
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.PO.PO_DeliveryNoteDetailed.title") }}</span>
    </p>
    <el-table
      v-loading="listDetailLoading"
      :data="detailList"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleDetailSelectionChange"
    >
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.DetailedID')"
        prop="DetailedID"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.DocNum')"
        prop="DocNum"
        sortable
        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.BarCode')"
        prop="BarCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.BatchNum')"
        prop="BatchNum"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.BaseNum')"
        prop="BaseNum"
        sortable
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.ItemCode')"
        prop="ItemCode"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.ItemName')"
        prop="ItemName"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_DeliveryNoteDetailed.Qty')" prop="Qty" sortable align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.SupplierBatch')"
        prop="SupplierBatch"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.PTime')"
        prop="PTime"
        sortable
        align="center"
        width="120"
        :formatter="formatDate"
      >
        <!--<template slot-scope="scope">
          <span>{{ scope.row.PTime }}</span>
        </template>-->
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.ItmsGrpName')"
        prop="ItmsGrpName"
        sortable
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.Unit')"
        prop="Unit"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryNoteDetailed.IsSendInspection')"
        prop="IsSendInspection"
        sortable
        align="center"
        width="120"
        fixed
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsSendInspection | yesnoFilter }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves';
import permission from '@/directive/permission/permission';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  fetchList,
  batchDelete,
  printToPDF,
  exportExcelFile
} from '../../../api/PO/PO_DeliveryNote';
import {
  getDetailList
} from '../../../api/PO/PO_DeliveryNoteDetailed';
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import {
  exportToExcel
} from '@/utils/excel-export';

export default {
  name: 'PODeliveryNote',
  directives: {
    waves,
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      listLoading: false,
      listDetailLoading: false,
      total: 0,
      totalDetail: 0,
      list: [],
      detailList: [],
      InspStatus: [],
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        Sort: '',
        keyword: '',
        dateValue: [
          new Date(),
          new Date()
        ],
        isInspection: '' // 过账状态select
      },
      listQueryDetail: {
        DocNum: ''
      },
      // isInspectionOptions: [{
      //   label: this.$i18n.t('Common.all'),
      //   key: ''
      // },
      // {
      //   label: this.$i18n.t('Common.yes'),
      //   key: true
      // },
      // {
      //   label: this.$i18n.t('Common.no'),
      //   key: false
      // }],
      isInspectionOptions: [],
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      multipleDetailSelection: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IsInspection) {
          return true;
        }
      }
      return false;
    },
    printDisable() {
      return this.multipleSelection.length !== 1;
    }
  },
  created() {
    this.getList();
    this.getDict('PO004').then(data => {
      // console.log("isInspectionOptions", data);
      this.isInspectionOptions = data;
      this.listLoading = false;
    });
  },
  methods: {
    formatDate,
    formatDateTime,
    formatInspectionStatus(row, column, currentValue) {
      const opt = this.isInspectionOptions.find(element => {
        return element.EnumKey === currentValue;
      });
        // console.log("formatInspectionStatus", opt, row, currentValue);
      return opt === undefined ? '' : opt.EnumValue;
    },
    getList() {
      // 获取数据表内数据构成表格
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    getDetailList(doc) {
      // 取出detailedlist的data数据，写入详情明细表中
      this.listDetailLoading = true;
      getDetailList({
        DocNum: doc
      }).then(res => {
        this.detailList = res.Data;
        this.listDetailLoading = false;
      });
    },
    handleDelete() {
      // 删除功能，可能有bug，待测试
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const selectNums = selectRows.map(v => v.DocNum); // 第二个参数给一个固定的ID值
        batchDelete(selectNums)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
              this.handleFilter();
            } else {
              this.showNotify('error', res.Message);
              this.handleFilter();
            }
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleFilter() {
      this.detailList = null;
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // handlePrint() {
    //   // 打印功能，打印表格数据
    // },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        state: this.listQuery.isInspection
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '采购送货单')
      );
    },
    sortChange(column) {
      const {
        prop,
        order
      } = column;
      if (order !== null) {
        this.listQuery.Sort = prop + ' ' + order;
        this.handleFilter();
      }
    },
    // detailSortChange (data) {
    //   console.log('detailSortChange', this.listQueryDetail)
    //   const { prop, order } = data;
    //   if (order !== null) {
    //     this.listQueryDetail.Sort = prop + " " + order;
    //     this.getDetailList(this.listQueryDetail);
    //   }
    // },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    showRowDetail(row) {
      this.listQueryDetail.DocNum = row.DocNum;
      this.getDetailList(row.DocNum);
    },
    handleDetailSelectionChange(val) {
      this.multipleDetailSelection = val;
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      const printDocNum = selectRows[0].DocNum;

      printToPDF({
        DocNum: printDocNum
      }).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
    }
  }
};
</script>
