<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        type="text"
        :placeholder="$t('ui.PO.SAP_PO_PurchaseOrder.PurchaseOrderID')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.from" filterable style="width: 140px" class="filter-item">
        <el-option value="1" :label="$t('ui.PO.SAP_PO_PurchaseOrder.Datasource.fromRemote')" />
        <el-option value="2" :label="$t('ui.PO.SAP_PO_PurchaseOrder.Datasource.fromLocal')" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />

      <el-button
        v-waves
        :disabled="isProcessing"
        class="filter-item"
        type="success"
        icon="el-icon-download"
        @click="handleSync"
      >{{ $t("Common.sync") }}</el-button>

      <hr>
    </div>

    <el-table
      v-loading="listLoading"
      :height="350"
      :data="list"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      @row-click="handleRowClick"
      @selection-change="handleMultiSelection"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrder.PurchaseOrderID')"
        prop="PurchaseOrderID"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PurchaseOrderID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrder.SupplierInternalID')"
        prop="SupplierInternalID"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierInternalID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrder.SupplierDescription')"
        prop="SupplierDescription"

        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrder.LastModifiedTime')"
        prop="LastModifiedTime"

        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.LastModifiedTime | date }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleFilter"
    />

    <p>{{ $t("ui.PO.SAP_PO_PurchaseOrderDetail.title") }}</p>
    <el-table
      v-loading="listLoadingDetail"
      :data="listDetail"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
    >
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.ItemID')"
        prop="ItemID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.ProductID')"
        prop="ProductID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.ProductDescription')"
        prop="ProductDescription"

        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ProductDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.OrderedDateTime')"
        prop="OrderedDateTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OrderedDateTime | date }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.Quantity')"
        prop="Quantity"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Quantity }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.UnitCode')"
        prop="UnitCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.UnitCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.TotalDeliveredQuantity')"
        prop="TotalDeliveredQuantity"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.TotalDeliveredQuantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.SAP_PO_PurchaseOrderDetail.TotalDeliveredUnitCode')"
        prop="TotalDeliveredUnitCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.TotalDeliveredUnitCode }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  fetchList,
  fetchPurchaseOrderSAP,
  fetchDetailList,
  syncPurchaseOrder
} from '@/api/PO/SAP_PO_PurchaseOrder';

import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页

export default {
  name: 'PO.SAP_PO_PurchaseOrder',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        keyword: '',
        from: '2'
      },
      listDetail: [],
      listLoadingDetail: false,
      listQueryDetail: {
        purchaseOrderID: '',
        from: '2'
      },
      multiSelection: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    console.log('dd');
    this.getList();
  },
  methods: {
    sortChange(data) {
      if (this.listQuery.from !== '2') {
        return false;
      }
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listDetail = [];
      // this.queryParams.productionLine = ''
      // this.queryParams.productionOrderStatus = ''

      if (this.listQuery.from == '2') {
        fetchList(this.listQuery)
          .then(response => {
            console.log('fetchList', response);
            this.list = response.Data.items;
            this.total = response.Data.total;
            this.listLoading = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      } else {
        console.log('fetchPurchaseOrderSAP', this.listQuery.keyword);
        fetchPurchaseOrderSAP({ purchaseOrderID: this.listQuery.keyword })
          .then(response => {
            this.total = 0;
            if (response.Data !== null) {
              this.list = response.Data.PurchaseOrder;
              this.listDetail = response.Data.PurchaseOrderDetail
            } else {
              this.list = [];
              this.listDetail = [];
            }
            this.listLoading = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      }
    },
    getDetailList() {
      this.listLoadingDetail = true;
      if (this.listQuery.from == '2') {
        console.log('listQueryDetail', this.listQueryDetail);
        fetchDetailList(this.listQueryDetail)
          .then(response => {
            if (response.Code === 2000) {
              this.listDetail = response.Data;
            } else {
              this.showNotify('error', response.Message);
            }
            this.listLoadingDetail = false;
          })
          .catch(err => {
            this.listLoading = false;
            console.log(err);
          });
      }
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRowClick(row) {
      if (this.listQuery.from == '2') {
        // 根据所选采购订单的产出品加载详情信息
        this.listQueryDetail.purchaseOrderID = row.PurchaseOrderID;
        this.getDetailList();
      }
    },
    handleMultiSelection(val) {
      this.multiSelection = val;
    },
    handleSync() {
      // 同步采购订单信息
      console.log('handleSync', this.multipleSelection);
      var id = this.multiSelection.map(x => x.PurchaseOrderID);
      syncPurchaseOrder({
        purchaseOrderID: id
      })
        .then(response => {
          this.isProcessing = false;
          if (response && response.Code === 2000) {
            this.showNotify('success', 'ui.PO.SAP_PO_PurchaseOrder.syncSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    }
  }
};
</script>
