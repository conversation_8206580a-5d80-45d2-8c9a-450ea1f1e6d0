<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>

      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <!--<el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-warning"
      >{{ $t('Common.cancellation') }}</el-button>-->
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_ConsignIn.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{name:'PO.PO_ConsignIn.Delete'}"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'PO.PO_ConsignIn.Posting'}"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_ConsignIn.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column v-if="false" :label="$t('ui.PO.PO_ConsignIn.ScanID')" prop="ScanID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.DocNum')" prop="DocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.BarCode')" prop="BarCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.BatchNum')" prop="BatchNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.BaseNum')" prop="BaseNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.ItemName')" prop="ItemName" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PO.PO_ConsignIn.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.SupplierCode')" prop="SupplierCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('ui.PO.PO_ConsignIn.OutRegionName')"
        prop="OutRegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ConsignIn.OutBinLocationName')"
        prop="OutBinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.InRegionName')" prop="InRegionName" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ConsignIn.InBinLocationName')"
        prop="InBinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ConsignIn.IsPosted')"
        prop="IsPosted"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.ERPDocNum')" prop="ERPDocNum" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.PostUser')" prop="PostUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ConsignIn.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
        :formatter="formatDateTime"
      />

      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '../../../directive/permission/permission';
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import {
  fetchList,
  batchDelete,
  doPost,
  exportExcelFile
} from '@/api/PO/PO_ConsignIn';
import {
  exportToExcel
} from '@/utils/excel-export';

export default {
  name: 'PO.PO_ConsignIn',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      isProcessing: false,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        isPosted: '', // 过账状态select
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      hasPostedData: false,
      postDisableStatus: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: [],
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ]
    };
  },
  computed: {
    deletable() {
      let i = this.multipleSelection.length;
      while (i--) {
        var Remark = this.multipleSelection[i].Remark;
        console.log(String(Remark));
        if (String(Remark).indexOf('自动生成') !== -1) {
          return true;
        }
      }
      return this.multipleSelection.length === 0 || this.hasPostedData;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0 ||
          this.hasPostedData ||
          this.postDisableStatus
      );
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      var selectRows = this.multipleSelection;

      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.ScanID);

        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            this.isProcessing = false;
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.getList();
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '寄售采购入库管理')
      );
    },
    handlePosting() {
      console.log(this.multipleSelection);

      // 过账功能模块
      if (this.multipleSelection) {
        this.postDisableStatus = true;
        this.isProcessing = true;
        doPost(this.multipleSelection)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.postSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.handleFilter();
            this.postDisableStatus = false;
            this.isProcessing = false;

            // eslint-disable-next-line handle-callback-err
          })
          .catch(err => {
            this.postDisableStatus = false;
            this.isProcessing = false;
            // this.getList()
          });
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData != undefined) {
        this.hasPostedData = true;
        console.log(this.hasPostedData);
      } else {
        this.hasPostedData = false;
      }
    },
    handleCreate() {
      this.routeTo('PO.PO_ConsignInEdit');
    }
  }
};
</script>
