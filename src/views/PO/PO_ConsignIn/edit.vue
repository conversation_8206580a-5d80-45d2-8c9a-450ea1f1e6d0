<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('ui.PO.PO_ConsignIn.title') }}</label>
    </p>
    <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.SupplierCode')" prop="SupplierCode">
            <el-input v-model="temp.SupplierCode" :disabled="editStatus==='edit'" readonly>
              <el-button
                slot="append"
                v-model="temp.SupplierCode"
                icon="el-icon-more"
                :disabled="editStatus==='edit'"
                @click="handleShowLocationDlg"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.SupplierName')" prop="SupplierName">
            <el-input v-model="temp.SupplierName" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.BarCode')" prop="BarCode">
            <el-input v-model="temp.BarCode" :disabled="editStatus==='edit'" readonly>
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="temp.SupplierCode===undefined"
                @click="handleShowBarCodeDlg"
              />
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.BatchNum')">
            <el-input v-model="temp.BatchNum" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.InRegionCode')" prop="InRegionCode">
            <el-input v-model="temp.InRegionCode" disabled />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.InRegionName')" prop="InRegionName">
            <el-input v-model="temp.InRegionName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" disabled />
          </el-form-item>
        </el-col>

        <el-col :span="16">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.Qty')" prop="Qty">
            <el-input-number v-model="temp.Qty" :disabled="editStatus==='edit'" :min="0" controls-position="right" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_ConsignIn.Unit')" prop="Unit">
            <el-input v-model="temp.Unit" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item :label="$t('Common.Remark')">
            <el-input
              v-model="temp.Remark"
              type="textarea"
              :autosize="{minRows: 2,maxRows: 5}"
              @input="change($event)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="filter-container">
      <el-button
        v-if="editStatus!=='edit'"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleAddBtnClick"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-if="editStatus!=='edit'"
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDeleteDetail"
      >{{ $t('Common.delete') }}</el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-edit" @click="handleCommit">
        {{ $t('Common.confirm') }}</el-button>
    </div>
    <el-table
      v-if="editStatus!=='edit'"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column v-if="false" :label="$t('ui.PO.PO_ConsignIn.ScanID')" prop="ScanID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.DocNum')" prop="DocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.BarCode')" prop="BarCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.BatchNum')" prop="BatchNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.BaseNum')" prop="BaseNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.ItemName')" prop="ItemName" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_ConsignIn.InRegionName')" prop="InRegionName" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ConsignIn.InBinLocationName')"
        prop="InBinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!--物料信息弹框-->
    <el-dialog :title="materialtitle" :visible.sync="materialDialogVisible" width="65%">
      <div class="filter-container">
        <el-input
          v-model="barcode.query.keyword"
          size="small"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('Common.keyword')"
          clearable
          @keyup.enter.native="handleBarCodeFilter(materialtitle)"
        />
        <el-button
          v-waves
          size="small"
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleBarCodeFilter(materialtitle)"
        >{{ $t('Common.search') }}</el-button>
      </div>
      <el-table
        v-loading="barcode.loading"
        :data="barcode.list"
        border
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%;height:100%;"
        @row-click="handleMaterialSelect"
      >
        <el-table-column v-if="false" :label="$t('ui.PO.PO_ConsignIn.ScanID')" prop="ScanID" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.ScanID }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.DocNum')" prop="DocNum" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.DocNum }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.BarCode')" prop="BarCode" align="center" width="130">
          <template slot-scope="scope">
            <span>{{ scope.row.BarCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.BatchNum')" prop="BatchNum" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.BatchNum }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.BaseNum')" prop="BaseNum" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.BaseNum }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.ItemCode')" prop="ItemCode" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.ItemName')" prop="ItemName" align="center" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.Qty')" prop="Qty" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.Qty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.Unit')" prop="Unit" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.Unit }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.SupplierName')" prop="SupplierName" align="center" width="240">
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_ConsignIn.InRegionName')" prop="InRegionName" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.InRegionName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PO_ConsignIn.InBinLocationName')"
          prop="InBinLocationName"
          align="center"
          width="140"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.InBinLocationName }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="barcode.total>0"
        :total="barcode.total"
        :page.sync="barcode.query.PageNumber"
        :limit.sync="barcode.query.PageSize"
        @pagination="handleBarCodeFilter"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="materialDialogVisible = false">{{ $t('Common.close') }}</el-button>
        <!-- <el-button type="primary" icon="el-icon-check" @click="handleSelectMaterialBtnClick">{{ $t('Common.select') }}</el-button> -->
      </span>
    </el-dialog>

    <!--位置信息弹框-->
    <SupplierDlg :show.sync="locationDlgVisible" :is-multiple="false" @close="handleSupplierSelect" />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import SupplierDlg from '@/components/FLD/SupplierDlg';

import {
  fetchPage as fetchMaterialPage
} from '@/api/MD/MD_Stock';
import {
  fetchPage as fetchLocationPage
} from '@/api/MD/MD_BinLocation';
import {
  AddList,
  getEntityBySupplier
} from '@/api/PO/PO_ConsignIn';
export default {
  // 验证规则rules暂未定义
  name: 'PO.PO_ConsignInEdit',
  directives: {
    waves
  },
  components: {
    Pagination,
    SupplierDlg
  },
  data() {
    return {
      rules: {
        Qty: [{
          required: true,
          validator: this.QtyValidator,
          trigger: 'change'
        },
        {
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }
        ]
      },
      materialDialogVisible: false,
      locationDlgVisible: false,
      rtypeDialogVisible: false,
      barcode: {
        list: [],
        total: 0,
        loading: true,
        query: {
          PageNumber: 1,
          PageSize: 10,
          supplierCode: ''
        }
      },
      location: {
        list: [],
        total: 0,
        loading: true,
        query: {
          keyword: '',
          PageNumber: 1,
          PageSize: 10,
          fromTime: '',
          totime: ''
        },
        currentBinLocation: undefined
      },
      rtype: {
        list: []
      },
      temp: {
        ScanID: undefined, // '扫描ID',
        CompanyCode: undefined, // '公司编号',
        DocNum: undefined, // '扫描单号',
        ShelfNum: undefined, // '上架单号',
        BaseEntry: undefined, // '采购订单编号',
        BaseNum: undefined, // '采购订单号',
        SupplierCode: undefined, // '供应商编号',
        SupplierName: undefined, // '供应商名称',
        BaseLine: undefined, // '行号',
        BoxBarCode: undefined, // '包装码',
        BarCode: undefined, // '条码',
        BatchNum: undefined, // '批次',
        ItemCode: undefined, // '物料件号',
        ItemName: undefined, // '物料名称',
        ItmsGrpCode: undefined, // '物料组编号',
        ItmsGrpName: undefined, // '物料组名称',
        Qty: undefined, // '扫描数量',
        POUnit: undefined, // '采购单位',
        Unit: undefined, // '库存单位',
        ConversionRate: undefined, // '单位换算率',
        OutWhsCode: undefined, // '转出仓库编号',
        OutWhsName: undefined, // '转出仓库名称',
        OutRegionCode: undefined, // '转出区域编号',
        OutRegionName: undefined, // '转出区域名称',
        OutBinLocationCode: undefined, // '转出库位编号',
        OutBinLocationName: undefined, // '转出库位名称',
        InWhsCode: undefined, // '转入仓库编号',
        InWhsName: undefined, // '转入仓库名称',
        InRegionCode: undefined, // '转入区域编号',
        InRegionName: undefined, // '转入区域名称',
        InBinLocationCode: undefined, // '转入库位编号',
        InBinLocationName: undefined, // '转入库位名称',
        IsPosted: undefined, // '是否过账',
        PostUser: undefined, // '过账人',
        PostTime: undefined, // '过账时间',
        ErpDocEntry: undefined, // 'ERP过账单编号',
        ErpDocNum: undefined, // 'ERP过账单号',
        Status: undefined, // '状态',
        Remark: ''
      },
      materialtitle: this.$i18n.t('Common.select'),
      // RTypetitle: this.$i18n.t("Common.selectRType"),
      multipleSelection: [],
      list: [],
      editStatus: 'create',
      checkQty: 0
    };
  },
  created() {
    this.getPageParams();
    if (this.editStatus == 'create') {
      // fetchDocNum().then(response => {
      //   if (response.Code === 2000) {
      //     this.temp.DocNum = response.Data;
      //   }
      // });
    }
  },
  methods: {
    getBarCodeList() {
      console.log(this.barcode.query);
      getEntityBySupplier(this.barcode.query).then(response => {
        if (response.Code === 2000) {
          this.barcode.list = response.Data.items;
          this.barcode.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.barcode.loading = false;
      });
    },
    getPageParams() {
      this.temp = Object.assign({}, this.$route.params);
      if (this.temp.ScanID) {
        // 编辑
        this.editStatus = 'edit';
      } else {
        // 新增
        this.editStatus = 'create';
      }
      if (this.temp.PTime) {} else {
        this.temp.PTime = new Date();
      }
    },
    getRtypeList() {
      this.getDict('MM004').then(data => {
        this.rtype.list = data.map(val => {
          return {
            RTypeCode: val.EnumKey,
            RTypeName: val.EnumValue,
            Subject: val.EnumValue2,
            CostCenter: val.Remark
          };
        });
        this.rtype.loading = false;
      });
    },
    getLocationList() {
      fetchLocationPage(this.location.query).then(response => {
        if (response.Code === 2000) {
          this.location.list = response.Data.items;
          this.location.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.location.loading = false;
      });
    },
    handleBarCodeFilter() {
      this.barcode.loading = true;
      this.barcode.query.supplierCode = this.temp.SupplierCode;
      this.getBarCodeList();
    },
    handleLocationFilter() {
      this.location.loading = true;
      this.location.query.fromTime = '';
      this.location.query.toTime = '';
      this.getLocationList();
    },
    handleSupplierSelect(row) {
      console.log(row);
      this.temp.SupplierCode = row.SupplierCode;
      this.temp.SupplierName = row.SupplierName;
      this.locationDlgVisible = false;
    },
    change(e) {
      this.$forceUpdate();
    },
    handleMaterialSelect(row) {
      var newRow = row;
      this.checkQty = newRow.Qty;
      Object.assign(this.temp, newRow);

      this.materialDialogVisible = false;
    },
    handleRTypeSelect(row) {
      Object.assign(this.temp, row);
      this.rtypeDialogVisible = false;
    },
    handleAddBtnClick() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.temp.Qty > this.checkQty) {
            this.showNotify(
              'error',
              'ui.PO.PO_ConsignIn.CheckQty'
            );
            return;
          }
          if (this.temp.InBinLocationCode) {} else {
            this.showNotify(
              'error',
              'ui.PO.PO_ConsignIn.binLocationCodeIsRequired'
            );
            return;
          }
          if (this.temp.ItemCode) {} else {
            this.showNotify('error', 'ui.PO.PO_ConsignIn.itemCodeIsRequired');
            return;
          }
          console.log('1', this.temp);
          var detail = this.list.find(
            obj =>
              obj.BarCode == this.temp.BarCode &&
              obj.InBinLocationCode == this.temp.InBinLocationCode
          );
          console.log('2', detail);
          if (detail) {
            // 如果找到已添加的同条码只增加数量不新增条目
            detail.Qty += this.temp.Qty;
          } else {
            this.list.push(Object.assign({}, this.temp));
            console.log(this.list);
          }
          this.temp.BarCode = '';
          this.temp.BatchNum = '';
          this.temp.PTime = '';
          this.temp.ItemCode = '';
          this.temp.ItemName = '';
          this.temp.ItmsGrpCode = '';
          this.temp.ItmsGrpName = '';
          this.temp.Qty = '';
          this.temp.Unit = '';
          this.temp.Remark = '';
          this.location.currentBinLocation = '';
          this.temp.InWhsCode = '';
          this.temp.InWhsName = '';
          this.temp.InRegionCode = '';
          this.temp.InRegionName = '';
          this.temp.InBinLocationCode = '';
          this.temp.InBinLocationName = '';
        } else {
          return;
        }
      });
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        if (this.editStatus == 'edit') {
          this.$refs['form'].validate(valid => {
            if (valid) {
              this.startLoading();
              update(this.temp).then(response => {
                if (response.Code === 2000) {
                  this.showNotify('success', 'Common.updateSuccess');
                  this.backTo('PO.PO_ConsignIn');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              });
            }
          });
        } else {
          if (this.list.length == 0) {
            this.showNotify('error', 'Common.noConfirmData');
            return;
          }
          this.startLoading();
          AddList(this.list).then(response => {
            if (response.Code === 2000) {
              // 跳转回主单页面
              this.backTo('PO.PO_ConsignIn');
            } else {
              this.showNotify('error', response.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          });
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleShowBarCodeDlg() {
      // 打开物料信息弹窗
      this.materialDialogVisible = true;
      this.handleBarCodeFilter();
    },
    handleShowLocationDlg() {
      // 打开供应商
      this.locationDlgVisible = true;
      this.location.query.keyword = '';
      // this.handleLocationFilter()
    }
  }
};
</script>
