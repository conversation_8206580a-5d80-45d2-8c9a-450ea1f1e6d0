<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <p>
      <label style="width:100%">{{ $t("ui.PO.PO_DeliveryPlan.title") }}</label>
    </p>
    <el-form
      ref="dataForm"
      :inline="false"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item
            v-show="this.editStatus === 'edit'"
            :label="$t('ui.PO.PO_DeliveryPlan.DocNum')"
            prop="DocNum"
          >
            <el-input v-model="temp.DocNum" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            v-show="this.editStatus != 'edit'"
            :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseNum')"
            prop="BaseNum"
          >
            <el-input
              v-model="temp.BaseNum"
              :placeholder="$t('ui.PO.PurchaseOrder.select')"
              readonly
            >
              <el-button slot="append" icon="el-icon-more" @click="selectPurchaseOrder" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('Common.Remark')" prop="Remark">
            <el-input v-model="temp.Remark" />
          </el-form-item>
        </el-col>
      </el-row>
      <p>
        <label style="width:100%">
          {{ $t("ui.PO.PO_DeliveryPlan.titlesub") }}
        </label>
      </p>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_DeliveryPlan.SupplierCode')" prop="SupplierName">
            <el-input v-model="detailTemp.SupplierCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.PO.PO_DeliveryPlan.SupplierName')" prop="SupplierName">
            <el-input v-model="detailTemp.SupplierName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemCode')" prop="ItemCode">
            <el-input v-model="detailTemp.ItemCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemName')" prop="ItemName">
            <el-input v-model="detailTemp.ItemName" readonly disabled />
          </el-form-item>
        </el-col>
        <!--<el-col :span="8">
          <el-form-item
            :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItmsGrpName')"
            prop="ItmsGrpName"
          >
            <el-input v-model="detailTemp.ItmsGrpName" readonly disabled />
          </el-form-item>
        </el-col>-->
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_DeliveryPlanDetailed.Unit')" prop="Unit">
            <el-input v-model="detailTemp.Unit" readonly disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            :label="$t('ui.PO.PO_DeliveryPlanDetailed.DeliveryTime')"
            prop="DeliveryTime"
          >
            <el-date-picker
              v-model="detailTemp.DeliveryTime"
              type="date"
              :clearable="false"
              :placeholder="$t('Common.select')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_DeliveryPlanDetailed.Qty')" prop="Qty">
            <el-input-number v-model="detailTemp.Qty" controls-position="right" :min="0" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        :disabled="updateDisable"
        @click="handleUpdateDetail"
      >{{ $t("Common.update") }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelDetail"
      >{{ $t("Common.delete") }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        :disabled="submitDisable"
        @click="handleCommitDetail"
      >{{ $t("Common.confirm") }}</el-button>
    </div>
    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @row-click="handleRowClick"
      @selection-change="handleDetailSelectionChange"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="editStatus === 'edit'"
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.PlanNum')"
        prop="PlanNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlanNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.SupplierName')"
        prop="SupplierName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseNum')"
        prop="BaseNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseLine')"
        prop="BaseLine"

        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.DeliveryPlanItemID')"
        prop="DeliveryPlanItemID"

        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryPlanItemID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemCode')"
        prop="ItemCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemName')"
        prop="ItemName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.Unit')"
        prop="Unit"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.DeliveryTime')"
        prop="DeliveryTime"

        align="center"
        width="120"
        :formatter="formatDate"
      >
        <!--<template slot-scope="scope">
          <span>{{ scope.row.DeliveryTime | formatDate }}</span>
        </template>-->
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.IsDelete')"
        prop="IsDelete"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MUser')"
        prop="MUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.MTime')"
        prop="MTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DUser')"
        prop="DUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('Common.DTime')"
        prop="DTime"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="$t('ui.PO.PurchaseOrder.title')"
      :visible.sync="dialogFormVisible"
      width="50%"
    >
      <div class="filter-container">
        <el-input
          v-model="listQuery.BaseNum"
          class="filter-item"
          style="width: 200px"
          :disabled="editStatus === 'edit'"
          :placeholder="$t('ui.PO.PO_DeliveryPlanDetailed.BaseNum')"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.SupplierName"
          class="filter-item"
          style="width: 200px"
          :disabled="editStatus==='edit'"
          :placeholder="$t('ui.PO.PO_DeliveryPlanDetailed.SupplierCode')"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        />
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        border
        :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
        highlight-current-row
        style="width: 100%;"
        height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="40px" />
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.BaseNum')"
          prop="BaseNum"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.PurchaseOrderID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.BaseLine')"
          prop="BaseLine"

          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.SupplierCode')"
          prop="SupplierName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierInternalID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.SupplierName')"
          prop="SupplierName"

          align="center"
          width="220"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierDescription }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.ItemCode')"
          prop="ItemCode"

          align="center"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.ItemName')"
          prop="ItemName"

          align="center"
          width="200"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductDescription }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column
          :label="$t('ui.PO.PurchaseOrder.ItmsGrpCode')"
          prop="ItmsGrpCode"

          align="center"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductCategoryInternalID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.ItmsGrpName')"
          prop="ItmsGrpName"

          align="center"
          width="200"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ProductCategoryDescription }}</span>
          </template>
        </el-table-column>-->
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.Quantity')"
          prop="Quantity"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.Quantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.UnitCode')"
          prop="UnitCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.UnitCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.TotalDeliveredQuantity')"
          prop="TotalDeliveredQuantity"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.TotalDeliveredQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.TotalDeliveredUnitCode')"
          prop="TotalDeliveredUnitCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.TotalDeliveredUnitCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PurchaseOrder.PTime')"
          prop="PTime"

          align="center"
          width="120"
          :formatter="formatDate"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.OrderedDateTime }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t("Common.close") }}
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          :disabled="multipleSelection.length < 1"
          @click="handleSelect"
        >{{ $t("Common.select") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
// import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import {
  fetchDetailedList,
  fetchPurchaseOrderDetailList,
  getDeliveryPlanDetailList
} from '../../../api/PO/PO_DeliveryPlanDetailed';
import { formatDate } from '../../../utils';
import { submitForAdd, submitForUpdate } from '../../../api/PO/PO_DeliveryPlan';

export default {
  name: 'PO.PO_DeliveryPlanDetailed',
  components: {},
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listDetail: [],
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        BaseNum: '',
        SupplierName: ''
      },
      listQueryDetail: {
        DocNum: ''
      },
      temp: {
        PlanID: '',
        DocNum: '',
        BaseNum: '',
        Remark: '',
        SupplierCode: '',
        SupplierName: ''
      },
      detailTemp: {
        ItemCode: '',
        ItemName: '',
        BaseNum: '',
        ItmsGrpName: '',
        SupplierCode: '',
        SupplierName: '',
        Unit: '',
        DeliveryTime: '',
        Qty: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      editStatus: '',
      rules: {
        DocNum: [
          {
            required: true,
            message: 'type is required',
            trigger: 'change'
          }
        ] /* ,
        BaseNum: [{
          type: 'date',
          required: true,
          message: 'timestamp is required',
          trigger: 'change'
        }]*/
      },
      selectRow: undefined,
      multipleDetailSelection: [],
      multipleSelection: [],
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      return this.multipleDetailSelection.length === 0;
    },
    submitDisable() {
      return this.listDetail.length < 1;
    },
    updateDisable() {
      // console.log(this.detailTemp.Qty)
      const isUse =
        this.listDetail.length > 0 &&
        this.detailTemp.Qty > 0 &&
        this.detailTemp.SupplierCode !== '';
      return !isUse;
    }
  },
  created() {
    this.getPageParams();
    if (this.editStatus === 'edit') {
      this.getDetailList(this.temp.DocNum);
    }
  },
  methods: {
    formatDate,
    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      if (this.temp.PlanID) {
        // 编辑
        this.editStatus = 'edit';
      } else {
        // 新增
        this.editStatus = 'create';
      }
    },
    getList() {
      // 获取采购订单表
      this.listLoading = true;
      fetchPurchaseOrderDetailList(this.listQuery).then(res => {
        this.list = res.Data;
        this.listLoading = false;
      });
    },
    getDetailList(doc) {
      this.listDetailLoading = true;
      this.listQueryDetail.DocNum = doc;

      fetchDetailedList(this.listQueryDetail).then(res => {
        this.listDetail = res.Data;
        this.listDetailLoading = false;
      });
    },
    handleFilter() {
      // 查询采购单信息
      if (
        this.listQuery.BaseNum.length === 0 &&
        this.listQuery.SupplierName.length === 0
      ) {
        this.showNotify(
          'error',
          this.$t(
            'ui.PO.PO_DeliveryPlanDetailed.ValidatorMessage.NotAllowConditionAllEmpty'
          )
        );
      } else {
        this.getList();
      }
    },
    resetTemp() {
      // 重置表单数据
      this.temp = {
        PlanID: '',
        DocNum: '',
        BaseNum: '',
        SupplierCode: '',
        SupplierName: ''
      };
      this.detailTemp = {
        ItemCode: '',
        ItemName: '',
        ItmsGrpName: '',
        SupplierCode: '',
        SupplierName: '',
        Unit: '',
        DeliveryTime: new Date(),
        Qty: ''
      };
    },
    selectPurchaseOrder() {
      // this.resetTemp()
      this.dialogStatus = 'select';
      this.dialogFormVisible = true;
      this.listQuery.BaseNum = '';
      this.listQuery.SupplierName = '';
      this.list = [];
      // this.listQuery.BaseNum = ''
      // if (this.editStatus === 'create') { // 新增信息时，返回全部的采购订单信息
      //   this.getList()
      // } else { // 编辑信息时，请求固定BaseNum采购订单号和SupplierName供应商名称的采购订单
      //   this.listQuery.BaseNum = this.listDetail[0].BaseNum
      //   this.getList()
      // }
    },
    handleUpdateDetail() {
      Object.assign(this.selectRow, this.detailTemp);
    },
    handleCommitDetail() {
      // 提交按钮，按editStatus的状态调用添加或是更新方法
      /* 这块写后台交互代码 */

      this.isProcessing = true;
      if (this.editStatus === 'create') {
        submitForAdd({
          Remark: this.temp.Remark,
          DetailsData: this.listDetail
        }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.createSuccess');
            this.isProcessing = false;
            this.backTo('PO.PO_DeliveryPlan');
          } else {
            this.showNotify('error', res.Message);
            this.isProcessing = false;
          }
        });
      } else {
        submitForUpdate({
          MasterData: this.temp,
          DetailsData: this.listDetail
        }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.updateSuccess');
            this.isProcessing = false;
            this.backTo('PO.PO_DeliveryPlan');
          } else {
            this.showNotify('error', res.Message);
            this.isProcessing = false;
          }
        });
      } // 返回上级页面语句写入then操作，成功写入数据库后返回上级页面
    },
    handleDelDetail() {
      // 只在该页面删除子表里面的信息，不会影响数据库操作
      const tableData = this.listDetail;
      const multDataLen = this.multipleDetailSelection.length;
      const tableDataLen = tableData.length;
      for (let i = 0;i < multDataLen;i++) {
        for (let j = 0;j < tableDataLen;j++) {
          if (
            JSON.stringify(tableData[j]) ===
            JSON.stringify(this.multipleDetailSelection[i])
          ) {
            // 判断是否相等，相等就删除
            this.listDetail.splice(j, 1);
            break;
          }
        }
      }
    },
    handleSelect() {
      // 选择采购订单信息，可选择多条
      // 勾选的条目传回后台，根据该信息去SAP查询相关数据返回前端
      console.log(this.multipleSelection);
      getDeliveryPlanDetailList(this.multipleSelection).then(res => {
        for (let i = 0;i < res.Data.length;i++) {
          this.listDetail.push(res.Data[i]);
        }
        this.dialogFormVisible = false;
      });
      console.log(this.listDetail);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleDetailSelectionChange(val) {
      this.multipleDetailSelection = val;
    },
    handleRowClick(row, col, e) {
      this.selectRow = row;
      Object.assign(this.detailTemp, row);
    }
  }
};
</script>
