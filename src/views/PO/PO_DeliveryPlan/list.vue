<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />

      <el-select
        v-model="listQuery.DeliveryPlanStatus"
        style="width: 140px"
        filterable
        class="filter-item"
        @change="handleFilter(0)"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Add' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Edit' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Assign' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-arrow-down"
        :disabled="assignable"
        @click="handleAssign"
      >{{ $t("Common.grant") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Affirm' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="success"
        icon="el-icon-check"
        :disabled="canAffirm"
        @click="handleAffirm"
      >{{ $t("Common.affirm") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.successf' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="success"
        icon="el-icon-check"
        :disabled="canSucess"
        @click="handleSuccess"
      >{{ $t("Common.successf") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Merge' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-circle-plus-outline"
        :disabled="canMerge"
        @click="handleMerge"
      >{{ $t("Common.merge") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_DeliveryPlan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      height="300"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_DeliveryPlan.PlanID')"
        prop="PlanID"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlan.DocNum')"
        prop="DocNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlan.SupplierCode')"
        prop="SupplierCode"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlan.SupplierName')"
        prop="SupplierName"

        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlan.DeliveryTime')"
        prop="DeliveryTime"

        align="center"
        width="120"
        :formatter="formatDate"
      >
        <!--<template slot-scope="scope">
          <span>{{ scope.row.DeliveryTime }}</span>
        </template>-->
      </el-table-column>
      <el-table-column
        v-if="!IsSupplier"
        :label="$t('ui.PO.PO_DeliveryPlan.Status')"
        prop="Status"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.Status === 1" type="danger">
            {{ $t("Dictionary.PO_DeliveryPlan_Status.One") }}
          </el-tag>
          <el-tag v-else-if="scope.row.Status === 2" type="primary">
            {{ $t("Dictionary.PO_DeliveryPlan_Status.Two") }}
          </el-tag>
          <el-tag v-else-if="scope.row.Status === 3" type="success">
            {{ $t("Dictionary.PO_DeliveryPlan_Status.Three") }}
          </el-tag>
          <el-tag v-else-if="scope.row.Status === 4" type="primary">
            {{ $t("Dictionary.PO_DeliveryPlan_Status.Four") }}
          </el-tag>
          <el-tag v-else-if="scope.row.Status === 6" type="success">
            {{ $t("Dictionary.PO_DeliveryPlan_Status.Six") }}
          </el-tag>
          <el-tag v-else type="primary">
            {{ $t("Dictionary.PO_DeliveryPlan_Status.Five") }}
          </el-tag>
          <!--<span>{{ scope.row.Status }}</span>-->
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="180"
        :formatter="formatDateTime"
      >
        <!--<template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>-->
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.PO.PO_DeliveryPlanDetailed.title") }}</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="detailSortChange"
    >
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.PlanNum')"
        prop="PlanNum"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PlanNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseNum')"
        prop="BaseNum"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseLine')"
        prop="BaseLine"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.DeliveryPlanItemID')"
        prop="DeliveryPlanItemID"
        sortable
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryPlanItemID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemCode')"
        prop="ItemCode"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemName')"
        prop="ItemName"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItmsGrpName')"
        prop="ItmsGrpName"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.Qty')"
        prop="Qty"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.Unit')"
        prop="Unit"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.DeliveryTime')"
        prop="DeliveryTime"
        sortable
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryTime | date }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PO.PO_DeliveryPlanDetailed.IsCreatedBarCode')"
        prop="IsCreatedBarCode"
        sortable
        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsCreatedBarCode | yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { exportToExcel } from '@/utils/excel-export';
import {
  fetchList,
  batchDelete,
  assignPlan,
  mergePlan,
  confirmPlan,
  successPlan,
  exportExcelFile
} from '../../../api/PO/PO_DeliveryPlan';

import { fetchDetailedList } from '../../../api/PO/PO_DeliveryPlanDetailed';
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { formatDate, formatDateTime } from '../../../utils';
import permission from '../../../directive/permission/permission';
import { mapGetters } from 'vuex';

export default {
  name: 'PO.PO_DeliveryPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      IsSupplier: false,
      list: [],
      listDetail: [],
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        DeliveryPlanStatus: 1, // 交货通知单状态
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      listQueryDetail: {
        DocNum: ''
      },
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      multipleSelection: [],
      statusOptions: [
        {
          value: '',
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.All')
        },
        {
          value: 1,
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.One')
        },
        {
          value: 2,
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.Two')
        },
        {
          value: 3,
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.Three')
        },
        {
          value: 4,
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.Four')
        },
        {
          value: 5,
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.Five')
        },
        {
          value: 6,
          label: this.$i18n.t('Dictionary.PO_DeliveryPlan_Status.Six')
        }
      ],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status > 3) return true;
      }
      return this.multipleSelection.length > 1;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status > 3) return true;
      }
      return false;
    },
    // 如果选“全部”，“导出”功能不可用
    exportable() {
      if (this.listQuery.DeliveryPlanStatus === '') return true;
      return false;
    },
    assignable() {
      // 只有未下发的条目才允许下发
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status !== 1) return true;
      }
      return false;
    },
    canAffirm() {
      // 已下发的条目才能进行确认
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status !== 2) return true;
      }
      return false;
    },
    canSucess() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].Status === 6) return true;
      }
      return false;
    },
    canMerge() {
      // 只有未下发和同一家供应商，相同交货日期的条目才允许合并
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      const standard = this.multipleSelection[0].SupplierCode;
      const standardTime = this.multipleSelection[0].DeliveryTime;
      while (i--) {
        if (this.multipleSelection[i].Status !== 1) return true;
        else if (this.multipleSelection[i].SupplierCode !== standard) {
          return true;
        } else if (this.multipleSelection[i].DeliveryTime !== standardTime) {
          return true;
        }
      }
      return this.multipleSelection.length < 2;
    }
    // IsSupplier(){
    //   console.log('this.$store.getters.userinfo.IsSupplier-created',this.$store.getters.userinfo)
    //   return this.$store.getters.userinfo.IsSupplier
    //   // return false
    // }
  },
  created() {
    console.log(
      'this.$store.getters.userinfo.IsSupplier-created',
      this.$store.getters.userinfo
    );
    this.IsSupplier = this.$store.getters.userinfo.IsSupplier;
    if (this.$store.getters.userinfo.IsSupplier) {
      // 供应商不需要未下发状态
      this.statusOptions.splice(
        this.statusOptions.findIndex(item => item.value === 1),
        1
      );
      this.listQuery.DeliveryPlanStatus = 2;
      // console.log(this.statusOptions)
    }
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        if (response.Data.total === 0) {
          this.$notify.info({
            title: i18n.t('Common.information'),
            message: i18n.t('Common.queryNoData')
          });
        }
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter(flag) {
      // 查询只能查询子表信息
      if (flag === 1) {
        // 查询传入参数的时候，意味着只对子表进行操作
        this.getDetailList();
      } else {
        this.listDetail = null;
        this.totalDetail = 0;

        this.listQuery.PageNumber = 1;
        this.listQuery.PageSize = 10;
        this.getList();
      }
    },
    getDetailList(doc) {
      this.listDetailLoading = true;
      this.listQueryDetail.DocNum = doc;

      fetchDetailedList(this.listQueryDetail).then(res => {
        this.listDetail = res.Data;
        this.listDetailLoading = false;
      });
    },
    handleCreate() {
      this.routeTo('PO.PO_DeliveryPlanDetailed');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      this.routeTo('PO.PO_DeliveryPlanDetailed', Object.assign(selectRows[0]));
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.DocNum);
        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
              this.handleFilter();
            } else {
              this.showNotify('error', res.Message);
            }
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        state: this.listQuery.DeliveryPlanStatus
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '采购交货计划')
      );
    },
    handleAssign() {
      // 下发操作
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.DocNum);
        assignPlan({ DocNum: arrRowsID }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    handleAffirm() {
      // 确认操作
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRows = selectRows.map(v => v.DocNum);

        confirmPlan({ DocNum: arrRows }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    handleSuccess() {
      // 完成操作
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRows = selectRows.map(v => v.DocNum);

        successPlan({ DocNum: arrRows }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    handleMerge() {
      // 合并操作
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.DocNum);

        mergePlan({ planDocNums: arrRowsID }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    sortChange(column) {
      const { prop, order } = column;
      if (order !== null) {
        console.log('sort');
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter();
      }
    },
    detailSortChange(data) {
      const { prop, order } = data;
      if (order !== null) {
        this.listQueryDetail.sort = prop + ' ' + order;
        this.getDetailList(this.listQueryDetail.DocNum);
      }
    },
    handleRowClick(row, column, event) {
      this.listQueryDetail.DocNum = row.DocNum;
      this.getDetailList(row.DocNum);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
