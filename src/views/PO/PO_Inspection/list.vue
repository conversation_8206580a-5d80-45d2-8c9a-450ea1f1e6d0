<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />

      <el-select
        v-model="listQuery.iStatus"
        :placeholder="$t('ui.PO.PO_Inspection.IStatus')"
        style="width: 140px"
        filterable
        class="filter-item"
        @change="handleFilter"
      >
        <el-option v-for="item in iStatusOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option v-for="item in isPostOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <!--<el-select v-if="false" v-model="listQuery.sort" style="width: 140px" class="filter-item" @change="handleFilter">
        <el-option v-for="item in sortOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>-->
      <!--现在是排序选择，换成质检状态选择-->

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_Inspection.Edit' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >
        {{ $t("Common.edit") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_Inspection.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_Inspection.Posting' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t("Common.posting") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_Inspection.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      height="300"
      @sort-change="sortChange"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChangeEdit"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_Inspection.InspectionID')"
        prop="InspectionID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InspectionID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.DocNum')" prop="DocNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.NoteNum')" prop="NoteNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.NoteNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_Inspection.SupplierCode')"
        prop="SupplierCode"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.ItemName')" prop="ItemName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_Inspection.SampleRange')"
        prop="SampleRange"
        align="center"
        width="180"
        :formatter="formatSampleRange"
      />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_Inspection.InspectionGrade')"
        prop="InspectionGrade"
        align="center"
        width="240"
        :formatter="formatInspectionGrade"
      />
      <el-table-column :label="$t('ui.PO.PO_Inspection.IQty')" prop="IQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IQty }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.PO.PO_Inspection.SQty')" prop="SQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SQty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_Inspection.IStatus')"
        prop="IStatus"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.IStatus" type="primary">
            {{ $t("ui.PO.PO_Inspection.Inspected") }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t("ui.PO.PO_Inspection.Uninspected") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_Inspection.IUser')" prop="IUser" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.IUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_Inspection.ITime')"
        prop="ITime"
        align="center"
        width="120"
        :formatter="formatDate"
      />

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.PO.PO_InspectionDetailed.title") }}</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="detailList"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="detailSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.DetailedID')"
        prop="DetailedID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DetailedID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.ScanNum')"
        prop="ScanNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.DocNum')"
        prop="DocNum"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.NoteNum')" prop="NoteNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.NoteNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.BarCode')" prop="BarCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.BatchNum')" prop="BatchNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.BaseEntry')"
        prop="BaseEntry"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseEntry }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.BaseNum')" prop="BaseNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.ItemName')" prop="ItemName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.OkQty')" prop="OkQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.OkQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.NoQty')" prop="NoQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.NoQty }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <!--<el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InWhsCode')"
        prop="InWhsCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InWhsName')"
        prop="InWhsName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.InRegionCode')"
        prop="InRegionCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InRegionName')"
        prop="InRegionName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_InspectionDetailed.InBinLocationCode')"
        prop="InBinLocationCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.InBinLocationName')"
        prop="InBinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.SupplierBatch')"
        prop="SupplierBatch"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.CustomsNum')"
        prop="CustomsNum"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CustomsNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.ERPDocNum')" prop="ERPDocNum" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ReturnScan.PostUser')" prop="PostUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PP.ReturnScan.PostTime')" prop="PostTime" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.PTime')"
        prop="PTime"
        align="center"
        width="120"
        :formatter="formatDate"
      />
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_InspectionDetailed.BaseLine')" prop="BaseLine" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.PO.PO_InspectionDetailed.IsPosted')"
        prop="IsPosted"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.IsPosted" type="primary">
            {{ $t("Dictionary.YesNoMap.YesValue") }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t("Dictionary.YesNoMap.NoValue") }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="$t('Common.edit')" :visible.sync="dialogContainerVisible">
      <el-form ref="dataForm" :inline="false" :model="temp" label-position="right" label-width="100px">
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item :label="$t('ui.SD.SD_Packing.CustomsNum')" prop="CustomsNum">
              <el-input v-model="temp.CustomsNum" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogContainerVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" icon="el-icon-check" @click="handleaffirmUpdate">{{ $t('Common.affirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import permission from '@/directive/permission/permission';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchList,
  fetchDetailList,
  batchDelete,
  doPost,
  update,
  exportExcelFile
} from '../../../api/PO/PO_Inspection';
import {
  fetchList as fetchInspectionGradeList
} from '@/api/MD/MD_POInspectionGrade';
// import { getDictDescription } from '@/utils' // 列表内容格式化

export default {
  name: 'PO.PO_Inspection',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      postDisableStatus: false,
      dialogContainerVisible: false,
      list: [],
      detailList: [],
      total: 0,
      listLoading: false,
      listDetailLoading: false,
      listQueryDetail: {
        DocNum: ''
      },
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        dateValue: [
          new Date(),
          new Date()
        ],
        iStatus: '',
        isPosted: ''
      },
      hasPostedData: false,
      SampleRangeOptions: [],
      InspectionGradeList: [],
      temp: {
        CustomsNum: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      iStatusOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('ui.PO.PO_Inspection.Inspected'),
        key: true
      },
      {
        label: this.$i18n.t('ui.PO.PO_Inspection.Uninspected'),
        key: false
      }
      ],
      isPostOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      selectedRow: '',
      isProcessing: false,
      multipleSelection: [],
      editMultipleSelection: []
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0 || this.hasPostedData;
    },
    selective() {
      // let i = this.multipleSelection.length;
      // if (i === 0) return true;
      // while (i--) {
      //   if (this.multipleSelection[i].Status > 3) return true;
      // }
      if (this.multipleSelection.length !== 0) {
        return true;
      }
      return this.editMultipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0 ||
          this.hasPostedData ||
          this.postDisableStatus
      );
    }
  },
  created() {
    this.getList();
    // 采样范围
    this.getDict('SYS005').then(data => {
      this.SampleRangeOptions = data;
      this.getList();
    });
    this.getInspectionGradeList();
  },
  methods: {
    formatDate,
    formatDateTime,
    formatSampleRange: function(row, column, currentValue) {
      const findedSampleRangeOptions = this.SampleRangeOptions.find(element => {
        return element.EnumKey === currentValue;
      });
      return findedSampleRangeOptions != null
        ? findedSampleRangeOptions.EnumValue
        : '';
    },
    formatInspectionGrade: function(row, column, currentValue) {
      const findedOptions = this.InspectionGradeList.find(element => {
        return element.GradeCode === currentValue;
      });
      return findedOptions != null ? findedOptions.GradeName : '';
    },
    getInspectionGradeList() {
      // this.listLoading = true
      fetchInspectionGradeList().then(response => {
        this.InspectionGradeList = response.Data;
      });
    },
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    getDetailList(doc) {
      this.listDetailLoading = true;
      fetchDetailList({
        docNum: doc
      }).then(res => {
        this.detailList = res.Data;
        console.log(this.detailList);
        this.listDetailLoading = false;
      });
    },
    handleFilter() {
      this.detailList = [];
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      // 删除功能
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.DetailedID);

        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            this.isProcessing = false;
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.handleFilter();
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleUpdate() {
      this.dialogContainerVisible = true;
      // var selectedRows = this.multipleSelection;
      // this.temp.CustomsNum = selectedRows[0].CustomsNum;
    },
    handleaffirmUpdate() {
      var selectedRows = this.editMultipleSelection;
      // selectedRows.forEach(element => {
      //   element.CustomsNum = this.temp.CustomsNum;
      // });
      var docNums = selectedRows.map(x => x.DocNum);
      var updateList = {
        CustomsNum: this.temp.CustomsNum,
        DocNums: docNums
      };
      update(updateList).then(response => {
        if (response.Code === 2000) {
          this.showNotify('success', 'Common.updateSuccess');
        } else {
          this.showNotify('error', response.Message);
        }
        // this.getDetailList(this.listQueryDetail.DocNum);
        this.getList();
        this.dialogContainerVisible = false;
      });
    },
    handlePosting() {
      // 过账功能
      // const postData = {master:this.selectedRow,details:this.multipleSelection}

      this.postDisableStatus = true;
      this.isProcessing = true;
      if (this.multipleSelection) {
        doPost(this.multipleSelection)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.postSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.handleFilter();
            console.log('采购报检postDisableStatus', this.postDisableStatus);
            this.postDisableStatus = false;
            this.isProcessing = false;
          })
          .catch(err => {
            console.log('采购报检过账错误', err);
            this.postDisableStatus = false;
            this.isProcessing = false;
          });
      }
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        iState: this.listQuery.iStatus,
        isPoasted: this.listQuery.isPosted
      };
      console.log(newListQuery);
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '采购质检查询')
      );
    },
    handleRowClick(row) {
      // 调出子表数据,this.geDetailList
      this.selectedRow = row;
      this.listQueryDetail.DocNum = row.DocNum;
      this.getDetailList(row.DocNum);
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },

    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order !== null) {
        this.listQueryDetail.sort = prop + ' ' + order;
        this.getDetailList(this.listQueryDetail.DocNum);
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      console.log('handleSelectionChange', postedData);
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleSelectionChangeEdit(val) {
      this.editMultipleSelection = val;
    }
  }
};
</script>
