<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isDelivery"
        filterable
        :placeholder="$t('ui.PO.PO_BarCode.IsDelivery')"
        style="width: 140px"
        class="filter-item"
        clearable
        @change="handleFilter"
      >
        <el-option v-for="item in isDeliveryOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.Print' }"
        class="filter-item"
        type="primary"
        icon="el-icon-printer"
        size="small"
        :disabled="printDisable"
        @click="handlePrint"
      >{{ $t("Common.print") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.CreateNote' }"
        class="filter-item"
        type="success"
        icon="el-icon-check"
        size="small"
        :disabled="canDelivery"
        @click="handleDelivery"
      >{{ $t("Common.gdelivery") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.Export' }"
        :loading="downloadLoading"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column v-if="false" :label="$t('ui.PO.PO_BarCode.BarID')" prop="BarID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.PO.PO_BarCode.DeliveryDetailId')"
        prop="DeliveryDetailId"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryDetailId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.DeliveryDocNum')" prop="DeliveryDocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.BarCode')" prop="BarCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.BatchNum')" prop="BatchNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.BaseNum')" prop="BaseNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.ItemName')" prop="ItemName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.SupplierCode')" prop="SupplierCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.PO.PO_BarCode.SupplierBatch')" prop="SupplierBatch" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_BarCode.PTime')"
        prop="PTime"
        align="center"
        width="120"
        :formatter="formatDate"
      >
        <!--<template slot-scope="scope">
          <span>{{ scope.row.PTime }}</span>
        </template>-->
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_BarCode.DeliveryTime')"
        prop="DeliveryTime"
        align="center"
        width="120"
        :formatter="formatDate"
      >
        <!--<template slot-scope="scope">
          <span>{{ scope.row.DeliveryTime }}</span>
        </template>-->
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.PO.PO_BarCode.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.PO.PO_BarCode.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('ui.PO.PO_BarCode.PrintTemplate')"
        prop="PrintTemplate"
        align="center"
        width="120"
        :formatter="formatLabelTemplate"
      />-->
      <el-table-column :label="$t('ui.MD.MD_BinLocation.IsConsign')" prop="IsConsign" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsConsign | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_BarCode.IsDelivery')"
        prop="IsDelivery"
        align="center"
        width="160"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.IsDelivery" type="primary">{{ $t("Dictionary.IsDelivery.yes") }}</el-tag>
          <el-tag v-else type="danger">{{ $t("Dictionary.IsDelivery.no") }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160" :formatter="formatDateTime">
        <!--<template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>-->
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import {
  fetchList,
  batchDelete,
  printToPDF,
  exportExcelFile
} from '../../../api/PO/PO_BarCode';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  addDeliveryNote
} from '../../../api/PO/PO_DeliveryNote';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '../../../utils';
import permission from '../../../directive/permission/permission';
import {
  formatLabelTemplate
} from '@/utils/buss-formatter';
export default {
  name: 'PO.PO_BarCode',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        isDelivery: '',
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isDeliveryOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Dictionary.IsDelivery.yes'),
        key: true
      },
      {
        label: this.$i18n.t('Dictionary.IsDelivery.no'),
        key: false
      }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      multipleSelection: [],
      downloadLoading: false,
      isProcessing: false
    };
  },
  computed: {
    selective() {
      const i = this.multipleSelection.length;
      return i > 1 || i === 0 || this.multipleSelection[0].IsDelivery;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IsDelivery) return true;
      }
      return false;
    },
    canDelivery() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IsDelivery) return true;
      }
      return false;
    },
    printDisable() {
      return this.multipleSelection.length <= 0;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatLabelTemplate,
    formatDate,
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      console.log(this.listQuery);
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleCreate() {
      this.routeTo('PO.PO_BarCodeDetailed');
    },
    handleUpdate() {
      console.log('选中啥了', this.multipleSelection[0]);
      this.routeTo(
        'PO.PO_BarCodeDetailed',
        Object.assign(this.multipleSelection[0])
      );
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.BarID);

        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
              this.handleFilter();
            } else {
              this.showNotify('error', res.Message);
            }
            this.isProcessing = false;
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handleDelivery() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        addDeliveryNote(selectRows).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleExport() {
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isDelivery: this.listQuery.isDelivery
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '采购标签')
      );
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      const barcodes = selectRows.map(v => v.BarCode);
      console.log(barcodes);
      printToPDF(barcodes).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
      });
    }
  }
};
</script>
