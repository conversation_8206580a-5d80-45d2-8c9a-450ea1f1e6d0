<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("route.PO.PO_BarCodeDetailed") }}</label>
    </p>
    <div class="filter-container">
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-delete" @click="resetFormData">
        {{ $t("Common.empty") }}</el-button>

      <el-button v-waves class="filter-item" type="success" icon="el-icon-edit" @click="handleCommit">
        {{ $t("Common.confirm") }}</el-button>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-back" @click="handleBack">
        {{ $t("Common.cancel") }}</el-button>
    </div>

    <el-form ref="dataForm" :inline="false" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item v-if="editStatus === 'edit'" :label="$t('ui.PO.PO_BarCode.BarCode')" prop="BarCode">
            <el-input v-model="temp.BarCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.DeliveryDocNum')" prop="DeliveryDocNum">
            <el-input v-model="temp.DeliveryDocNum" :placeholder="$t('ui.PO.PO_DeliveryPlanDetailed.select')" readonly>
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="editStatus === 'edit'"
                @click="selectDeliveryPlanOrder"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.BaseNum')" prop="BaseNum">
            <el-input v-model="temp.BaseNum" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item v-if="editStatus === 'edit'" :label="$t('ui.PO.PO_BarCode.BatchNum')" prop="BatchNum">
            <el-input v-model="temp.BatchNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.SupplierBatch')" prop="SupplierBatch">
            <el-input v-model="temp.SupplierBatch" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.DeliveryTime')" prop="DeliveryTime">
            <el-date-picker v-model="temp.DeliveryTime" :clearable="false" style="width: 165px;" type="date" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.SupplierCode')" prop="SupplierCode">
            <el-input v-model="temp.SupplierCode" disabled />
          </el-form-item>
          <el-form-item :label="$t('ui.MD.MD_BinLocation.IsConsign')" prop="IsConsign">
            <el-switch v-model="temp.IsConsign" active-color="#13ce66" inactive-color="#ff4949" />
          </el-form-item>
        </el-col>

        <el-col :span="16">
          <el-form-item :label="$t('ui.PO.PO_BarCode.SupplierName')" prop="SupplierName">
            <el-input v-model="temp.SupplierName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.PO.PO_BarCode.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.PTime')" prop="PTime">
            <el-date-picker v-model="temp.PTime" :clearable="false" style="width: 165px;" type="date" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.Qty')" prop="Qty">
            <el-input-number v-model="temp.Qty" style="width: 165px;" controls-position="right" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.Unit')" prop="Unit">
            <el-input v-model="temp.Unit" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item v-if="editStatus === 'create'" :label="$t('ui.PO.PO_BarCode.PrintQty')" prop="PrintQty">
            <el-input-number v-model="temp.PrintQty" controls-position="right" :min="1" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.PrintTemplate')" prop="PrintTemplate">
            <el-select v-model="temp.PrintTemplate" filterable>
              <el-option
                v-for="item in PrintTemplateOptions"
                :key="item.TempleteDesc"
                :label="item.TempleteDesc"
                :value="item.TempleteFile"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.PO.PO_BarCode.Remark')" prop="Remark">
            <el-input v-model="temp.Remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 采购交货计划选择弹出 -->
    <el-dialog
      :title="$t('ui.PO.PO_DeliveryPlanDetailed.title')"
      :visible.sync="dialogFormVisible"
      width="50%"
      top="5vh"
    >
      <div class="filter-container">
        <el-input
          v-model="listQuery.DeliveryPlanDocNum"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('ui.PO.PO_DeliveryPlanDetailed.PlanNum')"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.SupplierName"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('ui.PO.PO_DeliveryPlanDetailed.SupplierName')"
          @keyup.enter.native="handleFilter"
        />
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          {{ $t("Common.search") }}</el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
        highlight-current-row
        style="width: 100%"
        @sort-change="sortChange"
        @row-click="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" align="center" width="40" fixed /> -->

        <el-table-column :label="$t('ui.PO.PO_DeliveryPlanDetailed.PlanNum')" prop="PlanNum" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.PlanNum }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseNum')" prop="BaseNum" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.BaseNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PO_DeliveryPlanDetailed.BaseLine')"
          prop="BaseLine"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BaseLine }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PO_DeliveryPlanDetailed.SupplierName')"
          prop="SupplierName"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.SupplierName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_DeliveryPlanDetailed.PTime')" prop="PTime" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.PTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemCode')"
          prop="ItemCode"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItemName')"
          prop="ItemName"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column
          :label="$t('ui.PO.PO_DeliveryPlanDetailed.ItmsGrpName')"
          prop="ItmsGrpName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItmsGrpName }}</span>
          </template>
        </el-table-column>-->
        <el-table-column :label="$t('ui.PO.PO_DeliveryPlanDetailed.Qty')" prop="Qty" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.Qty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.PO.PO_DeliveryPlanDetailed.Unit')" prop="Unit" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.Unit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.PO.PO_DeliveryPlanDetailed.DeliveryTime')"
          prop="DeliveryTime"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.DeliveryTime }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t("Common.close") }}
        </el-button>
        <el-button type="primary" icon="el-icon-check" @click="handleSelect">{{ $t("Common.select") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
// import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import {
  add,
  update,
  getDeliveryPlanDetailList
} from '../../../api/PO/PO_BarCode';
import {
  fetchTemplate
} from '../../../api/MD/MD_LabelTemplate';
// import updateMainData from "../../PO/PO_BarCode/list";

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'PO.PO_BarCodeDetailed',
  components: {},
  directives: {
    waves
  },
  data() {
    return {
      PrintTemplateOptions: [],
      list: [],
      listLoading: false,
      listQuery: {
        DeliveryPlanDocNum: '',
        SupplierName: ''
      },
      temp: {
        BarCode: '',
        BaseNum: '',
        DeliveryDetailId: '',
        DeliveryDocNum: '',
        BatchNum: '',
        BaseEntry: '',
        BaseLine: '',
        SupplierCode: '',
        SupplierName: '',
        SupplierBatch: '',
        DeliveryTime: '',
        PTime: new Date(),
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: '',
        Unit: '',
        PrintTemplate: '', // 暂时写入数据，值不能为null
        PrintQty: 1,
        Remark: '',
        IsConsign: false
      },
      editStatus: '',
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        SupplierBatch: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        PrintTemplate: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        PTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        },
        {
          validator: function(rule, value, callback) {
            var selectDate = new Date(value);
            var nowDate = new Date();
            if (selectDate > nowDate) {
              callback(new Error(i18n.t('Common.selectDateIsBigToDay')));
            } else {
              callback();
            }
          },
          trigger: 'change'
        }
        ]
      },
      downloadLoading: false,
      multipleSelection: [],
      printTemplateOption: []
    };
  },
  computed: {
    selective() {
      const i = this.multipleSelection.length;
      return i === 0 || i > 1;
    }
  },
  created() {
    this.getPageParams();
    this.getTemplateList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      getDeliveryPlanDetailList(this.listQuery).then(response => {
        console.log('getDeliveryPlanDetailList', response);
        this.list = response.Data;
        this.listLoading = false;
      });
    },
    getTemplateList() {
      fetchTemplate({
        TemplateType: 1
      }).then(res => {
        this.PrintTemplateOptions = res.Data;
        this.printTemplateOption = res.Data;
        this.temp.PrintTemplate = this.PrintTemplateOptions[0].TempleteDesc;
      });
    },
    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      if (this.temp.BarID) {
        this.editStatus = 'edit';
      } else {
        this.editStatus = 'create';
      }
    },
    handleFilter() {
      this.getList();
    },
    resetFormData() {
      // 清空表单数据
      this.temp = {
        BarCode: '',
        BaseNum: '',
        DeliveryDetailId: '',
        DeliveryDocNum: '',
        BatchNum: '',
        BaseEntry: '',
        BaseLine: '',
        SupplierCode: '',
        SupplierName: '',
        SupplierBatch: '',
        DeliveryTime: '',
        PTime: new Date(),
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: '',
        CreateQty: 0,
        Unit: '',
        PrintTemplate: '', // 暂时写入数据，值不能为null
        PrintQty: 1,
        Remark: '',
        IsConsign: false
      };
    },
    handleCommit() {
      this.$refs['dataForm'].validate(val => {
        if (val) {
          var isChoose = this.printTemplateOption.find(
            x => x.TempleteDesc == this.temp.PrintTemplate
          );
          if (isChoose) {
            // 判断是否是key值，如果是key值换成value值
            this.temp.PrintTemplate = isChoose.TempleteFile;
          }
          this.startLoading();
          if (this.editStatus === 'create') {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.handleBack();
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading();
            }).catch(err => {
              console.log(err);
              this.endLoading();
            });
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.handleBack();
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading()
            }).catch(err => {
              console.log(err);
              this.endLoading();
            });
          }
        } else {
          return false;
        }
      });
    },
    handleBack() {
      // updateMainData.methods.handleFilter();
      this.backTo('PO.PO_BarCode');
    },
    handleSelectionChange(row, column, event) {
      console.log('handleSelectionChange', row);
      this.multipleSelection = row;
    },
    selectDeliveryPlanOrder() {
      this.dialogStatus = 'select';
      this.dialogFormVisible = true;
      this.getList();
    },
    handleSelect() {
      const selectRow = this.multipleSelection;
      // console.log('selectRow', this.multipleSelection)

      Object.assign(this.temp, selectRow);
      this.temp.IsConsign = selectRow.Remark == '1';
      this.temp.Remark = '';
      this.temp.DeliveryDocNum = selectRow.PlanNum;
      this.temp.DeliveryDetailId = selectRow.DetailedID;
      this.temp.PTime = new Date();
      this.dialogFormVisible = false;
    },
    sortChange(data) {
      // const {
      //   prop,
      //   order
      // } = data
      // if (prop === 'LogID') {
      //   this.sortByID(order)
      // }
    },
    sortByID(order) {
      // if (order === 'ascending') {
      //   this.listQuery.sort = 'LogID asc'
      // } else {
      //   this.listQuery.sort = 'LogID desc'
      // }
      // this.handleFilter()
    }
    // formatJson(filterVal, jsonData) {
    //   return jsonData.map(v =>
    //     filterVal.map(j => {
    //       if (j === 'timestamp') {
    //         return parseTime(v[j])
    //       } else {
    //         return v[j]
    //       }
    //     })
    //   )
    // },
  }
};
</script>
