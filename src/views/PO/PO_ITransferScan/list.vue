<template>
  <div
    v-loading="isProcessing"
    class="app-container"
    :element-loading-text="$t('Common.slowRunningWarning')"
  >
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option
          v-for="item in isPostedOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-if="false"
        v-model="listQuery.sort"
        filterable
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in sortOptions"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        />
      </el-select>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      />
      <hr>

      <el-button
        v-waves
        v-permission="{name:'PO.PO_ITransferScan.Delete'}"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deleteDisable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'PO.PO_ITransferScan.Posting'}"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'PO.PO_ITransferScan.Export'}"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.DocNum')"
        prop="DocNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.BaseNum')"
        prop="BaseNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.BarCode')"
        prop="BarCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.BatchNum')"
        prop="BatchNum"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.ItemCode')"
        prop="ItemCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.ItemName')"
        prop="ItemName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.PO.PO_ITransferScan.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="120"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.Qty')"
        prop="Qty"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.Unit')"
        prop="Unit"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.PO.PO_ITransferScan.OutWhsName')" prop="OutWhsName"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.OutWhsName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.OutRegionName')"
        prop="OutRegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.OutBinLocationName')"
        prop="OutBinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('ui.PO.PO_ITransferScan.InWhsName')" prop="InWhsName"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.InWhsName }}</span> </template> </el-table-column>-->
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.InRegionName')"
        prop="InRegionName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.InBinLocationName')"
        prop="InBinLocationName"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.IsPosted')"
        prop="IsPosted"

        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.ERPDocNum')"
        prop="ERPDocNum"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.PostUser')"
        prop="PostUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.PO.PO_ITransferScan.PostTime')"
        prop="PostTime"

        align="center"
        width="120"
        :formatter="formatDateTime"
      />

      <el-table-column
        v-if="false"
        :label="$t('Common.Remark')"
        prop="Remark"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"

        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import permission from '../../../directive/permission/permission';
import {
  fetchList,
  batchDelete,
  doPost,
  exportExcelFile
} from '../../../api/PO/PO_ITransferScan';
import { exportToExcel } from '@/utils/excel-export';
import { formatDate, formatDateTime } from '../../../utils';

export default {
  name: 'TransferScan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      isProcessing: false,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$i18n.t('Common.previousWeek'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: this.$i18n.t('Common.previousThreeMonths'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      isPostedOptions: [
        {
          label: this.$i18n.t('Common.all'),
          key: ''
        },
        {
          label: this.$i18n.t('Common.posted'),
          key: true
        },
        {
          label: this.$i18n.t('Common.notPosted'),
          key: false
        }
      ],
      multipleSelection: []
    };
  },
  computed: {
    deleteDisable() {
      return this.multipleSelection.length === 0 || this.hasPostedData;
    },
    postDisable() {
      return this.multipleSelection.length === 0 || this.hasPostedData;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.ScanID);

        // 删除逻辑处理
        batchDelete(arrRowsID)
          .then(res => {
            this.isProcessing = false;
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.deleteSuccess');
              this.handleFilter();
            } else {
              this.showNotify('error', res.Message);
            }
          })
          .catch(error => {
            this.isProcessing = false;
          });
      });
    },
    handlePosting() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        console.log(this.multipleSelection);
        doPost(this.multipleSelection)
          .then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.postSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.handleFilter();
            this.isProcessing = false;
          })
          .catch(err => {
            this.isProcessing = false;
          });
      }
    },
    handleExport() {
      var newListQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(newListQuery).then(res =>
        exportToExcel(res.data, '退供封存区移动记录')
      );
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted == true;
      });
      if (postedData != undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    }
  }
};
</script>
