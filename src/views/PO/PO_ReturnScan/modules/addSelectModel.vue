<template>
  <el-dialog title="采购退货信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="采购订单" prop="EBELN" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="创建日期" prop="AEDAT" align="center" width="100" :formatter="formatDate" />
      <!-- <el-table-column label="采购行号" prop="EBELP" align="center" show-overflow-tooltip /> -->
      <el-table-column label="供应商编号" prop="LIFNR" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="NAME1" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="数量" prop="MENGE" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="MEINS" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MEINS ==='KAR'">CAR</span>
          <span v-else-if="scope.row.MEINS ==='PAK'">PAC</span>
          <span v-else-if="scope.row.MEINS ==='ST'">PC</span>
          <span v-else>{{ scope.row.MEINS }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料件号" prop="MATNR" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="TXZ01" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="采购组织" prop="EKORG" align="center" show-overflow-tooltip />
      <el-table-column label="采购组" prop="EKGRP" align="center" show-overflow-tooltip />
      <el-table-column label="物料组" prop="MATKL" align="center" show-overflow-tooltip />
      <el-table-column label="库存地点代码" prop="LGORT" align="center" width="100" show-overflow-tooltip />
      <!-- <el-table-column label="库存地点描述" prop="LGOBE" align="center" show-overflow-tooltip /> -->

    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { formatDate, formatDateTime } from '@/utils';
import {
  GetDetailListForTest
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    },
    status: {
      type: String,
      defaule: 'create'
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false
    }
  },
  computed: {

  },
  created() {},
  methods: {
    formatDate,
    formatDateTime,
    add() {
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record)
    },
    handleCustomerRowSelectEvent(selection) {
      console.log(selection);
      const data = [];
      if (selection.length >= 1) {
        selection.map(res => {
          data.push(res.LIFNR)
        });
        const num = [...new Set(data)].length;
        console.log(this.status);
        if (this.status !== 'create' && num > 1) {
          this.showNotify('warning', '请勿选择多个供应商');
        } else if (this.dataList.length === 0) {
          this.multipleSelection = selection
        } else {
          let switchBtn = true;
          console.log(selection, this.dataList, 1);
          selection.some(v => {
            this.dataList.some(res => {
              if (v.EBELN + v.EBELP === res.onlyId) {
                this.showNotify('warning', '采购订单编号为：' + v.EBELN + '已选择，请勿重复选择');
                switchBtn = false;
                return true;
              }
              if (v.LIFNR !== res.SupplierCode) {
                this.showNotify('warning', '请勿选择多个供应商');
                switchBtn = false;
                return true;
              }
            })
          });
          if (switchBtn) {
            this.multipleSelection = selection;
          }
        }
      } else {
        this.multipleSelection = selection;
      }
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetDetailListForTest(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleSelectCustomer() {
      this.$emit('ok', this.multipleSelection);
      this.dialogCustomerFormVisible = false;
    }
  }
}
</script>

<style scoped>
</style>
