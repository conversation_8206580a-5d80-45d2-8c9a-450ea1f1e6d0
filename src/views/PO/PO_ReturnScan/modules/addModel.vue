<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form :model="model" label-width="100px">
        <el-form-item label="数量">
          <el-input v-model="model.ReturnScanQty" />
        </el-form-item>
        <!-- <el-form-item label="供应商批次" >
          <el-input v-model="model.SupplierBatch"></el-input>
        </el-form-item>
        <el-form-item label="批次" >
          <el-input v-model="model.BatchNum"></el-input>
        </el-form-item> -->
        <el-form-item label="库位">
          <el-select v-model="model.BinLocationCode" filterable placeholder="请选择" style="width: 100%;" @change="changeBinLocation">
            <el-option
              v-for="item in options"
              :key="item.BinLocationCode"
              :label="item.BinLocationCode+'-'+item.BinLocationName"
              :value="item.BinLocationCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-input v-model="model.BinLocationName" disabled />
        </el-form-item>
        <el-form-item label="仓库">
          <el-input v-model="model.WhsName" disabled />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="model.Remark" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZ_SAP,
  GetBinLocationAll
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        Quantity: '',
        TotalDeliveredQuantity: ''
      },
      options: [],
      Qty: 0
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.Qty = record.ReturnScanQty;
      // this.GetXZ_SAP()
      this.GetBinLocationAll();
      this.drawer = true;
      this.model = Object.assign({}, record);
    },
    handleSave() {
      this.$emit('ok', this.model);
      this.drawer = false;
    },
    GetBinLocationAll() {
      GetBinLocationAll().then(res => {
        if (res.Code === 2000) {
          this.options = res.Data
        }
      })
    },
    changeBinLocation(e) {
      const obj = this.options.find(v => v.BinLocationCode === e);
      this.model.BinLocationName = obj.BinLocationName; // 库位
      this.model.RegionCode = obj.RegionCode; // 区域编号
      this.model.RegionName = obj.RegionName; // 区域
      this.model.WhsCode = obj.WhsCode; // 仓库编号
      this.model.WhsName = obj.WhsName; // 仓库
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
