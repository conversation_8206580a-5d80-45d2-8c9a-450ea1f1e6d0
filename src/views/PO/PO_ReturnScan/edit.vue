<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("ui.PO.PO_ReturnScanDetail.twoTitle") }}</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox"
      :inline="true"
      :rules="rules"
      :model="listQuery"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" :label="$t('ui.PO.PO_ReturnScanDetail.DocNum')">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购退货">
            <el-input v-model="searchQuery.BaseNum" placeholder="采购退货" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账时间">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="过账时间"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="库位">
            <el-select v-model="searchQuery.BinLocationCode" filterable placeholder="请选择" @change="changeBinLocation">
              <el-option v-for="item in options" :key="item.BinLocationCode" :label="item.BinLocationName"
                :value="item.BinLocationCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="区域">
            <el-input v-model="searchQuery.BinLocationName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库">
            <el-input v-model="searchQuery.WhsName" disabled />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed="left" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        v-for="(col,index) in listColumn"
        :key="index"
        :label="col.title"
        align="center"
        :prop="col.name"
        width="160"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="col.title=== '采购单号'">
            <el-input
              v-if="scope.row.editable"
              v-model="scope.row.BaseNum"
              class="filter-item"
              @change="handleChange(scope.row.BaseNum, scope.$index, scope.row)"
            />
            <span v-else>{{ scope.row[col.name] }}</span>
          </span>
          <span v-else-if="col.title=== '库存单位'">
            <span v-if="scope.row.Unit ==='KAR'">CAR</span>
            <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
            <span v-else-if="scope.row.Unit ==='ST'">PC</span>
            <span v-else>{{ scope.row.Unit }}</span>
          </span>
          <span v-else-if="col.title=== '创建日期'">
            {{ scope.row.AEDAT | date }}
          </span>
          <span v-else>{{ scope.row[col.name] }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.$index, scope.row)">编辑</span>
          <!-- <span @click="saveRow(scope.$index, scope.row)">保存</span>
          <span @click="cancel(scope.$index, scope.row)">取消</span> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
    <!--查询采购退货申请单号  -->
    <add-select-model ref="modalForm" :data-list="list" :status="editStatus" @ok="modalFormOk" />
    <!-- 编辑明细 -->
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import AddModel from './modules/addModel'
import {
  parseTime
} from '@/utils';
import {
  GetDetailListForTest,
  SubmitScanInfo,
  GetDocNum,
  Update,
  GetList,
  GetBinLocationAll
} from '../../../api/PO/PO_ReturnScan';

export default {
  name: 'PO.PO_ReturnScanDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        DocNum: '',
        BaseNum: '',
        ManualPostTime: new Date(),
        PageNumber: 1,
        PageSize: 10
      },
      multipleSelection: [],
      rules: {
        PostTime: [{
          required: true,
          type: 'date',
          message: this.$i18n.t('ui.PO.PO_ReturnScanDetail.BaseNum'),
          trigger: ['blur', 'change']
        }]
      },
      listColumn: [{
        title: '采购单号',
        name: 'BaseNum'
      }, {
        title: '采购单行号',
        name: 'BaseLine'
      }, {
        title: '供应商编号',
        name: 'SupplierCode'
      }, {
        title: '供应商名称',
        name: 'SupplierName'
      }, {
        title: '供应商批次',
        name: 'SupplierBatch'
      }, {
        title: '物料件号',
        name: 'ItemCode'
      }, {
        title: '物料名称',
        name: 'ItemName'
      }, {
        title: '物料组',
        name: 'ItmsGrpCode'
      }, {
        title: '仓库编号',
        name: 'WhsCode'
      }, {
        title: '仓库名称',
        name: 'WhsName'
      },
      //  {
      //   title: '库位编号',
      //   name: 'BinLocationCode'
      // }, {
      //   title: '库位名称',
      //   name: 'BinLocationName'
      // },
      {
        title: '数量',
        name: 'ReturnScanQty'
      }, {
        title: '库存单位',
        name: 'Unit'
      }, {
        title: '批次',
        name: 'BatchNum'
      }, {
        title: '创建日期',
        name: 'AEDAT'
      }],
      drawer: false,
      searchQuery: {
        DocNum: '',
        BaseNum: '',
        BinLocationCode: '', // 库位编号
        BinLocationName: '', // 库位
        RegionCode: '', // 区域编号
        RegionName: '', // 区域
        WhsCode: '', // 仓库编号
        WhsName: '', // 仓库
        ManualPostTime: new Date(),
        Remark: ''
      },
      editStatus: 'create',
      delList: [],
      options: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetBinLocationAll();
  },
  activated() {
    if (Object.keys(this.$route.params).length > 0) {
      console.log('activated调用了', this.$route.params);
      if (this.$route.params.status === 'add') {
        this.searchQuery = {
          DocNum: '',
          BaseNum: '',
          ManualPostTime: new Date(),
          Remark: ''
        };
        this.list = [];
      }
      this.getPageParams();
      this.GetBinLocationAll();
    }
  },
  methods: {
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      GetDetailListForTest(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.ReturnScanDetailedID) {
            v.IsDelete = true;
            this.delList.push(v.ReturnScanDetailedID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择采购信息');
        return;
      }
      if (!this.searchQuery.ManualPostTime) {
        this.showNotify('warning', '过账时间不能为空');
        return;
      }
      // if (!this.searchQuery.BinLocationCode) {
      //   this.showNotify("warning", '请选择库位');
      //   return
      // }
      let switchBtn = true;
      this.list.some(res => {
        if (res.ReturnScanQty === null || res.ReturnScanQty === 0 || res
          .ReturnScanQty === '0') {
          this.showNotify('warning', '数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.WhsCode === '' || res.WhsCode === null || res.WhsCode === ' ') {
          this.showNotify('warning', '请选择库位');
          switchBtn = false;
          return true;
        }
        if (res.BinLocationCode === '' || res.BinLocationCode === null || res.BinLocationCode === ' ') {
          this.showNotify('warning', '请选择库位');
          switchBtn = false;
          return true;
        }
      });

      if (switchBtn) {
        this.startLoading();
        const query = {
          DocNum: this.searchQuery.DocNum,
          // BinLocationCode: this.searchQuery.BinLocationCode, // 库位编号
          // BinLocationName: this.searchQuery.BinLocationName, // 库位
          // RegionCode: this.searchQuery.RegionCode, // 区域编号
          // RegionName: this.searchQuery.RegionName, // 区域
          // WhsCode: this.searchQuery.WhsCode, // 仓库编号
          // WhsName: this.searchQuery.WhsName, // 仓库
          Remark: this.searchQuery.Remark,
          ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
          detailed: this.list,
          deletedetail: this.delList
        };

        if (this.editStatus === 'create') {
          SubmitScanInfo(query).then(res => {
            if (res.Code === 2000) {
              if (res.MessageParam === 2000) {
                this.showNotify('success', res.Message || 'Common.postSuccess');
              } else {
                this.showNotify('warning', res.Message || 'Common.postSuccess');
              }
              this.backTo('PO.PO_ReturnScan');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        } else {
          Update(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('PO.PO_ReturnScan');
              if (res.MessageParam === 2000) {
                this.showNotify('success', res.Message);
              } else {
                this.showNotify('warning', res.Message);
              }
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key, row) {
      // let target = this.list.filter(item => item === row)[0]
      // this.$set(target, 'editable', !target.editable)

      // console.log(target.editable)
      // return
      this.$refs.modalFormAdd.edit(row);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    saveRow(key, row) {
      const target = this.list.filter(item => item === row)[0];
      this.$set(target, 'editable', false);
      this.$set(target, 'isNew', false);
    },
    cancel(key, row) {
      const target = this.list.filter(item => item === row)[0];
      this.$set(target, 'editable', false);
    },
    handleChange(value, key, column) {
      console.log(value, key, column);
      // return
      const newData = [...this.list];
      const target = newData.filter(item => item === column)[0];
      if (target) {
        this.$set(this.list, key, column);
      }
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.ReturnScanDetailedID) {
          if (v.ReturnScanDetailedID === record.ReturnScanDetailedID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.onlyId === record.onlyId) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      // this.list = record
      // let model = ''
      // record.forEach(v => {
      //   model +=  v.PurchaseOrderID + ','
      //   this.listQuery.BaseNum = model.substr(0,model.length-1)
      // });
      const data = [];
      record.forEach((v, index) => {
        data.push({
          FactoryCode: v.BUKRS, // 采购订单
          BaseNum: v.EBELN, // 采购订单
          BaseLine: v.EBELP, // 采购单行号
          ItemCode: v.MATNR, // 物料件号
          ItemName: v.TXZ01, // 物料名称
          SupplierCode: v.LIFNR, // 供应商编号
          SupplierName: v.NAME1, // 供应商名称
          SupplierBatch: '', // 供应商批次
          ReturnScanQty: v.MENGE, // 数量
          Unit: v.MEINS, // 单位
          BatchNum: '', // 批次
          WhsCode: v.LGORT, // 仓库编号
          WhsName: v.LGOBE, // 仓库名称
          RegionCode: v.RegionCode, // 区域
          RegionName: v.RegionName, // 区域
          BinLocationCode: v.BinLocationCode, // 库位
          BinLocationName: v.BinLocationName, // 库位
          ItmsGrpCode: v.MATKL, // 物料组
          SalesOrderNum: v.VBELN, // 销售订单号
          SalesOrderLine: v.VBELP, // 销售订单行号
          EvaluationType: v.BWTAR || '', // 评估类型 BWTAR: 评估类型  BWTTY: 评估类别
          AEDAT: v.AEDAT,
          onlyId: v.EBELN + v.EBELP
        })
      });
      const obj = {};
      for (let i = 0;i < this.list.length;i++) {
        this.list[i].onlyId = this.list[i].BaseNum + this.list[i].BaseLine;
      }
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.onlyId] ? '' : obj[next.onlyId] = true && cur.push(next);
        return cur;
      }, []);
      console.log(this.list, 1)
    },
    getDetailList() {
      const query = {
        keyword: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
          this.searchQuery.BinLocationCode = res.Data[0].BinLocationCode; // 库位
          this.searchQuery.BinLocationName = res.Data[0].BinLocationName; // 库位
          this.searchQuery.RegionCode = res.Data[0].RegionCode; // 区域编号
          this.searchQuery.RegionName = res.Data[0].RegionName; // 区域
          this.searchQuery.WhsCode = res.Data[0].WhsCode; // 仓库编号
          this.searchQuery.WhsName = res.Data[0].WhsName; // 仓库
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ReturnScanID) {
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    GetBinLocationAll() {
      GetBinLocationAll().then(res => {
        if (res.Code === 2000) {
          this.options = res.Data;
        }
      })
    },
    changeBinLocation(e) {
      const obj = this.options.find(v => v.BinLocationCode === e);
      this.searchQuery.BinLocationName = obj.BinLocationName; // 库位
      this.searchQuery.RegionCode = obj.RegionCode; // 区域编号
      this.searchQuery.RegionName = obj.RegionName; // 区域
      this.searchQuery.WhsCode = obj.WhsCode; // 仓库编号
      this.searchQuery.WhsName = obj.WhsName; // 仓库
    }
  }
};
</script>

<style>

</style>
