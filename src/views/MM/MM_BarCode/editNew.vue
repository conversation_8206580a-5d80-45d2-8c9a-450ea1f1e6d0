<template>
  <div class="app-container">
    <div class="filter-container">
      <!--<el-button v-waves class="filter-item" type="primary" icon="el-icon-delete" @click="handleDelDetail">{{ $t('Common.empty') }}</el-button>-->
      <el-button v-waves class="filter-item" size="small" type="success" icon="el-icon-edit" @click="handleConfirm">
        {{ $t('Common.confirm') }}</el-button>
      <el-button v-waves class="filter-item" size="small" icon="el-icon-back" @click="handleCancel">
        {{ $t('Common.cancel') }}
      </el-button>
    </div>
    <p>
      <label style="width: 100%">{{ $t('ui.MM.BarCode.title') }}</label>
    </p>
    <el-form ref="dataForm" class="formBox" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <!-- <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.BarCode')" prop="BarCode">
            <el-input v-model="temp.BarCode" disabled />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.BatchNum')">
            <el-input v-model="temp.BatchNum" />
          </el-form-item>
        </el-col> -->

        <el-col :span="8">
          <el-form-item label="产品件号" prop="ItemCode">
            <el-input v-model="temp.ItemCode" readonly>
              <el-button slot="append" icon="el-icon-more" @click="handleMaterialDialogBtnClick" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品描述" prop="ItemName">
            <el-input v-model="temp.ItemName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.Unit')" prop="Unit">
            <el-input v-model="temp.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.Qty')" prop="Qty">
            <el-input-number v-model="temp.Qty" :min="0" controls-position="right" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.PrintTemplate')" prop="PrintTemplate">
            <el-select v-model="temp.PrintTemplate">
              <el-option v-for="item in printTemplateOptions" :key="item.TempleteDesc" :label="item.TempleteDesc"
                :value="item.TempleteFile" />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item :label="$t('Common.printQty')" prop="printQty">
            <el-input-number
              v-model="temp.printQty"
              :min="0"
              controls-position="right"
              :disabled="editStatus==='edit'"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="销售订单号">
            <el-input v-model="temp.BaseNum" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售订单行号">
            <el-input v-model="temp.BaseLine" type="number" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="出厂编号">
            <el-input v-model="temp.BarCode" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('Common.Remark')" prop="Remark">
            <el-input v-model="temp.Remark" type="textarea" :autosize="{minRows: 2, maxRows: 5}" @input="change()" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <MaterialDlg ref="materialDlg" @ok="modalFormOk" />
  </div>
</template>

<script>
import {
  fetchBatchNum,
  fetchEarliestStock,
  addList,
  update
} from '@/api/MM/MM_BarCode';
import {
  fetchTemplate as fetchPrintTemplate
} from '@/api/MD/MD_LabelTemplate';
import waves from '@/directive/waves'; // waves directive
import MaterialDlg from '@/components/FLD/MaterialDlg/indexNew';

export default {
  name: 'MM.MM_BarCodeEditNew',
  directives: {
    waves
  },
  components: {
    MaterialDlg
  },
  data() {
    return {
      temp: {
        BarID: '',
        BarCode: '',
        BatchNum: '',
        ItemCode: '',
        ItemName: '',
        BaseNum: '',
        BaseLine: undefined,
        Qty: undefined,
        Unit: '',
        PrintTemplate: '',
        printQty: undefined,
        Remark: ''
      },
      rules: {
        ItemCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        BarCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        BatchNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Qty: [{
          required: true,
          validator: this.QtyValidator,
          trigger: 'change'
        }],
        printQty: [{
          required: true,
          validator: this.QtyValidator,
          trigger: 'change'
        }],
        PTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        keyword: ''
      },
      printTemplateOptions: [],
      editStatus: 'create',
      dlgVisible: false
    };
  },
  created() {
    this.getPrintTemplateList();
    this.getPageParams();

    console.log(this.editStatus);
    if (this.editStatus == 'create') {
      this.temp.BaseLine = 1;
      this.temp.Qty = 1
    } else {
      this.temp.printQty = 1;
    }
  },
  methods: {
    getPrintTemplateList() {
      fetchPrintTemplate({
        templateType: 11
      }).then(response => {
        if (response.Code === 2000) {
          this.printTemplateOptions = response.Data;
          this.temp.PrintTemplate = this.printTemplateOptions[0].TempleteDesc;
          this.temp.printQty = 1;
        }
      });
    },
    change(e) {
      this.$forceUpdate()
    },
    getBatchNum() {
      return new Promise((resolve, reject) => {
        fetchBatchNum()
          .then(response => {
            if (response.Code === 2000) {
              resolve(response.Data);
            } else {
              this.showNotify('error', response.Message);
            }
          })
          .catch(err => {
            console.log(err);
            reject(false);
          });
      });
    },
    getPageParams() {
      this.temp = Object.assign({}, this.$route.params);
      if (this.temp.BarID) {
        // 编辑
        this.editStatus = 'edit'
      } else {
        // 新增
        this.editStatus = 'create'
      }
    },
    modalFormOk(material) {
      console.log(material[0].MATNR, 111);
      this.$nextTick(() => {
        // this.temp.ItemCode = material[0].MATNR
        // this.temp.ItemName = material[0].MAKTX
        // this.temp.Unit = material[0].MEINS
        this.$set(this.temp, 'ItemCode', material[0].MATNR);
        this.$set(this.temp, 'ItemName', material[0].MAKTX);
        this.$set(this.temp, 'Unit', material[0].MEINS)
      })
    },
    handleMaterialDialogBtnClick() {
      this.$refs.materialDlg.edit()
    },
    handleConfirm() {
      if (!this.temp.ItemCode) {
        this.showNotify('warning', 'Common.selectMaterial');
        return;
      }
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.startLoading();
          var isChoose = this.printTemplateOptions.find(
            x => x.TempleteDesc == this.temp.PrintTemplate
          );
          if (isChoose) { // 判断是否是key值，如果是key值换成value值
            this.temp.PrintTemplate = isChoose.TempleteFile;
          }
          if (this.editStatus == 'edit') {
            update(this.temp).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.backTo('MM.MM_BarCode');
              } else {
                this.showNotify('error', response.Message);
              }
              this.endLoading()
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          } else {
            // this.temp.BatchNum = this.BatchNum
            addList(this.temp).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.backTo('MM.MM_BarCode');
              } else {
                this.showNotify('error', response.Message);
              }
              this.endLoading()
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          }
        }
      });
    },
    handleCancel() {
      this.backTo('MM.MM_BarCode');
    }
  }
};
</script>
