<template>
  <div class="app-container">
    <div class="filter-container">
      <!--<el-button v-waves class="filter-item" type="primary" icon="el-icon-delete" @click="handleDelDetail">{{ $t('Common.empty') }}</el-button>-->
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleConfirm"
      >{{ $t('Common.confirm') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        icon="el-icon-back"
        @click="handleCancel"
      >{{ $t('Common.cancel') }}</el-button>
    </div>
    <p>
      <label style="width: 100%">{{ $t('ui.MM.BarCode.title') }}</label>
    </p>
    <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.BarCode')" prop="BarCode">
            <el-input v-model="temp.BarCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.BatchNum')" prop="BatchNum">
            <el-input v-model="temp.BatchNum" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" :disabled="editStatus==='edit'" readonly>
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="editStatus==='edit'"
                @click="handleMaterialDialogBtnClick"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.MM.BarCode.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" disabled />
          </el-form-item>
        </el-col>
        <!--<el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.ItmsGrpName')" prop="ItmsGrpName">
            <el-input v-model="temp.ItmsGrpName" disabled />
          </el-form-item>
        </el-col>-->
      </el-row>
      <el-row :gutter="10">
        <!--<el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.ItmsGrpCode')" prop="ItmsGrpCode">
            <el-input v-model="temp.ItmsGrpCode" disabled />
          </el-form-item>
        </el-col>-->
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.PTime')" prop="PTime">
            <el-date-picker v-model="temp.PTime" type="date" :disabled="editStatus==='edit'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.Unit')" prop="Unit">
            <el-input v-model="temp.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.Qty')" prop="Qty">
            <el-input-number v-model="temp.Qty" :min="0" controls-position="right" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.BarCode.PrintTemplate')" prop="PrintTemplate">
            <el-select v-model="temp.PrintTemplate" filterable style="width: 200px">
              <el-option
                v-for="item in printTemplateOptions"
                :key="item.TempleteDesc"
                :label="item.TempleteDesc"
                :value="item.TempleteFile"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('Common.printQty')" prop="printQty">
            <el-input-number
              v-model="temp.printQty"
              :min="0"
              controls-position="right"
              :disabled="editStatus==='edit'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item :label="$t('Common.Remark')" prop="Remark">
            <el-input v-model="temp.Remark" type="textarea" :autosize="{minRows: 2, maxRows: 5}" @input="change()" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <MaterialDlg :show.sync="dlgVisible" :title="$t('Common.selectMaterial')" :isMultiple="false" /> -->
    <MaterialDlg
      ref="materialDlg"
      :show.sync="dlgVisible"
      :title="$t('Common.selectMaterial')"
      :is-multiple="false"
      @close="materialSelected"
    />
  </div>
</template>

<script>
import {
  fetchBatchNum,
  fetchEarliestStock,
  addList,
  update
} from '@/api/MM/MM_BarCode';
import { fetchTemplate as fetchPrintTemplate } from '@/api/MD/MD_LabelTemplate';
import waves from '@/directive/waves'; // waves directive
import MaterialDlg from '@/components/FLD/MaterialDlg';

export default {
  name: 'MM.MM_BarCodeEdit',
  directives: {
    waves
  },
  components: {
    MaterialDlg
  },
  data() {
    return {
      temp: {
        BarID: '',
        BarCode: '',
        BatchNum: undefined,
        PTime: new Date(),
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: undefined,
        Unit: '',
        PrintTemplate: '',
        printQty: undefined,
        Remark: ''
      },
      rules: {
        // 表单校验逻辑
        // PrintTemplate: [{
        //     required: true,
        //     message: this.$i18n.t('Common.IsRequired'),
        //     trigger: 'change'
        // }],
        Qty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ],
        printQty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          }
        ],
        // ItemCode: [
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   }
        // ],
        PTime: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ]
      },
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        keyword: ''
      },
      printTemplateOptions: [],
      editStatus: 'create',
      dlgVisible: false
    };
  },
  created() {
    this.getPrintTemplateList();
    this.getPageParams();

    console.log(this.editStatus);
    if (this.editStatus == 'create') {
      // fetchBatchNum().then(response => {
      //   if (response.Code === 2000) {
      //     this.temp.BatchNum = response.Data;
      //   } else {
      //     this.showNotify("error", response.Message);
      //   }
      // });
    } else {
      this.temp.printQty = 1;
    }
  },
  methods: {
    getPrintTemplateList() {
      fetchPrintTemplate({ templateType: 11 }).then(response => {
        if (response.Code === 2000) {
          this.printTemplateOptions = response.Data;
          this.temp.PrintTemplate = this.printTemplateOptions[0].TempleteDesc;
          this.temp.printQty = 1;
        }
      });
    },
    change(e) {
      this.$forceUpdate()
    },
    getBatchNum() {
      return new Promise((resolve, reject) => {
        fetchBatchNum()
          .then(response => {
            if (response.Code === 2000) {
              resolve(response.Data);
            } else {
              this.showNotify('error', response.Message);
            }
          })
          .catch(err => {
            console.log(err);
            reject(false);
          });
      });
    },
    getPageParams() {
      this.temp = Object.assign({}, this.$route.params);
      if (this.temp.BarID) {
        // 编辑
        this.editStatus = 'edit'
      } else {
        // 新增
        this.editStatus = 'create'
      }
    },
    resetTemp() {
      this.temp = {
        BarID: '',
        BarCode: '',
        BatchNum: undefined,
        PTime: undefined,
        ItemCode: '',
        ItemName: '',
        ItmsGrpCode: '',
        ItmsGrpName: '',
        Qty: 0,
        Unit: '',
        PrintTemplate: '',
        printQty: 1
      };
      this.listQuery.keyword = ''
    },
    materialSelected(material) {
      this.listLoading = true;
      if (material) {
        this.temp = Object.assign(this.temp, material);
        this.temp.Remark = '';
        this.$forceUpdate();
        // 查询库存
        fetchEarliestStock({
          itemCode: this.temp.ItemCode
        }).then(response => {
          if (response.Code === 2000) {
            var newData = response.Data;
            newData.Qty = 0;
            Object.assign(this.temp, newData); // Remark
            // this.temp.Remark = "";
          } else {
            console.log('out of stock');
          }
          this.listLoading = false;
        });
        if (this.editStatus == 'create') {
        }
      }
    },
    handleMaterialDialogBtnClick() {
      this.dlgVisible = true;
    },
    handleConfirm() {
      if (!this.temp.ItemCode) {
        this.showNotify('error', 'Common.selectMaterial');
        return;
      }
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          var isChoose = this.printTemplateOptions.find(
            x => x.TempleteDesc == this.temp.PrintTemplate
          );
          if (isChoose) { // 判断是否是key值，如果是key值换成value值
            this.temp.PrintTemplate = isChoose.TempleteFile;
          }
          if (this.editStatus == 'edit') {
            update(this.temp).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.backTo('MM.MM_BarCode');
              } else {
                this.showNotify('error', response.Message);
              }
            });
          } else {
            // this.temp.BatchNum = this.BatchNum
            addList({
              Data: this.temp,
              Count: this.temp.printQty
            }).then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.backTo('MM.MM_BarCode');
              } else {
                this.showNotify('error', response.Message);
              }
            });
          }
        }
      });
    },
    handleCancel() {
      this.backTo('MM.MM_BarCode');
    }
  }
};
</script>
