<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.Remark"
        class="filter-item"
        size="small"
        placeholder="备注"
        style="width: 140px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.PrintTemplate"
        class="filter-item"
        size="small"
        placeholder="仓库"
        style="width: 140px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <!-- <el-button v-waves v-permission="{ name: 'MM.MM_BarCode.Edit' }" class="filter-item" type="primary"
        icon="el-icon-edit" size="small" @click="handleEdit" :disabled="selective">{{ $t('Common.edit') }}</el-button> -->
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入正式排产</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.DownLoadTemp' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModel"
      >下载正式排产模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.Print' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handlePrint"
      >{{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.ImportMateriel' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImportMateriel"
      >导入物料模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.DownLoadTempMateriel' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModelMateriel"
      >下载物料模板</el-button>
      <!-- <el-button v-waves v-permission="{ name: 'MM.MM_BarCode.PrintMateriel' }" class="filter-item" type="primary"
        icon="el-icon-document" size="small" @click="handlePrintMateriel" :disabled="deletable">打印物料号</el-button> -->
      <el-dropdown
        v-waves
        v-permission="{ name: 'MM.MM_BarCode.PrintMateriel' }"
        class="filter-item"
        split-button
        type="primary"
        size="small"
        style="margin-left: 10px;"
        @click="handlePrintMateriel"
        @command="handleCommand"
      >
        打印物料号
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="有条码" :disabled="deletable">有条码</el-dropdown-item>
          <el-dropdown-item command="无条码" :disabled="deletable">无条码</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!-- <el-button v-waves class="filter-item" type="primary" icon="el-icon-menu" @click="handleCompose">{{ $t('Common.compose') }}</el-button> -->
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      :height="tableHeight"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="条码" prop="BarCode" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="批次" prop="BatchNum" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="产品件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品描述" prop="ItemName" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合同号" prop="BaseNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部件代码" prop="PartCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PartCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="合同号" prop="PrintTemplate" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="仓库" prop="PrintTemplate" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PrintTemplate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 导入物料excel -->
    <el-dialog title="导入物料" :visible.sync="dialogImprotVisableMateriel" width="50%">
      <el-upload
        ref="uploadMateriel"
        class="upload-demo"
        action
        :on-change="handleChangeMateriel"
        :on-remove="handleRemoveMateriel"
        :on-exceed="handleExceedMateriel"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelMateriel">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcelMateriel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  formatDateTime
} from '@/utils'; // 列表内容格式化
import {
  fetchList,
  batchDelete,
  exportExcelFile,
  isScanList,
  printBarCodeToPDF,
  exportExcelModel,
  improtExcelFile,
  PrintItemCode,
  PrintItemCode2,
  ExportToExcelModelItem,
  ImportExcelToDataItem
} from '@/api/MM/MM_BarCode';
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export';

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MM.MM_BarCode',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  rules: [],
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      list: [],
      total: 0,
      listLoading: true,
      isDelete: true,
      listQuery: {
        keyword: '',
        Remark: '',
        PrintTemplate: '',
        PageNumber: 1,
        PageSize: 50,
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      multipleSelection: [],
      isProcessing: false,
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      dialogImprotVisableMateriel: false,
      fileTempMateriel: null,
      uploadExcelDataMateriel: [],
      tableHeight: 300
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM/MM_BarCode') {
        this.handleFilter();
      }
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDateTime,
    getList() {
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      console.log(query);
      fetchList(query).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getOrderList() {},
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 50;
      this.getList();
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = 'BarCode' + ' desc';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleCreate() {
      this.routeTo('MM.MM_BarCodeEditNew');
    },
    handleEdit() {
      const selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        let switchBtn = true;
        selectRows.some(v => {
          if (this.$store.getters.userRole !== 1) {
            if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
              this.showNotify('warning', '产品件号为：' + v.ItemCode + '信息，您无权操作');
              switchBtn = false;
              this.isProcessing = false;
              return true;
            }
          }
        });
        if (switchBtn) {
          this.routeTo('MM.MM_BarCodeEditNew', Object.assign(selectRows[0]));
        }
      }
    },
    isMMInScanList() {
      const BarCodeList = [];
      this.multipleSelection.forEach(element => {
        BarCodeList.push(element.BarCode);
      });
      isScanList({
        barCodeList: BarCodeList
      }).then(response => {
        if (response.Code === 2000) {
          if (response.Data > 0) {
            this.showNotify('error', 'Common.ErrNotDelete');
          } else {
            this.handleDelete();
          }
        }
      });
    },
    isMMInScanListEdit() {
      const BarCodeList = [];
      this.multipleSelection.forEach(element => {
        BarCodeList.push(element.BarCode);
      });
      isScanList({
        barCodeList: BarCodeList
      }).then(response => {
        if (response.Code === 2000) {
          if (response.Data > 0) {
            this.showNotify('error', 'Common.ErrNotDelete');
          } else {
            this.handleEdit();
          }
        }
      });
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        let switchBtn = true;
        selectRows.some(v => {
          if (this.$store.getters.userRole !== 1) {
            if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
              this.showNotify('warning', '产品件号为：' + v.ItemCode + '信息，您无权操作');
              switchBtn = false;
              this.isProcessing = false;
              return true;
            }
          }
        });
        if (switchBtn) {
          this.$confirm(
            this.$i18n.t('Common.batchDeletingConfirm'),
            this.$i18n.t('Common.tip'), {
              confirmButtonText: this.$i18n.t('Common.affirm'),
              cancelButtonText: this.$i18n.t('Common.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.isProcessing = true;
            const arrRowsID = selectRows.map(function(v) {
              return v.BarID;
            });

            // 删除逻辑处理
            batchDelete(arrRowsID)
              .then(response => {
                if (response.Code === 2000) {
                  this.showNotify('success', 'Common.deleteSuccess');
                  this.handleFilter(1);
                } else {
                  this.showNotify('error', response.Message);
                }
                this.isProcessing = false;
              })
              .catch(error => {
                this.isProcessing = false;
              });
          });
        }
      }
    },
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        Remark: this.listQuery.Remark,
        PrintTemplate: this.listQuery.PrintTemplate
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '物料标签打印管理');
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    handlePrint() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        printBarCodeToPDF(selectRows.map(x => x.BarCode)).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    handlePrintMateriel() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        PrintItemCode(selectRows.map(x => x.BarID)).then(response => {
          console.log(response);
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      } else {
        this.isProcessing = false;
      }
    },
    handleCommand(command) {
      this.isProcessing = true;
      if (command === '有条码') {
        this.handlePrintMateriel()
      } else if (command === '无条码') {
        const selectRows = this.multipleSelection;
        if (this.checkMultiSelection(selectRows)) {
          PrintItemCode2(selectRows.map(x => x.BarID)).then(response => {
            console.log(response);
            window.open(this.API.BaseURL + response.Data.PrintedPDF);
            this.isProcessing = false;
          }).catch(error => {
            this.isProcessing = false;
          });
        }
      }
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancel() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },

    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    /** ******物料导入导出****************/
    // 导出excel模板
    handleExportModelMateriel() {
      ExportToExcelModelItem().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImportMateriel() {
      this.dialogImprotVisableMateriel = true;
      this.fileTempMateriel = null;
      this.uploadExcelDataMateriel = [];
      this.$nextTick(() => {
        this.$refs['uploadMateriel'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelMateriel() {
      this.dialogImprotVisableMateriel = false;
    },
    // 回调导入excel表转换list
    getImprotDataMateriel(data) {
      this.uploadExcelDataMateriel = data;
    },

    // 导入excel数据到后台
    uploadExcelMateriel() {
      if (this.uploadExcelDataMateriel.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        return;
      }
      this.dialogImprotVisableMateriel = false;
      this.isProcessing = true;
      ImportExcelToDataItem(this.uploadExcelDataMateriel)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChangeMateriel(file, fileList) {
      this.fileTempMateriel = file.raw;
      if (this.fileTempMateriel) {
        if (
          this.fileTempMateriel.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTempMateriel.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotDataMateriel);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceedMateriel() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemoveMateriel(file, fileList) {
      this.fileTempMateriel = null;
      const _this = this;
      _this.uploadExcelDataMateriel = [];
    }
    /** ******物料导入导出****************/
  }
};
</script>
