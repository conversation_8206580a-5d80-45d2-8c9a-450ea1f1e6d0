<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="isPosted"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleSelectChangeValue"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.Posting' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-edit"
        :disabled="postable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.Review' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-check"
        :disabled="reviewable"
        @click="handleReivew(1)"
      >{{ $t('Common.review') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.UnReview' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-close"
        :disabled="unreviewable"
        @click="handleReivew(2)"
      >{{ $t('Common.reject') }}
      </el-button>
      <!-- :loading="downloadLoading" -->
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutScan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column v-if="false" :label="$t('ui.MM.OutScan.ScanID')" prop="ScanID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.DocNum')" prop="DocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.BoxBarCode')" prop="BoxBarCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.BarCode')" prop="BarCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.PTime')" prop="PTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PTime | date }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.ItemCode')" prop="ItemCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.ItemName')" prop="ItemName" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.MM.OutScan.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.OutScan.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.MM.OutScan.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.BatchNum')" prop="BatchNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.MM.OutScan.RTypeCode')" prop="RTypeCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.RTypeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.RTypeName')" prop="RTypeName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.RTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.Subject')" prop="Subject" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Subject }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.CostCenter')" prop="CostCenter" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.CostCenter }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        v-if="false"
        :label="$t('ui.MM.OutScan.OutWhsCode')"
        prop="OutWhsCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutWhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.OutScan.OutWhsName')"
        prop="OutWhsName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.OutScan.OutRegionCode')"
        prop="OutRegionCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.OutRegionName')" prop="OutRegionName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.OutRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.OutScan.OutBinLocationCode')"
        prop="OutBinLocationCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.OutScan.OutBinLocationName')"
        prop="OutBinLocationName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OutBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.InScan.IsCheck')"
        prop="IsCheck"
        align="center"
        width="120"
        fixed="right"
        :formatter="statusFormatter"
      >
        <!-- <template slot-scope="scope">
          <span>{{ scope.row.IsCheck }}</span>
        </template>-->
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.IsPosted')" prop="IsPosted" align="center" width="120" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.PDtype')" prop="PDtype" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PDtype }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.ERPDocNum')" prop="ERPDocNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ERPDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.OutScan.PostUser')" prop="PostUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.OutScan.PostTime')"
        prop="PostTime"
        align="center"
        width="120"
        :formatter="formatDate"
      />

      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  formatDate,
  formatDateTime
} from '@/utils';
import {
  fetchPage,
  batchDelete,
  postToSAP,
  reivew,
  exportExcelFile
} from '@/api/MM/MM_OutScan';
import {
  exportToExcel
} from '@/utils/excel-export';

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MM.MM_OutScan',
  directives: {
    waves,
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPosted: false, // 暂未加入搜索条件
      changeSelectValue: false,
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      dateRangeValue: [
        new Date(),
        new Date()
      ],

      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: ''
      },
      multipleSelection: [],
      statusOptions: [],
      isProcessing: false,
      hasPostedData: false
    };
  },
  computed: {

    selective() {
      let i = this.multipleSelection.length;
      if (i === 0 || this.hasPostedData) return true;
      while (i--) {
        if (this.multipleSelection[i].IsCheck > 0) {
          return true;
        }
      }
      return false;
    },
    deletable() {
      let i = this.multipleSelection.length;
      if (i === 0 || this.hasPostedData) return true;
      while (i--) {
        if (this.multipleSelection[i].IsCheck > 0) {
          return true;
        }
      }
      return false;
    },
    postable() {
      let i = this.multipleSelection.length;
      if (i === 0 || this.hasPostedData) return true;
      while (i--) {
        if (this.multipleSelection[i].IsCheck !== 1) {
          return true;
        }
      }
      return false;
    },
    reviewable() {
      let i = this.multipleSelection.length;
      if (i === 0 || this.hasPostedData) return true;
      while (i--) {
        if (this.multipleSelection[i].IsCheck > 0) {
          return true;
        }
      }
      return false;
    },
    unreviewable() {
      let i = this.multipleSelection.length;
      if (i === 0 || this.hasPostedData) return true;
      while (i--) {
        if (this.multipleSelection[i].IsCheck > 0) {
          return true;
        }
      }
      return false;
    }
  },
  created() {
    this.handleFilter();
    this.getDict('MMIO001').then(data => {
      this.statusOptions = data;
    });
  },
  methods: {
    statusFormatter(row, column) {
      var status = this.statusOptions.find(x => x.EnumKey == row.IsCheck + 1);
      return status.EnumValue;
    },
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      var exportQuery = {
        PageNumber: this.listQuery.PageNumber,
        PageSize: this.listQuery.PageSize,
        keyword: this.listQuery.keyword,
        dateTimes: this.dateRangeValue,
        isPosted: this.changeSelectValue
      };
      fetchPage(exportQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleSelectChangeValue(val) {
      this.changeSelectValue = val;
      this.getList();
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleCreate() {
      this.routeTo('MM.MM_OutScanEdit');
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleEdit() {
      var selectRows = this.multipleSelection;
      var row = selectRows.find(obj => obj.IsPosted);
      if (row) {
        this.showNotify('error', 'Common.handlingPostedDataNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck == 1);
      if (row) {
        this.showNotify('error', 'Common.reviewNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck == 2);
      if (row) {
        this.showNotify('error', 'Common.reviewTwoNotPermitted');
        return false;
      }
      if (this.checkSingleSelection(selectRows)) {
        this.routeTo('MM.MM_OutScanEdit', Object.assign(selectRows[0]));
      }
    },
    handleDelete() {
      // 删除功能，可能有bug，待测试
      var selectRows = this.multipleSelection;
      // todo: 已过账不允许删除
      var row = selectRows.find(obj => obj.IsPosted);
      if (row) {
        this.showNotify('error', 'Common.handlingPostedDataNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck == 1);
      if (row) {
        this.showNotify('error', 'Common.reviewNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck == 2);
      if (row) {
        this.showNotify('error', 'Common.reviewTwoNotPermitted');
        return false;
      }
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          var arrRowsID = selectRows.map(function(v) {
            return v.ScanID;
          });

          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      this.isProcessing = true;
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.dateRangeValue,
        state: this.changeSelectValue
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '其他发货记录');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleReivew(val) {
      var selectRows = this.multipleSelection;
      console.log(selectRows);
      var row = selectRows.find(obj => obj.IsPosted);
      if (row) {
        this.showNotify('error', 'Common.handlingPostedDataNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck == 1);
      if (row) {
        this.showNotify('error', 'Common.reviewNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck == 2);
      if (row) {
        this.showNotify('error', 'Common.reviewTwoNotPermitted');
        return false;
      }
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.actionConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.showNotify('warning', 'Common.syncStart');
          this.isProcessing = true;
          reivew({
            Data: selectRows,
            review: val
          })
            .then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.operationSuccess');
                this.handleFilter(1);
                this.isProcessing = false;
              } else {
                this.showNotify('error', response.Message);
                this.handleFilter(1);
                this.isProcessing = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        });
      }
    },
    handlePosting() {
      var selectRows = this.multipleSelection;
      var row = selectRows.find(obj => obj.IsPosted);
      if (row) {
        this.showNotify('error', 'Common.handlingPostedDataNotPermitted');
        return false;
      }
      row = selectRows.find(obj => obj.IsCheck != 1);
      if (row) {
        this.showNotify('error', 'Common.rejectNotPostToSAP');
        return false;
      }
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.postToSAPConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.showNotify('warning', 'Common.syncStart');
          this.isProcessing = true;
          postToSAP({
            Data: selectRows
          })
            .then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.operationSuccess');
                this.handleFilter(1);
                this.isProcessing = false;
              } else {
                this.showNotify('error', response.Message);
                this.isProcessing = false;
              }
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        });
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted == true;
      });
      if (postedData != undefined) {
        this.hasPostedData = true;
        console.log(this.hasPostedData);
      }
    }
  }
};
</script>
