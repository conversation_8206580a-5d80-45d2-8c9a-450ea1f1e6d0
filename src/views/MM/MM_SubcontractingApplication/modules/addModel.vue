<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form :model="model">
        <!-- <el-form-item label="供应商名称" label-width="120px" v-if="!model.BaseNum">
          <el-select v-model="model.SupplierName" filterable  placeholder="请选择" @change="changeSupplierName" style="width: 100%;">
            <el-option
              v-for="item in options"
              :key="item.SupplierName"
              :label="item.SupplierCode+'-'+item.SupplierName"
              :value="item.SupplierName">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商编号" label-width="120px" v-if="!model.BaseNum">
          <el-input v-model="model.SupplierCode" disabled></el-input>
        </el-form-item> -->
        <el-form-item label="领料单数量" label-width="120px">
          <el-input v-model="model.Qty" />
        </el-form-item>
        <el-form-item label="备注" label-width="120px">
          <el-input v-model="model.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
        <!-- <el-form-item label="单位" label-width="120px" v-if="!model.BaseNum">
          <el-input v-model="model.Unit"></el-input>
        </el-form-item> -->
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetSRM_SupplierInfo
} from '@/api/MM/MM_SubcontractingApplication';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        // SupplierName: '',
        // SupplierCode: '',
        // Unit: '',
        Qty: '',
        Remark: ''
      },
      options: [],
      Qty: ''
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.Qty = record.Qty;
      // this.GetSRM()
      this.drawer = true;
      this.model = Object.assign({}, record);
    },
    handleSave() {
      this.$emit('ok', this.model);
      this.drawer = false;
    },
    GetSRM() {
      GetSRM_SupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.options = res.Data;
        }
      })
    },
    changeSupplierName(e) {
      const obj = this.options.find(v => v.SupplierName === e);
      this.model.SupplierCode = obj.SupplierCode;
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (e > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return
      }
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
