<template>
  <el-dialog title="采购信息明细" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.BaseNum"
        size="small"
        class="filter-item"
        style="width: 200px"
        placeholder="采购订单"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.Supplier"
        size="small"
        class="filter-item"
        style="width: 200px"
        placeholder="供应商"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
      <el-button v-waves size="small" :disabled="deletable" class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
        添加</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="采购订单" prop="EBELN" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商编号" prop="LIFNR" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="NAME1" align="center" width="200" show-overflow-tooltip />
      <!-- <el-table-column label="状态码" prop="ZZSTACODE" align="center" show-overflow-tooltip /> -->
      <el-table-column label="订单行号" prop="EBELP" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="MATNR" align="center" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="TXZ01" align="center" width="200" show-overflow-tooltip />
      <!-- <el-table-column label="库存编号" prop="LGORT" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="库存名称" prop="LGOBE" align="center" show-overflow-tooltip /> -->
      <el-table-column label="数量" prop="MENGE" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="领料单数量" align="center" width="100">
        <template slot-scope="scope">
          <el-input v-model="scope.row.Qty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column> -->
      <el-table-column label="单位" prop="MEINS" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MEINS ==='KAR'">CAR</span>
          <span v-else-if="scope.row.MEINS ==='PAK'">PAC</span>
          <span v-else-if="scope.row.MEINS ==='ST'">PC</span>
          <span v-else>{{ scope.row.MEINS }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料组" prop="MATKL" align="center" show-overflow-tooltip /> -->

    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetXZSAP_PurchaseOrder,
  GetXZSAP_EKKO
} from '@/api/MM/MM_SubcontractingApplication';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        BaseNum: '',
        Supplier: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      puchaseData: []
    }
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {},
  methods: {
    add() {
      this.listQuery = {
        keyword: '',
        BaseNum: '',
        Supplier: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record)
    },
    handleCustomerRowSelectEvent(selection) {
      const data = [];
      if (selection.length >= 1) {
        selection.map(res => {
          data.push(res.LIFNR)
        });
        const num = [...new Set(data)].length;
        console.log('num:' + num);
        if (num > 1) {
          this.showNotify('warning', '请勿选择多个供应商');
        } else if (this.dataList.length === 0) {
          this.multipleSelection = selection
          // this.GetXZSAPEKKO()
        } else {
          let switchBtn = true;
          selection.some(v => {
            this.dataList.some(res => {
              if (v.EBELN + v.EBELP === res.onlyIds) {
                this.showNotify('warning', '采购单号为：' + v.EBELN + '已选择，请勿重复选择');
                switchBtn = false;
                return true;
              }
              if (res.SupplierCode !== '' && v.LIFNR !== res.SupplierCode) {
                this.showNotify('warning', '请勿选择多个供应商');
                switchBtn = false;
                return true;
              }
            })
          });
          if (switchBtn) {
            this.multipleSelection = selection;
            // this.GetXZSAPEKKO()
          }
        }
      } else {
        this.multipleSelection = selection;
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetXZSAP_PurchaseOrder(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      // this.$emit('ok', this.puchaseData)
      // this.dialogCustomerFormVisible = false;
      this.GetXZSAPEKKO();
      this.dialogCustomerFormVisible = false;
    },
    GetXZSAPEKKO() {
      GetXZSAP_EKKO(this.multipleSelection).then(res => {
        if (res.Code === 2000) {
          if (res.Data !== null && res.Data.length > 0) {
            this.puchaseData = res.Data;
            this.$emit('ok', this.puchaseData);
          } else {
            this.showNotify('warning', res.Message);
          }
        }
      })
    },
    handleAdd() {
      // let switchBtn = true;
      // this.puchaseData.some(res => {
      //   console.log(res)
      //   if (res.Qty === undefined || res.Qty === 0 || res.Qty === '0') {
      //     this.showNotify("warning", '领料单数量不能为空或者为零');
      //     switchBtn = false
      //     return true
      //   }
      //   if (res.Qty > res.MENGE) {
      //     this.showNotify("warning", '领料单数量不能大于库存数量');
      //     switchBtn = false
      //     return true
      //   }
      // })
      // if (switchBtn) {
      // this.$emit('ok', this.puchaseData)
      // }
      this.GetXZSAPEKKO();
    },
    inputOutQty(e) {
      if (e.Qty <= 0) {
        this.showNotify('warning', '领料单数量不得小于等于0');
        return;
      } else if (e.Qty > e.MENGE) {
        this.showNotify('warning', '领料单数量不得大于库存数量');
        return;
      }
    }
  }
}
</script>

<style scoped>
</style>
