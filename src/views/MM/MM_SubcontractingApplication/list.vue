<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <!-- <el-select v-model="listQuery.isPosted" filterable :placeholder="$t('Common.postingStatus')" style="width: 140px"
        class="filter-item">
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select> -->
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_SubcontractingApplication.Add' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_SubcontractingApplication.Edit' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-edit"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >
        {{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_SubcontractingApplication.Delete'}"
        class="filter-item"
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_SubcontractingApplication.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        :disabled="canNotUpdate"
        @click="handlePrint"
      >
        {{ $t('Common.print') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_SubcontractingApplication.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="Status" align="center" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.Status === 0">未开始</span>
          <span v-if="scope.row.Status === 1">进行中</span>
          <span v-if="scope.row.Status === 2">已完成</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>委外领料明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="产品编号" prop="MatnrCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="产品名称" prop="MatnrName" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="产品数量" prop="MatnrQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="领料单数量" prop="Qty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="实际领料数量" prop="DispatchQty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="未领料数量" prop="" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Qty - scope.row.DispatchQty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="Unit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="仓库" prop="WhsName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="区域" prop="RegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="库位" prop="BinLocationName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="状态" prop="Status" align="center" >
        <template slot-scope="scope">
          <span v-if="scope.row.Status === 0">未开始</span>
          <span v-if="scope.row.Status === 1">进行中</span>
          <span v-if="scope.row.Status === 2">已完成</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160"
        :formatter="formatDateTime" /> -->

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  GetPageList,
  batchDelete,
  exportExcelFile,
  printToPDF,
  CheckDeleteAndUpdate,
  CheckDelete
} from '@/api/MM/MM_SubcontractingApplication';

export default {
  name: 'MM.MM_SubcontractingApplication',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM_SubcontractingApplication') {
        this.getList();
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (this.checkSingleSelection(selectRows)) {
        const docNums = selectRows.map(v => v.DocNum);
        console.log(docNums);
        printToPDF({
          docNums: docNums
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '委外领料申请');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.getList();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      const postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        const query = {
          Type: 'delete',
          DocNums: arrRowsID
        };
        CheckDeleteAndUpdate(query).then(res => {
          // CheckDelete(arrRowsID).then(res=>{
          if (res.Code === 2000) {
            if (res.Data === null) {
              this.$confirm(
                this.$i18n.t('Common.batchDeletingConfirm'),
                this.$i18n.t('Common.tip'), {
                  confirmButtonText: this.$i18n.t('Common.affirm'),
                  cancelButtonText: this.$i18n.t('Common.cancel'),
                  type: 'warning'
                }
              ).then(() => {
                this.isProcessing = true;
                // 删除逻辑处理
                batchDelete(arrRowsID)
                  .then(res => {
                    this.isProcessing = false;
                    if (res.Code === 2000) {
                      this.showNotify('success', 'Common.deleteSuccess');
                    } else {
                      this.showNotify('error', res.Message);
                    }
                    this.getList();
                  })
                  .catch(error => {
                    this.isProcessing = false;
                  });
              });
            } else {
              const name = res.Data.toString();
              this.showNotify('warning', '单号为：' + name + '的信息中包含已发料的信息，请重新选择信息');
            }
          }
        })
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = {
        keyword: this.currentRow.DocNum,
        PageNumber: this.listDetailQuery.PageNumber,
        PageSize: this.listDetailQuery.PageSize
      };
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('MM.MM_SubcontractingApplicationDetail');
    },
    handleUpdate() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
      });
      if (switchBtn) {
        this.routeTo('MM.MM_SubcontractingApplicationDetail', this.multipleSelection[0]);
      }
    }
  }
};
</script>
