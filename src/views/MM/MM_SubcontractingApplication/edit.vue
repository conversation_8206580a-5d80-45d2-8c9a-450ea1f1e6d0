<template>
  <div class="app-container">
    <p>
      <label style="width:100%">委外领料登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="单号">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="供应商">
            <el-select
              v-model="searchQuery.SupplierCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              :disabled="SupplierDisabled"
              @change="changeSupplierName"
            >
              <el-option
                v-for="item in SupplierOptions"
                :key="item.SupplierCode"
                :label="item.SupplierCode+'-'+item.SupplierName"
                :value="item.SupplierCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购信息">
            <el-input v-model="searchQuery.purchase" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomerPurchase" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料信息">
            <el-input v-model="searchQuery.materials" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomerMaterials" />
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />

      <el-table-column label="产品编号" prop="MatnrCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="产品名称" prop="MatnrName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="产品数量" prop="MatnrQty" align="center" width="80" show-overflow-tooltip />

      <!-- <el-table-column v-if="" label="采购订单" prop="PurchaseOrder" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="领料单数量" prop="Qty" align="center" width="90" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
      <el-table-column
        fixed="right"
        :label="$t('ui.PO.PO_ReturnScanDetail.operation')"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <add-select-puchase-model ref="modalFormPurchase" :data-list="list" @ok="modalFormOkPurchase" />
    <add-select-materials-model ref="modalFormMaterials" :data-list="list" @ok="modalFormOkMaterials" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectPuchaseModel from './modules/addSelectPuchaseModel'
import AddSelectMaterialsModel from './modules/addSelectMaterialsModel'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  DeleteDetail,
  update,
  GetSRM_SupplierInfo
} from '@/api/MM/MM_SubcontractingApplication';
export default {
  name: 'MM.MM_SubcontractingApplicationDetail',
  components: {
    Pagination,
    AddSelectPuchaseModel,
    AddSelectMaterialsModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        purchase: '',
        materials: '',
        DocNum: '',
        SupplierName: '',
        SupplierCode: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {

      },
      editStatus: 'create',
      delList: [],
      SupplierOptions: [],
      SupplierDisabled: true
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetSRM()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DetailID) {
            v.IsDelete = true;
            this.delList.push(v.DetailID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList);
          const data = [];
          this.list.map(res => {
            if (res.MatnrCode) {
              data.push(res.MatnrCode);
            }
          });
          const num = [...new Set(data)].length;
          console.log('num:' + num);
          if (num > 0) {
            this.SupplierDisabled = true;
          } else {
            this.SupplierDisabled = false;
            this.searchQuery.SupplierCode = '';
            this.searchQuery.SupplierName = '';
            this.list.forEach((v, index) => {
              this.$set(v, 'SupplierCode', '');
              this.$set(v, 'SupplierName', '');
            });
          }
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择采购信息或者物料信息');
        return;
      }
      if (this.searchQuery.SupplierCode === '' || this.searchQuery.SupplierCode === null) {
        this.showNotify('warning', '请选择供应商信息');
        return;
      }
      let switchBtn = true;
      this.list.some(res => {
        // if (res.SupplierName === '' || res.SupplierName === null) {
        //   this.showNotify("warning", '供应商名称不能为空');
        //   switchBtn = false
        //   return true
        // }
        // if (res.Unit === '' || res.Unit === null) {
        //   this.showNotify("warning", '单位不能为空');
        //   switchBtn = false
        //   return true
        // }
        if (res.Qty === '' || res.Qty === '0' || res.Qty === 0 || res.Qty === null) {
          this.showNotify('warning', '数量不能小于等于0和为空');
          switchBtn = false;
          return true;
        }
      });

      if (switchBtn) {
        this.startLoading();
        const query = {
          DocNum: this.searchQuery.DocNum,
          Remark: this.searchQuery.Remark,
          detailed: this.list,
          deletedetail: this.delList
        };
        if (this.editStatus === 'create') {
          SubmitScanInfo(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('MM.MM_SubcontractingApplication');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        } else {
          update(query).then(res => {
            if (res.Code === 2000) {
              this.backTo('MM.MM_SubcontractingApplication');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading();
          }).catch(err => {
            console.log(err);
            this.endLoading();
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DetailID) {
          if (v.DetailID === record.DetailID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.onlyId === record.onlyId) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomerPurchase() {
      this.$refs.modalFormPurchase.add();
    },
    modalFormOkPurchase(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          BaseNum: v.BaseNum, // 采购订单
          BaseLine: v.BaseLine, // 行号
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          SupplierCode: v.SupplierCode, // 供应商编号
          SupplierName: v.SupplierName, // 供应商名称
          MatnrCode: v.MatnrCode, //  产品编号
          MatnrName: v.MatnrName, //  产品名称
          MatnrQty: v.MatnrQty, //   产品数量
          Qty: v.Qty, // 领料单数量
          Unit: v.Unit, // 单位
          onlyId: v.BaseNum + v.BaseLine + v.ItemCode,
          onlyIds: v.BaseNum + v.BaseLine
        })
      });
      if (data.length > 0) {
        this.searchQuery.SupplierCode = data[0].SupplierCode;
        this.searchQuery.SupplierName = data[0].SupplierName;
        this.SupplierDisabled = true;
      }
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.BaseNum + next.BaseLine + next.ItemCode] ? '' : obj[next.BaseNum + next.BaseLine + next.ItemCode] = true && cur.push(next);
        return cur;
      }, []);
      if (this.list.length > 0) {
        this.list.forEach((v, index) => {
          if (v.SupplierCode === '') {
            this.$set(v, 'SupplierCode', this.searchQuery.SupplierCode);
            this.$set(v, 'SupplierName', this.searchQuery.SupplierName);
          }
        });
      }
    },
    selectCustomerMaterials() {
      this.$refs.modalFormMaterials.add();
    },
    modalFormOkMaterials(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          BaseNum: '', // 采购订单
          BaseLine: '', // 行号
          ItemCode: v.MATNR, // 物料件号
          ItemName: v.MAKTX, // 物料名称
          SupplierCode: '', // 供应商编号
          SupplierName: '', // 供应商名称
          MatnrCode: '', //  产品编号
          MatnrName: '', //  产品名称
          MatnrQty: '', //   产品数量
          Qty: v.Qty || '', // 领料单数量
          Unit: v.MEINS, // 单位
          onlyId: v.MATNR
        })
      });
      if (this.searchQuery.SupplierCode === '') {
        this.SupplierDisabled = false;
      }

      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.BaseNum + next.BaseLine + next.ItemCode] ? '' : obj[next.BaseNum + next.BaseLine + next.ItemCode] = true && cur.push(next);
        return cur;
      }, []);
      if (this.list.length > 0) {
        this.list.forEach((v, index) => {
          if (this.searchQuery.SupplierCode !== '') {
            this.$set(v, 'SupplierCode', this.searchQuery.SupplierCode);
            this.$set(v, 'SupplierName', this.searchQuery.SupplierName);
          }
        });
      }
    },
    GetSRM() {
      GetSRM_SupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.SupplierOptions = res.Data
        }
      })
    },
    changeSupplierName(e) {
      const obj = this.SupplierOptions.find(v => v.SupplierCode === e);
      this.searchQuery.SupplierName = obj.SupplierName;
      this.list.forEach((v, index) => {
        if (this.searchQuery.SupplierCode !== '') {
          this.$set(v, 'SupplierCode', this.searchQuery.SupplierCode);
          this.$set(v, 'SupplierName', this.searchQuery.SupplierName);
        }
      });
    },
    getDetailList() {
      const query = {
        keyword: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.SubcontractingApplicationID) {
        this.editStatus = 'edit';
        this.getDetailList()
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    }
  }
}
</script>
