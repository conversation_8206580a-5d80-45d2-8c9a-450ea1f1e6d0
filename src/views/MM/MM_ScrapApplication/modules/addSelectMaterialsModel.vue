<template>
  <el-dialog title="物料信息明细" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        :placeholder="$t('Common.keyword')"
        clearable
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
      <el-button
        v-waves
        size="small"
        class="filter-item"
        :disabled="deletable"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >
        添加</el-button>
      <el-select
        v-model="searchQuery.WhsCode"
        size="small"
        class="filter-item"
        filterable
        placeholder="请选择库位"
        @change="changeWhsName"
      >
        <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
      </el-select>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物料件号" prop="MATNR" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="MAKTX" align="center" show-overflow-tooltip />
      <el-table-column label="单位" prop="MEINS" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.MEINS ==='KAR'">CAR</span>
          <span v-else-if="scope.row.MEINS ==='PAK'">PAC</span>
          <span v-else-if="scope.row.MEINS ==='ST'">PC</span>
          <span v-else>{{ scope.row.MEINS }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料组" prop="MATKL" align="center" width="80" show-overflow-tooltip />
      <!-- <el-table-column label="物料组描述" prop="WGBEZ" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料类型描述" prop="MTBEZ" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="外部物料组描述" prop="EWBEZ" align="center" width="120" show-overflow-tooltip /> -->
      <el-table-column label="报废数量" align="center" width="100">
        <template slot-scope="scope">
          <el-input v-model="scope.row.Qty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="采购单位" prop="BSTME" align="center" show-overflow-tooltip />
      <el-table-column label="采购类型" prop="BESKZ" align="center" show-overflow-tooltip />
      <el-table-column label="最小批重" prop="BSTMI" align="center" show-overflow-tooltip />
      <el-table-column label="最大批重" prop="BSTMA" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="重量单位" prop="GEWEI" align="center" width="80" show-overflow-tooltip /> -->
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { GetXZSAP_MARC } from '@/api/MM/MM_BarCode'
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      searchQuery: {
        WhsCode: '',
        WhsName: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: ''
      },
      options: [],
      RegionOptions: [],
      BinLocationOptions: []
    }
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {},
  methods: {
    add() {
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.searchQuery = {
        WhsCode: '',
        WhsName: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: ''
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
      this.GetXZ_SAP();
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      console.log(selection, this.dataList);
      const data = [];
      if (this.dataList.length > 0) {
        let switchBtn = true;
        selection.some(v => {
          this.dataList.some(res => {
            if (v.MATNR === res.onlyId) {
              this.showNotify('warning', '物料件号为：' + v.MATNR + '已选择，请勿重复选择');
              switchBtn = false;
              return true;
            }
          })
        });
        if (switchBtn) {
          this.multipleSelection = selection;
        }
      } else {
        this.multipleSelection = selection;
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter()
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetXZSAP_MARC(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      if (this.multipleSelection.length > 0) {
        if (this.searchQuery.WhsCode === '') {
          this.showNotify('warning', '请选择库位');
          return
        }
        let switchBtn = true;
        this.multipleSelection.some(res => {
          console.log(res);
          if (res.Qty === undefined || res.Qty === 0 || res.Qty === '0') {
            this.showNotify('warning', '领料单数量不能为空或者为零');
            switchBtn = false;
            return true
          }
        });
        this.multipleSelection.forEach(res => {
          this.$set(res, 'WhsCode', this.searchQuery.WhsCode);
          this.$set(res, 'WhsName', this.searchQuery.WhsName);
          this.$set(res, 'RegionCode', this.searchQuery.RegionCode);
          this.$set(res, 'RegionName', this.searchQuery.RegionName);
          this.$set(res, 'BinLocationCode', this.searchQuery.BinLocationCode);
          this.$set(res, 'BinLocationName', this.searchQuery.BinLocationName);
        });
        if (switchBtn) {
          this.$emit('ok', this.multipleSelection);
          this.dialogCustomerFormVisible = false;
        }
      }
    },
    ValidateStockOut() {
      const selectRows = this.multipleSelection;
      const ItemCode = selectRows.map(v => v.ItemCode);
      ValidateStockOut(ItemCode).then(res => {

      })
    },
    handleAdd() {
      if (this.multipleSelection.length > 0) {
        if (this.searchQuery.WhsCode === '') {
          this.showNotify('warning', '请选择库位');
          return;
        }
        let switchBtn = true;
        this.multipleSelection.some(res => {
          console.log(res);
          if (res.Qty === undefined || res.Qty === 0 || res.Qty === '0') {
            this.showNotify('warning', '领料单数量不能为空或者为零');
            switchBtn = false;
            return true;
          }
        });
        this.multipleSelection.forEach(res => {
          this.$set(res, 'WhsCode', this.searchQuery.WhsCode);
          this.$set(res, 'WhsName', this.searchQuery.WhsName);
          this.$set(res, 'RegionCode', this.searchQuery.RegionCode);
          this.$set(res, 'RegionName', this.searchQuery.RegionName);
          this.$set(res, 'BinLocationCode', this.searchQuery.BinLocationCode);
          this.$set(res, 'BinLocationName', this.searchQuery.BinLocationName);
        });
        if (switchBtn) {
          this.$emit('ok', this.multipleSelection);
        }
      }
    },
    inputOutQty(e) {
      if (e.Qty <= 0) {
        this.showNotify('warning', '领料单数量不得小于等于0');
        return;
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.WhsName = this.options.filter(item => item.value === e)[0].label;
      this.getRegion();
    },
    getRegion() {
      if (this.searchQuery.WhsCode) {
        const query = {
          WhsCode: this.searchQuery.WhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.RegionName = res.Data[0].RegionName;
              this.searchQuery.RegionCode = res.Data[0].RegionCode;
              this.getBinLocation();
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.searchQuery.RegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.searchQuery.RegionCode) {
        const query = {
          regionCode: this.searchQuery.RegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.BinLocationName = res.Data[0].BinLocationName;
              this.searchQuery.BinLocationCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.searchQuery.BinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    }
  }
}
</script>

<style scoped>
</style>
