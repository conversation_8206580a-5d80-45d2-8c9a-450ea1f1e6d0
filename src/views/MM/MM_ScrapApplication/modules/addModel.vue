<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" :rules="rules" label-width="80px">
        <el-form-item label="数量" prop="Qty">
          <el-input v-model="model.Qty" @change="changeQty" />
        </el-form-item>
        <!-- <el-form-item label="移动类型" prop="MovementType">
          <el-select v-model="model.MovementType"  filterable placeholder="请选择" @change="changeMovement">
            <el-option v-for="item in MovementOptions" :key="item.EnumValue" :label="item.EnumValue+'-'+item.EnumValue1"
              :value="item.EnumValue">
            </el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="入库名称" prop="InWhsCode">
          <el-select v-model="model.InWhsCode" filterable placeholder="请选择" @change="changeWhsName">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="入库区域" prop="InRegionCode">
          <el-select v-model="model.InRegionCode" filterable placeholder="请选择" @change="changeRegionName">
            <el-option v-for="item in RegionOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入库库位" prop="InBinLocationCode">
          <el-select v-model="model.InBinLocationCode" filterable placeholder="请选择" @change="changeBinLocationName">
            <el-option v-for="item in BinLocationOptions" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="出库名称" prop="OutWhsCode">
          <el-select v-model="model.OutWhsCode" filterable placeholder="请选择" @change="changeOutWhsName">
            <el-option v-for="item in optionsOut" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出库区域" prop="OutRegionCode">
          <el-select v-model="model.OutRegionCode" filterable placeholder="请选择" @change="changeOutRegionName">
            <el-option v-for="item in OutRegionOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出库库位" prop="OutBinLocationCode">
          <el-select v-model="model.OutBinLocationCode" filterable placeholder="请选择" @change="changeOutBinLocationName">
            <el-option v-for="item in OutBinLocationOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input v-model="model.Remark" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetDictionaryForDepReq
} from '@/api/MM/MM_ScrapApplication';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        Qty: '',
        MovementType: '',
        MovementTypeName: '', //  移动类型名称
        InWhsCode: '',
        InWhsName: '',
        InRegionCode: '',
        InRegionName: '',
        InBinLocationCode: '',
        InBinLocationName: '',
        // OutWhsCode: '',
        // OutWhsName: '',
        // OutRegionCode: '',
        // OutRegionName: '',
        // OutBinLocationCode: '',
        // OutBinLocationName: '',
        Remark: ''
      },
      rules: {
        Qty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        MovementType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        InWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InRegionCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InBinLocationCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        OutWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        OutRegionCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        OutBinLocationCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      MovementOptions: [],
      options: [],
      RegionOptions: [],
      BinLocationOptions: [],
      optionsOut: [],
      OutRegionOptions: [],
      OutBinLocationOptions: [],
      Qty: 0
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.Qty = record.sumQty;
      this.model = Object.assign({}, record);
      console.log(this.model.MovementType);
      // this.GetDictionaryForDepReq()
      // this.GetXZ_SAP()
      // this.getRegion()
      // this.getBinLocation()
      // this.GetXZ_SAP_Out()
      // this.getOutRegion()
      // this.getOutBinLocation()
      // if(this.model.MovementType !== undefined){
      //   this.changeMovement(this.model.MovementType)
      // }
      this.drawer = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleSave() {
      if (this.model.Qty <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (this.model.Qty > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return;
      } else if (e > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return;
      }
    },
    GetDictionaryForDepReq() {
      GetDictionaryForDepReq().then(res => {
        if (res.Code === 2000) {
          this.MovementOptions = res.Data;
        }
      })
    },
    changeMovement(e) {
      const obj = this.MovementOptions.find(v => v.EnumValue === e);
      this.model.MovementTypeName = obj.EnumValue1;
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.model.InWhsName = this.options.filter(item => item.value === e)[0].label;
      this.getRegion();
    },
    getRegion() {
      if (this.model.InWhsCode) {
        const query = {
          WhsCode: this.model.InWhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.model.InRegionName = res.Data[0].RegionName;
              this.model.InRegionCode = res.Data[0].RegionCode;
              this.getBinLocation();
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.model.InRegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      this.getBinLocation()
    },
    getBinLocation() {
      if (this.model.InRegionCode) {
        const query = {
          regionCode: this.model.InRegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.model.InBinLocationName = res.Data[0].BinLocationName;
              this.model.InBinLocationCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.model.InBinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    },
    GetXZ_SAP_Out() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.optionsOut = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.optionsOut.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeOutWhsName(e) {
      this.model.OutWhsName = this.optionsOut.filter(item => item.value === e)[0].label;
      this.getOutRegion();
    },
    getOutRegion() {
      if (this.model.OutWhsCode) {
        const query = {
          WhsCode: this.model.OutWhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.OutRegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.OutRegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.model.OutRegionName = res.Data[0].RegionName;
              this.model.OutRegionCode = res.Data[0].RegionCode;
              this.getOutBinLocation();
            }
          }
        })
      }
    },
    changeOutRegionName(e) {
      this.model.OutRegionName = this.OutRegionOptions.filter(item => item.value === e)[0].label;
      this.getOutBinLocation();
    },
    getOutBinLocation() {
      if (this.model.OutRegionCode) {
        const query = {
          regionCode: this.model.OutRegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.OutBinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.OutBinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.model.OutBinLocationName = res.Data[0].BinLocationName;
              this.model.OutBinLocationCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeOutBinLocationName(e) {
      this.model.OutBinLocationName = this.OutBinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }

</style>
