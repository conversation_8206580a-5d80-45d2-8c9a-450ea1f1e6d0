<template>
  <div class="app-container">
    <p>
      <label style="width:100%">报废申请登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="报废单号" prop="DocNum">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型" prop="MovementType">
            <el-select
              v-model="searchQuery.MovementType"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeMovement"
            >
              <el-option
                v-for="item in MovementOptions"
                :key="item.EnumValue"
                :label="item.EnumValue+'-'+item.EnumValue1"
                :value="item.EnumValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="成本中心"
            :rules="CostCenterDisabled===false?rules.CostCenter:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.CostCenter"
              filterable
              placeholder="请选择"
              :disabled="CostCenterDisabled"
              style="width: 100%;"
              @change="changeCostCenter"
            >
              <el-option
                v-for="item in CostCenterOptions"
                :key="item.KOSTL"
                :label="item.KOSTL+'-'+item.LTEXT"
                :value="item.KOSTL"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="库存信息" readonly :disabled="stockDisabled">
              <el-button slot="append" icon="el-icon-more" :disabled="stockDisabled" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="物料信息" readonly :disabled="materialsDisabled">
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="materialsDisabled"
                @click="selectCustomerMaterials"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="转入仓库"
            :rules="stockDisabled===false?rules.InWhsCode:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.InWhsCode"
              filterable
              placeholder="请选择"
              :disabled="stockDisabled"
              @change="changeWhsName"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.value+'-'+item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员" prop="HandlenCode">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门">
            <el-input v-model="searchQuery.Department" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="报废原因" prop="ScrapReason">
            <el-input v-model="searchQuery.ScrapReason" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('ui.MM.DepartmentPickingApplication.Remark')">
            <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        :disabled="ImportDisabled"
        size="small"
        @click="handleImport"
      >导入模板
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-download" size="small" @click="handleExportModel">下载模板
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="库存数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转出仓库编码" prop="OutWhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="转出仓库名称" prop="OutWhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="转出区域名称" prop="OutRegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="转出库位名称" prop="OutBinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="转入仓库编码" prop="InWhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="转入仓库名称" prop="InWhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="转入区域名称" prop="InRegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="转入库位名称" prop="InBinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
    <add-select-materials-model ref="modalFormMaterials" :data-list="list" @ok="modalFormOkMaterials" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddModel from './modules/addModel'
import AddSelectMaterialsModel from './modules/addSelectMaterialsModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetList,
  GetDictionaryForDepReq,
  GetXZ_SAP_CSKS,
  exportExcelModel
} from '@/api/MM/MM_ScrapApplication';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_ScrapApplicationDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel,
    AddSelectMaterialsModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        MovementType: '',
        ManualPostTime: new Date(),
        InWhsCode: '',
        InWhsName: '',
        InRegionCode: '',
        InRegionName: '',
        InBinLocationCode: '',
        InBinLocationName: '',
        HandlenCode: '',
        HandlenName: '',
        Department: '',
        ScrapReason: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        InWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        HandlenCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ScrapReason: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: '',
      delList: [],
      options: [],
      RegionOptions: [],
      BinLocationOptions: [],
      HandlenOptions: [],
      MovementType: [],
      MovementOptions: [],
      CostCenterOptions: [],
      CostCenterDisabled: true,
      stockDisabled: true,
      materialsDisabled: true,
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      ImportDisabled: true
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetDictionaryForDepReq();
    this.GetXZ_SAP();
    this.getRegion();
    this.getBinLocation();
    this.getUserlist();
    this.GetXZ_SAP_CSKS();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DetailedID) {
            v.IsDelete = true;
            this.delList.push(v.DetailedID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList);
        });
      }
    },
    handleCommit() {
      if (!this.searchQuery.MovementType) {
        this.showNotify('warning', '请选择移动类型');
        return;
      }
      if (this.searchQuery.MovementType === 'Z09' || this.searchQuery.MovementType === 'Z10' || this.searchQuery
        .MovementType === 'Z11' || this.searchQuery.MovementType === 'Z12') {
        if (this.searchQuery.CostCenter === '' || this.searchQuery.CostCenter === undefined || this.searchQuery
          .CostCenter === null) {
          this.showNotify('warning', '请选择成本中心');
          return;
        }
        if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0) {

        } else {
          if (this.searchQuery.InBinLocationCode === '' || this.searchQuery.InBinLocationCode === undefined || this
            .searchQuery
            .InBinLocationCode === null) {
            this.showNotify('warning', '请选择转入仓库');
            return;
          }
        }
      }
      if (this.searchQuery.ManualPostTime === '' || this.searchQuery.ManualPostTime === null) {
        this.showNotify('warning', '请选择过账日期');
        return;
      }
      if (this.list.length === 0) {
        if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0) {
          this.showNotify('warning', '请选择物料信息');
        } else {
          this.showNotify('warning', '请选择库存信息');
        }
        return;
      }
      // console.log(this.list)
      let switchBtn = true;
      this.list.some(res => {
        if (res.Qty === null || res.Qty === 0 || res.Qty === '0' || res.Qty === '') {
          this.showNotify('warning', '数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.MovementType === undefined) {
          this.showNotify('warning', '移动类型不能为空且只能是相同的');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.startLoading();
            const query = {
              DocNum: this.searchQuery.DocNum,
              ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
              HandlenCode: this.searchQuery.HandlenCode,
              HandlenName: this.searchQuery.HandlenName,
              Department: this.searchQuery.Department,
              ScrapReason: this.searchQuery.ScrapReason,
              Remark: this.searchQuery.Remark,
              deldetailArray: this.delList,
              DetailedList: this.list
            };
            console.log(query);
            if (this.editStatus === 'create') {
              SubmitScanInfo(query).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_ScrapApplication');
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            } else {
              update(query).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_ScrapApplication');
                } else {
                  this.showNotify('error', res.Message);
                }
                this.endLoading();
              }).catch(err => {
                console.log(err);
                this.endLoading();
              })
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DetailedID) {
          if (v.DetailedID === record.DetailedID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.StockID === record.StockID) {
            this.$set(this.list, index, record);
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      console.log(record);
      const data = [];
      record.forEach((v, index) => {
        data.push({
          BarCode: v.BarCode,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          OutWhsCode: v.WhsCode,
          OutWhsName: v.WhsName,
          OutRegionCode: v.RegionCode,
          OutRegionName: v.RegionName,
          OutBinLocationCode: v.BinLocationCode,
          OutBinLocationName: v.BinLocationName,
          InWhsCode: this.searchQuery.InWhsCode || '',
          InWhsName: this.searchQuery.InWhsName || '',
          InRegionCode: this.searchQuery.InRegionCode || '',
          InRegionName: this.searchQuery.InRegionName || '',
          InBinLocationCode: this.searchQuery.InBinLocationCode || '',
          InBinLocationName: this.searchQuery.InBinLocationName || '',
          MovementType: this.searchQuery.MovementType || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenter: this.searchQuery.CostCenter || '',
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          SaleLine: v.SaleLine,
          SaleNum: v.SaleNum,
          SpecialStock: v.SpecialStock,
          AssessType: v.AssessType,
          StockID: v.ItemCode + v.OutWhsCode + v.SaleLine
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.OutWhsCode + next.SaleLine] ? '' : obj[next.ItemCode + next.OutWhsCode + next.SaleLine] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        DocNum: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          if (this.list.length > 0) {
            this.searchQuery.InWhsCode = this.list[0].InWhsCode;
            this.searchQuery.InWhsName = this.list[0].InWhsName;
            this.searchQuery.InRegionCode = this.list[0].InRegionCode;
            this.searchQuery.InRegionName = this.list[0].InRegionName;
            this.searchQuery.InBinLocationCode = this.list[0].InBinLocationCode;
            this.searchQuery.InBinLocationName = this.list[0].InBinLocationName;
            this.searchQuery.MovementType = res.Data[0].MovementType; //  移动类型
            this.searchQuery.MovementTypeName = res.Data[0].MovementTypeName; //  移动类型名称
            this.searchQuery.CostCenter = res.Data[0].CostCenter; // 成本中心
            this.searchQuery.CostCenterName = res.Data[0].CostCenterName; // 成本中心名称
            if (this.searchQuery.MovementType === 'Z09' || this.searchQuery.MovementType === 'Z10' || this
              .searchQuery.MovementType === 'Z11' || this.searchQuery.MovementType === 'Z12') {
              this.CostCenterDisabled = false // 成本中心
            } else {
              this.CostCenterDisabled = true // 成本中心
            }
            if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0) {
              this.materialsDisabled = false;
              this.stockDisabled = true;
            } else {
              this.materialsDisabled = true;
              this.stockDisabled = false;
            }
          }
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.InWhsName = this.options.filter(item => item.value === e)[0].label;
      const warehouse = {
        InWhsCode: this.searchQuery.InWhsCode,
        InWhsName: this.searchQuery.InWhsName,
        InRegionCode: this.searchQuery.InRegionCode,
        InRegionName: this.searchQuery.InRegionName,
        InBinLocationCode: this.searchQuery.InBinLocationCode,
        InBinLocationName: this.searchQuery.InBinLocationName
      };
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'InWhsName', this.searchQuery.InWhsName);
          this.$set(res, 'InWhsCode', this.searchQuery.InWhsCode);
        })
      }
      this.getRegion();
    },
    getRegion() {
      if (this.searchQuery.InWhsCode) {
        const query = {
          WhsCode: this.searchQuery.InWhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.InRegionName = res.Data[0].RegionName;
              this.searchQuery.InRegionCode = res.Data[0].RegionCode;
              if (this.list.length > 0) {
                this.list.forEach(res => {
                  this.$set(res, 'InRegionName', this.searchQuery.InRegionName);
                  this.$set(res, 'InRegionCode', this.searchQuery.InRegionCode);
                })
              }
              this.getBinLocation()
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.searchQuery.InRegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'InRegionName', this.searchQuery.InRegionName);
          this.$set(res, 'InRegionCode', this.searchQuery.InRegionCode);
        })
      }
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.searchQuery.InRegionCode) {
        const query = {
          regionCode: this.searchQuery.InRegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.InBinLocationName = res.Data[0].BinLocationName;
              this.searchQuery.InBinLocationCode = res.Data[0].BinLocationCode;
              if (this.list.length > 0) {
                this.list.forEach(res => {
                  this.$set(res, 'InBinLocationName', this.searchQuery.InBinLocationName);
                  this.$set(res, 'InBinLocationCode', this.searchQuery.InBinLocationCode);
                })
              }
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.searchQuery.InBinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'InBinLocationName', this.searchQuery.InBinLocationName);
          this.$set(res, 'InBinLocationCode', this.searchQuery.InBinLocationCode);
        })
      }
      this.$forceUpdate();
    },
    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        // console.log(response);
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName;
    },
    GetDictionaryForDepReq() {
      GetDictionaryForDepReq().then(res => {
        if (res.Code === 2000) {
          this.MovementOptions = res.Data
        }
      })
    },
    changeMovement(e) {
      this.ImportDisabled = false;
      this.list = [];
      if (e === 'Z09' || e === 'Z10' || e === 'Z11' || e === 'Z12') {
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.CostCenterDisabled = false; // 成本中心
      } else {
        this.CostCenterDisabled = true; // 成本中心
      }
      const obj = this.MovementOptions.find(v => v.EnumValue === e);
      this.searchQuery.MovementTypeName = obj.EnumValue1;
      if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0) {
        this.materialsDisabled = false;
        this.stockDisabled = true;
      } else {
        this.materialsDisabled = true;
        this.stockDisabled = false;
      }
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter);
        })
      }
    },
    selectCustomerMaterials() {
      this.$refs.modalFormMaterials.add();
    },
    modalFormOkMaterials(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.MATNR, // 物料件号
          ItemName: v.MAKTX, // 物料名称
          Qty: v.Qty,
          Unit: v.MEINS,
          MovementType: this.searchQuery.MovementType || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenter: this.searchQuery.CostCenter || '',
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          OutWhsCode: '',
          OutWhsName: '',
          OutRegionCode: '',
          OutRegionName: '',
          OutBinLocationCode: '',
          OutBinLocationName: '',
          InWhsCode: v.WhsCode,
          InWhsName: v.WhsName,
          InBinLocationCode: v.BinLocationCode,
          InBinLocationName: v.BinLocationName,
          InRegionCode: v.RegionCode,
          InRegionName: v.RegionName,
          StockID: v.MATNR + v.WhsCode
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.InWhsCode] ? '' : obj[next.ItemCode + next.InWhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    GetXZ_SAP_CSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data;
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.searchQuery.CostCenterName = obj.LTEXT;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter);
        })
      }
      this.$forceUpdate();
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      console.log(1, data);
      data.forEach(res => {
        this.uploadExcelData.push({
          BarCode: res.出厂编号, // 出厂编号
          ItemCode: res.物料编码, // 物料件号
          ItemName: '', // 物料名称
          Qty: res.数量,
          Unit: '',
          MovementType: this.searchQuery.MovementType || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenter: this.searchQuery.CostCenter || '',
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          OutWhsCode: res.转出仓库编码,
          OutWhsName: '',
          OutRegionCode: '',
          OutRegionName: '',
          OutBinLocationCode: '',
          OutBinLocationName: '',
          InWhsCode: res.转入仓库编码 || this.searchQuery.WhsCode,
          InWhsName: this.searchQuery.WhsName,
          InBinLocationCode: this.searchQuery.BinLocationCode,
          InBinLocationName: this.searchQuery.BinLocationName,
          InRegionCode: this.searchQuery.RegionCode,
          InRegionName: this.searchQuery.RegionName
        })
      })
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      console.log(0, this.uploadExcelData);
      this.dialogImprotVisable = false;
      const obj = {};
      this.list = this.list.concat(this.uploadExcelData);
      this.isProcessing = false;
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },
    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    }
  }
}
</script>
