<template>
  <el-dialog title="库存信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        style="width: 200px"
        clearable
        :placeholder="$t('Common.keyword')"
        @keyup.enter.native="handleSearchFilter"
      />
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
        添加</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="库存数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="退料数量" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model="scope.row.OutQty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" />
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetSupplierStock
} from '@/api/MM/MM_OutsourcingReturn';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  props: {
    dataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false
    }
  },
  computed: {

  },
  created() {},
  methods: {
    formatDate,
    formatDateTime,
    add() {
      this.listQuery = {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      };
      this.dialogCustomerFormVisible = true;
      this.handleCustomerFilter();
    },
    edit(record) {
      this.model = Object.assign({}, record);
    },
    handleCustomerRowSelectEvent(selection) {
      const data = [];
      if (selection.length >= 1) {
        let switchBtn = true;
        selection.some(v => {
          this.dataList.some(res => {
            if (v.StockID === res.onlyId) {
              this.showNotify('warning', '物料件号为：' + v.ItemCode + '已选择，请勿重复选择');
              switchBtn = false;
              return true;
            }
          })
        });
        if (switchBtn) {
          this.multipleSelection = selection;
        }
      } else {
        this.multipleSelection = selection;
      }
    },
    handleSearchFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.handleCustomerFilter();
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetSupplierStock(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleSelectCustomer() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '退料数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.OutQty > res.Qty) {
          this.showNotify('warning', '退料数量不能大于库存数量');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
        this.dialogCustomerFormVisible = false;
      }
    },
    handleAdd() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '退料数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.OutQty > res.Qty) {
          this.showNotify('warning', '退料数量不能大于库存数量');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
      }
    },
    inputOutQty(e) {
      if (e.OutQty <= 0) {
        this.showNotify('warning', '退料数量不得小于等于0');
        return
      } else if (e.OutQty > e.Qty) {
        this.showNotify('warning', '退料数量不得大于库存数量');
        return
      }
    }
  }
}
</script>

<style scoped>
</style>
