<template>
  <div class="app-container">
    <p>
      <label style="width:100%">委外退料登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="单号">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="采购信息">
            <el-input v-model="searchQuery.purchase" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="searchQuery.stock" placeholder="" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomerStock" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账时间">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              :clearable="false"
              type="date"
              placeholder="过账时间"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="库位">
            <el-select v-model="searchQuery.BinLocationCode" filterable placeholder="请选择" @change="changeBinLocation">
              <el-option v-for="item in options" :key="item.BinLocationCode" :label="item.BinLocationCode+'-'+item.BinLocationName"
                :value="item.BinLocationCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8">
          <el-form-item label="区域">
            <el-input v-model="searchQuery.BinLocationName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库">
            <el-input v-model="searchQuery.WhsName" disabled />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="searchQuery.Remark" placeholder="" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />

      <!-- <el-table-column v-if="" label="采购订单" prop="PurchaseOrder" align="center" width="160" show-overflow-tooltip /> -->

      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="数量" prop="OutsourcingReturnQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <el-table-column label="转入仓库编号" prop="WhsCode" align="center" show-overflow-tooltip />
      <el-table-column label="转入仓库名称" prop="WhsName" align="center" show-overflow-tooltip />
      <el-table-column
        fixed="right"
        :label="$t('ui.PO.PO_ReturnScanDetail.operation')"
        width="80"
        align="center"
      >
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <add-select-model ref="modalForm" @ok="modalFormOk" :dataList="list"/> -->
    <add-select-stock-model ref="modalFormStock" :data-list="list" @ok="modalFormOkStock" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import addSelectStockModel from './modules/addSelectStockModel2'
import AddModel from './modules/addModel'

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList,
  update
} from '@/api/MM/MM_OutsourcingReturn';
import {
  GetBinLocationAll
} from '../../../api/PO/PO_ReturnScan';
export default {
  name: 'MM.MM_OutsourcingReturnDetail',
  components: {
    Pagination,
    AddSelectModel,
    addSelectStockModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        purchase: '',
        materials: '',
        DocNum: '',
        stock: '',
        ManualPostTime: new Date(),
        Remark: ''
      },
      multipleSelection: [],
      rules: {

      },
      editStatus: 'create',
      delList: [],
      options: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetBinLocationAll();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      console.log(selectedRows);
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.OutsourcingReturnDetailID) {
            v.IsDelete = true;
            this.delList.push(v.OutsourcingReturnDetailID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList);
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择库存信息');
        return;
      }
      if (!this.searchQuery.ManualPostTime) {
        this.showNotify('warning', '过账时间不能为空');
        return;
      }
      // if (!this.searchQuery.BinLocationCode) {
      //   this.showNotify("warning", '库位不能为空');
      //   return
      // }
      let switchBtn = true;
      this.list.some(res => {
        if (res.OutsourcingReturnQty === null || res.OutsourcingReturnQty === 0 || res
          .OutsourcingReturnQty === '0') {
          this.showNotify('warning', '数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (!res.BinLocationCode) {
          this.showNotify('warning', '库位不能为空');
          switchBtn = false;
          return true;
        }
      });

      if (switchBtn) {
        this.startLoading();
        const query = {
          DocNum: this.searchQuery.DocNum,
          // BinLocationCode: this.searchQuery.BinLocationCode, // 库位编号
          // BinLocationName: this.searchQuery.BinLocationName, // 库位
          // RegionCode: this.searchQuery.RegionCode, // 区域编号
          // RegionName: this.searchQuery.RegionName, // 区域
          // WhsCode: this.searchQuery.WhsCode, // 仓库编号
          // WhsName: this.searchQuery.WhsName, // 仓库
          ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
          Remark: this.searchQuery.Remark,
          detailed: this.list,
          deletedetail: this.delList
        };
        if (this.editStatus === 'create') {
          SubmitScanInfo(query).then(res => {
            if (res.Code === 2000) {
              if (res.MessageParam === 2000) {
                this.showNotify('success', res.Message);
              } else {
                this.showNotify('warning', res.Message);
              }
              this.backTo('MM.MM_OutsourcingReturn');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading()
          }).catch(err => {
            console.log(err);
            this.endLoading()
          })
        } else {
          update(query).then(res => {
            if (res.Code === 2000) {
              if (res.MessageParam === 2000) {
                this.showNotify('success', res.Message);
              } else {
                this.showNotify('warning', res.Message);
              }
              this.backTo('MM.MM_OutsourcingReturn');
            } else {
              this.showNotify('error', res.Message);
            }
            this.endLoading()
          }).catch(err => {
            console.log(err);
            this.endLoading()
          })
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      console.log(record);
      this.list.forEach((v, index) => {
        if (v.OutsourcingReturnDetailID) {
          if (v.OutsourcingReturnDetailID === record.OutsourcingReturnDetailID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.onlyId === record.onlyId) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          PurchaseOrder: v.EBELN, // 采购订单
          ItemCode: v.MATNR, // 物料件号
          ItemName: v.TXZ01, // 物料名称
          ItmsGrpName: v.MATKL, // 物料组名称
          SupplierCode: v.LIFNR, // 供应商编号
          SupplierName: v.NAME1, // 供应商名称
          OutsourcingReturnQty: v.MENGE, // 委外退料数量
          Unit: v.MEINS, // 单位
          onlyId: v.EBELN + v.MATNR
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.PurchaseOrder + next.ItemCode] ? '' : obj[next.PurchaseOrder + next.ItemCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    selectCustomerStock() {
      this.$refs.modalFormStock.add();
    },
    modalFormOkStock(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          SupplierCode: v.SupplierCode, // 供应商编号
          SupplierName: v.SupplierName, // 供应商名称
          OutsourcingReturnQty: v.OutQty, // 委外退料数量
          Unit: v.Unit, // 单位
          onlyId: v.ItemCode + v.SupplierCode
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.SupplierCode] ? '' : obj[next.ItemCode + next.SupplierCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        keyword: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      console.log(this.searchQuery, 1);
      if (this.searchQuery.OutsourcingReturnID) {
        this.editStatus = 'edit';
        this.getDetailList()
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    GetBinLocationAll() {
      GetBinLocationAll().then(res => {
        if (res.Code === 2000) {
          this.options = res.Data;
        }
      })
    },
    changeBinLocation(e) {
      const obj = this.options.find(v => v.BinLocationCode === e);
      this.searchQuery.BinLocationName = obj.BinLocationName; // 库位
      this.searchQuery.RegionCode = obj.RegionCode; // 区域编号
      this.searchQuery.RegionName = obj.RegionName; // 区域
      this.searchQuery.WhsCode = obj.WhsCode; // 仓库编号
      this.searchQuery.WhsName = obj.WhsName; // 仓库
    }
  }
}
</script>
