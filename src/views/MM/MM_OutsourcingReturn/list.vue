<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-input
        v-model="listQuery.supplierCode"
        size="small"
        class="filter-item"
        :placeholder="$t('MD_Supplier.SupplierCode')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutsourcingReturn.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutsourcingReturn.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingReturn.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingReturn.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingReturn.PassPost'}"
        class="filter-item"
        type="danger"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePassPost"
      >{{ $t('Common.passPost') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_OutsourcingReturn.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        :disabled="postDisable"
        @click="handlePrint"
      >{{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutsourcingReturn.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="过账日期" prop="ManualPostTime" align="center"  width="160" :formatter="formatDate"/> -->
      <el-table-column label="是否过账" prop="IsPosted" align="center" width="100" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="160"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>委外退料明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="行号" prop="Line" align="center" width="160" show-overflow-tooltip/> -->
      <!-- <el-table-column label="批次" prop="BatchNum" align="center" width="160" show-overflow-tooltip/>
      <el-table-column label="采购单号" prop="PurchaseOrder" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="采购单行号" prop="PurchaseLine" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="物料组编号" prop="ItmsGrpCode" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="物料组" prop="ItmsGrpName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="委外退料数量" prop="OutsourcingReturnQty" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转入仓库编号" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="转入仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="区域" prop="RegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="库位" prop="BinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="是否过账" prop="IsPosted" align="center" width="100" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="Sap物料凭证单号" prop="SapDocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="Sap物料凭证行号" prop="SapLine" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" />
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160"
        :formatter="formatDateTime" /> -->

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />

    <!-- 过账日期 -->
    <el-dialog title="请选择过账日期" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <el-form-item label="过账日期" label-width="120px" prop="PostTime">
          <el-date-picker
            v-model="ruleForm.PostTime"
            :clearable="false"
            type="date"
            placeholder="过账日期"
            style="width: 100%;"
            format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handlePosting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  GetPageList,
  batchDelete,
  exportExcelFile,
  DoPost,
  printToPDF,
  PassPost
} from '@/api/MM/MM_OutsourcingReturn';

export default {
  name: 'MM.MM_OutsourcingReturn',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        supplierCode: '', // 供应商代码
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50
      },
      dialogVisible: false,
      ruleForm: {
        PostTime: ''
      },
      rules: {
        PostTime: [{
          type: 'date',
          required: true,
          message: '请选择过账日期',
          trigger: 'change'
        }]
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return this.multipleSelection.length === 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM_OutsourcingReturn') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handlePrint() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (this.checkSingleSelection(selectRows)) {
        const docNums = selectRows.map(v => v.DocNum);
        console.log(docNums);
        printToPDF({
          docNums: docNums
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '委外退料');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handlePostingBtn() {
      this.ruleForm.PostTime = '';
      this.dialogVisible = true;
    },
    // 过账功能模块
    handlePosting() {
      // this.$refs['ruleForm'].validate((valid) => {
      //   if (valid) {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          this.dialogVisible = false;
          const query = {
            // PostTime: this.$moment(this.ruleForm.PostTime).format('YYYY-MM-DD'),
            entities: this.multipleSelection
          };
          DoPost(query)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message);
                } else {
                  this.showNotify('warning', res.Message);
                }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
      //   } else {
      //     console.log('error submit!!');
      //     return false;
      //   }
      // });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          var arrRowsID = selectRows.map(v => v.DocNum);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail()
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign(this.listDetailQuery, {
        keyword: this.currentRow.DocNum.trim()
      });
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('MM.MM_OutsourcingReturnDetail');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止编辑');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('MM.MM_OutsourcingReturnDetail', this.multipleSelection[0]);
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    handlePassPost() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === false) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，请先过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          const query = {
            // PostTime: this.$moment(this.ruleForm.PostTime).format('YYYY-MM-DD'),
            entities: this.multipleSelection
          };
          PassPost(query)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.postSuccess');
                }
              } else {
                this.showNotify('error', 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
              this.handleFilter()
            });
        }
      }
    }
  }
};
</script>
