<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="isPosted"
        filterable
        :placeholder="$t('Common.confirmStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleSelectChangeValue"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <!--input输入框暂时未处理好-->
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockDiff.Affirm' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-check"
        :disabled="selective"
        @click="handleConfirm"
      >{{ $t('Common.affirm') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockDiff.Review' }"
        class="filter-item"
        size="small"
        style="margin-left: 10px"
        type="success"
        icon="el-icon-check"
        :disabled="selective"
        @click="handleReview"
      >
        {{ $t('Common.review') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockDiff.Export' }"
        :loading="downloadLoading"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed :selectable="(row,index)=>{return row.DiffQty!=0;}" />
      <el-table-column v-if="false" :label="$t('ui.MM.TakeStockScan.ScanID')" prop="ScanID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.DocNum')" prop="DocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.BoxBarCode')" prop="BoxBarCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.BarCode')" prop="BarCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.PTime')" prop="PTime" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.ItemCode')" prop="ItemCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.ItemName')" prop="ItemName" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.MM.TakeStockScan.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.MM.TakeStockScan.StockQty')" prop="StockQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.StockQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.ScanQty')" prop="ScanQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.DiffQty')" prop="DiffQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DiffQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.BatchNum')" prop="BatchNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.RegionName')" prop="RegionName" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.WhsName')" prop="WhsName"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.WhsName }}</span> </template> </el-table-column> -->
      <el-table-column
        :label="$t('ui.MM.TakeStockScan.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.TakeStockScan.IsConfirm')"
        prop="IsConfirm"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime|datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  fetchPageCY,
  exportExcelFileCY
} from '@/api/MM/MM_TakeStockScan';
import {
  reviewTakeStockPlan,
  confirmTakeStockPlan
} from '@/api/MM/MM_TakeStockPlan';
import {
  exportToExcel
} from '@/utils/excel-export';
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MM.MM_TakeStockDiff',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      dialogVisible: false,
      reviewable: false,
      list: [],
      total: 0,
      downloadLoading: false,
      listLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        Status: 1, // 状态
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPosted: undefined, // 暂未加入搜索条件
      changeSelectValue: '',
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.isConfirm'),
        key: true
      },
      {
        label: this.$i18n.t('Common.unConfirm'),
        key: false
      }
      ],
      multipleSelection: [],
      temp: {},
      rules: [],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      let i = this.multipleSelection.length;
      if (i === 0) return true;
      while (i--) {
        if (this.multipleSelection[i].IsConfirm) {
          return true;
        }
      }
      return false;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      this.listQuery.isConfirm = this.changeSelectValue;

      fetchPageCY(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleSelectChangeValue(val) {
      this.changeSelectValue = val;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleReview() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        var confirmed = selectRows.find(x => x.IConfirm);
        if (confirmed) {
          this.showNotify('error', 'MM.TakeStockPlan.planStatusError');
          return;
        } else {
          this.$confirm(
            this.$i18n.t('Common.actionConfirm'),
            this.$i18n.t('Common.tip'), {
              confirmButtonText: this.$i18n.t('Common.affirm'),
              cancelButtonText: this.$i18n.t('Common.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.isProcessing = true;
            var confirmed = selectRows.find(x => x.IConfirm);
            if (confirmed) {
              this.showNotify('error', 'MM.TakeStockPlan.planStatusError');
            } else {
              reviewTakeStockPlan({
                Data: selectRows
              })
                .then(response => {
                  if (response.Code === 2000) {
                    this.showNotify('success', 'Common.operationSuccess');
                  } else {
                    this.showNotify('error', response.Message);
                  }
                  this.isProcessing = false;
                })
                .catch(error => {
                  this.isProcessing = false;
                });
            }
          });
        }
      }
    },
    handleConfirm() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        var confirmed = selectRows.find(x => x.IConfirm);
        if (confirmed) {
          this.showNotify('error', 'MM.TakeStockPlan.planStatusError');
          return;
        } else {
          this.$confirm(
            this.$i18n.t('Common.actionConfirm'),
            this.$i18n.t('Common.tip'), {
              confirmButtonText: this.$i18n.t('Common.affirm'),
              cancelButtonText: this.$i18n.t('Common.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.isProcessing = true;
            var confirmed = selectRows.find(x => x.IConfirm);
            if (confirmed) {
              this.showNotify('error', 'MM.TakeStockPlan.planStatusError');
            } else {
              confirmTakeStockPlan({
                Data: selectRows
              })
                .then(response => {
                  if (response.Code === 2000) {
                    this.showNotify('success', 'Common.operationSuccess');
                  } else {
                    this.showNotify('error', response.Message);
                  }
                  this.isProcessing = false;
                })
                .catch(error => {
                  this.isProcessing = false;
                });
            }
          });
        }
      }
    },
    handleExport() {
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.dateValue[0], this.listQuery.dateValue[1]],
        state: this.changeSelectValue
      };
      exportExcelFileCY(exportQuery).then(res =>
        exportToExcel(res.data, '盘点差异确认')
      );
    }
  }
};
</script>
