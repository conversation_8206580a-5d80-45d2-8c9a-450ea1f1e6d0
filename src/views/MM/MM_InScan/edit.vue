<template>
  <div class="app-container">
    <p>
      <label style="width: 100%">{{ $t('ui.MM.InScan.title') }}</label>
    </p>
    <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.DocNum')">
            <el-input v-model="temp.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.BarCode')">
            <el-input v-model="temp.BarCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.BatchNum')">
            <el-input v-model="temp.BatchNum" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.InBinLocationName')" prop="InBinLocationName">
            <el-input v-model="temp.InBinLocationName" :disabled="editStatus==='edit'" readonly>
              <el-button
                slot="append"
                v-model="temp.InBinLocationName"
                icon="el-icon-more"
                :disabled="editStatus==='edit'"
                @click="handleShowLocationDlg"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.InRegionName')" prop="InRegionName">
            <el-input v-model="temp.InRegionName" disabled />
          </el-form-item>
        </el-col>
        <!--<el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.InWhsName')" prop="InWhsName">
            <el-input v-model="temp.InWhsName" disabled />
          </el-form-item>
        </el-col>-->
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" :disabled="editStatus==='edit'" readonly>
              <el-button
                slot="append"
                :disabled="!this.location.currentBinLocation||!this.location.currentBinLocation.RegionCode||editStatus==='edit'"
                icon="el-icon-more"
                @click="handleShowMaterialDlg"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.MM.InScan.ItemName')">
            <el-input v-model="temp.ItemName" disabled />
            <!--此节点物料名称与下一节点物料组名称考虑换为选择框select-->
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.Qty')" prop="Qty">
            <el-input-number v-model="temp.Qty" :disabled="editStatus==='edit'" :min="0" controls-position="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.Unit')">
            <el-input v-model="temp.Unit" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.PTime')">
            <el-date-picker v-model="temp.PTime" type="date" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!--物料组
       <el-row :gutter="10">
        <el-col :span="16">
          <el-form-item :label="$t('ui.MM.InScan.ItmsGrpCode')">
            <el-input v-model="temp.ItmsGrpCode" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.ItmsGrpName')">
            <el-input v-model="temp.ItmsGrpName" />
          </el-form-item>
        </el-col>
      </el-row>-->

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.RTypeName')" prop="RTypeName">
            <el-input v-model="temp.RTypeName" readonly>
              <el-button slot="append" icon="el-icon-more" @click="handleShowRTypeDlg" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.Subject')">
            <el-input v-model="temp.Subject" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.InScan.CostCenter')">
            <el-input v-model="temp.CostCenter" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item :label="$t('Common.Remark')">
            <el-input
              v-model="temp.Remark"
              type="textarea"
              :autosize="{minRows: 2,maxRows: 5}"
              @input="change($event)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="filter-container">
      <el-button
        v-if="editStatus!=='edit'"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleAddBtnClick"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-if="editStatus!=='edit'"
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDeleteDetail"
      >{{ $t('Common.delete') }}</el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-edit" @click="handleCommit">
        {{ $t('Common.confirm') }}</el-button>
    </div>
    <el-table
      v-if="editStatus!=='edit'"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column v-if="false" :label="$t('ui.MM.InScan.ScanID')" prop="ScanID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.DocNum')" prop="DocNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.BarCode')" prop="BarCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.BatchNum')" prop="BatchNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.PTime')" prop="PTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.ItemName')" prop="ItemName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.MM.InScan.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.InScan.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.MM.InScan.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.MM.InScan.RTypeCode')" prop="RTypeCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.RTypeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.RTypeName')" prop="RTypeName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.RTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.Subject')" prop="Subject" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Subject }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.CostCenter')" prop="CostCenter" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.CostCenter }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.MM.InScan.InWhsCode')"
        prop="InWhsCode"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.InScan.InWhsName')"
        prop="InWhsName"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InWhsName }}</span>
        </template>
      </el-table-column>-->
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.InScan.InRegionCode')"
        prop="InRegionCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.InScan.InRegionName')" prop="InRegionName" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.InRegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.InScan.InBinLocationCode')"
        prop="InBinLocationCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.InScan.InBinLocationName')"
        prop="InBinLocationName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.InBinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.InScan.IsPosted')"
        prop="IsPosted"
        fixed="right"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.MM.InScan.PostUser')" prop="PostUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.MM.InScan.PostTime')" prop="PostTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PostTime|datetime }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!--物料信息弹框-->
    <el-dialog :title="materialtitle" :visible.sync="materialDialogVisible" width="65%">
      <div class="filter-container">
        <el-input
          v-model="material.query.keyword"
          class="filter-item"
          style="width: 200px"
          :placeholder="$t('Common.keyword')"
          clearable
          @keyup.enter.native="handleMaterialFilter(materialtitle)"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleMaterialFilter(materialtitle)"
        >{{ $t('Common.search') }}</el-button>
      </div>
      <el-table
        v-loading="material.loading"
        :data="material.list"
        border
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%;height:100%;"
        @row-click="handleMaterialSelect"
      >
        <el-table-column :label="$t('ui.MD.Stock.BarCode')" prop="BarCode" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.BarCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.ItemCode')" prop="ItemCode" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.ItemCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.ItemName')" prop="ItemName" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.ItemName }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column
          :label="$t('ui.MD.Stock.ItmsGrpCode')"
          prop="ItmsGrpCode"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItmsGrpCode}}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.Stock.ItmsGrpName')"
          prop="ItmsGrpName"

          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ItmsGrpName}}</span>
          </template>
        </el-table-column>-->
        <el-table-column :label="$t('ui.MD.Stock.BatchNum')" prop="BatchNum" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.BatchNum }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.PTime')" prop="PTime" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.PTime }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.Qty')" prop="Qty" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.Qty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.Unit')" prop="Unit" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.Unit }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.RegionCode')" prop="RegionCode" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.RegionCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.Stock.RegionName')" prop="RegionName" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.RegionName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.MD.Stock.BinLocationCode')"
          prop="BinLocationCode"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BinLocationCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="false"
          :label="$t('ui.MD.Stock.BinLocationName')"
          prop="BinLocationName"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.BinLocationName }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="material.total>0"
        :total="material.total"
        :page.sync="material.query.PageNumber"
        :limit.sync="material.query.PageSize"
        @pagination="handleMaterialFilter"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="materialDialogVisible = false">{{ $t('Common.close') }}</el-button>
        <!-- <el-button type="primary" icon="el-icon-check" @click="handleSelectMaterialBtnClick">{{ $t('Common.select') }}</el-button> -->
      </span>
    </el-dialog>

    <!--位置信息弹框-->
    <BinLocationDlg :title="locationtitle" :show.sync="locationDlgVisible" :is-multiple="false" @close="handleBinLocationSelect" />
    <!--收货类型弹框-->
    <el-dialog :title="RTypetitle" :visible.sync="rtypeDialogVisible" width="42%">
      <el-table
        :data="rtype.list"
        border
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%;height:100%;"
        @row-click="handleRTypeSelect"
      >
        <el-table-column :label="$t('ui.MM.InScan.RTypeCode')" prop="RTypeCode" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.RTypeCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MM.InScan.RTypeName')" prop="RTypeName" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.RTypeName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MM.InScan.Subject')" prop="Subject" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.Subject }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MM.InScan.CostCenter')" prop="CostCenter" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.CostCenter }}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rtypeDialogVisible = false">{{ $t('Common.close') }}</el-button>
        <!-- <el-button type="primary" icon="el-icon-check" @click="handleSelectTypeBtnClick">{{ $t('Common.select') }}</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import BinLocationDlg from '@/components/FLD/BinLocationDlg';

import {
  fetchPage as fetchMaterialPage
} from '@/api/MD/MD_Stock';
import {
  fetchPage as fetchLocationPage
} from '@/api/MD/MD_BinLocation';
import {
  fetchDocNum,
  addDetails,
  update
} from '@/api/MM/MM_InScan';
export default {
  // 验证规则rules暂未定义
  name: 'MM.MM_InScanEdit',
  directives: {
    waves
  },
  components: {
    Pagination,
    BinLocationDlg
  },
  data() {
    return {
      rules: {
        // InBinLocationName: [
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "blur"
        //   }
        // ],
        // ItemCode: [
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "blur"
        //   }
        // ],
        Qty: [{
          required: true,
          validator: this.QtyValidator,
          trigger: 'change'
        },
        {
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }
        ]
        // RTypeName: [
        //   {
        //     required: true,
        //     validator: function(rule, value, callback) {
        //       alert(value);
        //     },
        //     trigger: "blur"
        //   }
        // ]
      },
      materialDialogVisible: false,
      locationDlgVisible: false,
      rtypeDialogVisible: false,
      material: {
        list: [],
        total: 0,
        loading: true,
        query: {
          keyword: '',
          PageNumber: 1,
          PageSize: 10,
          fromTime: '',
          toTime: '',
          regionCode: ''
        }
      },
      location: {
        list: [],
        total: 0,
        loading: true,
        query: {
          keyword: '',
          PageNumber: 1,
          PageSize: 10,
          fromTime: '',
          totime: ''
        },
        currentBinLocation: undefined
      },
      rtype: {
        list: []
      },
      temp: {
        ScanID: undefined, // '扫描ID',
        DocNum: undefined, // '扫描单号',
        BarCode: undefined, // '条码',
        BatchNum: undefined, // '批次',
        PTime: undefined, // '生产日期',
        ItemCode: undefined, // '物料件号',
        ItemName: undefined, // '物料名称',
        ItmsGrpCode: undefined, // '物料组编号',
        ItmsGrpName: undefined, // '物料组名称',
        Qty: undefined, // '扫描数量',
        Unit: undefined, // '库存单位',
        RTypeCode: undefined, // '收货类型编号',
        RTypeName: undefined, // '收货类型',
        Subject: undefined, // '科目',
        CostCenter: undefined, // '成本中心',
        InWhsCode: undefined, // '转入仓库编号',
        InWhsName: undefined, // '转入仓库名称',
        InRegionCode: undefined, // '转入区域编号',
        InRegionName: undefined, // '转入区域名称',
        InBinLocationCode: undefined, // '转入库位编号',
        InBinLocationName: undefined, // '转入库位名称',
        IsCheck: undefined, // '财务复核（未复核/通过/驳回）',
        IsPosted: undefined, // '是否过账',
        PostUser: undefined, // '过账人',
        PostTime: undefined, // '过账时间',
        Remark: '' // 备注
      },
      materialtitle: this.$i18n.t('Common.selectMaterial'),
      locationtitle: this.$i18n.t('Common.selectLocation'),
      RTypetitle: this.$i18n.t('Common.selectRType'),
      multipleSelection: [],
      list: [],
      editStatus: 'create'
    };
  },
  created() {
    this.getPageParams();
    if (this.editStatus == 'create') {
      fetchDocNum().then(response => {
        if (response.Code === 2000) {
          this.temp.DocNum = response.Data;
        }
      });
    }
  },
  methods: {
    getMaterialList() {
      fetchMaterialPage(this.material.query).then(response => {
        if (response.Code === 2000) {
          this.material.list = response.Data.items;
          this.material.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.material.loading = false;
      });
    },
    getPageParams() {
      this.temp = Object.assign({}, this.$route.params);
      if (this.temp.ScanID) {
        // 编辑
        this.editStatus = 'edit';
      } else {
        // 新增
        this.editStatus = 'create';
      }
      if (this.temp.PTime) {} else {
        this.temp.PTime = new Date();
      }
    },
    getRtypeList() {
      this.getDict('MM004').then(data => {
        this.rtype.list = data.map(val => {
          return {
            RTypeCode: val.EnumKey,
            RTypeName: val.EnumValue,
            Subject: val.EnumValue2,
            CostCenter: val.Remark
          };
        });
        this.rtype.loading = false;
      });
    },
    getLocationList() {
      fetchLocationPage(this.location.query).then(response => {
        if (response.Code === 2000) {
          this.location.list = response.Data.items;
          this.location.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.location.loading = false;
      });
    },
    handleMaterialFilter() {
      this.material.loading = true;
      this.material.query.fromTime = '';
      this.material.query.toTime = '';
      this.material.query.regionCode = this.location.currentBinLocation.RegionCode;
      this.getMaterialList();
    },
    handleRTypeFilter() {
      this.rtype.loading = true;
      this.getRtypeList();
    },
    handleLocationFilter() {
      this.location.loading = true;
      this.location.query.fromTime = '';
      this.location.query.toTime = '';
      this.getLocationList();
    },
    handleBinLocationSelect(row) {
      if (row) {
        if (row.RegionCode == 'QM1') {
          this.showNotify('error', 'ui.MM.OutScan.QMRegionNotAllowed');
          return;
        }
        this.location.currentBinLocation = row;
        this.temp.InWhsCode = row.WhsCode;
        this.temp.InWhsName = row.WhsName;
        this.temp.InRegionCode = row.RegionCode;
        this.temp.InRegionName = row.RegionName;
        this.temp.InBinLocationCode = row.BinLocationCode;
        this.temp.InBinLocationName = row.BinLocationName;
      } else {
        this.location.currentBinLocation = '';
        this.temp.InWhsCode = '';
        this.temp.InWhsName = '';
        this.temp.InRegionCode = '';
        this.temp.InRegionName = '';
        this.temp.InBinLocationCode = '';
        this.temp.InBinLocationName = '';
      }
      this.temp.BarCode = '';
      this.temp.BatchNum = '';
      this.temp.PTime = '';
      this.temp.ItemCode = '';
      this.temp.ItemName = '';
      this.temp.ItmsGrpCode = '';
      this.temp.ItmsGrpName = '';
      this.temp.Qty = '';
      this.temp.Unit = '';
      this.temp.Remark = '';
      this.locationDlgVisible = false;
    },
    change(e) {
      this.$forceUpdate();
    },
    handleMaterialSelect(row) {
      var newRow = row;
      newRow.Qty = 0;
      Object.assign(this.temp, newRow);

      this.materialDialogVisible = false;
    },
    handleRTypeSelect(row) {
      Object.assign(this.temp, row);
      this.rtypeDialogVisible = false;
    },
    handleAddBtnClick() {
      if (!this.temp.RTypeName) {
        this.showNotify('error', 'Common.selectRType');
        return;
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.temp.InBinLocationCode) {} else {
            this.showNotify('error', 'ui.MM.InScan.binLocationCodeIsRequired');
            return;
          }
          if (this.temp.ItemCode) {} else {
            this.showNotify('error', 'ui.MM.InScan.itemCodeIsRequired');
            return;
          }
          console.log('1', this.temp);
          var detail = this.list.find(
            obj =>
              obj.BarCode == this.temp.BarCode &&
              obj.InBinLocationCode == this.temp.InBinLocationCode
          );
          console.log('2', detail);
          if (detail) {
            // 如果找到已添加的同条码只增加数量不新增条目
            detail.Qty += this.temp.Qty;
          } else {
            this.list.push(Object.assign({}, this.temp));
            console.log(this.list);
          }
          this.temp.BarCode = '';
          this.temp.BatchNum = '';
          this.temp.PTime = '';
          this.temp.ItemCode = '';
          this.temp.ItemName = '';
          this.temp.ItmsGrpCode = '';
          this.temp.ItmsGrpName = '';
          this.temp.Qty = '';
          this.temp.Unit = '';
          this.temp.Remark = '';
          this.location.currentBinLocation = '';
          this.temp.InWhsCode = '';
          this.temp.InWhsName = '';
          this.temp.InRegionCode = '';
          this.temp.InRegionName = '';
          this.temp.InBinLocationCode = '';
          this.temp.InBinLocationName = '';
        } else {
          return;
        }
      });
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.$confirm(
        this.$i18n.t('Common.actionConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        if (this.editStatus == 'edit') {
          this.$refs['form'].validate(valid => {
            if (valid) {
              this.startLoading()
              update(this.temp).then(response => {
                if (response.Code === 2000) {
                  this.showNotify('success', 'Common.updateSuccess');
                  this.backTo('MM.MM_InScan');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err)
                this.endLoading()
              });
            }
          });
        } else {
          if (this.list.length == 0) {
            this.showNotify('error', 'Common.noConfirmData');
            return;
          }
          this.startLoading();
          addDetails({
            Data: this.list
          }).then(response => {
            if (response.Code === 2000) {
              // 跳转回主单页面
              this.backTo('MM.MM_InScan');
            } else {
              this.showNotify('error', response.Message);
            }
          }).catch(err => {
            console.log(err);
            this.endLoading()
          });
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleShowMaterialDlg() {
      // 打开物料信息弹窗
      this.material.query.keyword = '';
      this.materialDialogVisible = true;
      this.handleMaterialFilter();
    },
    handleShowLocationDlg() {
      // 打开位置信息弹窗
      this.locationDlgVisible = true;
      this.location.query.keyword = '';
      // this.handleLocationFilter()
    },
    handleShowRTypeDlg() {
      // 打开收货类型信息弹窗
      this.rtypeDialogVisible = true;
      this.handleRTypeFilter();
    }
  }
};
</script>
