<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("ui.MM.MM_OtherInDetail.title") }}</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="入库单号" prop="DocNum">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型" prop="MovementType">
            <el-select
              v-model="searchQuery.MovementType"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeMovement"
            >
              <el-option
                v-for="item in MovementOptions"
                :key="item.EnumValue"
                :label="item.EnumValue+'-'+item.EnumValue1"
                :value="item.EnumValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="成本中心"
            prop="CostCenter"
            :rules="CostCenterDisabled===false?rules.CostCenter:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.CostCenter"
              filterable
              placeholder="请选择"
              :disabled="CostCenterDisabled"
              style="width: 100%;"
              @change="changeCostCenter"
            >
              <el-option
                v-for="item in CostCenterOptions"
                :key="item.KOSTL"
                :label="item.KOSTL+'-'+item.LTEXT"
                :value="item.KOSTL"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="总账科目"
            :rules="LedgerDisabled===false?rules.LedgerType:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.LedgerType"
              filterable
              placeholder="请选择"
              :disabled="LedgerDisabled"
              style="width: 100%;"
              @change="changeLedgerType"
            >
              <el-option
                v-for="item in LedgerTypeOptions"
                :key="item.SAKNR"
                :label="item.SAKNR+'-'+item.TXT50"
                :value="item.SAKNR"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="库存信息" readonly :disabled="stockDisabled">
              <el-button slot="append" icon="el-icon-more" :disabled="stockDisabled" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="生产订单号"
            :rules="OrderNumDisabled===false?rules.OrderNum:[{ required: false, message: '请输入', trigger: 'blur' }]"
          >
            <el-input
              v-model="searchQuery.OrderNum"
              placeholder="生产订单号"
              :disabled="OrderNumDisabled"
              @input="changeOrderNum"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="物料信息" readonly :disabled="materialsDisabled">
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="materialsDisabled"
                @click="selectCustomerMaterials"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              type="date"
              :clearable="false"
              placeholder="过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报废单号">
            <el-input v-model="searchQuery.ScrapNum" placeholder="报废单号" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('Common.Remark')">
            <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        :disabled="ImportDisabled"
        size="small"
        @click="handleImport"
      >导入模板
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-download" size="small" @click="handleExportModel">下载模板
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="生产订单号" prop="OrderNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="100" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
    <add-select-materials-model ref="modalFormMaterials" :data-list="list" @ok="modalFormOkMaterials" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddSelectMaterialsModel from './modules/addSelectMaterialsModel'
import AddModel from './modules/addModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetList,
  GetDictionaryForDepReq,
  GetXZ_SAP_CSKS,
  GetXZ_SAP_SKA1,
  GetXZ_SAP_SKA12,
  exportExcelModel
} from '@/api/MM/MM_OtherIn';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_OtherInDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddSelectMaterialsModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        MovementType: '',
        ManualPostTime: new Date(),
        CostCenter: '',
        LedgerType: '',
        MovementTypeName: '', //  移动类型名称
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        HandlenCode: '',
        HandlenName: '',
        ScrapNum: '',
        OrderNum: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        MovementType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LedgerType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        HandlenCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        OrderNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      editStatus: '',
      delList: [],
      MovementOptions: [],
      CostCenterOptions: [],
      LedgerTypeOptions: [],
      LedgerDisabled: true, // 总账科目
      CostCenterDisabled: true, // 成本中心
      OrderNumDisabled: true, // 生产订单号
      HandlenOptions: [],
      stockDisabled: true,
      materialsDisabled: true,
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      ImportDisabled: true
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    routeParams() {
      return this.$route.params;
    }
  },
  watch: {
    routeParams(val) {
      const obj = Object.keys(val);
      if (obj.length !== 0) {
        this.getPageParams();
      } else {
        this.$nextTick(() => { // 清除校验
          this.$refs['dataForm'].clearValidate();
        });
        // 新增
        this.clearSearchQuery(); // 清空字段
        this.list = []; // 清空表格
        if (this.$route.path === '/MM/MM_OtherInDetail') {
          this.getPageParams();
        }
      }
    }
  },
  created() {
    this.getPageParams();
    this.GetDictionaryForDepReq();
    this.GetXZ_SAP_CSKS();
    this.GetXZ_SAP_SKA1();
    this.getUserlist();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DepReqDetailedID) {
            v.IsDelete = true;
            this.delList.push(v.DepReqDetailedID)
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList)
        });
      }
    },
    handleCommit() {
      if (!this.searchQuery.MovementType) {
        this.showNotify('warning', '请选择移动类型');
        return
      }
      if (this.searchQuery.MovementType === 'Z01' || this.searchQuery.MovementType === 'Z02' ||
          this.searchQuery.MovementType === 'Z03' || this.searchQuery.MovementType === 'Z04' ||
          this.searchQuery.MovementType === 'Z07' || this.searchQuery.MovementType === 'Z08') {
        if (this.searchQuery.CostCenter === '' || this.searchQuery.CostCenter === undefined || this.searchQuery
          .CostCenter === null) {
          this.showNotify('warning', '请选择成本中心');
          return
        }
      } else if (this.searchQuery.MovementType === 'Z25' || this.searchQuery.MovementType === 'Z26') {
        if (this.searchQuery.LedgerType === '' || this.searchQuery.LedgerType === undefined || this.searchQuery
          .LedgerType === null) {
          this.showNotify('warning', '请选择总账科目');
          return
        }
        if (this.searchQuery.CostCenter === '' || this.searchQuery.CostCenter === undefined || this.searchQuery
          .CostCenter === null) {
          this.showNotify('warning', '请选择成本中心');
          return
        }
      } else if (this.searchQuery.MovementType === 'Z37' || this.searchQuery.MovementType === 'Z38') {
        if (this.searchQuery.LedgerType === '' || this.searchQuery.LedgerType === undefined || this.searchQuery
          .LedgerType === null) {
          this.showNotify('warning', '请选择总账科目');
          return
        }
      } else if (this.searchQuery.MovementType === '531' || this.searchQuery.MovementType === '532') {
        if (this.searchQuery.OrderNum === '' || this.searchQuery.OrderNum === undefined || this.searchQuery
          .OrderNum === null) {
          this.showNotify('warning', '请输入生产订单号');
          return
        }
      }
      if (this.searchQuery.ManualPostTime === '' || this.searchQuery.ManualPostTime === null) {
        this.showNotify('warning', '请选择过账日期');
        return
      }
      if (this.list.length === 0) {
        if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0 || this.searchQuery.MovementTypeName.indexOf('收货') >
            0) {
          this.showNotify('warning', '请选择库存信息');
        } else {
          this.showNotify('warning', '请选择物料信息');
        }
        return
      }
      let switchBtn = true;
      this.list.some(res => {
        if (res.Qty === null || res.Qty === 0 || res.Qty === '0') {
          this.showNotify('warning', '数量不能为空或者为零');
          switchBtn = false;
          return true
        }
        if (res.MovementType === undefined) {
          this.showNotify('warning', '移动类型不能为空且只能是相同的');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.startLoading();
            const query = {
              DocNum: this.searchQuery.DocNum,
              MovementType: this.searchQuery.MovementType,
              MovementTypeName: this.searchQuery.MovementTypeName,
              ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
              HandlenCode: this.searchQuery.HandlenCode,
              HandlenName: this.searchQuery.HandlenName,
              ScrapNum: this.searchQuery.ScrapNum,
              Remark: this.searchQuery.Remark,
              deldetailArray: this.delList,
              DetailedList: this.list
            };
            console.log(query);
            if (this.editStatus === 'create') {
              SubmitScanInfo(query).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_OtherIn');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err);
                this.endLoading()
              })
            } else {
              update(query).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_OtherIn');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err);
                this.endLoading()
              })
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      if (this.listQuery.MovementType === '') {
        this.showNotify('warning', '请选择移动类型');
        return false
      }
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DepReqDetailedID) {
          if (v.DepReqDetailedID === record.DepReqDetailedID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.StockID === record.StockID) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          OrderNum: this.searchQuery.OrderNum || '',
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          BinLocationCode: v.BinLocationCode,
          BinLocationName: v.BinLocationName,
          RegionCode: v.RegionCode,
          RegionName: v.RegionName,
          StockID: v.ItemCode + v.WhsCode
        })
      });

      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode] ? '' : obj[next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        DocNum: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          if (this.list.length > 0) {
            this.searchQuery.MovementType = res.Data[0].MovementType;
            this.searchQuery.CostCenter = res.Data[0].CostCenter;
            this.searchQuery.LedgerType = res.Data[0].LedgerType;
            this.searchQuery.MovementTypeName = res.Data[0].MovementTypeName; //  移动类型名称
            this.searchQuery.CostCenterName = res.Data[0].CostCenterName; // 成本中心名称,
            this.searchQuery.LedgerTypeName = res.Data[0].LedgerTypeName; // 总账科目名称,
            this.searchQuery.OrderNum = res.Data[0].OrderNum; // 生产订单号,
            if (this.searchQuery.MovementType === 'Z05' || this.searchQuery.MovementType === 'Z06') {
              this.GetXZ_SAP_SKA12()
            } else {
              this.GetXZ_SAP_SKA1()
            }
            if (this.searchQuery.MovementType === 'Z01' || this.searchQuery.MovementType === 'Z02' ||
                this.searchQuery.MovementType === 'Z03' || this.searchQuery.MovementType === 'Z04' ||
                this.searchQuery.MovementType === 'Z07' || this.searchQuery.MovementType === 'Z08') {
              this.LedgerDisabled = true; // 总账科目
              this.CostCenterDisabled = false; // 成本中心
              this.OrderNumDisabled = true; // 生产订单号
            } else if (this.searchQuery.MovementType === 'Z25' || this.searchQuery.MovementType === 'Z26') {
              this.LedgerDisabled = false; // 总账科目
              this.CostCenterDisabled = false; // 成本中心
              this.OrderNumDisabled = true; // 生产订单号
            } else if (this.searchQuery.MovementType === 'Z37' || this.searchQuery.MovementType === 'Z38') {
              this.LedgerDisabled = false; // 总账科目
              this.CostCenterDisabled = true; // 成本中心
              this.OrderNumDisabled = true; // 生产订单号
            } else if (this.searchQuery.MovementType === '531' || this.searchQuery.MovementType === '531') {
              this.LedgerDisabled = true; // 总账科目
              this.CostCenterDisabled = true; // 成本中心
              this.OrderNumDisabled = false; // 生产订单号
            } else {
              this.LedgerDisabled = true; // 总账科目
              this.CostCenterDisabled = true; // 成本中心
              this.OrderNumDisabled = true; // 生产订单号
            }

            if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0 || this.searchQuery.MovementType === '532') {
              this.materialsDisabled = true;
              this.stockDisabled = false;
            } else {
              this.materialsDisabled = false;
              this.stockDisabled = true;
            }
          }
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.DepRequisitionID) {
        this.editStatus = 'edit';
        this.getDetailList()
      } else {
        this.fetchDocNum();
        this.editStatus = 'create'
      }
    },
    clearSearchQuery() {
      this.searchQuery = {
        DocNum: '',
        MovementType: '',
        ManualPostTime: new Date(),
        CostCenter: '',
        LedgerType: '',
        MovementTypeName: '', // 移动类型名称
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        HandlenCode: '',
        HandlenName: '',
        ScrapNum: '',
        OrderNum: '',
        Remark: ''
      };
      this.LedgerDisabled = false;
      this.stockDisabled = true;
      this.materialsDisabled = true;
    },
    GetDictionaryForDepReq() {
      GetDictionaryForDepReq().then(res => {
        if (res.Code === 2000) {
          this.MovementOptions = res.Data
        }
      })
    },
    changeMovement(e) {
      this.ImportDisabled = false;
      this.list = [];
      if (e === 'Z05' || e === 'Z06') {
        this.GetXZ_SAP_SKA12()
      } else {
        this.GetXZ_SAP_SKA1()
      }
      if (e === 'Z01' || e === 'Z02' || e === 'Z03' || e === 'Z04' || e === 'Z07' || e === 'Z08') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.searchQuery.OrderNum = '';
        this.OrderNumDisabled = true; // 生产订单号
        this.LedgerDisabled = true; // 总账科目
        this.CostCenterDisabled = false // 成本中心
      } else if (e === 'Z25' || e === 'Z26') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.searchQuery.OrderNum = '';
        this.OrderNumDisabled = true; // 生产订单号
        this.LedgerDisabled = false; // 总账科目
        this.CostCenterDisabled = false // 成本中心
      } else if (e === 'Z37' || e === 'Z38') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.searchQuery.OrderNum = '';
        this.OrderNumDisabled = true; // 生产订单号
        this.LedgerDisabled = false; // 总账科目
        this.CostCenterDisabled = true // 成本中心
      } else if (e === '531' || e === '532') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.searchQuery.OrderNum = '';
        this.OrderNumDisabled = false; // 生产订单号
        this.LedgerDisabled = true; // 总账科目
        this.CostCenterDisabled = true // 成本中心
      } else {
        this.LedgerDisabled = true; // 总账科目
        this.CostCenterDisabled = true // 成本中心
      }
      const obj = this.MovementOptions.find(v => v.EnumValue === e);
      this.searchQuery.MovementTypeName = obj.EnumValue1;
      if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0 || this.searchQuery.MovementTypeName.indexOf('收货') >
          0) {
        this.materialsDisabled = true;
        this.stockDisabled = false
      } else {
        this.materialsDisabled = false;
        this.stockDisabled = true
      }
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'MovementTypeName', this.searchQuery.MovementTypeName);
          this.$set(res, 'MovementType', this.searchQuery.MovementType);
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter);
          this.$set(res, 'LedgerTypeName', this.searchQuery.LedgerTypeName);
          this.$set(res, 'LedgerType', this.searchQuery.LedgerType);
        })
      }
    },
    changeOrderNum(e) {
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'OrderNum', e)
        })
      }
    },
    GetXZ_SAP_CSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.searchQuery.CostCenterName = obj.LTEXT;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter);
        })
      }
      this.$forceUpdate();
    },
    GetXZ_SAP_SKA1() {
      this.LedgerTypeOptions = [];
      GetXZ_SAP_SKA1().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    GetXZ_SAP_SKA12() {
      this.LedgerTypeOptions = [];
      GetXZ_SAP_SKA12().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    changeLedgerType(e) {
      const obj = this.LedgerTypeOptions.find(v => v.SAKNR === e);
      this.searchQuery.LedgerTypeName = obj.TXT50;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'LedgerTypeName', this.searchQuery.LedgerTypeName);
          this.$set(res, 'LedgerType', this.searchQuery.LedgerType)
        })
      }
      this.$forceUpdate();
    },

    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        // console.log(response);
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName
    },
    selectCustomerMaterials() {
      this.$refs.modalFormMaterials.add();
    },
    modalFormOkMaterials(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.MATNR, // 物料件号
          ItemName: v.MAKTX, // 物料名称
          Qty: v.Qty,
          Unit: v.MEINS,
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          BinLocationCode: v.BinLocationCode,
          BinLocationName: v.BinLocationName,
          RegionCode: v.RegionCode,
          RegionName: v.RegionName,
          StockID: v.MATNR + v.WhsCode
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode] ? '' : obj[next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      data.forEach(res => {
        this.uploadExcelData.push({
          ItemCode: res.物料编码, // 物料件号
          ItemName: res.物料描述, // 物料名称
          Qty: res.数量,
          WhsCode: res.仓库编号,
          WhsName: res.仓库,
          Remark: res.备注,
          BinLocationCode: '',
          BinLocationName: '',
          RegionCode: '',
          RegionName: '',
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '' // 总账科目名称,
        })
      })
    },

    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      console.log(0, this.uploadExcelData);
      this.dialogImprotVisable = false;
      const obj = {};
      this.list = this.list.concat(this.uploadExcelData);
      this.isProcessing = false;
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    }
  }
}
</script>
