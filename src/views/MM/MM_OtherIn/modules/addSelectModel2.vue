<template>
  <el-dialog title="库存信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="filter-container">
      <el-input
        v-model="listQuery.ItemCode"
        size="small"
        clearable
        class="filter-item"
        placeholder="物料件号"
        style="width: 140px"
        @keydown.enter.native="handleSearchFilter"
      />
      <el-select
        v-model="listQuery.WhsCode"
        size="small"
        style="width: 140px"
        class="filter-item"
        clearable
        filterable
        placeholder="请选择仓库"
      >
        <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.SpecialStock"
        size="small"
        style="width: 140px"
        class="filter-item"
        clearable
        filterable
        placeholder="请选择特殊库存"
        @change="changeSpecialStock"
      >
        <el-option
          v-for="item in SpecialStockOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        />
      </el-select>
      <!-- <el-select size="small" style="width: 140px" class="filter-item" clearable v-model="listQuery.SupplierCode"
        filterable placeholder="请选择供应商" @change="changeSupplierName" :disabled="true">
        <el-option v-for="item in SupplierOptions" :key="item.SupplierCode"
          :label="item.SupplierCode+'-'+item.SupplierName" :value="item.SupplierCode">
        </el-option>
      </el-select> -->
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
        添加</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="入库数量" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model="scope.row.OutQty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" width="100" align="center" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" width="120" align="center" show-overflow-tooltip />
    </el-table>
    <!-- <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="handleCustomerFilter" /> -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetPageListBySAP
} from '@/api/MM/MM_OtherIn';
import {
  GetStockList
} from '@/api/RPT/RPT_Stock';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import {
  GetSRM_SupplierInfo
} from '@/api/MM/MM_SubcontractingApplication';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        ItemCode: '',
        WhsCode: '',
        SpecialStock: '',
        SupplierCode: ''
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      options: [],
      SpecialStockOptions: [{
        label: '正常',
        value: ''
      }, {
        label: '供应商',
        value: 'O',
        disabled: true
      }, {
        label: '销售',
        value: 'E'
      }],
      SupplierOptions: [],
      SupplierDisabled: true
    }
  },
  computed: {

  },
  created() {},
  methods: {
    formatDate,
    formatDateTime,
    add() {
      this.listQuery = {
        ItemCode: '',
        WhsCode: '',
        SpecialStock: '',
        SupplierCode: ''
      };
      this.list = [];
      this.GetXZ_SAP();
      this.GetSRM();
      this.dialogCustomerFormVisible = true;
    },
    edit(record) {
      this.model = Object.assign({}, record)
    },
    handleCustomerRowSelectEvent(selection) {
      this.multipleSelection = selection
    },
    handleSearchFilter() {
      if (this.listQuery.SpecialStock !== 'O') {
        if (this.listQuery.ItemCode === '' && this.listQuery.WhsCode === '') {
          this.showNotify('warning', '请输入物料件号或者选择仓库');
          return
        }
      } else {
        if (this.listQuery.SupplierCode === '') {
          this.showNotify('warning', '请选择供应商');
          return
        }
      }
      this.handleCustomerFilter()
    },
    handleCustomerFilter() {
      this.listLoading = true;
      GetStockList(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      }).catch(error => {
        this.listLoading = false;
      });
    },
    handleSelectCustomer() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '入库数量不能为空或者为零');
          switchBtn = false;
          return true
        }
        if (res.OutQty > res.Qty) {
          this.showNotify('warning', '入库数量不能大于库存数量');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
        this.dialogCustomerFormVisible = false;
      }
    },
    handleAdd() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '入库数量不能为空或者为零');
          switchBtn = false;
          return true;
        }
        if (res.OutQty > res.Qty) {
          this.showNotify('warning', '入库数量不能大于库存数量');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection)
      }
    },
    inputOutQty(e) {
      if (e.OutQty <= 0) {
        this.showNotify('warning', '入库数量不得小于等于0');
        return
      } else if (e.OutQty > e.Qty) {
        this.showNotify('warning', '入库数量不得大于库存数量');
        return
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.WhsName = this.options.filter(item => item.value === e)[0].label;
      // this.handleFilter()
    },
    GetSRM() {
      GetSRM_SupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.SupplierOptions = res.Data
        }
      })
    },
    changeSupplierName(e) {
      // const obj = this.SupplierOptions.find(v => v.SupplierCode === e)
      // this.searchQuery.SupplierName = obj.SupplierName
    },
    changeSpecialStock(e) {
      if (e === 'O') {
        this.SupplierDisabled = false;
      } else {
        this.SupplierDisabled = true;
        this.listQuery.SupplierCode = '';
      }
    }
  }
}
</script>

<style scoped>
</style>
