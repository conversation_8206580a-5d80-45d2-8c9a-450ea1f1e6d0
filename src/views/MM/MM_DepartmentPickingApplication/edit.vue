<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("ui.MM.DepartmentPickingApplicationDetail.title") }}</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item
            class="filter-item"
            :label="$t('ui.MM.DepartmentPickingApplication.RequisitionNum')"
            prop="DocNum"
          >
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型" prop="MovementType">
            <el-select
              v-model="searchQuery.MovementType"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeMovement"
            >
              <el-option
                v-for="item in MovementOptions"
                :key="item.EnumValue"
                :label="item.EnumValue+'-'+item.EnumValue1"
                :value="item.EnumValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="成本中心"
            prop="CostCenter"
            :rules="CostCenterDisabled===false?rules.CostCenter:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.CostCenter"
              filterable
              placeholder="请选择"
              :disabled="CostCenterDisabled"
              style="width: 100%;"
              @change="changeCostCenter"
            >
              <el-option
                v-for="item in CostCenterOptions"
                :key="item.KOSTL"
                :label="item.KOSTL+'-'+item.LTEXT"
                :value="item.KOSTL"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="总账科目"
            :rules="LedgerDisabled===false?rules.LedgerType:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.LedgerType"
              filterable
              placeholder="请选择"
              :disabled="LedgerDisabled"
              style="width: 100%;"
              @change="changeLedgerType"
            >
              <el-option
                v-for="item in LedgerTypeOptions"
                :key="item.SAKNR"
                :label="item.SAKNR+'-'+item.TXT50"
                :value="item.SAKNR"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="内部订单"
            :rules="OrderNumDisabled===false?rules.OrderNum:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.OrderNum"
              filterable
              placeholder="请选择"
              :disabled="OrderNumDisabled"
              style="width: 100%;"
              @change="changeOrderNum"
            >
              <el-option
                v-for="item in OrderNumOptions"
                :key="item.AUFNR"
                :label="item.AUFNR+'-'+item.KTEXT"
                :value="item.AUFNR"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="资产卡片"
            :rules="AssetCardDisabled===false?rules.AssetCard:[{ required: false, message: '请选择', trigger: 'change' }]"
          >
            <el-select
              v-model="searchQuery.AssetCard"
              filterable
              placeholder="请选择"
              :disabled="AssetCardDisabled"
              style="width: 100%;"
              @change="changeAssetCard"
            >
              <el-option
                v-for="item in AssetCardOptions"
                :key="item.ANLN1"
                :label="item.ANLN1+'-'+item.TXT50"
                :value="item.ANLN1"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="库存信息" readonly :disabled="stockDisabled">
              <el-button slot="append" icon="el-icon-more" :disabled="stockDisabled" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="物料信息" readonly :disabled="materialsDisabled">
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="materialsDisabled"
                @click="selectCustomerMaterials"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              type="date"
              :clearable="false"
              placeholder="过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员" prop="HandlenCode">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.MM.DepartmentPickingApplication.Remark')">
            <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit(false)">{{ $t("Common.confirm") }}
      </el-button>
      <el-button v-if="editStatus === 'create'" type="success" size="small" icon="el-icon-edit" @click="handleCommit(true)">批量提交
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        :disabled="ImportDisabled"
        size="small"
        @click="handleImport"
      >导入模板
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-download" size="small" @click="handleExportModel">下载模板
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="内部订单" prop="OrderNumName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="资产卡片" prop="AssetCardName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="100" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
    <add-select-materials-model ref="modalFormMaterials" :data-list="list" @ok="modalFormOkMaterials" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddSelectMaterialsModel from './modules/addSelectMaterialsModel'
import AddModel from './modules/addModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetList,
  GetDictionaryForDepReq,
  GetXZ_SAP_CSKS,
  GetXZ_SAP_SKA1,
  GetXZ_SAP_SKA12,
  GetSAP_001,
  GetSAP_002,
  exportExcelModel,
  BatchSubmitScanInfo
} from '@/api/MM/MM_DepartmentPickingApplication';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_DepartmentPickingApplicationDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddSelectMaterialsModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        MovementType: '',
        ManualPostTime: new Date(),
        CostCenter: '',
        LedgerType: '',
        OrderNum: '',
        AssetCard: '',
        MovementTypeName: '', //  移动类型名称
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        OrderNumName: '', // 内部订单名称,
        AssetCardName: '', // 资产卡片名称
        HandlenCode: '',
        HandlenName: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        MovementType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LedgerType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        OrderNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        AssetCard: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        HandlenCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: '',
      delList: [],
      MovementOptions: [],
      CostCenterOptions: [],
      LedgerTypeOptions: [],
      OrderNumOptions: [],
      AssetCardOptions: [],
      LedgerDisabled: true, // 总账科目
      OrderNumDisabled: true, // 内部订单
      AssetCardDisabled: true, // 资产卡片
      CostCenterDisabled: true, // 成本中心
      HandlenOptions: [],
      stockDisabled: true,
      materialsDisabled: true,
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      ImportDisabled: true
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    routeParams() {
      return this.$route.params;
    }
  },
  watch: {
    routeParams(val) {
      const obj = Object.keys(val);
      if (obj.length !== 0) {
        this.getPageParams();
      } else {
        this.$nextTick(() => { // 清除校验
          this.$refs['dataForm'].clearValidate();
        });
        // 新增
        this.clearSearchQuery(); // 清空字段
        this.list = []; // 清空表格
        if (this.$route.path === '/MM/MM_DepartmentPickingApplicationDetail') {
          this.getPageParams();
        }
      }
    }
  },
  created() {
    this.getPageParams();
    this.GetDictionaryForDepReq();
    this.GetXZ_SAP_CSKS();
    this.GetXZ_SAP_SKA1();
    this.GetSAP_001();
    this.GetSAP_002();
    this.getUserlist()
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DepReqDetailedID) {
            v.IsDelete = true;
            this.delList.push(v.DepReqDetailedID)
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList)
        });
      }
    },
    handleCommit(batch) {
      if (!this.searchQuery.MovementType) {
        this.showNotify('warning', '请选择移动类型');
        return
      }
      if (this.searchQuery.MovementType === 'Z23' || this.searchQuery.MovementType === 'Z24') {
        if (this.searchQuery.LedgerType === '' || this.searchQuery.LedgerType === undefined || this.searchQuery
          .LedgerType === null) {
          this.showNotify('warning', '请选择总账科目');
          return
        }
        if (this.searchQuery.CostCenter === '' || this.searchQuery.CostCenter === undefined || this.searchQuery
          .CostCenter === null) {
          this.showNotify('warning', '请选择成本中心');
          return
        }
      } else if (this.searchQuery.MovementType === 'Z15' || this.searchQuery.MovementType === 'Z16') {
        if (this.searchQuery.OrderNum === '' || this.searchQuery.OrderNum === undefined || this.searchQuery
          .OrderNum === null) {
          this.showNotify('warning', '请选择内部订单');
          return
        }
        if (this.searchQuery.CostCenter === '' || this.searchQuery.CostCenter === undefined || this.searchQuery
          .CostCenter === null) {
          this.showNotify('warning', '请选择成本中心');
          return
        }
        if (this.searchQuery.HandlenCode === '' || this.searchQuery.HandlenCode === undefined || this.searchQuery
          .HandlenCode === null) {
          this.showNotify('warning', '请选择办理人员');
          return
        }
      } else if (this.searchQuery.MovementType === '241' || this.searchQuery.MovementType === '242') {
        if (this.searchQuery.AssetCard === '' || this.searchQuery.AssetCard === undefined || this.searchQuery
          .AssetCard === null) {
          this.showNotify('warning', '请选择资产卡片');
          return
        }
        if (this.searchQuery.CostCenter === '' || this.searchQuery.CostCenter === undefined || this.searchQuery
          .CostCenter === null) {
          this.showNotify('warning', '请选择成本中心');
          return
        }
      } else if (this.searchQuery.MovementType === 'Z35' || this.searchQuery.MovementType === 'Z36' ||
        this.searchQuery.MovementType === 'Z37' || this.searchQuery.MovementType === 'Z38') {
        if (this.searchQuery.LedgerType === '' || this.searchQuery.LedgerType === undefined || this.searchQuery
          .LedgerType === null) {
          this.showNotify('warning', '请选择总账科目');
          return
        }
      }
      if (this.searchQuery.ManualPostTime === '' || this.searchQuery.ManualPostTime === null) {
        this.showNotify('warning', '请选择过账日期');
        return
      }
      if (this.list.length === 0) {
        if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0 || this.searchQuery.MovementTypeName.indexOf('收货') >
            0) {
          this.showNotify('warning', '请选择物料信息');
        } else {
          this.showNotify('warning', '请选择库存信息');
        }
        return
      }
      let switchBtn = true;
      this.list.some(res => {
        if (res.Qty === null || res.Qty === 0 || res.Qty === '0' || res.Qty === '') {
          this.showNotify('warning', '数量不能为空或者为零');
          switchBtn = false;
          return true
        }
        if (res.MovementType === undefined) {
          this.showNotify('warning', '移动类型不能为空且只能是相同的');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.startLoading();
            const query = {
              DocNum: this.searchQuery.DocNum,
              MovementType: this.searchQuery.MovementType,
              MovementTypeName: this.searchQuery.MovementTypeName,
              ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
              HandlenCode: this.searchQuery.HandlenCode,
              HandlenName: this.searchQuery.HandlenName,
              Remark: this.searchQuery.Remark,
              deldetailArray: this.delList,
              DetailedList: this.list
            };
            console.log(query);
            if (this.editStatus === 'create') {
              if (batch) {
                BatchSubmitScanInfo(query).then(res => {
                  if (res.Code === 2000) {
                    this.showNotify('success', res.Message);
                    this.backTo('MM.MM_DepartmentPickingApplication');
                  } else {
                    this.showNotify('error', response.Message);
                  }
                  this.endLoading()
                }).catch(err => {
                  console.log(err);
                  this.endLoading()
                })
              } else {
                SubmitScanInfo(query).then(res => {
                  if (res.Code === 2000) {
                    this.showNotify('success', res.Message);
                    this.backTo('MM.MM_DepartmentPickingApplication');
                  } else {
                    this.showNotify('error', response.Message);
                  }
                  this.endLoading()
                }).catch(err => {
                  console.log(err);
                  this.endLoading()
                })
              }
            } else {
              update(query).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_DepartmentPickingApplication');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err);
                this.endLoading()
              })
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      if (this.listQuery.MovementType === '') {
        this.showNotify('warning', '请选择移动类型');
        return false
      }
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DepReqDetailedID) {
          if (v.DepReqDetailedID === record.DepReqDetailedID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.StockID === record.StockID) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          OrderNum: this.searchQuery.OrderNum || '',
          AssetCard: this.searchQuery.AssetCard || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          OrderNumName: this.searchQuery.OrderNumName || '', // 内部订单名称,
          AssetCardName: this.searchQuery.AssetCardName || '', // 资产卡片名称
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          BinLocationCode: v.BinLocationCode,
          BinLocationName: v.BinLocationName,
          RegionCode: v.RegionCode,
          RegionName: v.RegionName,
          StockID: v.ItemCode + v.WhsCode
        })
      });

      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode] ? '' : obj[next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        DocNum: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          if (this.list.length > 0) {
            this.searchQuery.MovementType = res.Data[0].MovementType;
            this.searchQuery.CostCenter = res.Data[0].CostCenter;
            this.searchQuery.LedgerType = res.Data[0].LedgerType;
            this.searchQuery.OrderNum = res.Data[0].OrderNum;
            this.searchQuery.AssetCard = res.Data[0].AssetCard;
            this.searchQuery.MovementTypeName = res.Data[0].MovementTypeName; //  移动类型名称
            this.searchQuery.CostCenterName = res.Data[0].CostCenterName; // 成本中心名称,
            this.searchQuery.LedgerTypeName = res.Data[0].LedgerTypeName; // 总账科目名称,
            this.searchQuery.OrderNumName = res.Data[0].OrderNumName; // 内部订单名称,
            this.searchQuery.AssetCardName = res.Data[0].AssetCardName; // 资产卡片名称
            if (this.searchQuery.MovementType === 'Z35' || this.searchQuery.MovementType === 'Z36' ||
              this.searchQuery.MovementType === 'Z37' || this.searchQuery.MovementType === 'Z38') {
              this.GetXZ_SAP_SKA12()
            } else {
              this.GetXZ_SAP_SKA1()
            }
            if (this.searchQuery.MovementType === 'Z23' || this.searchQuery.MovementType === 'Z24') {
              this.OrderNumDisabled = true;
              this.AssetCardDisabled = true;
              this.LedgerDisabled = false;
              this.CostCenterDisabled = false // 成本中心
            } else if (this.searchQuery.MovementType === 'Z15' || this.searchQuery.MovementType === 'Z16') {
              this.LedgerDisabled = true;
              this.OrderNumDisabled = false;
              this.AssetCardDisabled = true;
              this.CostCenterDisabled = false // 成本中心
            } else if (this.searchQuery.MovementType === '241' || this.searchQuery.MovementType === '242') {
              this.OrderNumDisabled = true;
              this.LedgerDisabled = true;
              this.AssetCardDisabled = false;
              this.CostCenterDisabled = false // 成本中心
            } else if (this.searchQuery.MovementType === 'Z19' || this.searchQuery.MovementType === 'Z20' || this
              .searchQuery.MovementType === 'Z21' || this.searchQuery.MovementType === 'Z22') {
              this.OrderNumDisabled = true;
              this.LedgerDisabled = true;
              this.AssetCardDisabled = true;
              this.CostCenterDisabled = false // 成本中心
            } else if (this.searchQuery.MovementType === 'Z35' || this.searchQuery.MovementType === 'Z36' ||
              this.searchQuery.MovementType === 'Z37' || this.searchQuery.MovementType === 'Z38') {
              this.OrderNumDisabled = true; // 内部订单
              this.AssetCardDisabled = true; // 资产卡片
              this.LedgerDisabled = false; // 总账科目
              this.CostCenterDisabled = true // 成本中心
            } else {
              this.OrderNumDisabled = false; // 内部订单
              this.LedgerDisabled = false; // 总账科目
              this.AssetCardDisabled = false; // 资产卡片
              this.CostCenterDisabled = false // 成本中心
            }

            if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0 || this.searchQuery.MovementTypeName.indexOf(
              '收货') > 0) {
              this.materialsDisabled = false;
              this.stockDisabled = true
            } else {
              this.materialsDisabled = true;
              this.stockDisabled = false
            }
          }
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.DepRequisitionID) {
        this.editStatus = 'edit';
        this.getDetailList()
      } else {
        this.fetchDocNum();
        this.editStatus = 'create'
      }
    },
    clearSearchQuery() {
      this.searchQuery = {
        DocNum: '',
        MovementType: '',
        ManualPostTime: new Date(),
        CostCenter: '',
        LedgerType: '',
        OrderNum: '',
        AssetCard: '',
        MovementTypeName: '', //  移动类型名称
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        OrderNumName: '', // 内部订单名称,
        AssetCardName: '', // 资产卡片名称
        HandlenCode: '',
        HandlenName: '',
        Remark: ''
      };
      this.LedgerDisabled = false;
      this.OrderNumDisabled = false;
      this.AssetCardDisabled = false;
      this.stockDisabled = true;
      this.materialsDisabled = true
    },
    GetDictionaryForDepReq() {
      GetDictionaryForDepReq().then(res => {
        if (res.Code === 2000) {
          this.MovementOptions = res.Data
        }
      })
    },
    changeMovement(e) {
      this.ImportDisabled = false;
      this.list = [];
      if (e === 'Z35' || e === 'Z36' || e === 'Z37' || e === 'Z38') {
        this.GetXZ_SAP_SKA12()
      } else {
        this.GetXZ_SAP_SKA1()
      }
      if (e === 'Z23' || e === 'Z24') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.AssetCard = '';
        this.searchQuery.AssetCardName = '';
        this.searchQuery.OrderNum = '';
        this.searchQuery.OrderNumName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.OrderNumDisabled = true; // 内部订单
        this.AssetCardDisabled = true; // 资产卡片
        this.LedgerDisabled = false; // 总账科目
        this.CostCenterDisabled = false // 成本中心
      } else if (e === 'Z15' || e === 'Z16') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.AssetCard = '';
        this.searchQuery.AssetCardName = '';
        this.searchQuery.OrderNum = '';
        this.searchQuery.OrderNumName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.LedgerDisabled = true; // 总账科目
        this.OrderNumDisabled = false; // 内部订单
        this.AssetCardDisabled = true; // 资产卡片
        this.CostCenterDisabled = false // 成本中心
      } else if (e === '241' || e === '242') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.AssetCard = '';
        this.searchQuery.AssetCardName = '';
        this.searchQuery.OrderNum = '';
        this.searchQuery.OrderNumName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.OrderNumDisabled = true; // 内部订单
        this.LedgerDisabled = true; // 总账科目
        this.AssetCardDisabled = false; // 资产卡片
        this.CostCenterDisabled = false // 成本中心
      } else if (e === 'Z19' || e === 'Z20' || e === 'Z21' || e === 'Z22') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.AssetCard = '';
        this.searchQuery.AssetCardName = '';
        this.searchQuery.OrderNum = '';
        this.searchQuery.OrderNumName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.OrderNumDisabled = true; // 内部订单
        this.LedgerDisabled = true; // 总账科目
        this.AssetCardDisabled = true; // 资产卡片
        this.CostCenterDisabled = false // 成本中心
      } else if (e === 'Z35' || e === 'Z36' || e === 'Z37' || e === 'Z38') {
        this.searchQuery.LedgerType = '';
        this.searchQuery.LedgerTypeName = '';
        this.searchQuery.AssetCard = '';
        this.searchQuery.AssetCardName = '';
        this.searchQuery.OrderNum = '';
        this.searchQuery.OrderNumName = '';
        this.searchQuery.CostCenter = '';
        this.searchQuery.CostCenterName = '';
        this.OrderNumDisabled = true; // 内部订单
        this.AssetCardDisabled = true; // 资产卡片
        this.LedgerDisabled = false; // 总账科目
        this.CostCenterDisabled = true // 成本中心
      } else {
        this.OrderNumDisabled = false; // 内部订单
        this.LedgerDisabled = false; // 总账科目
        this.AssetCardDisabled = false; // 资产卡片
        this.CostCenterDisabled = false // 成本中心
      }
      const obj = this.MovementOptions.find(v => v.EnumValue === e);
      this.searchQuery.MovementTypeName = obj.EnumValue1;
      // if (this.searchQuery.MovementTypeName.indexOf('冲销') > 0 || this.searchQuery.MovementTypeName.indexOf('收货') >
      //   0) {
      if (e === '242' || e === 'Z16' || e === 'Z20' || e === 'Z22' || e === 'Z24' || e === 'Z37' || e === 'Z36') {
        this.materialsDisabled = false;
        this.stockDisabled = true
      } else {
        this.materialsDisabled = true;
        this.stockDisabled = false
      }
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'MovementTypeName', this.searchQuery.MovementTypeName);
          this.$set(res, 'MovementType', this.searchQuery.MovementType);
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter);
          this.$set(res, 'LedgerTypeName', this.searchQuery.LedgerTypeName);
          this.$set(res, 'LedgerType', this.searchQuery.LedgerType);
          this.$set(res, 'OrderNumName', this.searchQuery.OrderNumName);
          this.$set(res, 'OrderNum', this.searchQuery.OrderNum);
          this.$set(res, 'AssetCardName', this.searchQuery.AssetCardName);
          this.$set(res, 'AssetCard', this.searchQuery.AssetCard)
        })
      }
    },
    GetXZ_SAP_CSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.searchQuery.CostCenterName = obj.LTEXT;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter)
        })
      }
      this.$forceUpdate();
    },
    GetXZ_SAP_SKA1() {
      this.LedgerTypeOptions = [];
      GetXZ_SAP_SKA1().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    GetXZ_SAP_SKA12() {
      this.LedgerTypeOptions = [];
      GetXZ_SAP_SKA12().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    changeLedgerType(e) {
      const obj = this.LedgerTypeOptions.find(v => v.SAKNR === e);
      this.searchQuery.LedgerTypeName = obj.TXT50;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'LedgerTypeName', this.searchQuery.LedgerTypeName);
          this.$set(res, 'LedgerType', this.searchQuery.LedgerType)
        })
      }
      this.$forceUpdate();
    },
    GetSAP_001() {
      GetSAP_001().then(res => {
        if (res.Code === 2000) {
          this.OrderNumOptions = res.Data
        }
      })
    },
    changeOrderNum(e) {
      const obj = this.OrderNumOptions.find(v => v.AUFNR === e);
      this.searchQuery.OrderNumName = obj.KTEXT;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'OrderNumName', this.searchQuery.OrderNumName);
          this.$set(res, 'OrderNum', this.searchQuery.OrderNum)
        })
      }
      this.$forceUpdate();
    },
    GetSAP_002() {
      GetSAP_002().then(res => {
        if (res.Code === 2000) {
          this.AssetCardOptions = res.Data
        }
      })
    },
    changeAssetCard(e) {
      const obj = this.AssetCardOptions.find(v => v.ANLN1 === e);
      this.searchQuery.AssetCardName = obj.TXT50;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'AssetCardName', this.searchQuery.AssetCardName);
          this.$set(res, 'AssetCard', this.searchQuery.AssetCard)
        })
      }
      this.$forceUpdate();
    },
    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        // console.log(response);
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName
    },
    selectCustomerMaterials() {
      this.$refs.modalFormMaterials.add();
    },
    modalFormOkMaterials(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.MATNR, // 物料件号
          ItemName: v.MAKTX, // 物料名称
          Qty: v.Qty,
          Unit: v.MEINS,
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          OrderNum: this.searchQuery.OrderNum || '',
          AssetCard: this.searchQuery.AssetCard || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          OrderNumName: this.searchQuery.OrderNumName || '', // 内部订单名称,
          AssetCardName: this.searchQuery.AssetCardName || '', // 资产卡片名称
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          BinLocationCode: v.BinLocationCode,
          BinLocationName: v.BinLocationName,
          RegionCode: v.RegionCode,
          RegionName: v.RegionName,
          StockID: v.MATNR + v.WhsCode
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode] ? '' : obj[next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      data.forEach(res => {
        this.uploadExcelData.push({
          ItemCode: res.物料编码, // 物料件号
          ItemName: res.物料描述, // 物料名称
          Qty: res.数量,
          WhsCode: res.仓库编号,
          WhsName: res.仓库,
          Remark: res.备注,
          BinLocationCode: '',
          BinLocationName: '',
          RegionCode: '',
          RegionName: '',
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          OrderNum: this.searchQuery.OrderNum || '',
          AssetCard: this.searchQuery.AssetCard || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          OrderNumName: this.searchQuery.OrderNumName || '', // 内部订单名称,
          AssetCardName: this.searchQuery.AssetCardName || '', // 资产卡片名称
          StockID: res.物料编码 + res.仓库编号
        })
      })
    },

    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      console.log(0, this.uploadExcelData);
      this.dialogImprotVisable = false;
      const obj = {};
      this.list = this.list.concat(this.uploadExcelData);
      this.isProcessing = false;
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    }
  }
}
</script>
