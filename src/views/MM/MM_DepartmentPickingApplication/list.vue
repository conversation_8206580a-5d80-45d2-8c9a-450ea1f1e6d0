<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        filterable
        size="small"
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.IsCancel"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in IsCancelOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.Status"
        filterable
        size="small"
        placeholder="审核状态"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in statusOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        clearable
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ItemCode"
        clearable
        size="small"
        class="filter-item"
        placeholder="物料信息"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.DocNum"
        clearable
        size="small"
        class="filter-item"
        placeholder="领料单号"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.MovementType"
        clearable
        size="small"
        class="filter-item"
        placeholder="移动类型"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_DepartmentPickingApplication.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_DepartmentPickingApplication.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >
        {{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_DepartmentPickingApplication.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_DepartmentPickingApplication.Audit' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleAudit"
      >审核</el-button>
      <el-button
        v-permission="{ name: 'MM.MM_DepartmentPickingApplication.Cancel' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleCancel"
      >取消</el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_DepartmentPickingApplication.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_DepartmentPickingApplication.Nullify'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="postDisable"
        @click="handleNullify"
      >作废
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_DepartmentPickingApplication.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handlePrint"
      >
        {{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_DepartmentPickingApplication.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="领料单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="Status" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if=" scope.row.Status === 0">未审核</span>
          <span v-if=" scope.row.Status === 1">审核中</span>
          <span v-if=" scope.row.Status === 2">已审核</span>
          <span v-if=" scope.row.Status === 3">已取消</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="办理人员" prop="HandlenName" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="是否作废" prop="IsCancel" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsCancel | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.IsPosted')"
        prop="IsPosted"
        align="center"
        fixed="right"
        width="80"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.PostTime')"
        width="100"
        prop="PostTime"
        align="center"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.MM.DepartmentPickingApplication.DetailTitle") }}</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :default-sort="{prop: 'Line'}"
      @sort-change="detailSortChange"
    >
      <el-table-column prop="Line" align="center" width="50" label="行号" />
      <el-table-column label="领料单号" prop="DocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <!-- <el-table-column label="状态" prop="Status" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if=" scope.row.Status === 0">未审核</span>
          <span v-if=" scope.row.Status === 1">审核中</span>
          <span v-if=" scope.row.Status === 2">已审核</span>
          <span v-if=" scope.row.Status === 3">已取消</span>
        </template>
      </el-table-column> -->
      <el-table-column label="移动类型编号" prop="MovementType" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="移动类型名称" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="内部订单" prop="OrderNumName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="资产卡片" prop="AssetCardName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="区域" prop="RegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="库位" prop="BinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.Remark')"
        prop="Remark"
        align="center"
        width="200"
        show-overflow-tooltip
      />
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.IsPosted')"
        prop="IsPosted"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column :label="$t('ui.MM.DepartmentPickingApplication.PostTime')" width="160" prop="PostTime"
        align="center" :formatter="formatDateTime" show-overflow-tooltip /> -->
      <el-table-column label="sap物料凭证单号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="sap物料凭证行号" prop="SapLine" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160" show-overflow-tooltip
        :formatter="formatDateTime" /> -->

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  GetPageList,
  batchDelete,
  exportExcelFile,
  DoPost,
  printToPDF,
  Audit,
  Reject,
  exportToExcelModel,
  getCancel
} from '@/api/MM/MM_DepartmentPickingApplication';

export default {
  name: 'MM.MM_DepartmentPickingApplication',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        Status: '',
        IsCancel: '',
        ItemCode: '',
        DocNum: '',
        MovementType: ''
      },
      hasPostedData: false,
      dialogImprotVisable: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      statusOptions: [{
        label: '全部审核',
        key: ''
      },
      {
        label: '未审核',
        key: 0
      },
      {
        label: '审核中',
        key: 1
      },
      {
        label: '已审核',
        key: 2
      },
      {
        label: '已取消',
        key: 3
      }
      ],
      IsCancelOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '已作废',
        key: true
      },
      {
        label: '未作废',
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM_DepartmentPickingApplication') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handlePrint() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      //   if (this.checkSingleSelection(selectRows)) {
      const docNums = selectRows.map(v => v.DocNum);
      console.log(docNums);
      printToPDF({
        docNums: docNums
      }).then(response => {
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 导出
    handleExport() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        IsCancel: this.listQuery.IsCancel,
        Status: this.listQuery.Status,
        ItemCode: this.listQuery.ItemCode,
        DocNum: this.listQuery.DocNum,
        MovementType: this.listQuery.MovementType,
        DocNums: selectRows.map(v => v.DocNum) || []
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '部门领退料申请');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账功能模块
    handlePosting() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.Status === 3) {
            this.showNotify('warning', '已取消的信息不能过账');
            this.isProcessing = false;
            switchBtn = false;
            return true
          }
          if (v.Status !== 2) {
            this.showNotify('warning', '未审核过的信息不能过账');
            this.isProcessing = false;
            switchBtn = false;
            return true
          }
          if (v.IsCancel === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止过账');
            switchBtn = false;
            return true
          }
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                console.log(res.Message)
                this.$alert(res.Message, '订单过账', {
                  confirmButtonText: '好的',
                  type: 'info',
                  dangerouslyUseHTMLString: true
                }).then((rst) => {
                  this.listLoading = false;
                  this.$refs['upload'].clearFiles();
                });

                // if (res.MessageParam === 2000) {
                //   this.showNotify("success", res.Message || "Common.postSuccess");
                // } else {
                //   this.showNotify("warning", res.Message || "Common.postSuccess");
                // }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
              // this.handleFilter()
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      const postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止删除');
          switchBtn = false;
          return true
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿删除');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === 2) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已审核，请勿删除');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);

          const arrRowsID = selectRows.map(v => v.DocNum);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail()
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign({
        DocNum: this.currentRow.DocNum
      }, this.listDetailQuery);
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('MM.MM_DepartmentPickingApplicationDetail');
    },
    handleUpload() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    handleDownload() {
      exportToExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    handleUpdate() {
      let switchBtn = true;
      this.multipleSelection.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止修改');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === 2) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已审核，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('MM.MM_DepartmentPickingApplicationDetail', this.multipleSelection[0]);
      }
    },
    // 取消
    handleCancel() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿取消操作');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === 3) {
          this.showNotify('warning', '请勿重复取消');
          switchBtn = false;
          return true
        }
        if (v.Status === 2 && v.IsPosted === true) {
          this.showNotify('warning', '已审核并且已过账的信息不能取消');
          switchBtn = false;
          return true
        }
        if (v.Status === 0) {
          this.showNotify('warning', '未审核的信息不能取消');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止取消');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Reject({
          DocNums: arrRowsID
        }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', res.Message || '取消成功!');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        })
          .catch(error => {
            this.isProcessing = false;
          });
      }
    },
    // 审核
    handleAudit() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿提交审核');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止审核');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
        // if (v.Status === 3) {
        //   this.showNotify("warning", '已取消的信息不能再次审核');
        //   switchBtn = false
        //   return true
        // }
        if (v.Status === 2) {
          this.showNotify('warning', '请勿重复提交审核');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Audit({
          DocNums: arrRowsID
        }).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '审核成功!');
            } else {
              this.showNotify('warning', res.Message || '审核成功!');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        })
          .catch(error => {
            this.isProcessing = false;
          });
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    // 作废
    handleNullify() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止作废');
          switchBtn = false;
          return true
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，请勿重复操作');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.DocNum);
        const query = {
          DocNums: arrRowsID
        };
        getCancel(query).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '作废成功!');
            } else {
              this.showNotify('warning', res.Message || '作废成功!');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 导入excel数据到后台
    uploadExcel() {
      console.log('uploadExcelData', this.uploadExcelData)
      // this.isProcessing = true;
      // if (this.uploadExcelData.length === 0) {
      //   this.showNotify("warning", "Common.improtNoData");
      //   this.isProcessing = false;
      //   return;
      // }
      // console.log(0, this.uploadExcelData);
      // this.dialogImprotVisable = false;
      // let obj = {};
      // this.list = this.list.concat(this.uploadExcelData);
      // this.isProcessing = false;
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    }
  }
};
</script>
