<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" :rules="rules" label-width="80px">
        <el-form-item label="数量" prop="Qty">
          <el-input v-model="model.Qty" @change="changeQty" />
        </el-form-item>
        <!-- <el-form-item label="移动类型" prop="MovementType">
          <el-select v-model="model.MovementType" filterable placeholder="请选择" @change="changeMovement"
            style="width: 100%;">
            <el-option v-for="item in MovementOptions" :key="item.EnumValue" :label="item.EnumValue+'-'+item.EnumValue1"
              :value="item.EnumValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="成本中心" prop="CostCenter">
          <el-select v-model="model.CostCenter" filterable placeholder="请选择" @change="changeCostCenter"
            style="width: 100%;">
            <el-option v-for="item in CostCenterOptions" :key="item.KOSTL" :label="item.KOSTL+'-'+item.LTEXT"
              :value="item.KOSTL">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="总账科目"
          :rules="LedgerDisabled===false?rules.LedgerType:[{ required: false, message: '请选择', trigger: 'change' }]">
          <el-select v-model="model.LedgerType" filterable placeholder="请选择" :disabled="LedgerDisabled"
            @change="changeLedgerType" style="width: 100%;">
            <el-option v-for="item in LedgerTypeOptions" :key="item.SAKNR" :label="item.SAKNR+'-'+item.TXT50"
              :value="item.SAKNR">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内部订单"
          :rules="OrderNumDisabled===false?rules.OrderNum:[{ required: false, message: '请选择', trigger: 'change' }]">
          <el-select v-model="model.OrderNum" filterable placeholder="请选择" :disabled="OrderNumDisabled"
            @change="changeOrderNum" style="width: 100%;">
            <el-option v-for="item in OrderNumOptions" :key="item.AUFNR" :label="item.AUFNR+'-'+item.KTEXT"
              :value="item.AUFNR">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资产卡片"
          :rules="AssetCardDisabled===false?rules.AssetCard:[{ required: false, message: '请选择', trigger: 'change' }]">
          <el-select v-model="model.AssetCard" filterable placeholder="请选择" :disabled="AssetCardDisabled"
            @change="changeAssetCard" style="width: 100%;">
            <el-option v-for="item in AssetCardOptions" :key="item.ANLN1" :label="item.ANLN1+'-'+item.TXT50"
              :value="item.ANLN1">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input v-model="model.Remark" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetDictionaryForDepReq,
  GetXZ_SAP_CSKS,
  GetXZ_SAP_SKA1,
  GetSAP_001,
  GetSAP_002
} from '@/api/MM/MM_DepartmentPickingApplication';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        Qty: '',
        MovementType: '',
        CostCenter: '',
        LedgerType: '',
        OrderNum: '',
        AssetCard: '',
        MovementTypeName: '', //  移动类型名称
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        OrderNumName: '', // 内部订单名称,
        AssetCardName: '', // 资产卡片名称
        Remark: ''
      },
      rules: {
        Qty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        MovementType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LedgerType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        OrderNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        AssetCard: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      MovementOptions: [],
      CostCenterOptions: [],
      LedgerTypeOptions: [],
      OrderNumOptions: [],
      AssetCardOptions: [],
      LedgerDisabled: false,
      OrderNumDisabled: false,
      AssetCardDisabled: false,
      Qty: 0
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.Qty = record.sumQty;
      // this.OrderNumDisabled = false
      // this.LedgerDisabled = false
      // this.AssetCardDisabled = false
      this.model = Object.assign({}, record);
      // console.log(this.model.MovementType)
      // this.GetDictionaryForDepReq()
      // this.GetXZ_SAP_CSKS()
      // this.GetXZ_SAP_SKA1()
      // this.GetSAP_001()
      // this.GetSAP_002()
      // if (this.model.MovementType !== undefined) {
      //   this.changeMovement(this.model.MovementType)
      // }
      this.drawer = true;

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleSave() {
      if (this.model.Qty <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (this.model.Qty > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return
      }
      // if (this.model.MovementType === 'Z23' || this.model.MovementType === 'Z24') {
      //   if (this.model.LedgerType === '' || this.model.LedgerType === undefined || this.model.LedgerType === null) {
      //     this.showNotify("warning", '请选择总账科目');
      //     return
      //   }
      // } else if (this.model.MovementType === 'Z15' || this.model.MovementType === 'Z16') {
      //   if (this.model.OrderNum === '' || this.model.OrderNum === undefined || this.model.OrderNum === null ) {
      //     this.showNotify("warning", '请选择内部订单');
      //     return
      //   }

      // } else if (this.model.MovementType === '241' || this.model.MovementType === '242') {
      //   if (this.model.AssetCard === '' || this.model.AssetCard === undefined || this.model.AssetCard === null) {
      //     this.showNotify("warning", '请选择资产卡片 ');
      //     return
      //   }
      // }
      // console.log(this.model,123)
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (e > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return
      }
    },
    GetDictionaryForDepReq() {
      GetDictionaryForDepReq().then(res => {
        if (res.Code === 2000) {
          this.MovementOptions = res.Data
        }
      })
    },
    changeMovement(e) {
      console.log(e);
      if (e === 'Z23' || e === 'Z24') {
        this.model.AssetCard = '';
        this.model.OrderNum = '';
        this.OrderNumDisabled = true;
        this.AssetCardDisabled = true;
        this.LedgerDisabled = false
      } else if (e === 'Z15' || e === 'Z16') {
        this.model.LedgerType = '';
        this.model.AssetCard = '';
        this.LedgerDisabled = true;
        this.OrderNumDisabled = false;
        this.AssetCardDisabled = true
      } else if (e === '241' || e === '242') {
        this.model.LedgerType = '';
        this.model.OrderNum = '';
        this.OrderNumDisabled = true;
        this.LedgerDisabled = true;
        this.AssetCardDisabled = false
      } else if (e === 'Z19' || e === 'Z21') {
        this.model.LedgerType = '';
        this.model.OrderNum = '';
        this.model.AssetCard = '';
        this.OrderNumDisabled = true;
        this.LedgerDisabled = true;
        this.AssetCardDisabled = true
      } else {
        this.OrderNumDisabled = false;
        this.LedgerDisabled = false;
        this.AssetCardDisabled = false
      }
      const obj = this.MovementOptions.find(v => v.EnumValue === e);
      this.model.MovementTypeName = obj.EnumValue1
    },
    GetXZ_SAP_CSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.model.CostCenterName = obj.LTEXT;
      this.$forceUpdate();
    },
    GetXZ_SAP_SKA1() {
      GetXZ_SAP_SKA1().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    changeLedgerType(e) {
      const obj = this.LedgerTypeOptions.find(v => v.SAKNR === e);
      this.model.LedgerTypeName = obj.TXT50;
      this.$forceUpdate();
    },
    GetSAP_001() {
      GetSAP_001().then(res => {
        if (res.Code === 2000) {
          this.OrderNumOptions = res.Data
        }
      })
    },
    changeOrderNum(e) {
      const obj = this.OrderNumOptions.find(v => v.AUFNR === e);
      this.model.OrderNumName = obj.KTEXT;
      this.$forceUpdate();
    },
    GetSAP_002() {
      GetSAP_002().then(res => {
        if (res.Code === 2000) {
          this.AssetCardOptions = res.Data
        }
      })
    },
    changeAssetCard(e) {
      const obj = this.AssetCardOptions.find(v => v.ANLN1 === e);
      this.model.AssetCardName = obj.TXT50;
      this.$forceUpdate();
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }
</style>
