<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        size="small"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <!--input输入框暂时未处理好-->
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        :data="list"
        border
        fit
        highlight-current-row
        size="small"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockScan.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_TakeStockScan.Posting'}"
        class="filter-item"
        type="success"
        size="small"
        icon="el-icon-edit"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockScan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockScan.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockScan.DownLoadTemp' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModel"
      >下载模板</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column :label="$t('ui.MM.TakeStockScan.DocNum')" prop="DocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.BoxBarCode')" prop="BoxBarCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.MM.TakeStockScan.ItemCode')"
        prop="ItemCode"
        align="center"
        width="140"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.TakeStockScan.ItemName')"
        prop="ItemName"
        align="center"
        width="220"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.MM.TakeStockScan.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName }}</span>
        </template>
      </el-table-column>-->
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.BinLocationName')" prop="BinLocationName" align="center"
        width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.StockQty')" prop="StockQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.StockQty }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="库存数量" prop="StockQty" align="center" width="100" />
      <el-table-column label="盘点数量" prop="ScanQty" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.ScanQty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="差异数量" prop="DiffQty" align="center" width="100" />
      <el-table-column :label="$t('ui.MM.TakeStockScan.Unit')" prop="Unit" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.BatchNum')" prop="BatchNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockScan.PTime')" prop="PTime" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|datetime }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.RegionName')" prop="RegionName" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('ui.MM.TakeStockScan.WhsName')" prop="WhsName"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.WhsName }}</span> </template> </el-table-column> -->
      <el-table-column label="仓库编码" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" show-overflow-tooltip />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="120"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="120"
        :formatter="formatDateTime"
        show-overflow-tooltip
      />
      <el-table-column label="Sap物料凭证单号" prop="SapDocNum" align="center" width="160" show-overflow-tooltip />
      <!-- <el-table-column label="Sap物料凭证行号" prop="SapLine" align="center"width="160" show-overflow-tooltip /> -->
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.TakeStockScan.IsConfirm')"
        prop="IsConfirm"
        align="center"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchPage,
  batchDelete,
  exportExcelFile,
  DoPost,
  improtExcelFile,
  exportExcelModel
} from '@/api/MM/MM_TakeStockScan';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MM.MM_TakeStockScan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        isPosted: '', // 状态
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPosted: undefined, // 暂未加入搜索条件
      changeSelectValue: '',
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],

      multipleSelection: [],
      isProcessing: false,
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    };
  },
  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatDate,
    formatDateTime,
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      console.log('sort', this.listQuery.sort);
      this.handleFilter();
    },
    getList() {
      // 获取数据
      this.listLoading = true;

      this.listQuery.IsConfirm = this.changeSelectValue;

      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      }).catch(error => {
        this.listLoading = false;
      });
    },
    handleSelectChangeValue(val) {
      this.changeSelectValue = val;
      this.getList();
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          var tmpRow = selectRows.find(val => val.IsConfirm);
          if (tmpRow) {
            this.showNotify('error', 'Common.operationNotPermitted');
            return;
          }

          this.isProcessing = true;
          var arrRowsID = selectRows.map(function(v) {
            return v.ScanID;
          });

          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.handleFilter(1);
              } else {
                this.showNotify('error', response.Message);
              }
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleExport() {
      // if (this.dateRangeValue) {
      //   this.listQuery.fromTime = this.dateRangeValue[0];
      //   this.listQuery.toTime = this.dateRangeValue[1];
      // }
      this.isProcessing = true;
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateValue,
        // state: this.changeSelectValue
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '盘点明细管理');
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      console.log(1, data);
      this.uploadExcelData = data;
    },

    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    // 过账
    handlePosting() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.postSuccess');
                }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    }
  }
};
</script>
