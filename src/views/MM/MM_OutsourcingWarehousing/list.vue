<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container box">
      <el-form ref="form" :model="listQuery" label-width="100px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="listQuery.dateValue"
                :clearable="false"
                size="small"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="过账日期">
              <el-date-picker
                v-model="listQuery.ManualPostdateValue"
                :clearable="true"
                size="small"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="凭证日期">
              <el-date-picker
                v-model="listQuery.PostdateValue"
                :clearable="true"
                size="small"
                class="filter-item"
                type="daterange"
                style="width: 100%"
                :picker-options="pickerOptions"
                range-separator="-"
                :unlink-panels="true"
                :start-placeholder="$t('Common.startTime')"
                :end-placeholder="$t('Common.endTime')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="过账状态">
              <el-select
                v-model="listQuery.isPosted"
                size="small"
                filterable
                :placeholder="$t('Common.postingStatus')"
                style="width: 100%"
                class="filter-item"
              >
                <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="采购订单号">
              <el-input
                v-model="listQuery.BaseNum"
                size="small"
                class="filter-item"
                placeholder="采购订单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检验单号">
              <el-input
                v-model="listQuery.InspectionNum"
                size="small"
                class="filter-item"
                placeholder="检验单号"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物料">
              <el-input
                v-model="listQuery.Item"
                size="small"
                class="filter-item"
                placeholder="物料"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商">
              <el-input
                v-model="listQuery.Supplier"
                size="small"
                class="filter-item"
                placeholder="供应商"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关键字">
              <el-input
                v-model="listQuery.keyword"
                size="small"
                class="filter-item"
                :placeholder="$t('Common.keyword')"
                clearable
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingWarehousing.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingWarehousing.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingWarehousing.PassPost'}"
        class="filter-item"
        type="danger"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePassPost"
      >{{ $t('Common.passPost') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutsourcingWarehousing.Export' }"
        class="filter-item"
        type="primary"
        size="small"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="入库单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报检单号" prop="InspectionNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.InspectionNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="采购单号" prop="BaseNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.BaseNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="采购单行号" prop="BaseLine" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库" prop="WhsName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="区域" prop="RegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="库位" prop="BinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="是否过账" prop="IsPosted" align="center" width="80" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="SAP物料凭证单号" prop="SapDocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP物料凭证行号" prop="SapLine" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />

      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column fixed="right" label="操作" width="80" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.IsPosted" @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
    <p>
      <span>委外入库申请明细单</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      height="300"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="采购订单号" prop="BaseNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="采购订单行号" prop="BaseLine" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="组件编号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="组件名称" prop="ItemName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right" >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter}}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" show-overflow-tooltip />
      <el-table-column label="凭证日期" prop="PostTime" align="center" :formatter="formatDateTime" show-overflow-tooltip />
      <el-table-column label="SAP物料凭证单号" prop="SapDocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="SAP物料凭证行号" prop="SapLine" align="center" width="160" show-overflow-tooltip /> -->
    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />

    <el-dialog title="编辑" :visible.sync="dialogVisible" width="30%">
      <el-form ref="dataForm" :model="model" :rules="rules" label-width="80px">
        <el-form-item label="仓库名称" prop="WhsCode">
          <el-select v-model="model.WhsCode" filterable placeholder="请选择" style="width: 100%;" @change="changeWhsName">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域名称" prop="RegionCode">
          <el-select
            v-model="model.RegionCode"
            filterable
            placeholder="请选择"
            style="width: 100%;"
            @change="changeRegionName"
          >
            <el-option
              v-for="item in RegionOptions"
              :key="item.value"
              :label="item.value+'-'+item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="库位名称" prop="BinLocationCode">
          <el-select
            v-model="model.BinLocationCode"
            filterable
            placeholder="请选择"
            style="width: 100%;"
            @change="changeBinLocationName"
          >
            <el-option
              v-for="item in BinLocationOptions"
              :key="item.value"
              :label="item.value+'-'+item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="过账日期" prop="ManualPostTime">
          <el-date-picker
            v-model="model.ManualPostTime"
            type="date"
            format="yyyy-MM-dd"
            placeholder="选择过账日期"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  batchDelete,
  exportExcelFile,
  DoPost,
  GetDetailedPageList,
  Update,
  PassPost
} from '@/api/MM/MM_OutsourcingWarehousing';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'MM.MM_OutsourcingWarehousing',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        ManualPostdateValue: [],
        PostdateValue: [],
        isPosted: '',
        BaseNum: '',
        InspectionNum: '',
        Supplier: '',
        Item: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 50
      },
      dialogVisible: false,
      options: [],
      RegionOptions: [],
      BinLocationOptions: [],
      model: {
        WhsCode: '',
        WhsName: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: ''
      },
      rules: {
        WhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        RegionCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        BinLocationCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  created() {
    this.handleFilter();
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      if (this.listQuery.ManualPostdateValue.length > 0) {
        this.listQuery.ManualPostdateValue[0] = this.$moment(this.listQuery.ManualPostdateValue[0]).format(
          'YYYY-MM-DD');
        this.listQuery.ManualPostdateValue[1] = this.$moment(this.listQuery.ManualPostdateValue[1]).format(
          'YYYY-MM-DD');
      }
      if (this.listQuery.PostdateValue.length > 0) {
        this.listQuery.PostdateValue[0] = this.$moment(this.listQuery.PostdateValue[0]).format('YYYY-MM-DD');
        this.listQuery.PostdateValue[1] = this.$moment(this.listQuery.PostdateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        ManualPostdateValue: this.listQuery.ManualPostdateValue,
        PostdateValue: this.listQuery.PostdateValue,
        isPosted: this.listQuery.isPosted,
        BaseNum: this.listQuery.BaseNum,
        InspectionNum: this.listQuery.InspectionNum,
        Supplier: this.listQuery.Supplier,
        Item: this.listQuery.Item,
        DocNums: selectRows.map(v => v.DocNum) || []
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '委外入库');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }

          if (v.IsCancel === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message);
                } else {
                  this.showNotify('warning', res.Message);
                }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }

        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止删除');
          switchBtn = false;
          return true;
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const arrRowsID = selectRows.map(v => v.ID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail();
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = {
        DocNum: this.currentRow.DocNum,
        BaseNum: this.currentRow.BaseNum,
        BaseLine: this.currentRow.BaseLine,
        PageNumber: this.listDetailQuery.PageNumber,
        PageSize: this.listDetailQuery.PageSize
      };
      GetDetailedPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    toggle(record) {
      this.model = Object.assign({}, record);
      this.GetXZ_SAP();
      this.getRegion();
      this.getBinLocation();
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.model.WhsName = this.options.filter(item => item.value === e)[0].label;
      this.getRegion()
    },
    getRegion() {
      if (this.model.WhsCode) {
        const query = {
          WhsCode: this.model.WhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.model.RegionName = res.Data[0].RegionName;
              this.model.RegionCode = res.Data[0].RegionCode;
              this.getBinLocation();
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.model.RegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.model.RegionCode) {
        const query = {
          regionCode: this.model.RegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.model.BinLocationName = res.Data[0].BinLocationName;
              this.model.BinLocationCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.model.BinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    },
    handleSave() {
      this.startLoading();
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (this.model.ManualPostTime) {
            this.model.ManualPostTime = this.$moment(this.model.ManualPostTime).format('YYYY-MM-DD');
          }
          const query = [];
          query.push(this.model);
          Update(query).then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', res.Message);
            } else {
              this.showNotify('error', res.Message);
            }
            this.dialogVisible = false;
            this.handleFilter();
            this.endLoading();
          })
        } else {
          console.log('error submit!!');
          this.endLoading();
          return false;
        }
      });
    },
    handlePassPost() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === false) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，请先过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          PassPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.postSuccess');
                }
              } else {
                this.showNotify('error', 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
              this.handleFilter()
            });
        }
      }
    }
  }
};
</script>
