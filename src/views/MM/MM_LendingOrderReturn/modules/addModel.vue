<template>
  <el-dialog :title="title" :visible.sync="drawer" :direction="direction" width="50%">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" :rules="rules" label-width="80px">
        <el-form-item label="数量" prop="Qty">
          <el-input v-model="model.Qty" @change="changeQty" />
        </el-form-item>
        <el-form-item label="仓库" prop="WhsCode">
          <el-select v-model="model.WhsCode" filterable placeholder="请选择" style="width: 100%;" @change="changeWhsName">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="model.Remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        Qty: '',
        WhsCode: '',
        WhsName: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: '',
        Remark: ''
      },
      rules: {
        Qty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        WhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      Qty: 0,
      options: [],
      RegionOptions: [],
      BinLocationOptions: []
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.Qty = record.Qty;
      this.model = Object.assign({}, record);
      this.GetXZ_SAP();
      this.drawer = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleSave() {
      if (this.model.Qty <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (this.model.Qty > this.Qty) {
        this.showNotify('warning', '数量不得大于归还数量');
        return
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (e > this.Qty) {
        this.showNotify('warning', '数量不得大于归还数量');
        return
      }
    },

    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.model.WhsName = this.options.filter(item => item.value === e)[0].label;
      this.getRegion()
    },
    getRegion() {
      if (this.model.WhsCode) {
        const query = {
          WhsCode: this.model.WhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.model.RegionName = res.Data[0].RegionName;
              this.model.RegionCode = res.Data[0].RegionCode;
              this.getBinLocation();
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.model.RegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      this.getBinLocation();
    },
    getBinLocation() {
      if (this.model.RegionCode) {
        const query = {
          regionCode: this.model.RegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.model.BinLocationName = res.Data[0].BinLocationName;
              this.model.BinLocationCode = res.Data[0].BinLocationCode;
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.model.BinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      this.$forceUpdate();
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }
</style>
