<template>
  <div class="app-container">
    <p>
      <label style="width:100%">归还单申请单登记</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="归还单号" prop="DocNum">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="归还信息">
            <el-input
              v-model="searchQuery.ItemCode"
              :placeholder="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
              readonly
            >
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="归还仓库" prop="WhsCode">
            <el-select
              v-model="searchQuery.WhsCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeWhsName"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.value+'-'+item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员" prop="HandlenCode">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column fixed="left" :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <!-- <el-table-column label="借出单id" prop="LendingOrderId" align="center" width="120" show-overflow-tooltip /> -->
      <el-table-column label="借出单号" prop="LendingOrderNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="归还仓库编号" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="归还仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip />
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetList,
  GetSRM_SupplierInfo
} from '@/api/MM/MM_LendingOrderReturn';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_LendingOrderReturnDetailed',
  components: {
    Pagination,
    AddSelectModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        WhsCode: '',
        WhsName: '',
        RegionCode: '',
        RegionName: '',
        BinLocationCode: '',
        BinLocationName: '',
        HandlenName: '',
        HandlenCode: ''
      },
      multipleSelection: [],
      rules: {
        WhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        HandlenCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: '',
      delList: [],
      PartnersOptions: [],
      disabled: true,
      HandlenOptions: [],
      options: [],
      RegionOptions: [],
      BinLocationOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetXZ_SAP();
    this.getUserlist();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DetailID) {
            v.IsDelete = true;
            this.delList.push(v.DetailID)
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList)
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择归还信息');
        return
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let switchBtn = true;
          this.list.some(res => {
            if (res.Qty === null || res.Qty === 0 || res.Qty === '0') {
              this.showNotify('warning', '数量不能为空或者为零');
              switchBtn = false;
              return true
            }
            if (res.ReturnWhsCode === '' || res.ReturnWhsCode === null) {
              this.showNotify('warning', '请选择归还仓库');
              return
            }
          });
          if (switchBtn) {
            this.$refs.dataForm.validate((valid) => {
              if (valid) {
                this.startLoading();
                if (this.editStatus === 'create') {
                  SubmitScanInfo(this.list).then(res => {
                    if (res.Code === 2000) {
                      this.showNotify('success', res.Message);
                      this.backTo('MM.MM_LendingOrderReturn');
                    } else {
                      this.showNotify('error', response.Message);
                    }
                    this.endLoading()
                  }).catch(err => {
                    console.log(err);
                    this.endLoading()
                  })
                }
              } else {
                console.log('error submit!!');
                return false;
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          LendingOrderId: v.LendingOrderId, // 借出单id
          LendingOrderNum: v.LendingOrderNum, // 借出单号
          DocNum: this.searchQuery.DocNum,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          WhsCode: this.searchQuery.WhsCode,
          WhsName: this.searchQuery.WhsName,
          RegionCode: this.searchQuery.RegionCode, //
          RegionName: this.searchQuery.RegionName, //
          BinLocationCode: this.searchQuery.BinLocationCode, //
          BinLocationName: this.searchQuery.BinLocationName, //
          HandlenName: this.searchQuery.HandlenName,
          HandlenCode: this.searchQuery.HandlenCode,
          Remark: v.Remark
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.LendingOrderId + next.ItemCode + next.WhsCode] ? '' : obj[next.LendingOrderId + next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit'
      } else {
        this.fetchDocNum();
        this.editStatus = 'create'
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.WhsName = this.options.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'WhsName', this.searchQuery.WhsName);
          this.$set(res, 'WhsCode', this.searchQuery.WhsCode);
        })
      }
      this.getRegion();
    },
    getRegion() {
      if (this.searchQuery.WhsCode) {
        const query = {
          WhsCode: this.searchQuery.WhsCode
        };
        GetWarehouseRegion(query).then(res => {
          const _this = this;
          _this.RegionOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.RegionOptions.push({
                value: res.RegionCode,
                label: res.RegionName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.RegionName = res.Data[0].RegionName;
              this.searchQuery.RegionCode = res.Data[0].RegionCode;
              if (this.list.length > 0) {
                this.list.forEach(res => {
                  this.$set(res, 'RegionName', this.searchQuery.RegionName);
                  this.$set(res, 'RegionCode', this.searchQuery.RegionCode);
                })
              }
              this.getBinLocation()
            }
          }
        })
      }
    },
    changeRegionName(e) {
      this.searchQuery.RegionName = this.RegionOptions.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'RegionName', this.searchQuery.RegionName);
          this.$set(res, 'RegionCode', this.searchQuery.RegionCode);
        })
      }
      this.getBinLocation()
    },
    getBinLocation() {
      if (this.searchQuery.RegionCode) {
        const query = {
          regionCode: this.searchQuery.RegionCode
        };
        GetRegionBinLocation(query).then(res => {
          const _this = this;
          _this.BinLocationOptions = [];
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              _this.BinLocationOptions.push({
                value: res.BinLocationCode,
                label: res.BinLocationName
              })
            });
            if (res.Data.length > 0) {
              this.searchQuery.BinLocationName = res.Data[0].BinLocationName;
              this.searchQuery.BinLocationCode = res.Data[0].BinLocationCode;
              if (this.list.length > 0) {
                this.list.forEach(res => {
                  this.$set(res, 'BinLocationName', this.searchQuery.BinLocationName);
                  this.$set(res, 'BinLocationCode', this.searchQuery.BinLocationCode);
                })
              }
            }
          }
        })
      }
    },
    changeBinLocationName(e) {
      this.searchQuery.BinLocationName = this.BinLocationOptions.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'BinLocationName', this.searchQuery.BinLocationName);
          this.$set(res, 'BinLocationCode', this.searchQuery.BinLocationCode);
        })
      }
      this.$forceUpdate();
    },
    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'HandlenName', this.searchQuery.HandlenName);
          this.$set(res, 'HandlenCode', this.searchQuery.HandlenCode);
        })
      }
    }
  }
}
</script>
