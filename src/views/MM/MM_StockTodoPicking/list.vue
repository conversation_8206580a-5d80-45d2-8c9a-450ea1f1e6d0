<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.AuditStatus"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in IsCancelOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_EquipmentPicking.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_EquipmentPicking.Audit' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="postDisable"
        @click="handleAudit"
      >{{ $t('Common.audit') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_EquipmentPicking.UnReview' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="postDisable"
        @click="handleUnReview"
      >取消</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Print' }"
        class="filter-item"
        type="warning"
        icon="el-icon-document"
        size="small"
        @click="print"
      >打印</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Print' }"
        class="filter-item"
        type="warning"
        icon="el-icon-document"
        size="small"
        @click="printByDocNum"
      >按单号打印</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
      size="mini"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="Quantity" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="领料状态" prop="EquipmentPickingStatus" align="center" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.AuditStatus === 1">未复核</span>
          <span v-else-if="scope.row.AuditStatus === 2">通过</span>
          <span v-else-if="scope.row.AuditStatus === 3">取消</span>
        </template>
      </el-table-column>
      <el-table-column label="办理人员" prop="HandleName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 表单对话框 -->
    <el-dialog v-el-drag-dialog :title="formTitle" :visible.sync="dialogFormVisible" top="10vh" width="65%">
      <el-form ref="dataForm" :rules="rules" :model="formData" label-position="left" label-width="100px">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="PartNo">
              <el-input v-model="formData.ItemCode" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="OperateNo">
              <el-input v-model="formData.OperateNo" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('Common.Remark')">
          <el-input v-model="formData.Remark" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t("Common.cancel") }}</el-button>
        <el-button type="primary" @click="warehousing()">{{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <add-select ref="addSelect" @ok="addSelectOk" />

  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import AddSelect from './modules/addSelect.vue'; // 选择
import {
  formatDate,
  formatDateTime,
  parseTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出
import {
  fetchList,
  batchDelete,
  exportExcelFile,
  SubmitScanInfo,
  getAudit,
  getReject
} from '@/api/MM/MM_StockTodoPicking';

import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import temp from './print/temp.json';
import { warehousing } from '@/api/MM/MM_StockTodo';
import AddModel from '@/views/MM/MM_EquipmentPicking/modules/addModel.vue';
disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

const tempBox = new hiprint.PrintTemplate({ template: temp });

export default {
  name: 'MM.MM_EquipmentPicking',
  components: {
    AddModel,
    Pagination,
    AddSelect
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        AuditStatus: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      IsCancelOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '待审核',
        key: 1
      },
      {
        label: '已审核',
        key: 2
      },
      {
        label: '取消',
        key: 3
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 50
      },
      tableHeight: 300,
      formTitle: '', // 弹窗标题
      dialogFormVisible: false, // 表单窗口是否显示标志
      rules: {
        ItemCode: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'blur'
        }],
        OperateNo: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'blur'
        }]
      },
      formData: {
        Remark: '',
        CUser: '',
        CDate: new Date(),
        MUser: '',
        MDate: new Date(),
        PartNo: '',
        MaterialDesc: '',
        OperateNo: '',
        OperateTime: new Date(),
        Operator: '',
        OperateType: 1
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        IsCancel: this.listQuery.IsCancel
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '设备领料');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    warehousing() {
      // 添加
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          // 确认添加
          warehousing(this.formData).then(response => {
            this.dialogFormVisible = false;
            this.$notify({
              title: this.$t('Common.success'),
              message: this.$t('Common.operationSuccess'),
              type: 'success',
              duration: 2000
            });
            this.getList()
          })
        }
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止删除');
          switchBtn = false;
          return true
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const arrRowsID = selectRows.map(v => v.ID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    print() {
      const templates = [];
      const printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        console.log('this.multipleSelection[i]', this.multipleSelection[i]);
        const list = [];
        const obj = {
          num: 1,
          ItemCode: this.multipleSelection[i].ItemCode,
          ItemName: this.multipleSelection[i].ItemName,
          RegionCode: this.multipleSelection[i].RegionCode,
          RegionName: this.multipleSelection[i].RegionName,
          Remark: this.multipleSelection[i].Remark,
          EquipmentPickingQty: this.multipleSelection[i].EquipmentPickingQty,
          Unit: this.multipleSelection[i].Unit
        }
        list.push(obj)
        printData[i] = {
          // 主表
          CreateDate: parseTime(new Date(), '{y}-{m}-{d}'),
          FactoryCode: this.multipleSelection[i].FactoryCode,
          FactoryName: '西子富沃德',
          Remark: this.multipleSelection[i].Remark,
          DocNum: this.multipleSelection[i].DocNum,
          Applicant: this.multipleSelection[i].HandlenName,
          MoveType: this.multipleSelection[i].MovementTypeName + '-' + this.multipleSelection[i].MovementType,
          CostCenter: this.multipleSelection[i].CostCenterName,
          list: list
        }
        console.log('printData', printData[i])
        templates.push({ template: tempBox, data: printData[i] });
      }
      console.log(templates)
      // 打印
      hiprint.print({ templates: templates });
    },
    printByDocNum() {
      const templates = [];
      const groupedByCustomerPartNo = this.multipleSelection.reduce((acc, item) => {
        const key = item.DocNum;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});
      Object.keys(groupedByCustomerPartNo).forEach(key => {
        const item = groupedByCustomerPartNo[key]
        const list = [];
        for (let i = 0;i < item.length;i++) {
          const obj = {
            num: i + 1,
            ItemCode: item[i].ItemCode,
            ItemName: item[i].ItemName,
            RegionCode: item[i].RegionCode,
            RegionName: item[i].RegionName,
            Remark: item[i].Remark,
            EquipmentPickingQty: item[i].EquipmentPickingQty,
            Unit: item[i].Unit
          }
          list.push(obj)
        }
        const printData = {
          // 主表
          CreateDate: parseTime(new Date(), '{y}-{m}-{d}'),
          FactoryCode: item[0].FactoryCode,
          FactoryName: '西子富沃德',
          Remark: item[0].Remark,
          DocNum: item[0].DocNum,
          Applicant: item[0].HandlenName,
          MoveType: item[0].MovementTypeName + '-' + item[0].MovementType,
          CostCenter: item[0].CostCenterName,
          list: list
        }
        templates.push({ template: tempBox, data: printData });
      })
      console.log(templates)
      // 打印
      hiprint.print({ templates: templates });
    },
    // 取消
    handleUnReview() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      let switchBtn = true;
      selectRows.some(res => {
        if (res.AuditStatus === '2') {
          this.showNotify('warning', '已审核的信息不能取消');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
        if (res.AuditStatus === '3') {
          this.showNotify('warning', '请勿重复取消');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.ID);
        const query = {
          ids: arrRowsID
        };
        getReject(query).then(res => {
          if (res.Code === 2000) {
            this.handleFilter();
            this.showNotify('success', 'Common.rejectSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    // 审核
    handleAudit() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      let switchBtn = true;
      selectRows.some(res => {
        if (res.AuditStatus === 2) {
          this.showNotify('warning', '请勿重复提交审核');
          this.isProcessing = false;
          switchBtn = false
          return true
        }
        if (res.IsCancel === true) {
          this.showNotify('warning', '单号为：' + res.DocNum + '信息已作废，禁止审核');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.ID);
        const query = {
          ids: arrRowsID
        };
        getAudit(query).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '审核成功!');
            } else {
              this.showNotify('warning', res.Message || '审核成功!');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    handleCreate() {
      // this.dialogFormVisible = true;
      this.$refs.addSelect.add();
    },
    handleUpdate() {
      let switchBtn = true;
      this.multipleSelection.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止修改');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === 2) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已审核，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('MM.MM_EquipmentPickingDetail', this.multipleSelection[0]);
      }
    },
    addSelectOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Quantity: v.OutQty,
          Unit: v.Unit,
          HandleName: v.HandleName
        })
      });
      // 确认添加
      SubmitScanInfo(data).then(response => {
        this.$notify({
          title: this.$t('Common.success'),
          message: this.$t('Common.operationSuccess'),
          type: 'success',
          duration: 2000
        });
        this.getList()
      })
    }

  }
};
</script>
