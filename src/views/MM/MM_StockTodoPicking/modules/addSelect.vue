<template>
  <el-dialog title="库存信息" :visible.sync="dialogCustomerFormVisible" width="80%" top="5vh">
    <div class="container">
      <el-col :span="8">
        <el-form ref="dataForm" label-position="left" label-width="100px">
          <el-form-item label="办理人员">
            <el-select
              v-model="form.HandleName"
              filterable
              placeholder="请选择"
              style="width: 100%;"
            >
              <el-option
                v-for="item in HandleOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>
    </div>
    <br>
    <br>
    <br>
    <hr>
    <div class="filter-container">
      <el-input
        v-model="listQuery.itemCode"
        size="small"
        clearable
        class="filter-item"
        placeholder="物料件号"
        style="width: 140px"
        @keydown.enter.native="handleSearchFilter"
      />
      <!--      <el-select size="small" style="width: 140px" class="filter-item" clearable v-model="listQuery.WhsCode" filterable-->
      <!--        placeholder="请选择仓库">-->
      <!--        <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">-->
      <!--        </el-option>-->
      <!--      </el-select>-->
      <!--      <el-select size="small" style="width: 140px" class="filter-item" clearable v-model="listQuery.SpecialStock"-->
      <!--        filterable placeholder="请选择特殊库存" @change="changeSpecialStock">-->
      <!--        <el-option v-for="item in SpecialStockOptions" :key="item.value" :label="item.label" :value="item.value"-->
      <!--          :disabled="item.disabled">-->
      <!--        </el-option>-->
      <!--      </el-select>-->
      <el-button
        v-waves
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-left: 20px"
        @click="handleSearchFilter"
      >
        {{ $t('Common.search') }}</el-button>
    </div>
    <el-table
      ref="CustomerSelectTable"
      v-loading="listLoading"
      :data="list"
      border
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleCustomerRowSelectEvent"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="数量" prop="StoreNo" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="领料数量" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model="scope.row.OutQty" placeholder="请输入" size="mini" @input="inputOutQty(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCustomerFormVisible = false">{{ $t('Common.close') }}</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSelectCustomer">
        {{ $t('Common.select') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  GetStockList
} from '@/api/RPT/RPT_Stock';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import {
  GetSRM_SupplierInfo
} from '@/api/MM/MM_SubcontractingApplication';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  getPageList
} from '@/api/MM/MM_StockTodo'
import { fetchList } from '@/api/Sys/Sys_User';

export default {
  name: 'AddSelectModal',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  data() {
    return {
      dialogCustomerFormVisible: false,
      listQuery: {
        itemCode: '',
        PageNumber: 0,
        PageSize: 1000
      },
      total: 0,
      model: {},
      list: [],
      multipleSelection: [],
      listLoading: false,
      options: [],
      SpecialStockOptions: [{
        label: '正常',
        value: ''
      }, {
        label: '供应商',
        value: 'O',
        disabled: true
      }, {
        label: '销售',
        value: 'E',
        disabled: true
      }],
      SupplierOptions: [],
      SupplierDisabled: true,
      form: {
        HandleName: ''
      },
      HandleOptions: []
    }
  },
  computed: {

  },
  created() {
    this.getUserList();
  },
  methods: {
    formatDate,
    formatDateTime,
    getUserList() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        this.HandleOptions = response.Data.items;
      });
    },
    add() {
      this.listQuery = {
        itemCode: '',
        PageNumber: 1,
        PageSize: 1000
      };
      this.list = [];
      this.GetXZ_SAP();
      this.GetSRM();
      this.dialogCustomerFormVisible = true
    },
    edit(record) {
      this.model = Object.assign({}, record)
    },
    handleCustomerRowSelectEvent(selection) {
      this.multipleSelection = selection
    },
    handleSearchFilter() {
      if (this.listQuery.ItemCode === '') {
        this.showNotify('warning', '请输入物料件号');
        return
      }
      this.handleCustomerFilter()
    },
    handleCustomerFilter() {
      this.listLoading = true;
      getPageList(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      }).catch(error => {
        this.listLoading = false;
      });
    },
    handleSelectCustomer() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '领料数量不能为空或者为零');
          switchBtn = false;
          return true
        }
        if (res.OutQty > res.Qty) {
          this.showNotify('warning', '领料数量不能大于库存数量');
          switchBtn = false;
          return true
        }
        res.HandleName = this.form.HandleName
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection);
        this.dialogCustomerFormVisible = false;
      }
    },
    handleAdd() {
      let switchBtn = true;
      this.multipleSelection.some(res => {
        console.log(res);
        if (res.OutQty === undefined || res.OutQty === 0 || res.OutQty === '0') {
          this.showNotify('warning', '领料数量不能为空或者为零');
          switchBtn = false;
          return true
        }
        if (res.OutQty > res.Qty) {
          this.showNotify('warning', '领料数量不能大于库存数量');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$emit('ok', this.multipleSelection)
      }
    },
    inputOutQty(e) {
      if (e.OutQty <= 0) {
        this.showNotify('warning', '领料数量不得小于等于0');
        return
      } else if (e.OutQty > e.Qty) {
        this.showNotify('warning', '领料数量不得大于库存数量');
        return
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.WhsName = this.options.filter(item => item.value === e)[0].label
      // this.handleFilter()
    },
    GetSRM() {
      GetSRM_SupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.SupplierOptions = res.Data
        }
      })
    },
    changeSupplierName(e) {
      // const obj = this.SupplierOptions.find(v => v.SupplierCode === e)
      // this.searchQuery.SupplierName = obj.SupplierName
    },
    changeSpecialStock(e) {
      if (e === 'O') {
        this.SupplierDisabled = false
      } else {
        this.SupplierDisabled = true;
        this.listQuery.SupplierCode = '';
      }
    }
  }
}
</script>

<style scoped>
</style>
