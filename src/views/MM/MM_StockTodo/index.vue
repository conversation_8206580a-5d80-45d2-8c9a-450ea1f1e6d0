<template>
  <div class="app-container">
    <!-- 检索以及工具条区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.ItemCode"
        placeholder="件号"
        style="width: 230px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_StockTodo.Warehousing' }"
        class="filter-item"
        type="success"
        icon="el-icon-plus"
        @click="handleCreate(1)"
      >入库
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_StockTodo.Outbound' }"
        class="filter-item"
        type="warning"
        icon="el-icon-minus"
        @click="handleCreate(2)"
      >出库
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_StockTodo.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        :disabled="canNotDelete"
        @click="handleDelete"
      >{{ $t("Common.delete") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_StockTodo.Export' }"
        :loading="downloadLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}
      </el-button>
    </div>
    <!-- 数据列表 -->
    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
      height="450"
      :row-style="{ height: '30px' }"
      :cell-style="{ padding: '0' }"
      @selection-change="handleSelectChange"
    >
      <el-table-column type="selection" align="center" width="40" fixed />
      <el-table-column width="150px" align="center" prop="ItemCode" label="件号" />
      <el-table-column width="300px" align="center" prop="ItemName" label="物料描述" />
      <el-table-column width="120px" align="center" prop="StoreNo" label="在库数量" />
      <el-table-column width="120px" align="center" label="操作">
        <template slot-scope="scope">
          <div class="btnBox">
            <el-button type="text" @click="getDetailPageList(scope.row)">查看明细</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      :selectdata.sync="selectedRowsData.length"
      @pagination="getList"
    />
    <!-- 表单对话框 -->
    <el-dialog v-el-drag-dialog :title="formTitle" :visible.sync="dialogFormVisible" top="10vh" width="65%">
      <el-form ref="dataForm" :rules="rules" :model="formData" label-position="left" label-width="100px">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="PartNo">
              <el-input v-model="formData.ItemCode" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="OperateNo">
              <el-input v-model="formData.OperateNo" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('Common.Remark')">
          <el-input v-model="formData.Remark" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t("Common.cancel") }}</el-button>
        <el-button type="primary" @click="warehousing()">{{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 明细 -->
    <el-dialog v-el-drag-dialog title="操作明细" :visible.sync="dialogFormListVisible" top="10vh" width="50%">
      <el-table
        ref="multipleTable"
        v-loading="listDetailLoading"
        :data="detailList"
        border
        highlight-current-row
        style="width: 100%;"
        height="450"
        :row-style="{ height: '30px' }"
        :cell-style="{ padding: '0' }"
      >
        <el-table-column type="selection" align="center" width="40" fixed />
        <el-table-column width="150px" align="center" prop="ItemCode" label="物料编码" />
        <el-table-column width="300px" align="center" prop="ItemName" label="物料名称" />
        <el-table-column width="120px" align="center" prop="Operator" label="操作人" />
        <el-table-column width="120px" align="center" prop="OperateType" label="操作类型" />
        <el-table-column width="120px" align="center" prop="OperateNo" label="操作数量" />
        <el-table-column width="150px" align="center" prop="OperateTime" label="操作时间" :formatter="formatDateTime" />
      </el-table>
      <!-- 分页组件 -->
      <pagination
        :total="detailTotal"
        :page.sync="listDetailQuery.PageNumber"
        :limit.sync="listDetailQuery.PageSize"
        :selectdata.sync="selectedRowsData.length"
        @pagination="getDetailPageList"
      />
    </el-dialog>

    <!-- 创建件号 -->
    <el-dialog v-el-drag-dialog :title="formPartTitle" :visible.sync="dialogFormPartVisible" top="10vh" width="50%">
      <el-form ref="dataPartForm" :rules="partRules" :model="formPartData" label-position="left" label-width="100px">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="ItemCode">
              <el-input v-model="formPartData.PartNo" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="ItemName">
              <el-input v-model="formPartData.MaterialDesc" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('Common.Remark')">
          <el-input v-model="formPartData.Remark" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormPartVisible = false">{{ $t("Common.cancel") }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import elDragDialog from '@/directive/el-drag-dialog' // base on element-ui
import {
  getPageList,
  add,
  batchDelete,
  exportExcelFile, warehousing, getPartList, getDetailPageList
} from '@/api/MM/MM_StockTodo'
import {
  exportToExcel
} from '@/utils/excel-export'

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js' // 权限判断指令

import {
  convertToKeyValue
} from '@/utils'
import waves from '@/directive/waves' // waves directive 特效
import Pagination from '@/components/Pagination' // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'

const statusMapArr = [{
  value: true,
  label: i18n.t('Dictionary.AccountIsEnableMap.TrueValue')
}, // 正常
{
  value: false,
  label: i18n.t('Dictionary.AccountIsEnableMap.FalseValue')
} // 冻结
];

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'Sys.Sys_User',
  components: {
    Pagination
  },
  directives: {
    waves,
    elDragDialog,
    permission
  },
  filters: {
    statusFilter(status) {
      const statusMap = convertToKeyValue(statusMapArr);
      return statusMap[status]
    }
  },
  data() {
    return {
      handleDisable: false,
      // define the default value
      treeSelectedValue: null,
      // define options
      treeOptions: [],
      organizationList: [],
      list: null,
      partList: null,
      total: 0,
      detailList: null,
      detailTotal: 0,
      listLoading: true,
      listDetailLoading: true,
      listQuery: {
        itemCode: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 100
      },
      listPartQuery: {
        PageNumber: 1,
        PageSize: 1000
      },
      listDetailQuery: {
        itemCode: '',
        PageNumber: 1,
        PageSize: 1000
      },
      roleList: [],
      selectedRowsData: [],
      selectedUserRolesData: [],
      genderOptions: [{
        value: 1,
        label: this.$t('Dictionary.GenderMap.One')
      }, // 男
      {
        value: 2,
        label: this.$t('Dictionary.GenderMap.Two')
      } // 女
      ],
      userRoleOptions: [{
        value: 1,
        label: this.$t('Dictionary.UserRoleMap.One')
      }, // 系统管理员
      {
        value: 2,
        label: this.$t('Dictionary.UserRoleMap.Two')
      } // 业务人员
      ],
      formData: {
        Remark: '',
        CUser: '',
        CDate: new Date(),
        MUser: '',
        MDate: new Date(),
        PartNo: '',
        MaterialDesc: '',
        OperateNo: '',
        OperateTime: new Date(),
        Operator: '',
        OperateType: 1
      },
      formPartData: {
        Remark: '',
        CUser: '',
        CDate: new Date(),
        MUser: '',
        MDate: new Date(),
        PartNo: '',
        MaterialDesc: '',
        StoreNo: ''
      },
      supplyAbled: true,
      supplierList: [],
      companyList: [],
      PasswordInfo: {
        LoginAccount: '',
        OldPassword: '',
        NewPassword: '',
        ConfirmNewPassword: ''
      },
      formTitle: '', // 弹窗标题
      formPartTitle: '', // 弹窗标题
      formSetRoleTitle: this.$t('ui.Sys.Sys_User.Titles.SetUserRole'), // "设置用户角色", // 设置角色
      formMode: '',
      formPartMode: '',
      dialogFormResetVisible: false,
      dialogFormVisible: false, // 表单窗口是否显示标志
      dialogFormListVisible: false, // 表单窗口是否显示标志
      dialogFormPartVisible: false, // 表单窗口是否显示标志
      rules: {
        ItemCode: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'blur'
        }],
        OperateNo: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'blur'
        }]
      },
      partRules: {
        ItemCode: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'blur'
        }],
        OperateNo: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'blur'
        }]
      },
      downloadLoading: false
    }
  },
  computed: {
    canNotUpdate() {
      return this.selectedRowsData.length !== 1
    },
    canNotDelete() {
      return this.selectedRowsData.length !== 1
    }
  },
  watch: {
    'formData.IsSupplier'() {
      if (this.formData.IsSupplier) {
        this.supplyAbled = false
      } else {
        this.supplyAbled = true
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    formatDateTime,
    formatGender(row, column, currentValue) {
      const opt = this.genderOptions.find(element => {
        return element.value === currentValue
      });
      return opt === undefined ? '' : opt.label
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      getPageList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false
      })
    },
    getPartList(SupplierCode) {
      // 获取数据
      getPartList({ SupplierCode: SupplierCode }).then(response => {
        this.partList = response.Data.items;
      })
    },
    getDetailPageList(row) {
      // 获取数据
      this.dialogFormListVisible = true;
      this.listDetailLoading = true;
      this.listDetailQuery.itemCode = row.ItemCode;
      getDetailPageList(this.listDetailQuery).then(response => {
        this.detailList = response.Data.items;
        this.detailList.forEach(item => {
          item.OperateType = item.OperateType === 1 ? '入库' : '出库';
        });
        this.detailTotal = response.Data.total;
        this.listDetailLoading = false
      })
    },
    handleFilter() {
      // 检索处理
      this.getList()
    },
    resetFormData() {
      // 重置表单数据
      this.formData = {
        SupplyName: '',
        SupplyCode: '',
        Remark: '',
        CUser: '',
        CDate: new Date(),
        MUser: '',
        MDate: new Date(),
        PartNo: '',
        MaterialDesc: '',
        OperateNo: '',
        OperateTime: new Date(),
        Operator: '',
        SupplierName: '',
        SupplierCode: '',
        OperateType: 1
      }
    },
    resetFormPartData() {
      // 重置表单数据
      this.formPartData = {
        Remark: '',
        CUser: '',
        CDate: new Date(),
        MUser: '',
        MDate: new Date(),
        PartNo: '',
        MaterialDesc: '',
        StoreNo: '',
        SupplierName: '',
        SupplierCode: ''
      }
    },
    handleSelectChange(selection) {
      this.selectedRowsData = selection
    },
    handleCreatePart(type) {
      this.resetFormPartData();
      this.formTitle = type === 1 ? '入库' : '出库';
      this.formMode = 'Create';
      this.dialogFormVisible = true;
      this.formData.OperateType = type;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleCreate(type) {
      this.resetFormData();
      this.formTitle = type === 1 ? '入库' : '出库';
      this.dialogFormVisible = true;
      this.formData.OperateType = type;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    createPart() {
      this.resetFormPartData();
      this.formPartTitle = '创建库存';
      this.formPartMode = 'Create';
      this.dialogFormPartVisible = true;
      this.$nextTick(() => {
        this.$refs['dataPartForm'].clearValidate() // 清除校验
      })
    },
    isSupply() {
      // if (this.$refs['dataForm']) {
      //   this.$refs['dataForm'].clearValidate()
      // }
    },
    createData() {
      // 添加
      this.$refs['dataPartForm'].validate(valid => {
        if (valid) {
          // 确认添加
          add(this.formPartData).then(response => {
            this.dialogFormPartVisible = false;
            this.$notify({
              title: this.$t('Common.success'),
              message: this.$t('Common.operationSuccess'),
              type: 'success',
              duration: 2000
            });
            this.getList()
            if (this.formPartData.SupplierCode !== null) {
              this.getPartList(this.formPartData.SupplierCode);
            }
          })
        }
      })
    },
    warehousing() {
      // 添加
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          // 确认添加
          warehousing(this.formData).then(response => {
            this.dialogFormVisible = false;
            this.$notify({
              title: this.$t('Common.success'),
              message: this.$t('Common.operationSuccess'),
              type: 'success',
              duration: 2000
            });
            this.getList()
          })
        }
      })
    },
    supplyChange(val) {
      this.formData.SupplierCode = val;
      this.formData.SupplierName = this.getSupplierName(val);
      getPartList({ SupplierCode: this.formData.SupplierCode }).then(response => {
        this.partList = response.Data;
      })
    },
    supplyPartChange(val) {
      console.log('val', val)
      this.formPartData.SupplierCode = val;
      this.formPartData.SupplierName = this.getSupplierName(val);
    },
    getSupplierName(code) {
      for (let i = 0;i < this.supplierList.length;i++) {
        if (this.supplierList[i].SupplyerCode === code) {
          return this.supplierList[i].SupplyerName1
        }
      }
    },
    handleDelete() {
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const ids = this.selectedRowsData.map(v => v.ID);

        // 删除逻辑处理
        batchDelete(ids).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList()
        })
      })
    },
    handleExport() {
      exportExcelFile({
        PartNo: this.listQuery.PartNo
      }).then(res =>
        exportToExcel(res.data, res.fileName)
      )
    }
  }
}
</script>

<style></style>
