<template>
  <div class="app-container">
    <p>
      <label style="width:100%">设备领料申请登记单</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item
            class="filter-item"
            :label="$t('ui.MM.DepartmentPickingApplication.RequisitionNum')"
            prop="DocNum"
          >
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="移动类型" prop="MovementType">
            <el-select
              v-model="searchQuery.MovementType"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeMovement"
            >
              <el-option
                v-for="item in MovementOptions"
                :key="item.EnumValue"
                :label="item.EnumValue+'-'+item.EnumValue1"
                :value="item.EnumValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成本中心" prop="CostCenter">
            <el-select
              v-model="searchQuery.CostCenter"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeCostCenter"
            >
              <el-option
                v-for="item in CostCenterOptions"
                :key="item.KOSTL"
                :label="item.KOSTL+'-'+item.LTEXT"
                :value="item.KOSTL"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="总账科目" prop="LedgerAccount">
            <el-select
              v-model="searchQuery.LedgerAccount"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeLedgerAccount"
            >
              <el-option
                v-for="item in LedgerAccountOptions"
                :key="item.SAKNR"
                :label="item.SAKNR+'-'+item.TXT50"
                :value="item.SAKNR"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              type="date"
              :clearable="false"
              placeholder="过账日期"
              format="yyyy-MM-dd"
              @change="changeManualPostTime"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="资产卡片">
            <el-select
              v-model="searchQuery.AssetCard"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeAssetCard"
            >
              <el-option
                v-for="item in AssetCardOptions"
                :key="item.ANLN1"
                :label="item.ANLN1+'-'+item.TXT50"
                :value="item.ANLN1"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设备编号">
            <el-input v-model="searchQuery.EquipmentNum" @change="changeEquipmentNum" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="searchQuery.ItemCode" placeholder="库存信息" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('ui.MM.DepartmentPickingApplication.Remark')">
            <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" @change="changeRemark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-upload" size="small" @click="handleImport">导入模板
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-download" size="small" @click="handleExportModel">下载模板
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="EquipmentPickingQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerAccountName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="资产卡片" prop="AssetCardName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="设备编号" prop="EquipmentNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="过账日期" prop="ManualPostTime" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="办理人员" prop="HandlenName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="100" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
        @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelBtn">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddModel from './modules/addModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export'; // 导出
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetDictionaryForEquipment,
  GetXZ_SAP_CSKS,
  GetXZ_SAP_SKA1,
  GetSAP_002,
  exportExcelModel
} from '@/api/MM/MM_EquipmentPicking';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_EquipmentPickingDetail',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        ManualPostTime: new Date(),
        MovementType: '',
        CostCenter: '',
        LedgerAccount: '',
        AssetCard: '',
        MovementTypeName: '', //  移动类型名称
        CostCenterName: '', // 成本中心名称,
        LedgerAccountName: '', // 总账科目名称,
        AssetCardName: '', // 资产卡片名称
        HandlenCode: '',
        HandlenName: '',
        EquipmentNum: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        MovementType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LedgerAccount: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        AssetCard: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: '',
      delList: [],
      MovementOptions: [],
      CostCenterOptions: [],
      LedgerAccountOptions: [],
      AssetCardOptions: [],
      LedgerDisabled: true, // 总账科目
      AssetCardDisabled: true, // 资产卡片
      CostCenterDisabled: true, // 成本中心
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: [],
      ImportDisabled: true,
      HandlenOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    },
    routeParams() {
      return this.$route.params;
    }
  },
  watch: {

  },
  created() {
    this.getPageParams();
    this.GetDictionaryForEquipment();
    this.GetXZ_SAP_CSKS();
    this.GetXZ_SAP_SKA1();
    this.GetSAP_002();
    this.getUserlist();
  },
  methods: {
    formatDate,
    formatDateTime,
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancelBtn() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      data.forEach(res => {
        this.uploadExcelData.push({
          DocNum: this.searchQuery.DocNum,
          ItemCode: res.物料编号, // 物料件号
          // ItemName: res.物料描述, // 物料名称
          EquipmentPickingQty: res.数量,
          sumQty: res.数量,
          WhsCode: res.仓库编号,
          // WhsName: res.仓库,
          // Remark: res.备注,
          BinLocationCode: '',
          BinLocationName: '',
          RegionCode: '',
          RegionName: '',
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerAccount: this.searchQuery.LedgerAccount || '',
          AssetCard: this.searchQuery.AssetCard || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerAccountName: this.searchQuery.LedgerAccountName || '', // 总账科目名称,
          AssetCardName: this.searchQuery.AssetCardName || '', // 资产卡片名称
          ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
          HandlenCode: this.searchQuery.HandlenCode || '',
          HandlenName: this.searchQuery.HandlenName || '',
          Remark: this.searchQuery.Remark || '',
          EquipmentNum: this.searchQuery.EquipmentNum || '',
          StockID: res.物料编码 + res.仓库编号
        })
      })
    },
    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      console.log(0, this.uploadExcelData);
      this.dialogImprotVisable = false;
      const obj = {};
      this.list = this.list.concat(this.uploadExcelData);
      this.isProcessing = false;
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          DocNum: this.searchQuery.DocNum,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          EquipmentPickingQty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          MovementType: this.searchQuery.MovementType || '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerAccount: this.searchQuery.LedgerAccount || '',
          AssetCard: this.searchQuery.AssetCard || '',
          MovementTypeName: this.searchQuery.MovementTypeName || '', //  移动类型名称
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerAccountName: this.searchQuery.LedgerAccountName || '', // 总账科目名称,
          AssetCardName: this.searchQuery.AssetCardName || '', // 资产卡片名称
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          BinLocationCode: v.BinLocationCode,
          BinLocationName: v.BinLocationName,
          RegionCode: v.RegionCode,
          RegionName: v.RegionName,
          ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
          HandlenCode: this.searchQuery.HandlenCode || '',
          HandlenName: this.searchQuery.HandlenName || '',
          Remark: this.searchQuery.Remark || '',
          EquipmentNum: this.searchQuery.EquipmentNum || '',
          StockID: v.ItemCode + v.WhsCode
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode] ? '' : obj[next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.DepRequisitionID) {
        this.editStatus = 'edit'
      } else {
        this.fetchDocNum();
        this.editStatus = 'create'
      }
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    GetDictionaryForEquipment() {
      this.MovementOptions = [];
      GetDictionaryForEquipment().then(res => {
        if (res.Code === 2000) {
          this.MovementOptions = res.Data
        }
      })
    },
    changeMovement(e) {
      const obj = this.MovementOptions.find(v => v.EnumValue === e);
      this.searchQuery.MovementTypeName = obj.EnumValue1;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'MovementTypeName', this.searchQuery.MovementTypeName);
          this.$set(res, 'MovementType', this.searchQuery.MovementType)
        })
      }
      this.$forceUpdate();
    },
    GetXZ_SAP_CSKS() {
      this.CostCenterOptions = [];
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.searchQuery.CostCenterName = obj.LTEXT;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter)
        })
      }
      this.$forceUpdate();
    },
    GetXZ_SAP_SKA1() {
      this.LedgerAccountOptions = [];
      GetXZ_SAP_SKA1().then(res => {
        if (res.Code === 2000) {
          this.LedgerAccountOptions = res.Data
        }
      })
    },
    changeLedgerAccount(e) {
      const obj = this.LedgerAccountOptions.find(v => v.SAKNR === e);
      this.searchQuery.LedgerAccountName = obj.TXT50;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'LedgerAccountName', this.searchQuery.LedgerAccountName);
          this.$set(res, 'LedgerAccount', this.searchQuery.LedgerAccount)
        })
      }
      this.$forceUpdate();
    },
    GetSAP_002() {
      GetSAP_002().then(res => {
        if (res.Code === 2000) {
          this.AssetCardOptions = res.Data
        }
      })
    },
    changeAssetCard(e) {
      const obj = this.AssetCardOptions.find(v => v.ANLN1 === e);
      this.searchQuery.AssetCardName = obj.TXT50;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'AssetCardName', this.searchQuery.AssetCardName);
          this.$set(res, 'AssetCard', this.searchQuery.AssetCard)
        })
      }
      this.$forceUpdate();
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DepReqDetailedID) {
            v.IsDelete = true;
            this.delList.push(v.DepReqDetailedID)
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList)
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择库存信息');
        return
      }
      let switchBtn = true;
      this.list.some(res => {
        if (res.EquipmentPickingQty === null || res.EquipmentPickingQty === 0 || res.EquipmentPickingQty ===
            '0' || res.EquipmentPickingQty === '') {
          this.showNotify('warning', '数量不能为空或者为零');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.startLoading();
            if (this.editStatus === 'create') {
              SubmitScanInfo(this.list).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_EquipmentPicking');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err);
                this.endLoading()
              })
            } else {
              update(query).then(res => {
                if (res.Code === 2000) {
                  this.showNotify('success', res.Message);
                  this.backTo('MM.MM_EquipmentPicking');
                } else {
                  this.showNotify('error', response.Message);
                }
                this.endLoading()
              }).catch(err => {
                console.log(err);
                this.endLoading()
              })
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DepReqDetailedID) {
          if (v.DepReqDetailedID === record.DepReqDetailedID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.StockID === record.StockID) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    changeManualPostTime(e) {
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'ManualPostTime', this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'))
        })
      }
      this.$forceUpdate();
    },
    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'HandlenName', this.searchQuery.HandlenName);
          this.$set(res, 'HandlenCode', this.searchQuery.HandlenCode)
        })
      }
      this.$forceUpdate();
    },
    changeRemark(e) {
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'Remark', this.searchQuery.Remark)
        })
      }
      this.$forceUpdate();
    },
    changeEquipmentNum(e) {
      console.log(e);
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'EquipmentNum', this.searchQuery.EquipmentNum)
        })
      }
      this.$forceUpdate();
    }
  }
}
</script>
