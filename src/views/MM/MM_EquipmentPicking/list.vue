<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-select
        v-model="listQuery.IsCancel"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in IsCancelOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <!-- <el-button v-waves v-permission="{ name: 'MM.MM_EquipmentPicking.Edit' }" class="filter-item"
        type="primary" icon="el-icon-edit" size="small" :disabled="canNotUpdate" @click="handleUpdate">
        {{ $t("Common.edit") }}
      </el-button> -->
      <el-button
        v-waves
        v-permission="{name:'MM.MM_EquipmentPicking.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_EquipmentPicking.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_EquipmentPicking.Nullify'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="postDisable"
        @click="handleCancel"
      >作废
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_EquipmentPicking.Audit' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="postDisable"
        @click="handleAudit"
      >{{ $t('Common.audit') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_EquipmentPicking.UnReview' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="postDisable"
        @click="handleUnReview"
      >取消</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Print' }"
        class="filter-item"
        type="warning"
        icon="el-icon-document"
        size="small"
        @click="print"
      >打印</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_EquipmentPicking.Print' }"
        class="filter-item"
        type="warning"
        icon="el-icon-document"
        size="small"
        @click="printByDocNum"
      >按单号打印</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
      size="mini"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="行号" prop="Line" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="出厂编号" prop="BarCode" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="批次" prop="BatchNum" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="移动类型" prop="MovementTypeName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.MovementTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CostCenterName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总账科目" prop="LedgerAccountName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.LedgerAccountName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="资产卡片" prop="AssetCardName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="设备编号" prop="EquipmentNum" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="供应商名称" prop="SupplierName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="数量" prop="EquipmentPickingQty" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.EquipmentPickingQty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库" prop="WhsName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="区域" prop="RegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="库位" prop="BinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="设备领料状态" prop="EquipmentPickingStatus" align="center" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.EquipmentPickingStatus === '1'">未复核</span>
          <span v-else-if="scope.row.EquipmentPickingStatus === '2'">通过</span>
          <span v-else-if="scope.row.EquipmentPickingStatus === '3'">取消</span>
        </template>
      </el-table-column>
      <el-table-column label="办理人员" prop="HandlenName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="是否作废" prop="IsCancel" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsCancel | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否过账" prop="IsPosted" align="center" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="Sap物料凭证单号" prop="SapDocNum" align="center" show-overflow-tooltip width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SapDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Sap物料凭证行号" prop="SapLine" align="center" show-overflow-tooltip width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime,
  parseTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  batchDelete,
  exportExcelFile,
  DoPost,
  getAudit,
  getReject,
  getCancel
} from '@/api/MM/MM_EquipmentPicking';

import { disAutoConnect, hiprint } from 'vue-plugin-hiprint';
import temp from './print/temp.json';
disAutoConnect(); // 取消自动连接直接打印客户端
hiprint.init();

const tempBox = new hiprint.PrintTemplate({ template: temp });

export default {
  name: 'MM.MM_EquipmentPicking',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        IsCancel: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      IsCancelOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '已作废',
        key: true
      },
      {
        label: '未作废',
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 50
      },
      tableHeight: 300
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM_EquipmentPicking') {
        this.handleFilter();
      }
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        IsCancel: this.listQuery.IsCancel
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '设备领料');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true
          }
          if (v.IsCancel === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止过账');
            switchBtn = false;
            this.isProcessing = false;
            return true
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.postSuccess');
                }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    // 删除
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止删除');
          switchBtn = false;
          return true
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          const arrRowsID = selectRows.map(v => v.EquipmentPickingID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    print() {
      const templates = [];
      const printData = [];
      for (let i = 0;i < this.multipleSelection.length;i++) {
        console.log('this.multipleSelection[i]', this.multipleSelection[i]);
        const list = [];
        const obj = {
          num: 1,
          ItemCode: this.multipleSelection[i].ItemCode,
          ItemName: this.multipleSelection[i].ItemName,
          RegionCode: this.multipleSelection[i].RegionCode,
          RegionName: this.multipleSelection[i].RegionName,
          Remark: this.multipleSelection[i].Remark,
          EquipmentPickingQty: this.multipleSelection[i].EquipmentPickingQty,
          Unit: this.multipleSelection[i].Unit
        }
        list.push(obj)
        printData[i] = {
          // 主表
          CreateDate: parseTime(new Date(), '{y}-{m}-{d}'),
          FactoryCode: this.multipleSelection[i].FactoryCode,
          FactoryName: '西子富沃德',
          Remark: this.multipleSelection[i].Remark,
          DocNum: this.multipleSelection[i].DocNum,
          Applicant: this.multipleSelection[i].HandlenName,
          MoveType: this.multipleSelection[i].MovementTypeName + '-' + this.multipleSelection[i].MovementType,
          CostCenter: this.multipleSelection[i].CostCenterName,
          list: list
        }
        console.log('printData', printData[i])
        templates.push({ template: tempBox, data: printData[i] });
      }
      console.log(templates)
      // 打印
      hiprint.print({ templates: templates });
    },
    printByDocNum() {
      const templates = [];
      const groupedByCustomerPartNo = this.multipleSelection.reduce((acc, item) => {
        const key = item.DocNum;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});
      Object.keys(groupedByCustomerPartNo).forEach(key => {
        const item = groupedByCustomerPartNo[key]
        const list = [];
        for (let i = 0;i < item.length;i++) {
          const obj = {
            num: i + 1,
            ItemCode: item[i].ItemCode,
            ItemName: item[i].ItemName,
            RegionCode: item[i].RegionCode,
            RegionName: item[i].RegionName,
            Remark: item[i].Remark,
            EquipmentPickingQty: item[i].EquipmentPickingQty,
            Unit: item[i].Unit
          }
          list.push(obj)
        }
        const printData = {
          // 主表
          CreateDate: parseTime(new Date(), '{y}-{m}-{d}'),
          FactoryCode: item[0].FactoryCode,
          FactoryName: '西子富沃德',
          Remark: item[0].Remark,
          DocNum: item[0].DocNum,
          Applicant: item[0].HandlenName,
          MoveType: item[0].MovementTypeName + '-' + item[0].MovementType,
          CostCenter: item[0].CostCenterName,
          list: list
        }
        templates.push({ template: tempBox, data: printData });
      })
      console.log(templates)
      // 打印
      hiprint.print({ templates: templates });
    },
    // 取消
    handleUnReview() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      let switchBtn = true;
      selectRows.some(res => {
        if (res.EquipmentPickingStatus === '2') {
          this.showNotify('warning', '已审核的信息不能取消');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
        if (res.EquipmentPickingStatus === '3') {
          this.showNotify('warning', '请勿重复取消');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
        if (res.IsCancel === true) {
          this.showNotify('warning', '单号为：' + res.DocNum + '信息已作废，禁止取消');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.EquipmentPickingID);
        const query = {
          ids: arrRowsID
        };
        getReject(query).then(res => {
          if (res.Code === 2000) {
            this.handleFilter();
            this.showNotify('success', 'Common.rejectSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    // 审核
    handleAudit() {
      const selectRows = this.multipleSelection;
      this.isProcessing = true;
      let switchBtn = true;
      selectRows.some(res => {
        // if (res.EquipmentPickingStatus === '3') {
        //   this.showNotify("warning", '已取消的信息不能再次审核');
        //   this.isProcessing = false;
        //   switchBtn = false
        //   return true
        // }
        if (res.EquipmentPickingStatus === '2') {
          this.showNotify('warning', '请勿重复提交审核');
          this.isProcessing = false;
          switchBtn = false
          return true
        }
        if (res.IsCancel === true) {
          this.showNotify('warning', '单号为：' + res.DocNum + '信息已作废，禁止审核');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.EquipmentPickingID);
        const query = {
          ids: arrRowsID
        };
        getAudit(query).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '审核成功!');
            } else {
              this.showNotify('warning', res.Message || '审核成功!');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    // 作废
    handleCancel() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止作废');
          switchBtn = false;
          return true
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，请勿重复操作');
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        this.isProcessing = true;
        const arrRowsID = selectRows.map(v => v.EquipmentPickingID);
        const query = {
          ids: arrRowsID
        };
        getCancel(query).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '作废成功!');
            } else {
              this.showNotify('warning', res.Message || '作废成功!');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        }).catch(error => {
          this.isProcessing = false;
        });
      }
    },
    handleCreate() {
      this.routeTo('MM.MM_EquipmentPickingDetail');
    },
    handleUpdate() {
      let switchBtn = true;
      this.multipleSelection.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsCancel === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已作废，禁止修改');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === 2) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已审核，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('MM.MM_EquipmentPickingDetail', this.multipleSelection[0]);
      }
    }
  }
};
</script>
