<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
    <div class="demo-drawer__content">
      <el-form ref="dataForm" :model="model" :rules="rules" label-width="80px">
        <el-form-item label="数量" prop="Qty">
          <el-input v-model="model.Qty" @change="changeQty" />
        </el-form-item>
        <!-- <el-form-item label="成本中心" prop="CostCenter">
          <el-select v-model="model.CostCenter" filterable placeholder="请选择" @change="changeCostCenter" style="width: 100%;">
            <el-option v-for="item in CostCenterOptions" :key="item.KOSTL" :label="item.KOSTL+'-'+item.LTEXT"
              :value="item.KOSTL">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="总账科目" prop="LedgerType">
          <el-select v-model="model.LedgerType" filterable placeholder="请选择" @change="changeLedgerType" style="width: 100%;">
            <el-option v-for="item in LedgerTypeOptions" :key="item.SAKNR" :label="item.SAKNR+'-'+item.TXT50"
              :value="item.SAKNR">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归还日期" prop="ReturnDate">
          <el-date-picker type="date" :clearable="false" placeholder="归还时间" v-model="model.ReturnDate" style="width: 100%;"
            format="yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="归还仓库" prop="ReturnWhsCode">
          <el-select v-model="model.ReturnWhsCode" filterable placeholder="请选择" @change="changeWhsName" style="width: 100%;">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input v-model="model.Remark" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button @click="drawer = false">{{ $t('Common.close') }}</el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('Common.save') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import {
  GetXZ_SAP_CSKS,
  GetXZ_SAP_SKA1
} from '@/api/MM/MM_LendingOrder';
import {
  GetXZ_SAP
} from '@/api/PO/PO_ReturnScan';
export default {
  name: 'AddModal',
  components: {

  },
  directives: {

  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      model: {
        Qty: '',
        CostCenter: '',
        LedgerType: '',
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        ReturnDate: null, // 归还日期
        Remark: ''
      },
      rules: {
        Qty: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LedgerType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ReturnDate: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ReturnWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      CostCenterOptions: [],
      LedgerTypeOptions: [],
      Qty: 0,
      options: []
    }
  },
  computed: {

  },
  created() {},
  methods: {
    add() {

    },
    edit(record) {
      console.log(record);
      this.Qty = record.sumQty;
      this.model = Object.assign({}, record);
      // this.model.ReturnDate = new Date()
      // this.GetXZ_SAP()
      // this.GetXZ_SAP_CSKS()
      // this.GetXZ_SAP_SKA1()
      this.drawer = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleSave() {
      if (this.model.Qty <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (this.model.Qty > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return
      }
      // if (this.model.LedgerType === '') {
      //   this.showNotify("warning", '请选择总账科目');
      //   return
      // }
      // if (this.model.CostCenter === '') {
      //   this.showNotify("warning", '请选择成本中心');
      //   return
      // }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // this.model.ReturnDate = this.$moment(this.model.ReturnDate).format('YYYY-MM-DD')
          this.$emit('ok', this.model);
          this.drawer = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    changeQty(e) {
      if (e <= 0) {
        this.showNotify('warning', '数量不得小于等于0');
        return
      } else if (e > this.Qty) {
        this.showNotify('warning', '数量不得大于库存数量');
        return
      }
    },
    GetXZ_SAP_CSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.model.CostCenterName = obj.LTEXT;
      this.$forceUpdate();
    },
    GetXZ_SAP_SKA1() {
      GetXZ_SAP_SKA1().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    changeLedgerType(e) {
      const obj = this.LedgerTypeOptions.find(v => v.SAKNR === e);
      this.model.LedgerTypeName = obj.TXT50;
      this.$forceUpdate();
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      // console.log(e,this.options)
      this.model.ReturnWhsName = this.options.filter(item => item.value === e)[0].label;
      console.log(this.model)
    }
  }
}
</script>

<style scoped>
  .demo-drawer__content {
    padding: 20px;
  }

  .demo-drawer__footer {
    display: flex;
  }

  .demo-drawer__footer button {
    flex: 1;
  }
</style>
