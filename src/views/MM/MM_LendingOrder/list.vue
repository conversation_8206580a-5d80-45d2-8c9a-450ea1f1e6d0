<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <span class="name">过账：</span>
      <el-select
        v-model="listQuery.isPosted"
        filterable
        size="small"
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <span class="name">状态：</span>
      <el-select
        v-model="listQuery.Status"
        filterable
        size="small"
        placeholder="状态"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in StatusOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <span class="name">类型：</span>
      <el-select
        v-model="listQuery.LendingType"
        filterable
        size="small"
        placeholder="借出单类型"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in LendingTypeOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <span class="name">归还状态：</span>
      <el-select
        v-model="listQuery.Rqty"
        size="small"
        filterable
        placeholder="归还状态"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in RqtyOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        clearable
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ItemCode"
        clearable
        size="small"
        class="filter-item"
        placeholder="物料件号"
        style="width: 140px"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_LendingOrder.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_LendingOrder.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="canNotUpdate"
        @click="handleUpdate"
      >
        {{ $t("Common.edit") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_LendingOrder.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_LendingOrder.Audit' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleAudit"
      >审核</el-button>
      <el-button
        v-permission="{ name: 'MM.MM_LendingOrder.Cancel' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handleCancel"
      >取消</el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_LendingOrder.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >
        {{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_LendingOrder.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="deletable"
        @click="handlePrint"
      >
        {{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_LendingOrder.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="借出单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="借出单类型" prop="LendingType" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if=" scope.row.LendingType === '0'">同步SAP</span>
          <span v-if=" scope.row.LendingType === '1'">不同步SAP</span>
          <span v-if=" scope.row.LendingType === '2'">期初</span>
          <span v-if=" scope.row.LendingType === '3'">OA创建</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="Status" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if=" scope.row.Status === '0'">未审核</span>
          <span v-if=" scope.row.Status === '1'">审核中</span>
          <span v-if=" scope.row.Status === '2'">已审核</span>
          <span v-if=" scope.row.Status === '3'">已取消</span>
        </template>
      </el-table-column>
      <el-table-column label="交易对象" prop="TradingPartnersName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TradingPartnersName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对象编号" prop="PartnersNum" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PartnersNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对象名称" prop="PartnersName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PartnersName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="办理人员" prop="HandlenName" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.HandlenName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" prop="AuditUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.AuditUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核日期" prop="AuditDate" align="center" width="100" show-overflow-tooltip :formatter="formatDate" />
      <el-table-column label="取消人" prop="RejectUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RejectUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="取消日期" prop="RejectDate" align="center" width="100" show-overflow-tooltip :formatter="formatDate" />
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.IsPosted')"
        prop="IsPosted"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.PostTime')"
        width="100"
        prop="PostTime"
        align="center"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column
        label="过账日期"
        prop="ManualPostTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDate"
      />
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="100" show-overflow-tooltip :formatter="formatDateTime" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>借出单明细</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      size="mini"
      height="300"
      :row-class-name="tableDetailRowClassName"
      @sort-change="detailSortChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="借出单号" prop="DocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="ItemName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="归还数量" prop="ReturnQty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit " align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="借出仓库编号" prop="WhsCode" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="借出仓库名称" prop="WhsName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="归还仓库编号" prop="ReturnWhsCode" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="归还仓库名称" prop="ReturnWhsName" align="center" width="140" show-overflow-tooltip />
      <el-table-column
        label="归还日期"
        prop="ReturnDate"
        align="center"
        width="140"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column label="成本中心" prop="CostCenter" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="成本中心名称" prop="CostCenterName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerType" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="总账科目名称" prop="LedgerTypeName" align="center" width="140" show-overflow-tooltip />
      <el-table-column label="sap物料凭证单号" prop="SapDocNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="sap物料凭证行号" prop="SapLine" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售单号" prop="SaleNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售单行号" prop="SaleLine" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessType" align="center" width="80" show-overflow-tooltip />
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.Remark')"
        prop="Remark"
        align="center"
        width="160"
        show-overflow-tooltip
      />

    </el-table>
    <pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getListDetail"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  GetPageList,
  batchDelete,
  exportExcelFile,
  DoPost,
  printToPDF,
  Audit,
  Reject
} from '@/api/MM/MM_LendingOrder';

export default {
  name: 'MM.MM_LendingOrder',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listDetailLoading: false,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 10,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: '',
        LendingType: '',
        Status: '',
        ItemCode: '',
        Rqty: '' // 归还状态
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      StatusOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '未审核',
        key: '0'
      }, {
        label: '审核中',
        key: '1'
      }, {
        label: '已审核',
        key: '2'
      }, {
        label: '已取消',
        key: '3'
      }],
      LendingTypeOptions: [{
        label: '全部',
        key: ''
      }, {
        label: '同步SAP',
        key: '0'
      }, {
        label: '不同步SAP',
        key: '1'
      }, {
        label: '期初',
        key: '2'
      }, {
        label: 'OA创建',
        key: '3'
      }],
      RqtyOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: '已归还',
        key: '已归还'
      },
      {
        label: '未归还',
        key: '未归还'
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50
      }
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM_LendingOrder') {
        this.handleFilter();
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listDetail = [];
        this.totalDetail = 0;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handlePrint() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      //   if (this.checkSingleSelection(selectRows)) {
      const docNums = selectRows.map(v => v.DocNum);
      console.log(docNums);
      printToPDF({
        docNums: docNums
      }).then(response => {
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
      //   }
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted,
        LendingType: this.listQuery.LendingType,
        Status: this.listQuery.Status,
        ItemCode: this.listQuery.ItemCode,
        Rqty: this.listQuery.Rqty
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '借出单');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    // 过账功能模块
    handlePosting() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.Status === '3') {
            this.showNotify('warning', '已取消的信息不能过账');
            this.isProcessing = false;
            switchBtn = false;
            return true
          }
          if (v.Status !== '2') {
            this.showNotify('warning', '未审核过的信息不能过账');
            this.isProcessing = false;
            switchBtn = false;
            return true
          }
          if (v.LendingType !== '0') {
            this.showNotify('warning', '单据类型无需过账');
            this.isProcessing = false;
            switchBtn = false;
            return true
          }
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.operationFailed');
                }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
              // this.handleFilter()
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      const postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.LendingType === '3') {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息为OA创建，请勿删除');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿删除');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === '2') {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已审核，请勿删除');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);

          const arrRowsID = selectRows.map(v => v.DocNum);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', res.Message || 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.getListDetail()
    },
    getListDetail() {
      this.listDetailLoading = true;
      const query = Object.assign({
        DocNum: this.currentRow.DocNum
      }, this.listDetailQuery);
      GetPageList(query).then(res => {
        if (res.Code === 2000) {
          this.listDetail = res.Data.items;
          this.totalDetail = res.Data.total;
          this.listDetailLoading = false;
        }
      })
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getListDetail();
    },
    handleCreate() {
      this.routeTo('MM.MM_LendingOrderDetailed');
    },
    handleUpdate() {
      let switchBtn = true;
      this.multipleSelection.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.LendingType === '1') {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息为OA创建，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === '2') {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已审核，请勿修改');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
      });
      if (switchBtn) {
        this.routeTo('MM.MM_LendingOrderDetailed', this.multipleSelection[0]);
      }
    },
    // 取消
    handleCancel() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿取消操作');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        if (v.Status === '3') {
          this.showNotify('warning', '请勿重复取消');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
        if (v.Status === '2' && v.IsPosted === true) {
          this.showNotify('warning', '已审核并且已过账的信息不能取消');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
        if (v.Status === '0') {
          this.showNotify('warning', '未审核的信息不能取消');
          this.isProcessing = false;
          switchBtn = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Reject({
          DocNums: arrRowsID
        }).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', res.Message || '取消成功!');
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        })
          .catch(error => {
            this.isProcessing = false;
          });
      }
    },
    // 审核
    handleAudit() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿提交审核');
          switchBtn = false;
          this.isProcessing = false;
          return true;
        }
        // if (v.Status === '3') {
        //   this.showNotify("warning", '已取消的信息不能再次审核');
        //   switchBtn = false
        //   this.isProcessing = false;
        //   return true
        // }
        if (v.Status === '2') {
          this.showNotify('warning', '请勿重复提交审核');
          switchBtn = false;
          this.isProcessing = false;
          return true
        }
      });
      if (switchBtn) {
        const arrRowsID = selectRows.map(v => v.DocNum);
        Audit({
          DocNums: arrRowsID
        }).then(res => {
          if (res.Code === 2000) {
            if (res.MessageParam === 2000) {
              this.showNotify('success', res.Message || '审核成功!');
            } else {
              this.showNotify('warning', res.Message || '审核成功!');
            }
          } else {
            this.showNotify('error', res.Message);
          }
          this.handleFilter();
          this.isProcessing = false;
        })
          .catch(error => {
            this.isProcessing = false;
          });
      }
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    tableDetailRowClassName({
      row,
      rowIndex
    }) {
      console.log(row.Qty === row.ReturnQty);
      if (row.Qty === row.ReturnQty) {
        return 'success-row';
      }
      return '';
    }
  }
};
</script>
<style lang="scss" scoped>
  .filter-container .name {
      line-height: 32px;
      margin-bottom: 10px;
      height: 32px;
      font-size: 14px;
      display: inline-block;
      vertical-align: middle;
    }
</style>
