<template>
  <div class="app-container">
    <p>
      <label style="width:100%">借出单申请单登记</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="借出单号" prop="DocNum">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" prop="LendingType">
            <el-select
              v-model="searchQuery.LendingType"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeLendingType"
            >
              <el-option v-for="item in LendingTypeOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账日期" prop="ManualPostTime">
            <el-date-picker
              v-model="searchQuery.ManualPostTime"
              type="date"
              :clearable="false"
              placeholder="过账日期"
              format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input
              v-model="searchQuery.ItemCode"
              :placeholder="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
              readonly
              :disabled="lendingTypeDisabled"
            >
              <el-button slot="append" icon="el-icon-more" :disabled="lendingTypeDisabled" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料信息">
            <el-input
              v-model="searchQuery.ItemCode"
              :placeholder="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
              readonly
              :disabled="!lendingTypeDisabled"
            >
              <el-button slot="append" icon="el-icon-more" :disabled="!lendingTypeDisabled" @click="selectMaterials" />
            </el-input>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8">
          <el-form-item label="归还时间" prop="ReturnDate">
            <el-date-picker type="date" :clearable="false" placeholder="归还时间" v-model="searchQuery.ReturnDate" format="yyyy-MM-dd" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="交易对象" prop="TradingPartners">
            <el-select
              v-model="searchQuery.TradingPartners"
              filterable
              placeholder="请选择"
              @change="changeTradingPartners"
            >
              <el-option
                v-for="item in TradingPartnersOptions"
                :key="item.EnumKey"
                :label="item.EnumKey+'-'+item.EnumValue"
                :value="item.EnumKey"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="对象编号" prop="PartnersNum">
            <el-select
              v-model="searchQuery.PartnersNum"
              filterable
              placeholder="请选择"
              :disabled="disabled"
              @change="changePartnersName"
            >
              <el-option
                v-for="item in PartnersOptions"
                :key="item.key"
                :label="item.key+'-'+item.label"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成本中心" prop="CostCenter">
            <el-select
              v-model="searchQuery.CostCenter"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeCostCenter"
            >
              <el-option
                v-for="item in CostCenterOptions"
                :key="item.KOSTL"
                :label="item.KOSTL+'-'+item.LTEXT"
                :value="item.KOSTL"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="总账科目" prop="LedgerType">
            <el-select
              v-model="searchQuery.LedgerType"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeLedgerType"
            >
              <el-option
                v-for="item in LedgerTypeOptions"
                :key="item.SAKNR"
                :label="item.SAKNR+'-'+item.TXT50"
                :value="item.SAKNR"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="归还日期">
            <el-date-picker
              v-model="searchQuery.ReturnDate"
              type="date"
              :clearable="false"
              placeholder="归还时间"
              style="width: 100%;"
              format="yyyy-MM-dd"
              @change="changeReturnDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="归还仓库">
            <el-select
              v-model="searchQuery.ReturnWhsCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeWhsName"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.value+'-'+item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员" prop="HandlenCode">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('ui.MM.DepartmentPickingApplication.Remark')">
            <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column fixed="left" :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="归还日期" prop="ReturnDate" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="成本中心" prop="CostCenterName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="总账科目" prop="LedgerTypeName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="借出仓库编号" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="借出仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="归还仓库编号" prop="ReturnWhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="归还仓库名称" prop="ReturnWhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售单号" prop="SaleNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售单行号" prop="SaleLine" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessType" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" width="160" show-overflow-tooltip />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" :data-list="list" @ok="modalFormOk" />
    <add-select-materials-model ref="modalFormAddMaterials" @ok="modalFormOkAddMaterials" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddModel from './modules/addModel'
import AddSelectMaterialsModel from './modules/addSelectMaterialsModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetList,
  GetSRM_SupplierInfo,
  GetDictionaryForLenOrder,
  GetXZ_SAP_KNA1,
  GetXZ_SAP_KNA1ForEmp,
  GetXZ_SAP_CSKS,
  GetXZ_SAP_SKA1
} from '@/api/MM/MM_LendingOrder';
import {
  GetXZ_SAP
} from '@/api/PO/PO_ReturnScan';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_LendingOrderDetailed',
  components: {
    Pagination,
    AddSelectModel,
    AddSelectMaterialsModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        MovementType: '',
        ManualPostTime: new Date(),
        TradingPartners: '', // 交易对象
        TradingPartnersName: '',
        PartnersNum: '', // 对象编号
        PartnersName: '', // 对象名称
        CostCenter: '',
        LedgerType: '',
        CostCenterName: '', // 成本中心名称,
        LedgerTypeName: '', // 总账科目名称,
        ReturnDate: null, // 归还日期
        ReturnWhsCode: '',
        ReturnWhsName: '',
        HandlenCode: '',
        HandlenName: '',
        LendingType: '0',
        // ReturnDate: new Date(),
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ManualPostTime: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        PartnersNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        TradingPartners: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        CostCenter: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LedgerType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ReturnDate: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        ReturnWhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        HandlenCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LendingType: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: '',
      delList: [],
      TradingPartnersOptions: [],
      PartnersOptions: [],
      disabled: true,
      CostCenterOptions: [],
      LedgerTypeOptions: [],
      options: [],
      HandlenOptions: [],
      LendingTypeOptions: [{
        label: '同步SAP',
        key: '0'
      }, {
        label: '不同步SAP',
        key: '1'
      }],
      lendingTypeDisabled: false
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetXZ_SAP();
    this.GetXZ_SAP_CSKS();
    this.GetXZ_SAP_SKA1();
    this.getUserlist();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DetailID) {
            v.IsDelete = true;
            this.delList.push(v.DetailID)
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList)
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        if (this.LendingType === '0') {
          this.showNotify('warning', '请选择库存信息');
        } else {
          this.showNotify('warning', '请选择物料信息');
        }
        return
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let switchBtn = true;
          this.list.some(res => {
            if (res.Qty === null || res.Qty === 0 || res.Qty === '0') {
              this.showNotify('warning', '数量不能为空或者为零');
              switchBtn = false;
              return true
            }
            if (res.CostCenter === undefined) {
              this.showNotify('warning', '成本中心不能为空');
              switchBtn = false;
              return true
            }
            if (res.LedgerType === undefined) {
              this.showNotify('warning', '总账科目不能为空');
              switchBtn = false;
              return true
            }
            // if (res.ReturnDate === '' || res.ReturnDate === null) {
            //   this.showNotify("warning", '请选择归还时间');
            //   return
            // }
            // if (res.ReturnWhsCode === '' || res.ReturnWhsCode === null) {
            //   this.showNotify("warning", '请选择归还仓库');
            //   return
            // }
          });
          if (switchBtn) {
            this.$refs.dataForm.validate((valid) => {
              if (valid) {
                // let item = {
                //   CostCenter: this.searchQuery.CostCenter,
                //   LedgerType: this.searchQuery.LedgerType,
                //   CostCenterName: this.searchQuery.CostCenterName, // 成本中心名称,
                //   LedgerTypeName: this.searchQuery.LedgerTypeName, // 总账科目名称,
                //   ReturnWhsCode: this.searchQuery.ReturnWhsCode,
                //   ReturnWhsName: this.searchQuery.ReturnWhsName,
                // }
                // this.list.forEach(res => {
                //   Object.assign(res, item)
                // })
                this.startLoading();
                const query = {
                  DocNum: this.searchQuery.DocNum,
                  ManualPostTime: this.$moment(this.searchQuery.ManualPostTime).format('YYYY-MM-DD'),
                  // ReturnDate: this.$moment(this.searchQuery.ReturnDate).format('YYYY-MM-DD'),
                  TradingPartners: this.searchQuery.TradingPartners, // 交易对象
                  TradingPartnersName: this.searchQuery.TradingPartnersName,
                  PartnersNum: this.searchQuery.PartnersNum, // 对象编号
                  PartnersName: this.searchQuery.PartnersName, // 对象名称
                  HandlenCode: this.searchQuery.HandlenCode,
                  HandlenName: this.searchQuery.HandlenName,
                  LendingType: this.searchQuery.LendingType, // 类型
                  Remark: this.searchQuery.Remark,
                  deldetailArray: this.delList,
                  DetailedList: this.list
                };
                if (this.editStatus === 'create') {
                  SubmitScanInfo(query).then(res => {
                    if (res.Code === 2000) {
                      this.showNotify('success', res.Message);
                      this.backTo('MM.MM_LendingOrder');
                    } else {
                      this.showNotify('error', response.Message);
                    }
                    this.endLoading()
                  }).catch(err => {
                    console.log(err);
                    this.endLoading()
                  })
                } else {
                  update(query).then(res => {
                    if (res.Code === 2000) {
                      this.showNotify('success', res.Message);
                      this.backTo('MM.MM_LendingOrder');
                    } else {
                      this.showNotify('error', response.Message);
                    }
                    this.endLoading()
                  }).catch(err => {
                    console.log(err);
                    this.endLoading()
                  })
                }
              } else {
                console.log('error submit!!');
                return false;
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      if (this.listQuery.MovementType === '') {
        this.showNotify('warning', '请选择移动类型');
        return false
      }
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DetailID) {
          if (v.DetailID === record.DetailID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.StockID === record.StockID) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          BarCode: v.BarCode,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          RegionCode: v.RegionCode, //
          RegionName: v.RegionName, //
          BinLocationCode: v.BinLocationCode, //
          BinLocationName: v.BinLocationName, //
          AssessType: v.AssessType,
          ReturnDate: this.searchQuery.ReturnDate ? this.$moment(this.searchQuery.ReturnDate).format(
            'YYYY-MM-DD') : '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          ReturnWhsCode: this.searchQuery.ReturnWhsCode || '',
          ReturnWhsName: this.searchQuery.ReturnWhsName || '',
          SaleNum: v.SaleNum,
          SaleLine: v.SaleLine,
          StockID: v.ItemCode + v.WhsCode + v.AssessType
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode + next.AssessType + next.SaleNum + next.SaleLine] ? '' : obj[next.ItemCode + next.WhsCode + next.AssessType + next.SaleNum + next.SaleLine] = true && cur.push(next);
        return cur;
      }, [])
    },
    getDetailList() {
      const query = {
        DocNum: this.searchQuery.DocNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          if (this.list.length > 0) {
            this.searchQuery.CostCenter = res.Data[0].CostCenter;
            this.searchQuery.LedgerType = res.Data[0].LedgerType;
            this.searchQuery.CostCenterName = res.Data[0].CostCenterName; // 成本中心名称,
            this.searchQuery.LedgerTypeName = res.Data[0].LedgerTypeName; // 总账科目名称,
            this.searchQuery.ReturnWhsCode = res.Data[0].ReturnWhsCode;
            this.searchQuery.ReturnWhsName = res.Data[0].ReturnWhsName;
            this.searchQuery.ReturnDate = this.$moment(res.Data[0].ReturnDate).format('YYYY-MM-DD');
          }
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      this.getTradingPartners();
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
        this.getDetailList();
        this.getPartners(this.searchQuery.TradingPartnersName)
      } else {
        this.fetchDocNum();
        this.editStatus = 'create'
      }
    },
    getTradingPartners() {
      GetDictionaryForLenOrder().then(res => {
        if (res.Code === 2000) {
          this.TradingPartnersOptions = res.Data
        }
      })
    },
    changeTradingPartners(e) {
      const obj = this.TradingPartnersOptions.find(v => v.EnumKey === e);
      this.searchQuery.TradingPartnersName = obj.EnumValue;
      this.searchQuery.PartnersNum = '';
      this.searchQuery.PartnersName = '';
      this.getPartners(obj.EnumValue);
    },
    getPartners(item) {
      this.PartnersOptions = [];
      if (item === '供应商') {
        GetSRM_SupplierInfo().then(res => {
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              this.PartnersOptions.push({
                key: res.SupplierCode,
                label: res.SupplierName
              })
            })
          }
        })
      } else if (item === '人员') {
        GetXZ_SAP_KNA1ForEmp().then(res => {
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              this.PartnersOptions.push({
                key: res.CustomerCode,
                label: res.CustomerName
              })
            })
          }
        })
      } else if (item === '客户') {
        GetXZ_SAP_KNA1().then(res => {
          if (res.Code === 2000) {
            res.Data.forEach(res => {
              this.PartnersOptions.push({
                key: res.CustomerCode,
                label: res.CustomerName
              })
            })
          }
        })
      }
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate();
      });
      this.disabled = false
    },
    changePartnersName(e) {
      const obj = this.PartnersOptions.find(v => v.key === e);
      this.searchQuery.PartnersName = obj.label
    },
    GetXZ_SAP_CSKS() {
      GetXZ_SAP_CSKS().then(res => {
        if (res.Code === 2000) {
          this.CostCenterOptions = res.Data;
        }
      })
    },
    changeCostCenter(e) {
      const obj = this.CostCenterOptions.find(v => v.KOSTL === e);
      this.searchQuery.CostCenterName = obj.LTEXT;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'CostCenterName', this.searchQuery.CostCenterName);
          this.$set(res, 'CostCenter', this.searchQuery.CostCenter);
        })
      }
      this.$forceUpdate();
    },
    GetXZ_SAP_SKA1() {
      GetXZ_SAP_SKA1().then(res => {
        if (res.Code === 2000) {
          this.LedgerTypeOptions = res.Data
        }
      })
    },
    changeLedgerType(e) {
      const obj = this.LedgerTypeOptions.find(v => v.SAKNR === e);
      this.searchQuery.LedgerTypeName = obj.TXT50;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'LedgerTypeName', this.searchQuery.LedgerTypeName);
          this.$set(res, 'LedgerType', this.searchQuery.LedgerType);
        })
      }
      this.$forceUpdate();
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.searchQuery.ReturnWhsName = this.options.filter(item => item.value === e)[0].label;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'ReturnWhsName', this.searchQuery.ReturnWhsName);
          this.$set(res, 'ReturnWhsCode', this.searchQuery.ReturnWhsCode);
        })
      }
    },
    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        // console.log(response);
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName
    },
    changeReturnDate(e) {
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'ReturnDate', this.$moment(e).format('YYYY-MM-DD'))
        })
      }
    },
    changeLendingType(e) {
      if (e === '0') {
        this.lendingTypeDisabled = false
      } else {
        this.lendingTypeDisabled = true
      }
      this.list = []
    },
    selectMaterials() {
      this.$refs.modalFormAddMaterials.add();
    },
    modalFormOkAddMaterials(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          BarCode: v.BarCode,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          RegionCode: v.RegionCode, //
          RegionName: v.RegionName, //
          BinLocationCode: v.BinLocationCode, //
          BinLocationName: v.BinLocationName, //
          AssessType: v.AssessType,
          ReturnDate: this.searchQuery.ReturnDate ? this.$moment(this.searchQuery.ReturnDate).format(
            'YYYY-MM-DD') : '',
          CostCenter: this.searchQuery.CostCenter || '',
          LedgerType: this.searchQuery.LedgerType || '',
          CostCenterName: this.searchQuery.CostCenterName || '', // 成本中心名称,
          LedgerTypeName: this.searchQuery.LedgerTypeName || '', // 总账科目名称,
          ReturnWhsCode: this.searchQuery.ReturnWhsCode || '',
          ReturnWhsName: this.searchQuery.ReturnWhsName || '',
          StockID: v.ItemCode + v.WhsCode + v.AssessType
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode + next.AssessType] ? '' : obj[next.ItemCode + next.WhsCode + next.AssessType] = true && cur.push(next);
        return cur;
      }, [])
    }
  }
}
</script>
