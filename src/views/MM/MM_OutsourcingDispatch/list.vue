<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <!--检索区域-->
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateValue"
        :clearable="false"
        size="small"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
      />
      <el-select
        v-model="listQuery.isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.postingStatus')"
        style="width: 140px"
        class="filter-item"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingDispatch.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingDispatch.Posting'}"
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePosting"
      >{{ $t('Common.posting') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{name:'MM.MM_OutsourcingDispatch.PassPost'}"
        class="filter-item"
        type="danger"
        icon="el-icon-edit"
        size="small"
        :disabled="postDisable"
        @click="handlePassPost"
      >{{ $t('Common.passPost') }}
      </el-button>
      <el-button
        v-permission="{ name: 'MM.MM_OutsourcingDispatch.Print' }"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="postDisable"
        @click="handlePrint"
      >{{ $t('Common.print') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_OutsourcingDispatch.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :height="tableHeight"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
      size="mini"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="单号" prop="DocNum" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" prop="ItemName" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品数量" prop="MatnrQty" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.MatnrQty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="委外发料数量" prop="OutsourcingDispatchQty" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.OutsourcingDispatchQty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="行号" prop="Line" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column
        label="领料申请单单号"
        prop="SubcontractingApplicationNum"
        align="center"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SubcontractingApplicationNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品编号" prop="MatnrCode" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.MatnrCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" prop="MatnrName" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.MatnrName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="领料申请单行号" prop="SubcontractingApplicationLine" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="批次" prop="BatchNum" align="center" width="160" show-overflow-tooltip /> -->
      <!-- <el-table-column label="采购单号" prop="PurchaseNum" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="采购单行号" prop="PurchaseLine" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="区域" prop="RegionName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="库位" prop="BinLocationName" align="center" width="160" show-overflow-tooltip /> -->
      <el-table-column label="备注" prop="Remark" align="center" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="过账日期" prop="ManualPostTime" align="center"  width="160" :formatter="formatDate"/> -->
      <el-table-column label="是否过账" prop="IsPosted" align="center" width="100" fixed="right">
        <template slot-scope="scope">
          <span>{{ scope.row.IsPosted | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过账人" prop="PostUser" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PostUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="凭证日期"
        prop="PostTime"
        align="center"
        width="160"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
      <el-table-column label="Sap物料凭证单号" prop="SapDocNum" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapDocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Sap物料凭证行号" prop="SapLine" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SapLine }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="100"
        show-overflow-tooltip
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import permission from '@/directive/permission/permission'; // 权限
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  exportToExcel
} from '@/utils/excel-export'; // 导出

import {
  fetchList,
  batchDelete,
  exportExcelFile,
  DoPost,
  printToPDF,
  PassPost
} from '@/api/MM/MM_OutsourcingDispatch';

export default {
  name: 'MM.MM_OutsourcingDispatch',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      isProcessing: false,
      listLoading: true,
      listQuery: {
        keyword: '', // 用户姓名 | 登录账号
        PageNumber: 1,
        PageSize: 50,
        dateValue: [
          new Date(),
          new Date()
        ],
        isPosted: ''
      },
      hasPostedData: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPostedOptions: [{
        label: '全部',
        key: ''
      },
      {
        label: this.$i18n.t('Common.posted'),
        key: true
      },
      {
        label: this.$i18n.t('Common.notPosted'),
        key: false
      }
      ],
      multipleSelection: [],
      listDetail: [],
      totalDetail: 0,
      listDetailQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 50
      },
      tableHeight: 300
    };
  },
  computed: {
    canNotUpdate() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    postDisable() {
      return (
        this.multipleSelection.length === 0
      );
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM_DepartmentPickingApplication') {
        this.handleFilter();
      }
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.tableHeight = window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 150;
      // 监听窗口大小变化
      const self = this;
      window.onresize = function() {
        self.tableHeight = window.innerHeight - self.$refs.multipleTable.$el.offsetTop - 150;
      }
    })
  },
  created() {
    this.handleFilter()
  },
  methods: {
    formatDate,
    formatDateTime,
    getList() {
      this.listLoading = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      const query = Object.assign({}, this.listQuery);
      fetchList(query).then(response => {
        console.log('查询退货信息查出来的数据', response);
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handlePrint() {
      this.isProcessing = true;
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        var docNums = selectRows.map(v => v.DocNum);
        printToPDF({
          docNums: docNums
        }).then(response => {
          window.open(this.API.BaseURL + response.Data.PrintedPDF);
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      }
    },
    // 导出
    handleExport() {
      this.isProcessing = true;
      if (this.listQuery.dateValue) {
        this.listQuery.dateValue[0] = this.$moment(this.listQuery.dateValue[0]).format('YYYY-MM-DD');
        this.listQuery.dateValue[1] = this.$moment(this.listQuery.dateValue[1]).format('YYYY-MM-DD');
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateValue: this.listQuery.dateValue,
        isPosted: this.listQuery.isPosted
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '委外发料');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handlePosting() {
      console.log(this.multipleSelection);
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === true) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，请勿重复过账');
            switchBtn = false;
            this.isProcessing = false;
            return true
          }
        });
        if (switchBtn) {
          DoPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.postSuccess');
                }
              } else {
                this.showNotify('error', res.Message || 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
            });
        }
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.hasPostedData = false;
      this.multipleSelection = val;
      var postedData = this.multipleSelection.find(item => {
        return item.IsPosted === true;
      });
      if (postedData !== undefined) {
        this.hasPostedData = true;
      } else {
        this.hasPostedData = false;
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      let switchBtn = true;
      selectRows.some(v => {
        if (this.$store.getters.userRole !== 1) {
          if (v.CUser !== this.$store.getters.userinfo.LoginAccount) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息，您无权操作');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        }
        if (v.IsPosted === true) {
          this.showNotify('warning', '单号为：' + v.DocNum + '信息已过账，禁止删除');
          switchBtn = false;
          return true;
        }
      });
      if (switchBtn) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          this.isProcessing = true;
          console.log('delete--', selectRows);
          var arrRowsID = selectRows.map(v => v.OutsourcingDispatchID);
          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(res => {
              this.isProcessing = false;
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.handleFilter();
            })
            .catch(error => {
              this.isProcessing = false;
            });
        });
      }
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
    },
    tableRowClassName({
      row,
      rowIndex
    }) {
      if (row.SAPmark === 'E' && row.IsPosted === false) {
        return 'warning-row';
      }
      return '';
    },
    handlePassPost() {
      this.isProcessing = true;
      if (this.multipleSelection) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.IsPosted === false) {
            this.showNotify('warning', '单号为：' + v.DocNum + '信息未过账，请先过账');
            switchBtn = false;
            this.isProcessing = false;
            return true
          }
        });
        if (switchBtn) {
          PassPost(this.multipleSelection)
            .then(res => {
              if (res.Code === 2000) {
                if (res.MessageParam === 2000) {
                  this.showNotify('success', res.Message || 'Common.postSuccess');
                } else {
                  this.showNotify('warning', res.Message || 'Common.postSuccess');
                }
              } else {
                this.showNotify('error', 'Common.operationFailed');
              }
              this.handleFilter();
              this.isProcessing = false;
            })
            .catch(err => {
              console.log(err);
              this.isProcessing = false;
              this.handleFilter()
            });
        }
      }
    }
  }
};
</script>
