<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("ui.MM.DepartmentPickingApplicationDetail.title") }}</label>
    </p>

    <el-form
      ref="dataForm"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="80px"
    >
      <el-form-item
        class="filter-item"
        :label="$t('ui.MM.DepartmentPickingApplication.RequisitionNum')"
        prop="RequisitionNum"
      >
        <el-input v-model="searchQuery.RequisitionNum" disabled />
      </el-form-item>
      <el-form-item :label="$t('ui.MM.DepartmentPickingApplication.ItemCode')">
        <el-input
          v-model="searchQuery.ItemCode"
          :placeholder="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
          readonly
        >
          <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('ui.MM.DepartmentPickingApplication.Remark')">
        <el-input v-model="searchQuery.Remark" type="textarea" :rows="2" />
      </el-form-item>
    </el-form>
    <p>
      <el-button type="danger" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}</el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.RequisitionNum')"
        prop="RequisitionNum"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RequisitionNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.BaseLine')"
        prop="BaseLine"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BaseLine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
        prop="ItemCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.ItemName')"
        prop="ItemName"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.DepartmentPickingApplication.FactoryCode')" prop="FactoryCode" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.FactoryCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.MovementType')"
        prop="MovementType"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MovementType }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.ApplyQty')"
        prop="ApplyQty"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ApplyQty }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.DepartmentPickingApplication.Unit')" prop="Unit" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.WhsCode')"
        prop="WhsCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.WhsName')"
        prop="WhsName"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.OrderNum')"
        prop="OrderNum"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.SalesOrderNum')"
        prop="SalesOrderNum"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderNum }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.SalesOrderPro')"
        prop="SalesOrderPro"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SalesOrderPro }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.AssessmentType')"
        prop="AssessmentType"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.AssessmentType }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.DepartmentPickingApplication.Remark')"
        prop="Remark"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.Cuser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
      <el-table-column fixed="right" :label="$t('ui.PO.PO_ReturnScanDetail.operation')" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
        @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
  </div>
</template>
<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination

import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  GetList
} from '@/api/MM/MM_DepartmentPickingApplication';
export default {
  name: 'MM.MM_SubcontractingApplicationDetail',
  components: {
    Pagination
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DepRequisitionID: '',
        RequisitionNum: '',
        ItemCode: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        ItemCode: [{
          required: true,
          message: this.$i18n.t('ui.MM.DepartmentPickingApplication.ItemCode'),
          trigger: 'blur'
        }]
      },
      editStatus: ''
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    getList() {
      this.listLoading = true;
      GetDetailListForTest(this.listQuery).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data.items;
          this.total = res.Data.total;
          this.listLoading = false;
        }
      })
    },
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
        });
      }
    },
    handleCommit() {
      this.startLoading();
      const query = {
        DepRequisition: this.searchQuery,
        DetailedList: this.list
      };
      SubmitScanInfo(query).then(res => {
        if (res.Code === 2000) {
          this.backTo('MM.MM_OutsourcingDispatch');
        } else {
          this.showNotify('error', res.Message);
        }
        this.endLoading()
      }).catch(err => {
        console.log(err);
        this.endLoading();
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit')
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        // if (v.PurchaseOrderDetailID === record.PurchaseOrderDetailID) {
        //   this.$set(this.list, index, record)
        // }
        if (v.PurchaseOrderDetailID) {
          if (v.PurchaseOrderDetailID === record.PurchaseOrderDetailID) {
            this.$set(this.list, index, record)
          }
        } else {
          if (v.onlyId === record.onlyId) {
            this.$set(this.list, index, record)
          }
        }
      });
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.RequisitionNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      this.list = record;
      let model = '';
      record.forEach(v => {
        model += v.ItemCode + ',';
        this.searchQuery.ItemCode = model.substr(0, model.length - 1)
      });
    },
    getDetailList() {
      const query = {
        RequisitionNum: this.searchQuery.RequisitionNum
      };
      this.listLoading = true;
      GetList(query).then(res => {
        if (res.Code === 2000) {
          this.list = res.Data;
          this.listLoading = false;
        }
      })
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.DepRequisitionID) {
        this.editStatus = 'edit';
        console.log(this.searchQuery);
        this.getDetailList()
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    }
  }
}
</script>
