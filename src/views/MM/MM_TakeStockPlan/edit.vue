<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('ui.MM.TakeStockPlan.title') }}</label>
    </p>
    <el-form ref="dataForm" :inline="false" :rules="rules" :model="primary" label-position="right" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.TakeStockPlan.DocNum')" prop="DocNum">
            <el-input v-model="primary.DocNum" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.TakeStockPlan.PUser')" prop="PUser">
            <el-input v-model="primary.PUser" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MM.TakeStockPlan.Status')" prop="Status">
            <el-select v-model="primary.Status" filterable style="width: 100%;">
              <el-option
                v-for="item in statusOptions"
                :key="item.EnumKey"
                :label="item.EnumValue"
                :value="item.EnumKey"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="仓库" >
          <el-select v-model="primary.WhsCode" filterable placeholder="请选择" @change="changeWhsName" style="width: 100%;">
            <el-option v-for="item in options" :key="item.value" :label="item.value+'-'+item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input v-model="primary.ItemCode" placeholder="库存信息" readonly>
              <el-button slot="append" icon="el-icon-more" @click="selectCustomerMaterials" />
            </el-input>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <add-select-materials-model ref="modalFormMaterials" :data-list="listDetail" @ok="modalFormOkMaterials" />

    <p>
      <el-button v-waves class="filter-item" type="danger" icon="el-icon-delete" size="small" @click="handleDelDetail">
        {{ $t('Common.delete') }}</el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-edit" size="small" @click="handleCommitDetail">
        {{ $t('Common.confirm') }}</el-button>
    </p>
    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortDetailChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <!-- <el-table-column :label="$t('ui.MM.TakeStockPlan.DocNum')" prop="DocNum"   align="center" width="120">
            <template slot-scope="scope">
                <span>{{ scope.row.DocNum }}</span>
            </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MM.TakeStockPlan.PUser')" prop="PUser"   align="center" width="220">
            <template slot-scope="scope">
                <span>{{ scope.row.PUser }}</span>
            </template>
        </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockPlan.Status')" prop="Status"   align="center" width="140" :formatter="statusFormatter"></el-table-column>-->
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.ItemCode')" prop="ItemCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.ItemName')" prop="ItemName" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售单号" prop="SaleNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售单行号" prop="SaleLine" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessType" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="特殊库存" prop="SpecialStock" align="center" width="100" show-overflow-tooltip />
    </el-table>
  </div>
</template>

<script>
import {
  fetchDetailPage,
  addDetail,
  updateDetail,
  batchDeleteDetail,
  addDetails,
  updateDetails,
  fetchListByDocNum
} from '@/api/MM/MM_TakeStockPlanDetailed';
import {
  fetchDocNum
} from '@/api/MM/MM_TakeStockPlan';
import {
  fetchAllList as fetchAllRegionList
} from '@/api/MD/MD_Region';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import StockDlg from '@/components/FLD/StockDlg';
import MaterialDlg from '@/components/FLD/MaterialDlg';
import BinLocationDlg from '@/components/FLD/BinLocationDlgTake';
import SupplierDlg from '@/components/FLD/SupplierDlg';
import AddSelectMaterialsModel from './modules/addSelectModel2'

export default {
  name: 'MM.MM_TakeStockPlanDetailed',
  components: {
    Pagination,
    StockDlg,
    BinLocationDlg,
    MaterialDlg,
    SupplierDlg,
    AddSelectMaterialsModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      listDetail: [],
      totalDetail: 0,
      listDetailLoading: false,
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        docNum: ''
      },
      primary: {
        PlanID: undefined,
        DocNum: undefined,
        PUser: undefined,
        Status: undefined,
        Remark: undefined,
        ItemCode: '',
        WhsCode: '',
        WhsName: '',
        BinLocationCode: undefined,
        BinLocationName: undefined,
        RegionName: undefined,
        RegionCode: undefined
      },
      temp: {
        PlanID: undefined,
        DocNum: undefined,
        RegionCode: undefined,
        RegionName: undefined,
        BinLocationCode: undefined,
        BinLocationName: undefined,
        SupplierCode: undefined,
        SupplierName: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        Remark: undefined,
        PUser: undefined,
        Status: undefined
      },
      dlgVisible: false,
      dlgLocationVisible: false,
      dlgSupplierVisible: false,
      rules: {
        DocNum: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        PUser: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        Status: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      multipleSelection: [],
      statusOptions: [{
        EnumKey: 1,
        EnumValue: '未开始'
      }, {
        EnumKey: 2,
        EnumValue: '已开始'
      },
      {
        EnumKey: 3,
        EnumValue: '已完成'
      }],
      regionOptions: [],
      options: []
    };
  },
  created() {
    this.getPageParams();
    this.GetXZ_SAP();
    // 盘点状态下拉框
    this.primary.Status = this.statusOptions[0].EnumKey;
  },
  methods: {
    statusFormatter(row, column) {
      var status = this.statusOptions.find(x => x.EnumKey == row.Status);
      if (status) return status.EnumValue;
    },
    getPageParams() {
      Object.assign(this.primary, this.$route.params);
      if (this.primary.DocNum) {
        // 编辑
        this.editStatus = 'edit';
        this.getDetailList();
      } else {
        // 新增
        this.editStatus = 'create';
        this.getDocNum();
      }
    },
    getDocNum() {
      fetchDocNum().then(response => {
        if (response.Code === 2000) {
          this.primary.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    getAllRegionList() {
      fetchAllRegionList().then(response => {
        if (response.Code === 2000) {
          this.regionOptions = response.Data;
          this.regionOptions.splice(0, '', {
            RegionCode: '',
            RegionName: '无'
          });
          this.regionOptions = this.regionOptions.filter(
            x => x.RegionCode != 'QM1'
          );
          // this.temp.RegionCode = response.Data[0].RegionCode
        } else {
          // console.log(response.Message)
        }
      });
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.primary.DocNum;
      fetchListByDocNum(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data;
          if (response.Data.length > 0) {
            this.primary.WhsCode = this.listDetail[0].WhsCode;
            this.primary.WhsName = this.listDetail[0].WhsName;
          }
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    handleDetailFilter() {
      this.getDetailList();
    },
    sortDetailChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleDetailFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    resetTemp() {
      this.temp = {
        PlanID: undefined,
        DocNum: undefined,
        RegionCode: undefined,
        RegionName: undefined,
        BinLocationCode: undefined,
        BinLocationName: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        Remark: undefined
      };
    },
    handleAddDetail() {
      console.log(this.temp);
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (
            (this.primary.RegionCode == undefined || this.primary.RegionCode == '') &&
              (this.primary.BinLocationCode == undefined || this.primary.BinLocationCode == '') &&
              (this.temp.ItemCode == undefined || this.temp.ItemCode == '') &&
              (this.temp.SupplierCode == undefined || this.temp.SupplierCode == '')
          ) {
            this.showNotify('error', 'ui.MM.TakeStockPlan.missingEssential');
            return;
          }
          var detail = this.listDetail.find(
            obj =>
              obj.ItemCode == this.temp.ItemCode &&
              obj.RegionCode == this.primary.RegionCode &&
              obj.BinLocationCode == this.primary.BinLocationCode &&
              obj.DocNum == this.primary.DocNum || (obj.SupplierCode != '' || obj.SupplierCode != undefined)
          );
          if (detail) {} else {
            if (this.temp.SupplierCode) {
              this.listDetail = [Object.assign({}, this.temp)];
              this.temp.SupplierCode = undefined;
              this.temp.SupplierName = undefined;
            } else {
              this.temp.DocNum = this.primary.DocNum;
              this.temp.PUser = this.primary.PUser;
              this.temp.Status = this.primary.Status;
              this.listDetail.push(Object.assign({}, this.temp)); // this.temp));
              this.temp;
            }
          }
        } else {
          return false;
        }
      });
    },
    handleCommitDetail() {
      this.$confirm(
        this.$i18n.t('Common.committingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.commit();
      });
    },
    handleDelDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.PlanID) {
            v.IsDelete = true;
            this.delList.push(v.PlanID)
          }
          var i = this.listDetail.indexOf(v);
          this.listDetail.splice(i, 1);
        });
      }
    },
    handleMaterialDialogBtnClick() {
      this.dlgVisible = true;
    },
    handleSupplierDialogBtnClick() {
      this.dlgSupplierVisible = true;
    },
    handleLocationDialogBtnClick() {
      this.dlgLocationVisible = true;
    },
    handleRegionChange(val) {
      this.primary.BinLocationCode = undefined;
      this.primary.BinLocationName = undefined;
      this.temp.BinLocationCode = undefined;
      this.temp.BinLocationName = undefined;
      if (val) {
        var region = this.regionOptions.find(x => x.RegionCode === val);
        if (region) {
          this.primary.RegionCode = val;
          this.primary.RegionName = region.RegionName;
          this.temp.RegionCode = val;
          this.temp.RegionName = region.RegionName;
          // this.primary.BinLocationCode = undefined;
          // this.primary.BinLocationName = undefined;
          // this.temp.BinLocationCode = undefined;
          // this.temp.BinLocationName = undefined;
        }
      } else {
        this.primary.RegionCode = '';
        this.primary.RegionName = '';
        this.temp.RegionCode = '';
        this.temp.RegionName = '';
      }
    },
    supplierSelected(supplier) {
      this.resetTemp();
      this.primary.BinLocationCode = undefined;
      this.primary.BinLocationName = undefined;
      this.primary.RegionCode = undefined;
      this.primary.RegionName = undefined;
      this.temp.SupplierCode = supplier.SupplierCode;
      this.temp.SupplierName = supplier.SupplierName;
    },
    locationSelected(location) {
      if (location.length > 1) {
        // console.log(location);
        this.$refs.dataForm.validate(valid => {
          if (valid) {
            if (location) {
              // && materials.length > 0
              var isHaveQM1 = location.find(x => x.RegionCode == 'QM1');
              if (isHaveQM1) {
                this.showNotify(
                  'error',
                  'ui.MM.TakeStockPlan.QMRegionNotAllowed'
                );
                return;
              }
              location.forEach(x => {
                // console.log(materials);
                var obj = this.listDetail.find(
                  y => y.BinLocationCode == x.BinLocationCode
                );
                if (obj) {
                  // 已存在的不添加
                } else {
                  var binLocation = Object.assign({}, x);
                  binLocation.RegionCode = this.primary.RegionCode;
                  binLocation.RegionName = this.primary.RegionName;
                  binLocation.DocNum = this.primary.DocNum;
                  binLocation.PUser = this.primary.PUser;
                  binLocation.Status = this.primary.Status;
                  this.listDetail.push(binLocation);
                }
              });
            }
            this.$forceUpdate();
          } else return false;
        });
      } else {
        // console.log(location[0]);
        var singleLocation = location[0];

        if (singleLocation.RegionCode == 'QM1') {
          this.showNotify('error', 'ui.MM.TakeStockPlan.QMRegionNotAllowed');
          return;
        }
        if (singleLocation) {
          this.primary.BinLocationCode = singleLocation.BinLocationCode;
          this.primary.BinLocationName = singleLocation.BinLocationName;
          this.temp.BinLocationCode = singleLocation.BinLocationCode;
          this.temp.BinLocationName = singleLocation.BinLocationName;
        } else {
          this.primary.BinLocationCode = '';
          this.primary.BinLocationName = '';
          this.temp.BinLocationCode = '';
          this.temp.BinLocationName = '';
        }
      }
    },
    materialSelected(materials) {
      console.log('materialSelected-test', materials);
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (materials) {
            // && materials.length > 0
            // materials.forEach(x => {
            // console.log(materials);
            var obj = this.listDetail.find(
              y => y.ItemCode == materials.ItemCode
            );
            if (obj) {
              // 已存在的不添加
            } else {
              var material = Object.assign({}, materials);
              material.RegionCode = this.primary.RegionCode;
              material.RegionName = this.primary.RegionName;
              material.BinLocationCode = this.primary.BinLocationCode;
              material.BinLocationName = this.primary.BinLocationName;
              material.DocNum = this.primary.DocNum;
              material.PUser = this.primary.PUser;
              material.Status = this.primary.Status;
              this.listDetail.push(material);
            }
            // });
          }
          this.$forceUpdate();
        } else return false;
      });
    },
    commit() {
      this.startLoading();
      if (this.editStatus === 'edit') {
        updateDetails({
          primary: this.primary,
          list: this.listDetail
        }).then(response => {
          if (response.Code === 2000) {
            // 跳转回主单页面
            this.backTo('MM.MM_TakeStockPlan');
          } else {
            this.showNotify('error', response.Message);
          }
          this.endLoading()
        });
      } else {
        addDetails({
          primary: this.primary,
          list: this.listDetail
        }).then(response => {
          if (response.Code === 2000) {
            // 跳转回主单页面
            this.backTo('MM.MM_TakeStockPlan');
          } else {
            this.showNotify('error', response.Message);
          }
          this.endLoading()
        });
      }
    },
    GetXZ_SAP() {
      GetXZ_SAP().then(res => {
        const _this = this;
        _this.options = [];
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.options.push({
              value: res.LGORT,
              label: res.LGOBE
            })
          })
        }
      })
    },
    changeWhsName(e) {
      this.primary.WhsName = this.options.filter(item => item.value === e)[0].label;
      if (this.listDetail.length > 0) {
        this.listDetail.forEach(res => {
          this.$set(res, 'WhsName', this.primary.WhsName);
          this.$set(res, 'WhsCode', this.primary.WhsCode);
        })
      }
    },
    selectCustomerMaterials() {
      this.$refs.modalFormMaterials.add();
    },
    modalFormOkMaterials(record) {
      const data = [];
      // record.forEach((v, index) => {
      //   data.push({
      //     ItemCode: v.MATNR, // 物料件号
      //     ItemName: v.MAKTX, // 物料名称
      //     // Unit: v.MEINS,
      //     WhsName: this.primary.WhsName || '',
      //     WhsCode: this.primary.WhsCode || '',
      //     StockID: v.MATNR+v.MAKTX,
      //   })
      // })
      // let obj = {};
      // this.listDetail = this.listDetail.concat(data).reduce((cur, next) => {
      //   obj[next.ItemCode + next.ItemName] ? "" : obj[next.ItemCode + next.ItemName] = true && cur.push(next);
      //   return cur;
      // }, [])
      record.forEach((v, index) => {
        data.push({
          BarCode: v.BarCode,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.Qty,
          Unit: v.Unit,
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          RegionCode: v.RegionCode,
          RegionName: v.RegionName,
          BinLocationCode: v.BinLocationCode,
          BinLocationName: v.BinLocationName,
          SaleLine: v.SaleLine,
          SaleNum: v.SaleNum,
          SpecialStock: v.SpecialStock,
          AssessType: v.AssessType,
          StockID: v.ItemCode + v.WhsCode + v.SaleLine
        })
      });
      const obj = {};
      this.listDetail = this.listDetail.concat(data).reduce((cur, next) => {
        obj[next.ItemCode + next.WhsCode + next.SaleLine ] ? '' : obj[next.ItemCode + next.WhsCode + next.SaleLine] = true && cur.push(next);
        return cur;
      }, [])
    }
  }
};
</script>
