<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.dateRangeValue"
        size="small"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="isPosted"
        size="small"
        filterable
        :placeholder="$t('Common.status')"
        style="width: 140px"
        class="filter-item"
        @change="handleSelectChangeValue"
      >
        <el-option v-for="item in this.statusOptions" :key="item.key" :label="item.EnumValue" :value="item.EnumKey" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button v-waves class="filter-item" type="primary" size="small" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.Add' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleCreate"
      >
        {{ $t("Common.add") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.Edit' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >
        {{ $t("Common.edit") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.Start' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="success"
        icon="el-icon-check"
        :disabled="unAccomplishable"
        @click="handleStart"
      >
        {{ $t("Common.start") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.SuccessF' }"
        class="filter-item"
        style="margin-left: 10px"
        size="small"
        type="success"
        icon="el-icon-check"
        :disabled="accomplishable"
        @click="handleFinish"
      >
        {{ $t("Common.successf") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.Print' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        :disabled="printDisable"
        @click="handlePrint"
      >{{ $t("Common.print") }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_TakeStockPlan.Export' }"
        :loading="downloadLoading"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t("Common.export") }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column :label="$t('ui.MM.TakeStockPlan.DocNum')" prop="DocNum" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockPlan.PUser')" prop="PUser" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.PUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.TakeStockPlan.Status')"
        prop="Status"
        align="center"
        :formatter="statusFormatter"
      />

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <p>
      <span>{{ $t("ui.MM.TakeStockPlanDetailed.title") }}</span>
    </p>

    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      size="mini"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.DocNum')" prop="DocNum" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.DocNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.SupplierCode')" prop="SupplierCode" align="center"
        width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.SupplierName')" prop="SupplierName" align="center"
        width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName}}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.ItemCode')" prop="ItemCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.TakeStockPlanDetailed.ItemName')" prop="ItemName" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.TakeStockPlanDetailed.RegionCode')"
        prop="RegionCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.TakeStockPlanDetailed.RegionName')"
        prop="RegionName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.TakeStockPlanDetailed.BinLocationCode')"
        prop="BinLocationCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.TakeStockPlanDetailed.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <!-- <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{scope.row.Unit}}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="仓库编码" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="销售单号" prop="SaleNum" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="销售单行号" prop="SaleLine" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="评估类型" prop="AssessType" align="center" width="100" show-overflow-tooltip />
      <el-table-column label="特殊库存" prop="SpecialStock" align="center" width="100" show-overflow-tooltip />
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalDetail > 0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />
  </div>
</template>

<script>
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchPage,
  batchDelete,
  finishTakeStockPlan,
  printToPDF,
  exportExcelFile1
} from '@/api/MM/MM_TakeStockPlan';
import {
  fetchDetailPage,
  updatePlanList
} from '@/api/MM/MM_TakeStockPlanDetailed';
import waves from '@/directive/waves'; // waves directive
import {
  parseTime
} from '@/utils';

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

import Pagination from '@/components/Pagination'; // secondary package based on el-pagination

_ = require('lodash');

export default {
  name: 'MM.MM_TakeStockPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: undefined,
      listDetail: undefined,
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      downloadLoading: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        fromTime: '',
        toTime: '',
        dateRangeValue: [
          new Date(),
          new Date()
        ]
      },
      isPosted: 0, // 暂未加入搜索条件
      changeSelectValue: 0,
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: 0
      },
      {
        label: this.$i18n.t('Common.begin'),
        key: 2
      },
      {
        label: this.$i18n.t('Common.unbegin'),
        key: 1
      }
      ],
      listDetailQuery: {
        PageNumber: 1,
        PageSize: 20,
        docNum: ''
      },
      multipleSelection: [],
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      statusOptions: [{
        EnumKey: 0,
        EnumValue: '全部'
      }, {
        EnumKey: 1,
        EnumValue: '未开始'
      }, {
        EnumKey: 2,
        EnumValue: '已开始'
      },
      {
        EnumKey: 3,
        EnumValue: '已完成'
      }
      ],
      isProcessing: false
    };
  },
  computed: {
    selective() {
      if (this.multipleSelection && this.multipleSelection.length === 1) {
        return this.multipleSelection[0].Status > 1;
      }
      return true;
    },
    deletable() {
      if (this.multipleSelection.length === 0) return true;
      if (this.multipleSelection && this.multipleSelection.length > 0) {
        var obj = this.multipleSelection.find(x => x.Status > 1);
        if (obj) {
          return true;
        }
      }
      return false;
    },
    accomplishable() {
      if (this.multipleSelection && this.multipleSelection.length == 1) {
        return !(this.multipleSelection[0].Status === 2);
      }
      return true;
    },
    unAccomplishable() {
      if (this.multipleSelection && this.multipleSelection.length == 1) {
        return !(this.multipleSelection[0].Status === 1);
      }
      return true;
    },
    printDisable() {
      // || this.multipleSelection[0].Status !== 1
      return this.multipleSelection.length === 0;
    }
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      if (newVal === '/MM/MM/MM_TakeStockPlan') {
        this.getList();
      }
    }
  },
  created() {
    this.getList()
    // this.getDetailList()
    // this.getDict("MM001").then(data => {
    //   this.statusOptions = data;
    //   this.statusOptions.unshift({
    //     EnumKey: 0,
    //     EnumValue: "全部"
    //   });
    // });
  },
  methods: {
    statusFormatter(row, column) {
      var status = this.statusOptions.find(x => x.EnumKey == row.Status);
      return status.EnumValue;
    },
    getList() {
      // 获取数据
      this.listLoading = true;
      // this.listQuery.fromTime = "";

      this.listQuery.Status = this.changeSelectValue;
      // this.listQuery.toTime = "";
      // if (this.listQuery.dateRangeValue) {
      //   this.listQuery.fromTime = this.listQuery.dateRangeValue[0];
      //   this.listQuery.toTime = this.listQuery.dateRangeValue[1];
      // }

      console.log(this.listQuery);
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          console.log(response.Data);

          this.list = response.Data.items;
          this.total = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleSelectChangeValue(val) {
      this.changeSelectValue = val;
      this.getList();
    },
    getDetailList() {
      this.listDetailLoading = true;
      this.listDetailQuery.docNum = this.currentRow.DocNum;

      fetchDetailPage(this.listDetailQuery).then(response => {
        if (response.Code === 2000) {
          this.listDetail = response.Data.items;
          this.totalDetail = response.Data.total;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listDetailLoading = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter(1);
    },
    detailSortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.handleFilter(2);
    },
    clearTables(flag) {
      if (flag === 1) {
        this.list = [];
      } else if (flag === 2) this.listDetail = [];
      else {
        this.list = [];
        this.listDetail = [];
      }
    },
    finishPlan(planID) {
      this.isProcessing = true;
      finishTakeStockPlan({
        PlanID: planID
      }).then(response => {
        if (response.Code === 2000) {
          this.showNotify('success', 'Common.operationSuccess');
          this.getList();
        } else {
          this.showNotify('error', response.Message);
        }
        this.isProcessing = false;
      });
    },
    handleFilter(flag) {
      if (flag == 2) {
        this.clearTables(2);
        if (this.currentRow) this.getDetailList();
      } else {
        this.listQuery.PageNumber = 1;
        this.listQuery.PageSize = 10;
        this.clearTables(3);
        this.getList();
      }
    },
    handleCreate() {
      this.routeTo('MM.MM_TakeStockPlanDetailed');
    },
    handleUpdate() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.Status !== 1) {
            this.showNotify('warning', '计划单号为：' + v.DocNum + '信息已开始或完成，禁止编辑');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          this.routeTo(
            'MM.MM_TakeStockPlanDetailed',
            Object.assign(selectRows[0])
          );
        }
      }
    },
    handleDelete() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        let switchBtn = true;
        this.multipleSelection.some(v => {
          if (v.Status !== 1) {
            this.showNotify('warning', '计划单号为：' + v.DocNum + '信息已开始或完成，禁止删除');
            switchBtn = false;
            this.isProcessing = false;
            return true;
          }
        });
        if (switchBtn) {
          this.$confirm(
            this.$i18n.t('Common.batchDeletingConfirm'),
            this.$i18n.t('Common.tip'), {
              confirmButtonText: this.$i18n.t('Common.affirm'),
              cancelButtonText: this.$i18n.t('Common.cancel'),
              type: 'warning'
            }
          ).then(() => {
            this.isProcessing = true;
            var arrRowsID = selectRows.map(function(v) {
              return v.PlanID;
            });
              // 删除逻辑处理
            batchDelete(arrRowsID)
              .then(response => {
                if (response.Code === 2000) {
                  this.showNotify('success', 'Common.deleteSuccess');
                  this.handleFilter(1);
                } else {
                  this.showNotify('error', response.Message);
                }
                this.isProcessing = false;
              })
              .catch(error => {
                this.isProcessing = false;
              });
          });
        }
      }
    },
    handleStart() {
      this.isProcessing = true;
      var selectRows = this.multipleSelection;
      var arrRowsID = selectRows.map(function(v) {
        return v.PlanID;
      });
      updatePlanList({
        primaryId: arrRowsID,
        status: 2
      }).then(response => {
        if (response.Code === 2000) {
          this.showNotify('success', 'Common.operationSuccess');
          this.getList();
        } else {
          this.showNotify('error', response.Message);
        }

        this.isProcessing = false;
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRowClick(row, col, evt) {
      this.currentRow = Object.assign({}, row);
      this.handleFilter(2);
    },
    handleFinish() {
      var selectRows = this.multipleSelection;
      if (this.checkSingleSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.actionConfirm'),
          this.$i18n.t('Common.tip'), {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          var selectedRow = selectRows[0];
          if (selectedRow.Status === 2) {
            this.finishPlan(selectedRow.PlanID);
          } else {
            this.showNotify('error', 'MM.TakeStockPlan.planStatusError');
          }
        });
      }
    },
    handleExport() {
      // eslint-disable-next-line no-undefthis.listQuery.Status = this.changeSelectValue;
      this.isProcessing = true;
      this.downloadLoading = true;
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: this.listQuery.dateRangeValue,
        state: this.changeSelectValue
      };
      console.log(exportQuery);
      exportExcelFile1(exportQuery).then(res => {
        exportToExcel(res.data, '盘点计划单');
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
      this.downloadLoading = false;
    },
    handlePrint() {
      // const selectRows = this.multipleSelection[0].DocNum;
      // const binlocationCodes = selectRows.map(v => v.BarCode)
      const selectRows = this.multipleSelection;
      const docNums = selectRows.map(v => v.DocNum);
      this.isProcessing = true;
      printToPDF({
        docNums: docNums
      }).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    }
  }
};
</script>
