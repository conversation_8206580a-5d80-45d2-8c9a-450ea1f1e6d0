<template>
  <div class="app-container">
    <p>
      <label style="width:100%">供应商返修记录申请单登记</label>
    </p>

    <el-form
      ref="dataForm"
      class="formBox formBox120"
      :inline="true"
      :rules="rules"
      :model="searchQuery"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item class="filter-item" label="返修单号" prop="DocNum">
            <el-input v-model="searchQuery.DocNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存信息">
            <el-input
              v-model="searchQuery.ItemCode"
              :placeholder="$t('ui.MM.DepartmentPickingApplication.ItemCode')"
              readonly
            >
              <el-button slot="append" icon="el-icon-more" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商信息" prop="SupplierCode">
            <el-select
              v-model="searchQuery.SupplierCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeSupplierName"
            >
              <el-option
                v-for="item in SupplierOptions"
                :key="item.SupplierCode"
                :label="item.SupplierCode+'-'+item.SupplierName"
                :value="item.SupplierCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人员" prop="HandlenCode">
            <el-select
              v-model="searchQuery.HandlenCode"
              filterable
              placeholder="请选择"
              style="width: 100%;"
              @change="changeHandlenName"
            >
              <el-option
                v-for="item in HandlenOptions"
                :key="item.LoginAccount"
                :label="item.LoginAccount+'-'+item.UserName"
                :value="item.LoginAccount"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="16">
          <el-form-item label="备注">
            <el-input type="textarea" :rows="2" v-model="searchQuery.Remark" @change="changeRemark">
            </el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <p>
      <el-button type="danger" size="small" icon="el-icon-delete" :disabled="deletable" @click="handleDeleteDetail">
        {{ $t("Common.delete") }}</el-button>
      <el-button type="success" size="small" icon="el-icon-edit" @click="handleCommit">{{ $t("Common.confirm") }}
      </el-button>
    </p>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      style="width: 100%"
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column fixed="left" :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="供应商编码" prop="SupplierCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="出厂编号" prop="BarCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料件号" prop="ItemCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="物料名称" prop="ItemName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="数量" prop="Qty" align="center" width="80" show-overflow-tooltip />
      <el-table-column label="单位" prop="Unit" align="center" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.Unit ==='KAR'">CAR</span>
          <span v-else-if="scope.row.Unit ==='PAK'">PAC</span>
          <span v-else-if="scope.row.Unit ==='ST'">PC</span>
          <span v-else>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" prop="WhsCode" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="仓库名称" prop="WhsName" align="center" width="120" show-overflow-tooltip />
      <!-- <el-table-column label="区域编号" prop="RegionCode" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="区域名称" prop="RegionName" align="center" width="120" show-overflow-tooltip /> -->
      <!-- <el-table-column label="库位编号" prop="BinLocationCode" align="center" show-overflow-tooltip /> -->
      <!-- <el-table-column label="库位名称" prop="BinLocationName" align="center" width="120" show-overflow-tooltip /> -->
      <!-- <el-table-column label="办理人员编号" prop="HandlenCode" align="center" width="120" show-overflow-tooltip /> -->
      <el-table-column label="办理人员名称" prop="HandlenName" align="center" width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip />
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <span @click="toggle(scope.row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.PageNumber" :limit.sync="listQuery.PageSize"
      @pagination="getList" /> -->
    <!--  -->
    <add-select-model ref="modalForm" @ok="modalFormOk" />
    <add-model ref="modalFormAdd" @ok="modalFormOkAdd" />
  </div>
</template>

<script>
import Vue from 'vue'
import waves from '@/directive/waves'; // waves directive
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import AddSelectModel from './modules/addSelectModel2'
import AddModel from './modules/addModel'
import {
  parseTime
} from '@/utils';
import {
  formatDate,
  formatDateTime
} from '@/utils'; //
import {
  GetDocNum,
  SubmitScanInfo,
  update,
  GetList,
  GetSRM_SupplierInfo
} from '@/api/MM/MM_SupplierRepair';
import {
  GetXZ_SAP,
  GetWarehouseRegion,
  GetRegionBinLocation
} from '@/api/PO/PO_ReturnScan';
import {
  fetchList
} from '@/api/Sys/Sys_User';
export default {
  name: 'MM.MM_SupplierRepairDetailed',
  components: {
    Pagination,
    AddSelectModel,
    AddModel
  },
  directives: {
    waves
  },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 10
      },
      searchQuery: {
        DocNum: '',
        SupplierCode: '',
        SupplierName: '',
        HandlenCode: '',
        HandlenName: '',
        Remark: ''
      },
      multipleSelection: [],
      rules: {
        SupplierCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        HandlenCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      editStatus: '',
      delList: [],
      PartnersOptions: [],
      disabled: true,
      HandlenOptions: [],
      SupplierOptions: []
    };
  },

  computed: {
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getPageParams();
    this.GetSRM();
    this.getUserlist();
  },
  methods: {
    formatDate,
    formatDateTime,
    handleDeleteDetail() {
      var selectedRows = this.multipleSelection;
      if (this.checkMultiSelection(selectedRows)) {
        selectedRows.forEach(v => {
          if (v.DetailID) {
            v.IsDelete = true;
            this.delList.push(v.DetailID);
          }
          var i = this.list.indexOf(v);
          this.list.splice(i, 1);
          console.log(this.list, this.delList);
        });
      }
    },
    handleCommit() {
      if (this.list.length === 0) {
        this.showNotify('warning', '请选择库存信息');
        return;
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let switchBtn = true;
          this.list.some(res => {
            if (res.Qty === null || res.Qty === 0 || res.Qty === '0') {
              this.showNotify('warning', '数量不能为空或者为零');
              switchBtn = false;
              return true;
            }
          });
          if (switchBtn) {
            this.$refs.dataForm.validate((valid) => {
              if (valid) {
                this.startLoading();
                if (this.editStatus === 'create') {
                  SubmitScanInfo(this.list).then(res => {
                    if (res.Code === 2000) {
                      this.showNotify('success', res.Message);
                      this.backTo('MM.MM_SupplierRepair');
                    } else {
                      this.showNotify('error', response.Message);
                    }
                    this.endLoading();
                  }).catch(err => {
                    console.log(err);
                    this.endLoading();
                  })
                }
              } else {
                console.log('error submit!!');
                return false;
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    fetchDocNum() {
      GetDocNum().then(response => {
        if (response.Code === 2000) {
          this.searchQuery.DocNum = response.Data;
        } else {
          this.showNotify('error', response.Message);
        }
      });
    },
    selectCustomer() {
      this.$refs.modalForm.add();
    },
    modalFormOk(record) {
      const data = [];
      record.forEach((v, index) => {
        data.push({
          DocNum: this.searchQuery.DocNum,
          BarCode: v.BarCode,
          ItemCode: v.ItemCode, // 物料件号
          ItemName: v.ItemName, // 物料名称
          Qty: v.OutQty,
          sumQty: v.Qty,
          Unit: v.Unit,
          WhsCode: v.WhsCode,
          WhsName: v.WhsName,
          RegionCode: v.RegionCode, //
          RegionName: v.RegionName, //
          BinLocationCode: v.BinLocationCode, //
          BinLocationName: v.BinLocationName, //
          HandlenName: this.searchQuery.HandlenName,
          HandlenCode: this.searchQuery.HandlenCode,
          SupplierCode: this.searchQuery.SupplierCode,
          SupplierName: this.searchQuery.SupplierName,
          Remark: this.searchQuery.Remark,
          StockID: v.BarCode + v.ItemCode + v.WhsCode
        })
      });
      const obj = {};
      this.list = this.list.concat(data).reduce((cur, next) => {
        obj[next.BarCode + next.ItemCode + next.WhsCode] ? '' : obj[next.BarCode + next.ItemCode + next.WhsCode] = true && cur.push(next);
        return cur;
      }, [])
    },
    getPageParams() {
      Object.assign(this.searchQuery, this.$route.params);
      if (this.searchQuery.ID) {
        this.editStatus = 'edit';
      } else {
        this.fetchDocNum();
        this.editStatus = 'create';
      }
    },
    GetSRM() {
      GetSRM_SupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.SupplierOptions = res.Data;
        }
      })
    },
    changeSupplierName(e) {
      const obj = this.SupplierOptions.find(v => v.SupplierCode === e);
      this.searchQuery.SupplierName = obj.SupplierName;
      this.list.forEach((v, index) => {
        if (this.searchQuery.SupplierCode !== '') {
          this.$set(v, 'SupplierCode', this.searchQuery.SupplierCode);
          this.$set(v, 'SupplierName', this.searchQuery.SupplierName);
        }
      });
    },
    getUserlist() {
      const query = {
        keyword: '',
        PageNumber: 1,
        PageSize: 1000
      };
      fetchList(query).then(response => {
        this.HandlenOptions = response.Data.items;
      });
    },
    changeHandlenName(e) {
      this.searchQuery.HandlenName = this.HandlenOptions.filter(item => item.LoginAccount === e)[0].UserName;
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'HandlenName', this.searchQuery.HandlenName);
          this.$set(res, 'HandlenCode', this.searchQuery.HandlenCode);
        })
      }
    },
    changeRemark(e) {
      console.log(e);
      if (this.list.length > 0) {
        this.list.forEach(res => {
          this.$set(res, 'Remark', this.searchQuery.Remark);
        })
      }
    },
    toggle(key) {
      this.$refs.modalFormAdd.edit(key);
      this.$refs.modalFormAdd.title = this.$i18n.t('Common.edit');
    },
    modalFormOkAdd(record) {
      this.list.forEach((v, index) => {
        if (v.DetailedID) {
          if (v.DetailedID === record.DetailedID) {
            this.$set(this.list, index, record);
          }
        } else {
          if (v.StockID === record.StockID) {
            this.$set(this.list, index, record);
          }
        }
      });
    }
  }
}
</script>
