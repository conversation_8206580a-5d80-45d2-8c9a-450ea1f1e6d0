<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-date-picker
        v-model="dateRangeValue"
        :clearable="false"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-"
        :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />
      <el-select
        v-model="isPosted"
        filterable
        :placeholder="$t('Common.confirmStatus')"
        style="width: 140px"
        class="filter-item"
        @change="handleSelectChangeValue"
      >
        <el-option v-for="item in isPostedOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCodeBox.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handlePrint"
      >{{ $t('Common.print') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MM.MM_BarCodeBox.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column v-if="false" :label="$t('ui.MM.BarCodeBox.BarID')" prop="BarID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBox.BoxBarCode')" prop="BoxBarCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.ItemCode')" prop="ItemCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.Qty')" prop="Qty" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.BarCodeBox.BoxQRCode')"
        prop="BoxQRCode"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BoxQRCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBox.IConfirm')" prop="IConfirm" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime |datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
    <p>{{ $t('ui.MM.BarCodeBoxDetailed.title') }}</p>
    <el-table
      v-loading="listDetailLoading"
      :data="listDetail"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortDetailChange"
    >
      <el-table-column
        v-if="false"
        :label="$t('ui.MM.BarCodeBoxDetailed.BarID')"
        prop="BarID"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BarID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.BoxBarCode')" prop="BoxBarCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BoxBarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.BarCode')" prop="BarCode" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BarCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.PTime')" prop="PTime" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.PTime|date }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.ItemCode')" prop="ItemCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.ItemName')" prop="ItemName" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column
        :label="$t('ui.MM.BarCodeBoxDetailed.ItmsGrpCode')"
        prop="ItmsGrpCode"

        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpCode}}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.BarCodeBoxDetailed.ItmsGrpName')"
        prop="ItmsGrpName"

        align="center"
        width="220"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ItmsGrpName}}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.Qty')" prop="Qty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Qty }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MM.BarCodeBoxDetailed.IConfirm')"
        prop="IConfirm"
        align="center"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IConfirm|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('ui.MM.BarCodeBoxDetailed.BatchNum')" prop="BatchNum" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ scope.row.BatchNum }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CTime')" prop="CTime" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime |datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime |datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="totalDetail>0"
      :total="totalDetail"
      :page.sync="listDetailQuery.PageNumber"
      :limit.sync="listDetailQuery.PageSize"
      @pagination="getDetailList"
    />

    <el-dialog :title="$t('Common.select')" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form>
        <el-form-item :label="$t('ui.MM.BarCode.PrintTemplate')" prop="PrintTemplate">
          <el-select v-model="value" filterable>
            <el-option
              v-for="item in printModel"
              :key="item.TempleteDesc"
              :value="item.TempleteDesc"
              :label="item.TempleteDesc"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="getPrintModel">{{ $t('Common.print') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchPage,
  getPrintModel
} from '@/api/MM/MM_BarCodeBox';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchDetailPage,
  exportExcelFile
} from '@/api/MM/MM_BarCodeBoxDetailed';
import {
  printBarCodeToPDF
} from '@/api/PP/PP_Print';

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MM.MM_BarCodeBox',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  rules: [],
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      isPosted: undefined, // 暂未加入搜索条件
      changeSelectValue: '',
      dialogVisible: false,
      printModel: [],
      value: '',
      isPostedOptions: [{
        label: this.$i18n.t('Common.all'),
        key: ''
      },
      {
        label: this.$i18n.t('Common.isConfirm'),
        key: true
      },
      {
        label: this.$i18n.t('Common.unConfirm'),
        key: false
      }
      ],
      list: [],
      listDetail: [],
      total: 0,
      totalDetail: 0,
      listLoading: false,
      listDetailLoading: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10,
        fromTime: '',
        toTime: ''
      },
      listDetailQuery: {
        barCodeBox: '',
        PageNumber: 1,
        PageSize: 10
      },
      dateRangeValue: [
        new Date(),
        new Date()
      ],
      multipleSelection: [],
      selectedRow: {},
      isProcessing: false,
      dictPrintModels: []
    };
  },
  created() {
    this.getList();
    this.getPrintDict();
  },
  methods: {
    getPrintDict() {
      this.getDict('MM006').then(data => {
        this.dictPrintModels = data;
      });
    },
    getList() {
      this.listLoading = true;
      this.listQuery.fromTime = '';
      this.listQuery.toTime = '';
      this.listQuery.isConfirm = this.changeSelectValue;
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      fetchPage(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listDetail = null;
          this.totalDetail = 0;
          // this.getDetailList();
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    handleSelectChangeValue(val) {
      this.changeSelectValue = val;
      this.getList();
    },
    getDetailList() {
      this.listDetailLoading = true;
      if (this.selectedRow) {
        this.listDetailQuery.barCodeBox = this.selectedRow.BoxBarCode;
        fetchDetailPage(this.listDetailQuery).then(response => {
          if (response.Code === 2000) {
            this.listDetail = response.Data.items;
            this.totalDetail = response.Data.total;
          } else {
            this.showNotify('error', response.Message);
          }
          this.listDetailLoading = false;
        });
      }
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    sortDetailChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listDetailQuery.sort = prop + ' asc';
        } else {
          this.listDetailQuery.sort = prop + ' desc';
        }
      } else {
        this.listDetailQuery.sort = '';
      }
      this.getDetailList();
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleExport() {
      if (this.dateRangeValue) {
        this.listQuery.fromTime = this.dateRangeValue[0];
        this.listQuery.toTime = this.dateRangeValue[1];
      }
      var exportQuery = {
        keyword: this.listQuery.keyword,
        dateTimes: [this.listQuery.fromTime, this.listQuery.toTime],
        isConfirm: this.changeSelectValue
      };
      exportExcelFile(exportQuery).then(res =>
        exportToExcel(res.data, '包装箱码管理')
      );
    },
    getPrintModel() {
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        printBarCodeToPDF({
          barcodes: selectRows.map(x => x.BoxBarCode),
          templateCode: 'MM_BoxBarCode',
          typeCode: this.value
        }).then(response => {
          console.log(response);
          window.open(response.Data.PrintedPDF);
        });
      }
      this.value = this.printModel[this.printModel.length - 1].TempleteDesc;
      this.dialogVisible = false;
    },
    handlePrint() {
      var selectedRows = this.multipleSelection;
      var isDiffrent = selectedRows.find(
        x => x.ItemCode != selectedRows[0].ItemCode
      );
      if (isDiffrent) {
        this.showNotify('warning', 'ui.MM.BarCodeBox.PrintDiffrent');
        return;
      }
      this.dialogVisible = true;
      getPrintModel()
        .then(response => {
          if (response.Code === 2000) {
            this.printModel = response.Data;
            var dictDesc = this.dictPrintModels.find(
              x => x.EnumValue == selectedRows[0].ItemCode
            );
            console.log(dictDesc)
            if (dictDesc) {
              this.value = this.printModel.find(
                x => x.TempleteDesc == dictDesc.EnumValue1
              ).TempleteDesc;
            } else {
              this.value = this.printModel[
                this.printModel.length - 1
              ].TempleteDesc;
            }
          } else {
            this.showNotify('error', response.Message);
          }
        })
        .catch(error => {});
    },
    handleRowClick(row) {
      this.selectedRow = Object.assign({}, row);
      this.getDetailList();
    }
  }
};
</script>
