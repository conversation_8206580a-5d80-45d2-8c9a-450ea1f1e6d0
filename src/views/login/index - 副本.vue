<template>
  <div class="login-box">
    <!-- <img :src="logoTitle" class="logoTitle"> -->
    <div class="login-box-img">
      <!-- leftBox -->
      <!-- <img :src="logo"> -->
    </div>
    <div class="rightBox">
      <el-card class="card">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on" label-position="right">
          <div class="title-container">
            <h3 class="title">WMS仓储管理平台</h3>
          </div>
          <el-form-item prop="username" class="user">
            <!-- <span class="svg-container">
              <svg-icon icon-class="user" />用户名
            </span> -->
            <el-input ref="username" v-model="loginForm.username" placeholder="请输入用户名" name="username" type="text" tabindex="1" autocomplete="on" clearable />
          </el-form-item>

          <el-tooltip v-model="capsTooltip" :content="$t('Common.CheckCapslock')" placement="right" manual>
            <el-form-item prop="password" class="user">
              <!-- <span class="svg-container">
                <svg-icon icon-class="password" />密码
              </span> -->
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                autocomplete="on"
                clearable
                @keyup.native="checkCapslock"
                @blur="capsTooltip = false"
                @keyup.enter.native="handleLogin"
              />
              <!-- <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span> -->
            </el-form-item>
          </el-tooltip>
          <!-- <el-form-item>
          <div class="password">Forgot your password?</div>
        </el-form-item> -->
          <!-- <div
            class="login-box-form-btn"
            @click.native.prevent="handleLogin"
            @click="handleLogin"
          >
            <el-button type="primary" class="btn">
              <svg-icon icon-class="arrow-right" />
            </el-button>
          </div> -->
          <el-button :loading="loading" type="primary" class="loginBtn" @click.native.prevent="handleLogin">{{ $t("login.logIn") }}</el-button>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import {
  validUsername
} from '@/utils/validate';
import LangSelect from '@/components/LangSelect';
import md5 from 'js-md5';
// import SocialSign from './components/SocialSignin'
import logo from '@/assets/Login.png'
import logoTitle from '@/assets/XZ_logo_title.png'
export default {
  name: 'Login',
  components: {
    LangSelect
    // , SocialSign
  },
  data() {
    // const validateUsername = (rule, value, callback) => {
    //   if (!validUsername(value)) {
    //     callback(new Error('Please enter the correct user name'))
    //   } else {
    //     callback()
    //   }
    // }
    // const validatePassword = (rule, value, callback) => {
    //   if (value.length < 6) {
    //     callback(new Error('The password can not be less than 6 digits'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{
          required: true,
          message: this.$t('Common.ValidatorMessage.MustInput'),
          trigger: 'change'
        },
        {
          required: true,
          min: 3,
          max: 15,
          message: this.$t('Common.ValidatorMessage.LengthRange', {
            Range: '[3-15]'
          }),
          trigger: 'change'
        }
        ],
        password: [{
          required: true,
          min: 3,
          max: 15,
          message: this.$t('Common.ValidatorMessage.LengthRange', {
            Range: '[3-15]'
          }),
          trigger: 'change'
        }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      logo: logo,
      logoTitle: logoTitle
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        // console.log('$route',route)
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    checkCapslock({
      shiftKey,
      key
    } = {}) {
      if (key && key.length === 1) {
        if (
          (shiftKey && key >= 'a' && key <= 'z') ||
            (!shiftKey && key >= 'A' && key <= 'Z')
        ) {
          this.capsTooltip = true
        } else {
          this.capsTooltip = false
        }
      }
      if (key === 'CapsLock' && this.capsTooltip === true) {
        this.capsTooltip = false
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = '';
      } else {
        this.passwordType = 'password';
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      // 登陆按钮处理事件
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          // 前端校验通过
          this.loading = true;
          // console.log('handlelogin',md5('123456'))
          // this.loginForm.password = md5(this.loginForm.password)
          const loginData = {
            username: this.loginForm.username,
            password: md5(this.loginForm.password)
          };
          this.$store
            .dispatch('user/login', loginData)
            .then((data) => {
              if (data.Message) {
                this.$alert(i18n.t(data.Message), {
                  confirmButtonText: i18n.t('Common.affirm'),
                  callback: action => {
                    // 校验通过，跳转到布局首页
                    // console('login.otherQuery',this.otherQuery)
                    // console('login.redirect',this.redirect)
                    this.$router.push({
                      path: this.redirect || '/',
                      query: this.otherQuery
                    });
                    this.loading = false
                  }
                });
              } else {
                // 校验通过，跳转到布局首页
                // console('login.otherQuery',this.otherQuery)
                // console('login.redirect',this.redirect)
                this.$router.push({
                  path: this.redirect || '/',
                  query: this.otherQuery
                });
                this.loading = false
              }
            })
            .catch(() => {
              this.loading = false
            })
        } else {
          console.log('error submit!!');
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}

.login-box {
  background: url("~@/assets/Login.png") no-repeat;
  background-size: 100% 100%;
  .title-container {
    text-align: center;
    .title {
      font-size: 30px;
      color: #fff;
      font-weight: bold;
    }
  }

  .el-form-item {
    margin-bottom: 0;
  }

  // .el-input--medium .el-input__inner {
  //   background: #262626 !important;
  //   border-color: #262626 !important;
  // }

  .el-form {
    position: relative;

    &:after {
      content: "";
      /* width: 60px;
          height: 60px;
          display: inline-block;
          position: absolute;
          background: #404040;
          top: 15px;
          right: -30px;
          border-radius: 100%; */
    }
  }
  .el-form-item__error {
    color: #ff4949;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: auto;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0 auto 40px auto;
      text-align: center;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}

.login-box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  background-color: #404040;
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;

  .rightBox {
    // width: 25%;
    width: 400px;
    position: absolute;
    left: 65%;
    top: 20%;
  }
  .card {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.562);
    border: none;
    .user {
      margin: 25px 10%;
      width: 80%;
    }
    .loginBtn {
      margin: 20px 10%;
      width: 80%;
    }
  }
  .logoTitle {
    position: absolute;
    top: 30px;
    right: 50px;
    width: 250px;
  }

  &-img {
    margin-right: 30px;
    position: absolute;
    left: -28%;

    img {
      width: 50%;
    }
  }

  &-form {
    width: 450px;
    position: relative;
    margin-left: 300px;

    .svg-container {
      padding: 6px 5px 6px 5px;
      color: #fff;
      vertical-align: middle;
      width: 80px;
      display: inline-block;
      text-align: right;
    }

    .el-input {
      display: inline-block;
      height: 47px;
      width: 72%;
    }
  }
  .login-box-form-btn {
    width: 60px;
    height: 60px;
    display: inline-block;
    position: absolute;
    background: #404040;
    top: 100px;
    right: -3px;
    border-radius: 100%;
    cursor: pointer;

    .btn {
      position: absolute;
      top: 7px;
      right: 7px;
      width: 45px;
      height: 45px;
      background: #409eff;
      z-index: 1;
      border-radius: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: center;
      align-items: center;
      padding: 0;
      font-size: 20px;
      border-color: #409eff;
    }
  }
  .password {
    color: #0070c0;
    margin-left: 85px;
  }
}
</style>
