<template>
  <div class="components-container">
    <aside>
      Based on <a class="link-type" href="https://github.com/rowanwins/vue-dropzone"> dropzone </a>.
      {{ $t('components.dropzoneTips') }}
    </aside>
    <div class="editor-container">
      <dropzone id="myVueDropzone" url="https://httpbin.org/post" @dropzone-removedFile="dropzoneR" @dropzone-success="dropzoneS" />
    </div>
  </div>
</template>

<script>
import Dropzone from '@/components/Dropzone'

export default {
  name: 'DropzoneDemo',
  components: { Dropzone },
  methods: {
    dropzoneS(file) {
      console.log(file);
      this.$message({ message: 'Upload success', type: 'success',
        duration: 5000 })
    },
    dropzoneR(file) {
      console.log(file);
      this.$message({ message: 'Delete success', type: 'success',
        duration: 5000 })
    }
  }
}
</script>

