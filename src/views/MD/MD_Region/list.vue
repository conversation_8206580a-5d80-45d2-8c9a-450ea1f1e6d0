<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Region.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Region.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Region.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Region.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />

      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column v-if="false" :label="$t('ui.MD.MD_Region.RegionID')" prop="RegionID" align="center" width="240">
        <template slot-scope="scope"><span>{{ scope.row.RegionID }}</span></template></el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Region.RegionCode')" prop="RegionCode" align="center" width="240"><template
        slot-scope="scope"
      ><span>{{ scope.row.RegionCode }}</span></template></el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Region.RegionName')" prop="RegionName" align="center" width="240"><template
        slot-scope="scope"
      ><span>{{ scope.row.RegionName }}</span></template></el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Region.WhsCode')" prop="WhsCode" align="center" width="240"><template
        slot-scope="scope"
      ><span>{{ scope.row.WhsCode }}</span></template></el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Region.WhsName')" prop="WhsName" align="center" width="240"><template
        slot-scope="scope"
      ><span>{{ scope.row.WhsName }}</span></template></el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="340"><template
        slot-scope="scope"
      ><span>{{ scope.row.Remark }}</span></template></el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CUser')" prop="CUser" align="center" width="240"> <template
        slot-scope="scope"
      > <span>{{ scope.row.CUser }}</span> </template> </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CTime')" prop="CTime" align="center" width="240"> <template
        slot-scope="scope"
      > <span>{{ scope.row.CTime }}</span> </template> </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">

        <el-form-item :label="$t('ui.MD.MD_Region.RegionCode')" prop="RegionCode">
          <el-input v-model="temp.RegionCode" :disabled="edit" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Region.RegionName')" prop="RegionName">
          <el-input v-model="temp.RegionName" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Region.WhsName1')" prop="WhsCode">
          <el-select v-model="temp.WhsCode" filterable @change="getWhs(temp.WhsCode)">
            <el-option v-for="item in WhsList" :key="item.WhsCode" :value="item.WhsCode" :label="item.WhsName" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves'
import adaptive from '../../../directive/el-table/adaptive'
import Pagination from '../../../components/Pagination/index'
import {
  exportToExcel
} from '@/utils/excel-export'
import {
  fetchList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  GetXZ_SAP
} from '../../../api/MD/MD_Region'
import {
  fetchList as fetchWarehouse
} from '../../../api/MD/MD_Warehouse'
// import permission from '../../../directive/permission/permission'
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Region',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      temp: {
        RegionID: '',
        RegionCode: '',
        RegionName: '',
        WhsName: '',
        WhsCode: ''
      },
      WhsList: [],
      edit: false,
      multipleSelection: [],
      rules: {
        RegionCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        RegionName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        WhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      }
    }
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1
    },
    deletable() {
      return this.multipleSelection.length === 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false
        })
    },
    handleFilter() { // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    resetFormData() {
      this.temp = {
        RegionCode: '',
        RegionName: '',
        WhsCode: '',
        WhsName: ''
      }
    },
    fetchWhs() {
      this.WhsList = [];
      // fetchWarehouse(this.listQuery)
      //   .then(response => {
      //     this.WhsList = response.Data.items
      //   })
      GetXZ_SAP().then(res => {
        const _this = this;
        if (res.Code === 2000) {
          res.Data.forEach(res => {
            _this.WhsList.push({
              WhsCode: res.LGORT,
              WhsName: res.LGOBE
            })
          })
        }
      })
    },
    getWhs(val) {
      // 选择框实际上是按照仓库编号选择
      // 该方法获取选择框选择的仓库名称
      const array = this.WhsList;
      for (let i = 0;i < array.length;i++) {
        const element = array[i].WhsCode;
        if (element === val) {
          this.temp.WhsName = array[i].WhsName;
          break
        }
      }
    },
    handleCreate() {
      this.resetFormData();
      this.fetchWhs();
      this.title = this.$t('Common.add');
      this.dialogVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleEdit() {
      this.resetFormData();
      this.fetchWhs();
      this.title = this.$t('Common.edit');
      this.dialogVisible = true;
      this.edit = true;
      Object.assign(this.temp, this.multipleSelection[0]);
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.startLoading();
          if (this.edit === false) {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }).then(() => {
        const arrRowsID = selectRows.map(v => v.RegionID);

        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList();
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      })
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => {
        exportToExcel(res.data, '仓库区域管理');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.handleFilter()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order != undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
