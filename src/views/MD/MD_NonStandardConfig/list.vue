<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.AcceptNo"
        placeholder="接受编号"
        style="width: 200px;"
        class="filter-item"
        size="small"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.SeriesModel"
        placeholder="系列型号"
        style="width: 200px;"
        class="filter-item"
        size="small"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.SAPPartNo"
        placeholder="SAP件号"
        style="width: 200px;"
        class="filter-item"
        size="small"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.SAPProductModel"
        placeholder="SAP产品型号"
        style="width: 200px;"
        class="filter-item"
        size="small"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.TractionRatio"
        placeholder="曳引比"
        style="width: 200px;"
        class="filter-item"
        size="small"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        style="margin-left: 10px"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <hr>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        添加
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        size="mini"
        :disabled="multipleSelection.length !== 1"
        @click="handleUpdate"
      >
        编辑
      </el-button>
      <el-button
        v-waves
        :disabled="multipleSelection.length === 0"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="mini"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
      <el-button
        v-waves
        :loading="downloadLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="mini"
        @click="handleDownload"
      >
        导出
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-upload2"
        size="mini"
        @click="handleImportTemplate"
      >
        导入模板
      </el-button>
      <el-upload
        ref="upload"
        class="upload-demo"
        style="display: inline-block; margin-left: 10px;"
        action=""
        :auto-upload="false"
        :on-change="handleImportExcel"
        :show-file-list="false"
        accept=".xlsx,.xls"
      >
        <el-button
          v-waves
          class="filter-item"
          type="warning"
          icon="el-icon-upload"
          size="mini"
        >
          导入数据
        </el-button>
      </el-upload>
    </div>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      size="mini"
      height="600px"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="接受编号" prop="AcceptNo" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.AcceptNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="系列型号" prop="SeriesModel" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.SeriesModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP件号" prop="SAPPartNo" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.SAPPartNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP产品型号" prop="SAPProductModel" align="center" width="180" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.SAPProductModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="曳引比" prop="TractionRatio" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.TractionRatio }}</span>
        </template>
      </el-table-column>
      <el-table-column label="极数" prop="PolesNo" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.PolesNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节径" prop="PitchDiameter" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.PitchDiameter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绳槽" prop="RopeGroove" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.RopeGroove }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定功率" prop="RatedPower" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.RatedPower }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定载重" prop="RatedLoad" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.RatedLoad }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定速度" prop="RatedSpeed" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.RatedSpeed }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="CTime" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.CTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="60%">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 100%; margin-left:50px;"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="评审单号" prop="ReviewNo">
              <el-input v-model="temp.ReviewNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主机型号" prop="HostModel">
              <el-input v-model="temp.HostModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="主机件号" prop="HostPartNo">
              <el-input v-model="temp.HostPartNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规格型号" prop="SpecificationModel">
              <el-input v-model="temp.SpecificationModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="曳引比" prop="TractionRatio">
              <el-input v-model="temp.TractionRatio" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="极对数" prop="PolePairsNum">
              <el-input v-model="temp.PolePairsNum" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="极数" prop="PolesNo">
              <el-input v-model="temp.PolesNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="防护等级" prop="ProtectionGrade">
              <el-input v-model="temp.ProtectionGrade" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启动次数" prop="StartNo">
              <el-input v-model="temp.StartNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="节径" prop="PitchDiameter">
              <el-input v-model="temp.PitchDiameter" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="绳槽" prop="RopeGroove">
              <el-input v-model="temp.RopeGroove" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="槽距" prop="GrooveSpacing">
              <el-input v-model="temp.GrooveSpacing" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="铭牌重量" prop="NameplateWeight">
              <el-input v-model="temp.NameplateWeight" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定功率" prop="RatedPower">
              <el-input v-model="temp.RatedPower" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="额定载重" prop="RatedLoad">
              <el-input v-model="temp.RatedLoad" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定速度" prop="RatedSpeed">
              <el-input v-model="temp.RatedSpeed" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="额定转速" prop="RatedRotationSpeed">
              <el-input v-model="temp.RatedRotationSpeed" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定转矩" prop="RatedTorque">
              <el-input v-model="temp.RatedTorque" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="额定电压" prop="RatedVoltage">
              <el-input v-model="temp.RatedVoltage" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定电流" prop="RatedCurrent">
              <el-input v-model="temp.RatedCurrent" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="额定频率" prop="RatedFrequency">
              <el-input v-model="temp.RatedFrequency" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="效率" prop="Efficiency">
              <el-input v-model="temp.Efficiency" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="定子电阻" prop="StatorResistance">
              <el-input v-model="temp.StatorResistance" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="定子电抗" prop="StatorReactance">
              <el-input v-model="temp.StatorReactance" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="反电动势" prop="BackElectromotiveForce">
              <el-input v-model="temp.BackElectromotiveForce" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电感" prop="Inductance">
              <el-input v-model="temp.Inductance" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="绕组相电阻" prop="WindingPhaseResistance">
              <el-input v-model="temp.WindingPhaseResistance" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装箱尺寸" prop="PackingDimensions">
              <el-input v-model="temp.PackingDimensions" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="装箱净重" prop="PackingNetWeight">
              <el-input v-model="temp.PackingNetWeight" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装箱毛重" prop="PackingGrossWeight">
              <el-input v-model="temp.PackingGrossWeight" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchPageList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  exportExcelModel,
  importExcelData
} from '@/api/MD/MD_NonStandardConfig'
import { exportToExcel } from '@/utils/excel-export'
import { parseExcelFile } from '@/utils/excel-import'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'
import permission from '@/directive/permission/index.js'

export default {
  name: 'MD.MD_NonStandardConfig',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        AcceptNo: '',
        SeriesModel: '',
        SAPPartNo: '',
        SAPProductModel: '',
        TractionRatio: ''
      },
      temp: {
        Id: '',
        AcceptNo: '',
        SeriesModel: '',
        SAPPartNo: '',
        SAPProductModel: '',
        TractionRatio: '',
        PolePairsNum: '',
        PolesNo: '',
        ProtectionGrade: '',
        StartNo: '',
        PitchDiameter: '',
        RopeGroove: '',
        GrooveDistance: '',
        HostWeight: '',
        RatedPower: '',
        RatedLoad: '',
        RatedSpeed: '',
        RatedRotationSpeed: '',
        RatedTorque: '',
        RatedVoltage: '',
        RatedCurrent: '',
        RatedFrequency: '',
        Efficiency: '',
        StatorResistance: '',
        StatorReactance: '',
        BackElectromotiveForce: '',
        Inductance: '',
        PhaseResistance: '',
        PackingSize: '',
        PackingNetWeight: '',
        PackingGrossWeight: '',
        Remark: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '创建'
      },
      downloadLoading: false,
      multipleSelection: [],
      rules: {
        AcceptNo: [{ required: true, message: '接受编号不能为空', trigger: 'blur' }],
        SeriesModel: [{ required: true, message: '系列型号不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchPageList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          this.list = response.Data.items
          this.total = response.Data.total
        } else {
          this.$notify({
            title: '错误',
            message: response.Message,
            type: 'error',
            duration: 2000
          })
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    resetTemp() {
      this.temp = {
        Id: '',
        ReviewNo: '',
        HostModel: '',
        HostPartNo: '',
        SpecificationModel: '',
        TractionRatio: '',
        PolePairsNum: '',
        PolesNo: '',
        ProtectionGrade: '',
        StartNo: '',
        PitchDiameter: '',
        RopeGroove: '',
        GrooveSpacing: '',
        NameplateWeight: '',
        RatedPower: '',
        RatedLoad: '',
        RatedSpeed: '',
        RatedRotationSpeed: '',
        RatedTorque: '',
        RatedVoltage: '',
        RatedCurrent: '',
        RatedFrequency: '',
        Efficiency: '',
        StatorResistance: '',
        StatorReactance: '',
        BackElectromotiveForce: '',
        Inductance: '',
        WindingPhaseResistance: '',
        PackingDimensions: '',
        PackingNetWeight: '',
        PackingGrossWeight: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          add(this.temp).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '创建成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleUpdate(row) {
      // 如果没有传入row参数，则从选中的行中获取
      const selectedRow = this.multipleSelection[0]
      if (!selectedRow) {
        this.$message.warning('请选择要编辑的数据')
        return
      }
      this.temp = Object.assign({}, selectedRow)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          update(this.temp).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleDelete(row, index) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchDelete([row.Id]).then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message,
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    handleBatchDelete() {
      const selectRows = this.multipleSelection
      if (selectRows.length === 0) {
        this.$message.warning('请选择要删除的记录')
        return
      }
      this.$confirm('此操作将永久删除选中的记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const arrRowsID = selectRows.map(v => v.Id)
        batchDelete(arrRowsID).then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '批量删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message,
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    handleDownload() {
      this.downloadLoading = true
      exportExcelFile(this.listQuery).then(response => {
        exportToExcel(response, '非标参数配置')
        this.downloadLoading = false
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    handleImportTemplate() {
      exportExcelModel().then(response => {
        exportToExcel(response, '非标参数配置-导入模板')
      })
    },
    handleImportExcel(file) {
      const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }

      parseExcelFile(file.raw).then(data => {
        if (data && data.length > 0) {
          // 定义字段映射关系，Excel表头到实体属性的映射
          const fieldMapping = {
            '接受编号': 'AcceptNo',
            '系列型号': 'SeriesModel',
            'SAP件号': 'SAPPartNo',
            'SAP产品型号': 'SAPProductModel',
            '曳引比': 'TractionRatio',
            '极对数': 'PolePairsNum',
            '极数': 'PolesNo',
            '防护等级': 'ProtectionGrade',
            '启动次数': 'StartNo',
            '节径': 'PitchDiameter',
            '绳槽': 'RopeGroove',
            '槽距': 'GrooveDistance',
            '主机重量': 'HostWeight',
            '额定功率': 'RatedPower',
            '额定载重': 'RatedLoad',
            '额定速度': 'RatedSpeed',
            '额定转速': 'RatedRotationSpeed',
            '额定转矩': 'RatedTorque',
            '额定电压': 'RatedVoltage',
            '额定电流': 'RatedCurrent',
            '额定频率': 'RatedFrequency',
            '效率': 'Efficiency',
            '定子电阻': 'StatorResistance',
            '定子电抗': 'StatorReactance',
            '反电动势': 'BackElectromotiveForce',
            '电感': 'Inductance',
            '相电阻': 'PhaseResistance',
            '装箱尺寸': 'PackingSize',
            '装箱净重': 'PackingNetWeight',
            '装箱毛重': 'PackingGrossWeight',
            '备注': 'Remark'
          }

          // 将解析后的数据映射到实体
          const mappedData = data.map(row => {
            const entity = {}

            // 遍历字段映射关系，将Excel数据映射到实体属性
            Object.keys(fieldMapping).forEach(excelHeader => {
              const entityProperty = fieldMapping[excelHeader]
              if (row[excelHeader] !== undefined && row[excelHeader] !== null) {
                entity[entityProperty] = row[excelHeader]
              }
            })

            return entity
          }).filter(entity => Object.keys(entity).length > 0) // 过滤掉空行

          console.log('映射后的数据:', mappedData) // 调试信息
          importExcelData(mappedData).then(response => {
            if (response.Code === 2000) {
              this.$notify({
                title: '成功',
                message: '导入成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      }).catch(error => {
        this.$message.error('文件解析失败: ' + error.message)
      })
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop && order) {
        this.listQuery.sort = prop + ' ' + (order === 'ascending' ? 'asc' : 'desc')
      } else {
        this.listQuery.sort = ''
      }
      this.handleFilter()
    },
    // 处理JSON数据导入
    handleJsonImport(jsonData) {
      const fieldMapping = {
        '接受编号': 'AcceptNo',
        '系列型号': 'SeriesModel',
        'SAP件号': 'SAPPartNo',
        'SAP产品型号': 'SAPProductModel',
        '曳引比': 'TractionRatio',
        '极对数': 'PolePairsNum',
        '极数': 'PolesNo',
        '防护等级': 'ProtectionGrade',
        '启动次数': 'StartNo',
        '节径': 'PitchDiameter',
        '绳槽': 'RopeGroove',
        '槽距': 'GrooveDistance',
        '主机重量': 'HostWeight',
        '额定功率': 'RatedPower',
        '额定载重': 'RatedLoad',
        '额定速度': 'RatedSpeed',
        '额定转速': 'RatedRotationSpeed',
        '额定转矩': 'RatedTorque',
        '额定电压': 'RatedVoltage',
        '额定电流': 'RatedCurrent',
        '额定频率': 'RatedFrequency',
        '效率': 'Efficiency',
        '定子电阻': 'StatorResistance',
        '定子电抗': 'StatorReactance',
        '反电动势': 'BackElectromotiveForce',
        '电感': 'Inductance',
        '相电阻': 'PhaseResistance',
        '装箱尺寸': 'PackingSize',
        '装箱净重': 'PackingNetWeight',
        '装箱毛重': 'PackingGrossWeight',
        '备注': 'Remark'
      }

      const mappedData = jsonData.map(row => {
        const entity = {}
        Object.keys(fieldMapping).forEach(excelHeader => {
          const entityProperty = fieldMapping[excelHeader]
          if (row[excelHeader] !== undefined && row[excelHeader] !== null) {
            entity[entityProperty] = row[excelHeader]
          }
        })
        return entity
      }).filter(entity => Object.keys(entity).length > 0)

      // 发送到后端
      importExcelData(mappedData).then(response => {
        if (response.Code === 2000) {
          this.$notify({
            title: '成功',
            message: '导入成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        } else {
          this.$notify({
            title: '错误',
            message: response.Message,
            type: 'error',
            duration: 2000
          })
        }
      }).catch(error => {
        this.$notify({
          title: '错误',
          message: '导入失败: ' + error.message,
          type: 'error',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style scoped>
.upload-demo {
  display: inline-block;
}
</style>
