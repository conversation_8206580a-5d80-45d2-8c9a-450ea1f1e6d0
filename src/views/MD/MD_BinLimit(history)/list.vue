<template>
  <div class="app-container">
    <el-container>
      <el-aside style="width:250px;background:#fff;border-right:solid 1px #ddd; height: 620px">
        <el-menu style="border:0px" default-active="100" @select="handleSelectModule">
          <el-submenu index="main">
            <template slot="title">
              <i class="el-icon-menu" />
              <span>{{ $t("ui.MD.MD_BinLimit.functionModule") }}</span>
            </template>
            <el-submenu v-for="subItem in pdaModule" :key="subItem.id" :index="subItem.id">
              <span slot="title">{{ subItem.label }}</span>
              <el-menu-item
                v-for="childItem in subItem.children"
                :key="childItem.id"
                :index="childItem.id"
              >
                <!-- <i class="el-icon-menu"></i> -->
                <span slot="title">{{ childItem.label }}</span>
              </el-menu-item>
            </el-submenu>
          </el-submenu>
        </el-menu>
      </el-aside>

      <el-container style="padding-left:20px">
        <el-main>
          <el-button type="primary" @click="handleSaveBinLimitSetting">
            {{ $t("Common.save") }}
          </el-button>
          <el-input
            v-model="listQuery.keyword"
            class="filter-item"
            :placeholder="$t('ui.MD.MD_BinLimit.keyword')"
            style="width: 180px"
            clearable
            @keydown.enter.native="getBinList"
          />
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getBinList" />

          <el-table
            ref="multipleTable"
            v-loading="listLoading"
            :data="list"
            :row-key="bindRowKey"
            border
            :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
            highlight-current-row
            style="width: 100%; margin-top: 5px"
            height="470"
            @sort-change="sortChange"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" :reserve-selection="true" width="40" fixed />
            <el-table-column
              v-if="false"
              :label="$t('ui.MD.MD_BinLocation.BinID')"
              prop="BinID"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.BinID }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.BinLocationCode')"
              prop="BinLocationCode"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.BinLocationCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.BinLocationName')"
              prop="BinLocationName"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.BinLocationName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.RegionCode')"
              prop="RegionCode"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.RegionCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.RegionName')"
              prop="RegionName"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.RegionName }}</span>
              </template>
            </el-table-column>
            <!--<el-table-column
              v-if="false"
              :label="$t('ui.MD.MD_BinLocation.WhsCode')"
              prop="WhsCode"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.WhsCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="false"
              :label="$t('ui.MD.MD_BinLocation.WhsName')"
              prop="WhsName"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.WhsName }}</span>
              </template>
            </el-table-column>-->

            <el-table-column
              v-if="true"
              :label="$t('Common.Remark')"
              prop="Remark"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.Remark }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="true"
              :label="$t('Common.CUser')"
              prop="CUser"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.CUser }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="true"
              :label="$t('Common.CTime')"
              prop="CTime"

              align="center"
              width="160"
              :formatter="formatDateTime"
            />
          </el-table>
          <Pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.PageNumber"
            :limit.sync="listQuery.PageSize"
            @pagination="getBinList"
          />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { convertToSelectTree } from '../../../utils';

import { fetchList as fetchBinList } from '../../../api/MD/MD_BinLocation';
import {
  fetchModule,
  submitBinLimit,
  fetchBinLocation
} from '../../../api/MD/MD_BinLimit';
import { GetResourcesByAppID } from '../../../api/Sys/Sys_Resource';
import { formatDate, formatDateTime } from '../../../utils'; // 列表内容格式化

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_BinLimit',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: ''
      },
      pdaModule: '',
      selectedModule: '',
      multipleSelection: []
    };
  },
  computed: {},
  created() {
    this.initPdaAppMenus();
    this.getBinList();
  },
  methods: {
    formatDate,
    formatDateTime,
    bindRowKey(row) {
      return row.BinID;
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getBinList();
    },
    getBinList() {
      this.listLoading = true;
      fetchBinList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    initPdaAppMenus() {
      const pdaAppID = 'M001';
      GetResourcesByAppID({ appid: pdaAppID }).then(res => {
        const resDataMap = res.Data.map(item => {
          item.ResourceTitle = this.$t('route.' + item.ResourceTitle); // 翻译菜单名称
          return item;
        });
        this.pdaModule = convertToSelectTree(
          resDataMap,
          'ResourceID',
          'FathResourceID',
          'ResourceTitle'
        );
      });
    },
    handleSelectModule(index, path) {
      this.$refs.multipleTable.clearSelection();
      this.selectedModule = index;
      fetchModule({ menuCode: index }).then(res => {
        if (res.Data.length) {
          const tempBinList = res.Data;
          const listId = tempBinList.map(v => v.BinLocationCode);
          fetchBinLocation({ ids: listId }).then(res => {
            const havenSelection = res.Data;
            this.toggleTableSelection(havenSelection);
            this.getBinList();
          });
        }
      });
    },
    toggleTableSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, true);
        });
      }
    },
    handleSaveBinLimitSetting() {
      // 保存库位限制设置
      if (!this.selectedModule) {
        this.showNotify('error', 'ui.MD.MD_BinLimit.checkModule');
        return;
      }
      const selectRows = this.multipleSelection;
      const arrRowsID = selectRows.map(v => v.BinID);
      if (arrRowsID.length == 0) {
        this.showNotify('error', 'Common.noSelection');
        return;
      }
      submitBinLimit({ id: this.selectedModule, ids: arrRowsID }).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', 'Common.operationSuccess');
        } else {
          this.showNotify('error', res.Message);
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
