<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">

      <el-input
        v-model="listQuery.SeriesModel"
        size="small"
        placeholder="请输入系列型号"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.EnergyEfficiencyModel"
        size="small"
        placeholder="请输入能效备案型号"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ForvordaPartNo"
        size="small"
        placeholder="请输入Forvorda件号"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.SAPPartNo"
        size="small"
        placeholder="请输入SAP件号"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.SAPProductModel"
        size="small"
        placeholder="请输入SAP产品型号"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.Model"
        size="small"
        placeholder="请输入型号"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.TractionRatio"
        size="small"
        placeholder="请输入曳引比"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        size="mini"
        class="filter-item"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="mini"
        @click="handleCreate"
      >
        新增
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        size="mini"
        :disabled="multipleSelection.length !== 1"
        @click="handleEdit"
      >
        编辑
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        size="mini"
        :disabled="multipleSelection.length === 0"
        @click="handleDelete"
      >
        删除
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        size="mini"
        @click="handleExport"
      >
        导出
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        size="mini"
        @click="handleExportTemplate"
      >
        导出模板
      </el-button>
      <el-upload
        class="filter-item"
        :action="'#'"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button v-waves type="primary" size="mini">导入</el-button>
      </el-upload>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%"
      height="calc(100vh - 320px)"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="Forvorda产品型号" prop="ForvordaProductModel" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ForvordaProductModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="系列型号" prop="SeriesModel" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SeriesModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="能效备案型号" prop="EnergyEfficiencyModel" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.EnergyEfficiencyModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Forvorda件号" prop="ForvordaPartNo" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ForvordaPartNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP件号" prop="SAPPartNo" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SAPPartNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SAP产品型号" prop="SAPProductModel" align="center" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.SAPProductModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="曳引比" prop="TractionRatio" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TractionRatio }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定载重" prop="RatedLoad" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedLoad }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定速度" prop="RatedSpeed" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedSpeed }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定功率" prop="RatedPower" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedPower }}</span>
        </template>
      </el-table-column>
      <el-table-column label="额定电压" prop="RatedVoltage" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RatedVoltage }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节径" prop="PitchDiameter" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PitchDiameter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绳槽" prop="RopeGroove" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.RopeGroove }}</span>
        </template>
      </el-table-column>
      <el-table-column label="槽距" prop="GrooveDistance" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.GrooveDistance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="防护等级" prop="ProtectionLevel" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProtectionLevel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="CTime" align="center" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.CTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="CUser" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="800px">
      <el-form ref="form" :model="temp" :rules="rules" label-position="right" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="系列型号" prop="SeriesModel">
              <el-input v-model="temp.SeriesModel" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="能效备案型号" prop="EnergyEfficiencyModel">
              <el-input v-model="temp.EnergyEfficiencyModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="曳引比" prop="TractionRatio">
              <el-input v-model="temp.TractionRatio" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="额定载重" prop="RatedLoad">
              <el-input v-model="temp.RatedLoad" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Forvorda件号" prop="ForvordaPartNo">
              <el-input v-model="temp.ForvordaPartNo" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Forvorda产品型号" prop="ForvordaProductModel">
              <el-input v-model="temp.ForvordaProductModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SAP件号" prop="SAPPartNo">
              <el-input v-model="temp.SAPPartNo" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="SAP产品型号" prop="SAPProductModel">
              <el-input v-model="temp.SAPProductModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="额定速度" prop="RatedSpeed">
              <el-input v-model="temp.RatedSpeed" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="额定转速" prop="RatedRotationSpeed">
              <el-input v-model="temp.RatedRotationSpeed" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="额定转矩" prop="RatedTorque">
              <el-input v-model="temp.RatedTorque" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="许用径向载荷" prop="AllowableRadialLoad">
              <el-input v-model="temp.AllowableRadialLoad" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主机重量" prop="HostWeight">
              <el-input v-model="temp.HostWeight" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="额定功率" prop="RatedPower">
              <el-input v-model="temp.RatedPower" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="额定电压" prop="RatedVoltage">
              <el-input v-model="temp.RatedVoltage" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="额定电流" prop="RatedCurrent">
              <el-input v-model="temp.RatedCurrent" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="相电阻" prop="PhaseResistance">
              <el-input v-model="temp.PhaseResistance" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电感" prop="Inductance">
              <el-input v-model="temp.Inductance" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="极数" prop="PoleNumber">
              <el-input v-model="temp.PoleNumber" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="额定频率" prop="RatedFrequency">
              <el-input v-model="temp.RatedFrequency" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="效率" prop="Efficiency">
              <el-input v-model="temp.Efficiency" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="防护等级" prop="ProtectionLevel">
              <el-input v-model="temp.ProtectionLevel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="启动次数" prop="StartupTimes">
              <el-input v-model="temp.StartupTimes" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="极对数" prop="PolePairs">
              <el-input v-model="temp.PolePairs" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="定子电阻" prop="StatorResistance">
              <el-input v-model="temp.StatorResistance" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定子电抗" prop="StatorReactance">
              <el-input v-model="temp.StatorReactance" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="反电动势" prop="BackEMF">
              <el-input v-model="temp.BackEMF" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="Model">
              <el-input v-model="temp.Model" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="电流" prop="Current">
              <el-input v-model="temp.Current" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电压" prop="Voltage">
              <el-input v-model="temp.Voltage" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="制动力矩" prop="BrakingTorque">
              <el-input v-model="temp.BrakingTorque" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节径" prop="PitchDiameter">
              <el-input v-model="temp.PitchDiameter" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="绳槽" prop="RopeGroove">
              <el-input v-model="temp.RopeGroove" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="β角" prop="BetaAngle">
              <el-input v-model="temp.BetaAngle" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="γ角" prop="GammaAngle">
              <el-input v-model="temp.GammaAngle" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="槽距" prop="GrooveDistance">
              <el-input v-model="temp.GrooveDistance" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="编码器" prop="Encoder">
              <el-input v-model="temp.Encoder" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="盘车装置/手动松闸扳手" prop="ManualDevice">
              <el-input v-model="temp.ManualDevice" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="远程手动松闸装置" prop="RemoteManualDevice">
              <el-input v-model="temp.RemoteManualDevice" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装箱尺寸" prop="PackingSize">
              <el-input v-model="temp.PackingSize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="装箱毛重" prop="PackingGrossWeight">
              <el-input v-model="temp.PackingGrossWeight" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装箱净重" prop="PackingNetWeight">
              <el-input v-model="temp.PackingNetWeight" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus === 'create'" type="primary" @click="handleCreateConfirm">确定</el-button>
        <el-button v-else type="primary" @click="handleEditConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import adaptive from '@/directive/el-table/adaptive'
import Pagination from '@/components/Pagination/index'
import { formatDateTime } from '@/utils'
import {
  fetchList,
  add,
  update,
  batchDelete,
  deleteSingle,
  exportExcelFile,
  exportExcelTemplate,
  importExcelData
} from '@/api/MD/MD_BasicSpecification'
import { exportToExcel } from '@/utils/excel-export'
import { parseExcelFile } from '@/utils/excel-import'

export default {
  name: 'MD.MD_BasicSpecification',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      isProcessing: false,
      multipleSelection: [],
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        SeriesModel: '',
        EnergyEfficiencyModel: '',
        ForvordaPartNo: '',
        SAPPartNo: '',
        SAPProductModel: '',
        Model: '',
        TractionRatio: '',
        sort: '+Id'
      },
      temp: {
        Id: '',
        SeriesModel: '',
        EnergyEfficiencyModel: '',
        ForvordaPartNo: '',
        ForvordaProductModel: '',
        SAPPartNo: '',
        SAPProductModel: '',
        TractionRatio: '',
        RatedLoad: '',
        RatedSpeed: '',
        RatedRotationSpeed: '',
        RatedTorque: '',
        AllowableRadialLoad: '',
        HostWeight: '',
        RatedPower: '',
        RatedVoltage: '',
        RatedCurrent: '',
        PhaseResistance: '',
        Inductance: '',
        PoleNumber: '',
        RatedFrequency: '',
        Efficiency: '',
        ProtectionLevel: '',
        StartupTimes: '',
        PolePairs: '',
        StatorResistance: '',
        StatorReactance: '',
        BackEMF: '',
        Model: '',
        Current: '',
        Voltage: '',
        BrakingTorque: '',
        PitchDiameter: '',
        RopeGroove: '',
        BetaAngle: '',
        GammaAngle: '',
        GrooveDistance: '',
        Encoder: '',
        ManualDevice: '',
        RemoteManualDevice: '',
        PackingSize: '',
        PackingGrossWeight: '',
        PackingNetWeight: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑基本规格参数',
        create: '新增基本规格参数'
      },
      rules: {
        SeriesModel: [{ required: true, message: '请输入系列型号', trigger: 'blur' }],
        EnergyEfficiencyModel: [{ required: true, message: '请输入能效备案型号', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatDateTime,
    getList() {
      this.listLoading = true
      this.isProcessing = true
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items
        this.total = res.Data.total
        this.listLoading = false
        this.isProcessing = false
      }).catch(error => {
        console.error('获取列表失败:', error)
        this.listLoading = false
        this.isProcessing = false
      })
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === null || order === null) return
      this.listQuery.sort = (order === 'ascending' ? '+' : '-') + prop
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        Id: '',
        SeriesModel: '',
        EnergyEfficiencyModel: '',
        ForvordaPartNo: '',
        ForvordaProductModel: '',
        SAPPartNo: '',
        SAPProductModel: '',
        TractionRatio: '',
        RatedLoad: '',
        RatedSpeed: '',
        RatedRotationSpeed: '',
        RatedTorque: '',
        AllowableRadialLoad: '',
        HostWeight: '',
        RatedPower: '',
        RatedVoltage: '',
        RatedCurrent: '',
        PhaseResistance: '',
        Inductance: '',
        PoleNumber: '',
        RatedFrequency: '',
        Efficiency: '',
        ProtectionLevel: '',
        StartupTimes: '',
        PolePairs: '',
        StatorResistance: '',
        StatorReactance: '',
        BackEMF: '',
        Model: '',
        Current: '',
        Voltage: '',
        BrakingTorque: '',
        PitchDiameter: '',
        RopeGroove: '',
        BetaAngle: '',
        GammaAngle: '',
        GrooveDistance: '',
        Encoder: '',
        ManualDevice: '',
        RemoteManualDevice: '',
        PackingSize: '',
        PackingGrossWeight: '',
        PackingNetWeight: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleEdit(row) {
      this.resetTemp()
      // 如果没有传入row参数，则从选中的行中获取
      const selectedRow = this.multipleSelection[0]
      if (!selectedRow) {
        this.$message.warning('请选择要编辑的数据')
        return
      }
      this.temp = Object.assign({}, selectedRow)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleSingleDelete(row) {
      this.$confirm('确定要删除这条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSingle(row.Id).then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message,
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    handleDelete() {
      this.$confirm('确定要删除选中的数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.Id)
        batchDelete(ids).then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message,
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    handleCreateConfirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          add(this.temp).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleEditConfirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          update(tempData).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '编辑成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleExport() {
      this.isProcessing = true
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res, '基本规格参数')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export error:', error)
        this.isProcessing = false
      })
    },
    handleExportTemplate() {
      this.isProcessing = true
      exportExcelTemplate().then(res => {
        exportToExcel(res, '基本规格参数模板')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export template error:', error)
        this.isProcessing = false
      })
    },
    beforeUpload(file) {
      this.isProcessing = true

      // 定义字段映射关系，Excel表头到实体属性的映射
      const fieldMapping = {
        '系列型号': 'SeriesModel',
        '能效备案型号': 'EnergyEfficiencyModel',
        'Forvorda 件号': 'ForvordaPartNo',
        'Forvorda 产品型号': 'ForvordaProductModel',
        'SAP件号': 'SAPPartNo',
        'SAP产品型号': 'SAPProductModel',
        '曳引比': 'TractionRatio',
        '额定载重': 'RatedLoad',
        '额定速度': 'RatedSpeed',
        '额定转速': 'RatedRotationSpeed',
        '额定转矩': 'RatedTorque',
        '许用径向载荷': 'AllowableRadialLoad',
        '主机重量': 'HostWeight',
        '额定功率': 'RatedPower',
        '额定电压': 'RatedVoltage',
        '额定电流': 'RatedCurrent',
        '相电阻': 'PhaseResistance',
        '电感': 'Inductance',
        '极数': 'PoleNumber',
        '额定频率': 'RatedFrequency',
        '效率': 'Efficiency',
        '防护等级': 'ProtectionLevel',
        '启动次数': 'StartupTimes',
        '极对数': 'PolePairs',
        '定子电阻': 'StatorResistance',
        '定子电抗': 'StatorReactance',
        '反电动势': 'BackEMF',
        '型号': 'Model',
        '电流': 'Current',
        '电压': 'Voltage',
        '制动力矩': 'BrakingTorque',
        '节径': 'PitchDiameter',
        '绳槽': 'RopeGroove',
        'β角': 'BetaAngle',
        'γ角': 'GammaAngle',
        '槽距': 'GrooveDistance',
        '编码器': 'Encoder',
        '盘车装置/手动松闸扳手': 'ManualDevice',
        '远程手动松闸装置': 'RemoteManualDevice',
        '装箱尺寸': 'PackingSize',
        '装箱毛重': 'PackingGrossWeight',
        '装箱净重': 'PackingNetWeight'
      }

      // 解析Excel文件，表头在第一行，所以索引是0
      parseExcelFile(file, 0)
        .then(data => {
          // 将解析后的数据映射到实体
          const entities = data.map(row => {
            const entity = {}

            // 遍历字段映射关系，将Excel数据映射到实体属性
            Object.keys(fieldMapping).forEach(excelHeader => {
              const entityProperty = fieldMapping[excelHeader]
              if (row[excelHeader] !== undefined && row[excelHeader] !== null) {
                entity[entityProperty] = row[excelHeader]
              }
            })

            return entity
          }).filter(entity => Object.keys(entity).length > 0) // 过滤掉空行

          // 发送解析后的数据到后端
          return importExcelData(entities)
        })
        .then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '导入成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '导入失败',
              type: 'error',
              duration: 2000
            })
          }
          this.isProcessing = false
        })
        .catch(error => {
          console.error('导入失败:', error)
          this.$notify({
            title: '错误',
            message: '解析Excel文件失败，请检查文件格式',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        })

      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    padding-bottom: 10px;

    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
      margin-right: 10px;
    }
  }

  .text-muted {
    color: #999;
  }
}

</style>
