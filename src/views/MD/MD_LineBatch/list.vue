<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_FreightMileage.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_FreightMileage.Edit' }"
        class="filter-item"
        type="warning"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_FreightMileage.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column label="生产线名称" prop="LineName" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="开始时间" prop="StartTime" width="120" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ formatTime(scope.row.StartTime) }}
        </template>
      </el-table-column>
      <el-table-column label="小时批次数" prop="BatchNo" align="center" width="120" />
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible">
      <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item label="生产线名称" prop="LineName">
          <el-input v-model="temp.LineName" />
        </el-form-item>
        <el-form-item label="开始时间" prop="StartTime">
          <el-time-picker v-model="temp.StartTime" arrow-control :clearable="false" placeholder="开始时间" format="HH:mm" />
        </el-form-item>
        <el-form-item label="小时批次数" prop="BatchNo">
          <el-input v-model="temp.BatchNo" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button v-waves type="primary" @click="handleEditConfirm">{{ $t('Common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  add,
  batchDelete,
  update
} from '@/api/MD/MD_LineBatch';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页

import InfiniteLoading from 'vue-infinite-loading';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Item',
  components: {
    Pagination,
    InfiniteLoading
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        keyword: '',
        PageSize: 10,
        PageNumber: 1,
        StockManWay: ''
      },
      StockManWayOptions: [],
      isProcessing: false,
      regionOptions: [],
      multipleSelection: [],
      dialogFormVisible: false,
      rules: {
        LineName: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'blur'
          }
        ],
        StartTime: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ],
        BatchNo: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'blur'
          }
        ]
      },
      temp: {
        Id: undefined,
        LineName: undefined,
        BatchNo: undefined,
        StartTime: undefined
      },
      firstID: '',
      dialogTitle: '', // 添加对话框标题
      isEdit: false // 添加编辑状态标识
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.handleFilter();
    this.GetDictionary()
  },
  methods: {
    formatTime(minutes) {
      if (!minutes && minutes !== 0) return '';
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    },
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          if (response.Code === 2000) {
            this.list = response.Data.items;
            this.total = response.Data.total;
          } else {
            this.showNotify('error', response.Message);
          }
          this.listLoading = false;
        })
        .catch(err => {
          console.log(err);
          this.listLoading = false;
        });
    },
    handleCreate() {
      this.isEdit = false;
      this.dialogTitle = '添加';
      this.ClearForm();
      this.dialogFormVisible = true;
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        Id: undefined,
        LineName: undefined,
        BatchNo: undefined,
        StartTime: undefined
      };
    },
    handleFilter() {
      this.list = [];
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleEdit() {
      this.isEdit = true;
      this.dialogTitle = '编辑';
      this.resetTemp();
      const row = this.multipleSelection[0];
      // 将分钟数转换为时间对象
      if (row.StartTime) {
        const hours = Math.floor(row.StartTime / 60);
        const minutes = row.StartTime % 60;
        const date = new Date();
        date.setHours(hours);
        date.setMinutes(minutes);
        date.setSeconds(0);
        date.setMilliseconds(0);
        this.temp.StartTime = date;
      }
      // 复制其他字段
      this.temp.LineName = row.LineName;
      this.temp.BatchNo = row.BatchNo;
      this.temp.Id = row.Id; // 确保编辑时传递ID
      this.dialogFormVisible = true;
    },
    handleDelete() {
      this.$confirm('是否确认要删除选中行数据？', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        // 删除逻辑处理
        batchDelete(this.multipleSelection.map(t => t.Id)).then(response => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', response.Message);
          }
        });
      });
    },
    handleEditConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 创建一个临时对象来存储要提交的数据
          const submitData = { ...this.temp };
          // 转换为从0点到选中时间点的分钟数（整型）
          if (submitData.StartTime) {
            const time = submitData.StartTime;
            const totalMinutes = time.getHours() * 60 + time.getMinutes();
            submitData.StartTime = totalMinutes;
          }
          const request = this.isEdit ? update(submitData) : add(submitData);
          request.then(response => {
            if (response.Code === 2000) {
              this.showNotify('success', this.isEdit ? 'Common.updateSuccess' : 'Common.addSuccess');
              this.handleFilter();
              this.dialogFormVisible = false;
            } else {
              this.showNotify('error', response.Message);
            }
          });
        }
      });
    },
    ClearForm() {
      this.temp.SapCode = ''
      this.temp.LineName = ''
      this.temp.StartTime = null // 设置默认时间为空
      this.temp.BatchNo = ''
    }
  }
};
</script>
