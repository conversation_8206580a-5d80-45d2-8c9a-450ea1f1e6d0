<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.EmployeeName"
        size="small"
        class="filter-item"
        placeholder="员工姓名"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ProductionLineCode"
        size="small"
        class="filter-item"
        placeholder="线体编码"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ItemName"
        size="small"
        class="filter-item"
        placeholder="物料描述"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProductionDistributionSettingController.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t('Common.add') }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProductionDistributionSettingController.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleEdit"
      >
        {{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProductionDistributionSettingController.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProductionDistributionSettingController.Import' }"
        class="filter-item"
        type="primary"
        icon="el-icon-upload"
        size="small"
        @click="handleImport"
      >导入模板</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProductionDistributionSettingController.DownLoad' }"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="small"
        @click="handleExportModel"
      >下载模板</el-button>
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="员工号" prop="EmployeeNumber" align="center" show-overflow-tooltip />
      <el-table-column label="员工姓名" prop="EmployeeName" align="center" show-overflow-tooltip />
      <el-table-column label="线体编码" prop="ProductionLineCode" align="center" show-overflow-tooltip />
      <el-table-column label="线体描述" prop="ProductionLineDes" align="center" show-overflow-tooltip />
      <el-table-column label="物料描述" prop="ItemName" align="center" show-overflow-tooltip />
      <el-table-column label="物料组编码" prop="MaterialGroupCode" align="center" show-overflow-tooltip />
      <el-table-column label="物料组描述" prop="MaterialGroupDes" align="center" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip />

    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">

        <el-form-item label="员工号" prop="EmployeeNumber">
          <el-input v-model="temp.EmployeeNumber" />
        </el-form-item>
        <el-form-item label="员工姓名" prop="EmployeeName">
          <el-input v-model="temp.EmployeeName" />
        </el-form-item>
        <el-form-item label="线体" prop="ProductionLineCode">
          <el-select
            v-model="temp.ProductionLineCode"
            filterable
            style="width: 100%;"
            @change="getProductionLine(temp.ProductionLineCode)"
          >
            <el-option
              v-for="item in ProductionLine"
              :key="item.ProductionLineCode"
              :value="item.ProductionLineCode"
              :label="item.ProductionLineCode+'--'+item.ProductionLineDes"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物料描述" prop="ItemName">
          <el-input v-model="temp.ItemName" />
        </el-form-item>
        <el-form-item label="物料组" prop="MaterialGroupCode">
          <el-select
            v-model="temp.MaterialGroupCode"
            filterable
            style="width: 100%;"
            @change="getMaterialGroup(temp.MaterialGroupCode)"
          >
            <el-option
              v-for="item in MaterialGroup"
              :key="item.MaterialGroupCode"
              :value="item.MaterialGroupCode"
              :label="item.MaterialGroupCode+'--'+item.MaterialGroupDes"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
    <!-- 导入excel -->
    <el-dialog :title="$t('Common.import')" :visible.sync="dialogImprotVisable" width="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        action
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="1"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :auto-upload="false"
        width="50px"
      >
        <el-button size="small" type="primary">读取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一个文件.xls/.xlsx文件</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="uploadExcel">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import adaptive from '@/directive/el-table/adaptive'
import Pagination from '@/components/Pagination/index'
import {
  exportToExcel,
  importExcel
} from '@/utils/excel-export';
import {
  fetchList,
  add,
  update,
  batchDelete,
  GetProductionLineList,
  GetMaterialGroupList,
  exportExcelModel,
  improtExcelFile
} from '@/api/MD/MD_ProductionDistributionSettingController'
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_ProductionDistributionSettingController',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        EmployeeName: '',
        ProductionLineCode: '',
        ItemName: '',
        PageNumber: 1,
        PageSize: 10
      },
      temp: {
        EmployeeNumber: '',
        EmployeeName: '',
        ProductionLineCode: '',
        ProductionLineDes: '',
        MaterialGroupCode: '',
        MaterialGroupDes: '',
        Remark: ''
      },
      ProductionLine: [],
      MaterialGroup: [],
      edit: false,
      multipleSelection: [],
      rules: {
        EmployeeNumber: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        EmployeeName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        ProductionLineCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        MaterialGroupCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        Remark: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        ItemName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      },
      dialogImprotVisable: false,
      fileTemp: null,
      uploadExcelData: []
    }
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1
    },
    deletable() {
      return this.multipleSelection.length === 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false
        })
    },
    handleFilter() { // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    resetFormData() {
      this.temp = {
        EmployeeNumber: '',
        EmployeeName: '',
        ProductionLineCode: '',
        ProductionLineDes: '',
        MaterialGroupCode: '',
        MaterialGroupDes: '',
        Remark: ''
      }
    },
    getProductionLineList() {
      this.ProductionLine = [];
      GetProductionLineList().then(res => {
        if (res.Code === 2000) {
          this.ProductionLine = res.Data
        }
      })
    },
    getProductionLine(e) {
      const obj = this.ProductionLine.find(v => v.ProductionLineCode === e);
      this.temp.ProductionLineDes = obj.ProductionLineDes
    },
    getMaterialGroupList() {
      GetMaterialGroupList().then(res => {
        if (res.Code === 2000) {
          this.MaterialGroup = res.Data
        }
      })
    },
    getMaterialGroup(e) {
      const obj = this.MaterialGroup.find(v => v.MaterialGroupCode === e);
      this.temp.MaterialGroupDes = obj.MaterialGroupDes
    },
    handleCreate() {
      this.resetFormData();
      this.getProductionLineList();
      this.getMaterialGroupList();
      this.title = this.$t('Common.add');
      this.dialogVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleEdit() {
      this.resetFormData();
      this.getProductionLineList();
      this.getMaterialGroupList();
      this.title = this.$t('Common.edit');
      this.dialogVisible = true;
      this.edit = true;
      Object.assign(this.temp, this.multipleSelection[0]);
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.startLoading();
          add(this.temp).then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.createSuccess')
            } else {
              this.showNotify('error', res.Message)
            }
            this.endLoading();
            this.getList();
            this.dialogVisible = false
          }).catch(err => {
            console.log(err);
            this.endLoading()
          })
        } else {
          return false
        }
      })
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }).then(() => {
        const arrRowsID = selectRows.map(v => v.ID);
        this.isProcessing = true;

        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList();
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      })
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword,
        EmployeeName: this.listQuery.EmployeeName,
        ProductionLineCode: this.listQuery.ProductionLineCode,
        ItemName: this.listQuery.ItemName
      }).then(res => {
        exportToExcel(res.data, '生产配送配置');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.handleFilter()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order != undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 导出excel模板
    handleExportModel() {
      exportExcelModel().then((res) => exportToExcel(res.data, res.fileName));
    },
    // 导入按钮点击
    handleImport() {
      this.dialogImprotVisable = true;
      this.fileTemp = null;
      this.uploadExcelData = [];
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles();
      });
    },
    // 导入界面关闭
    handleCancel() {
      this.dialogImprotVisable = false;
    },
    // 回调导入excel表转换list
    getImprotData(data) {
      this.uploadExcelData = data;
    },

    // 导入excel数据到后台
    uploadExcel() {
      this.isProcessing = true;
      if (this.uploadExcelData.length === 0) {
        this.showNotify('warning', 'Common.improtNoData');
        this.isProcessing = false;
        return;
      }
      this.dialogImprotVisable = false;
      improtExcelFile(this.uploadExcelData)
        .then((response) => {
          this.showNotify('success', 'Common.operationSuccess');
          this.handleFilter();
          this.isProcessing = false;
        })
        .catch((error) => {
          this.isProcessing = false;
          this.handleFilter();
        });
    },
    // 上传校验模块
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          importExcel(this, this.getImprotData);
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('Common.excel.errorFiles'),
            duration: 5000
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: this.$t('Common.excel.uploadFilse'),
          duration: 5000
        });
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: this.$t('Common.excel.overMaxNum'),
        duration: 5000
      });
      return;
    },

    handleRemove(file, fileList) {
      this.fileTemp = null;
      const _this = this;
      _this.uploadExcelData = [];
    }
  }
}
</script>
