<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input v-model="listQuery.WorkCenterName" size="small" placeholder="请输入工作中心名称" style="width: 220px" class="filter-item" clearable @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.WorkCenterCode" size="small" placeholder="请输入工作中心代码" style="width: 220px" class="filter-item" clearable @keyup.enter.native="handleFilter" />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-plus" size="small" @click="handleCreate">新增
      </el-button>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-edit" size="small" :disabled="multipleSelection.length !== 1" @click="handleEdit">编辑
      </el-button>
      <el-button v-waves class="filter-item" type="danger" icon="el-icon-delete" size="small" :disabled="multipleSelection.length === 0" @click="handleDelete">删除
      </el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-download" size="small" @click="handleExport">导出
      </el-button>
      <el-button v-waves class="filter-item" type="warning" icon="el-icon-download" size="small" @click="handleExportTemplate">导出模板
      </el-button>
      <el-upload
        class="filter-item"
        :action="'#'"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button v-waves type="primary" icon="el-icon-upload" size="small">导入</el-button>
      </el-upload>
    </div>

    <el-table
      ref="multipleTable"
      v-adaptive
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" prop="Id" sortable="custom" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.Id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作中心名称" prop="WorkCenterName" sortable="custom" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作中心代码" prop="WorkCenterCode" sortable="custom" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产能" prop="Capacity" sortable="custom" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.Capacity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="CTime" sortable="custom" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.CTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="CUser" sortable="custom" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="MTime" sortable="custom" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.MTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人" prop="MUser" sortable="custom" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="500px">
      <el-form ref="form" :model="temp" :rules="rules" label-position="right" label-width="120px">
        <el-form-item label="工作中心名称" prop="WorkCenterName">
          <el-input v-model="temp.WorkCenterName" />
        </el-form-item>
        <el-form-item label="工作中心代码" prop="WorkCenterCode">
          <el-input v-model="temp.WorkCenterCode" />
        </el-form-item>
        <el-form-item label="产能" prop="Capacity">
          <el-input-number v-model="temp.Capacity" :min="1" :max="10000" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus === 'create'" type="primary" @click="handleCreateConfirm">确定</el-button>
        <el-button v-else type="primary" @click="handleEditConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import adaptive from '@/directive/el-table/adaptive'
import Pagination from '@/components/Pagination/index'
// import ImportExport from '@/components/ImportExport'
import { exportToExcel } from '@/utils/excel-export'
import {
  fetchList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  exportExcelTemplate,
  importExcelData
} from '@/api/MD/MD_ProduceLineCapacity'
// import permission from "@/directive/permission/index.js" // 权限判断指令
import { formatDateTime } from '@/utils'
import { parseExcelFile } from '@/utils/excel-import' // 使用excel-import中的parseExcelFile方法

export default {
  name: 'MD.MD_ProduceLineCapacity',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      isProcessing: false,
      multipleSelection: [],
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        WorkCenterName: '',
        WorkCenterCode: ''
      },
      temp: {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        Capacity: 1
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑生产线产能',
        create: '新增生产线产能'
      },
      rules: {
        WorkCenterName: [{ required: true, message: '请输入工作中心名称', trigger: 'blur' }],
        WorkCenterCode: [{ required: true, message: '请输入工作中心代码', trigger: 'blur' }],
        Capacity: [{ required: true, message: '请输入产能', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatDateTime,
    getList() {
      this.listLoading = true
      this.isProcessing = true
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items
        this.total = res.Data.total
        this.listLoading = false
        this.isProcessing = false
      })
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === null || order === null) return
      this.listQuery.sort = (order === 'ascending' ? '+' : '-') + prop
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        Capacity: 1
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleEdit() {
      this.resetTemp()
      this.temp = Object.assign({}, this.multipleSelection[0]) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleDelete() {
      this.$confirm('确定要删除选中的数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.Id);
        batchDelete(ids).then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message,
              type: 'error',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        // 取消删除
      })
    },
    handleCreateConfirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          add(this.temp).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleEditConfirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          update(tempData).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '编辑成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message,
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },

    showNotify(type, message) {
      this.$notify({
        title: type === 'success' ? '成功' : '错误',
        message: message,
        type: type,
        duration: 2000
      })
    },

    handleExport() {
      this.isProcessing = true
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res, '生产线产能')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export error:', error)
        this.isProcessing = false
      })
    },

    handleExportTemplate() {
      this.isProcessing = true
      exportExcelTemplate().then(res => {
        exportToExcel(res, '生产线产能模板')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export template error:', error)
        this.isProcessing = false
      })
    },

    beforeUpload(file) {
      this.isProcessing = true

      // 定义字段映射关系，Excel表头到实体属性的映射
      const fieldMapping = {
        '工作中心名称': 'WorkCenterName',
        '工作中心代码': 'WorkCenterCode',
        '产能': 'Capacity'
      }

      // 解析Excel文件，表头在第一行，所以索引是0
      parseExcelFile(file, 0)
        .then(data => {
          // 将解析后的数据映射到实体
          const entities = data.map(row => {
            const entity = {}

            // 遍历字段映射关系，将Excel数据映射到实体属性
            Object.keys(fieldMapping).forEach(excelHeader => {
              const entityProperty = fieldMapping[excelHeader]
              if (row[excelHeader] !== undefined && row[excelHeader] !== null) {
                // 对于产能，需要转换为数字
                if (entityProperty === 'Capacity') {
                  entity[entityProperty] = Number(row[excelHeader]) || 0
                } else {
                  entity[entityProperty] = row[excelHeader]
                }
              }
            })

            return entity
          }).filter(entity => entity.WorkCenterName || entity.WorkCenterCode) // 过滤掉没有工作中心名称或代码的行

          // 直接将解析后的实体数组传递给后端
          return importExcelData(entities)
        })
        .then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '导入成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '导入失败',
              type: 'error',
              duration: 2000
            })
          }
          this.isProcessing = false
        })
        .catch(error => {
          console.error('导入出错:', error)
          this.$notify({
            title: '错误',
            message: '导入Excel文件失败',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        })

      return false
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
