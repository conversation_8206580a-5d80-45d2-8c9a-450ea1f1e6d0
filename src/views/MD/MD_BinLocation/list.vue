<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_BinLocation.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_BinLocation.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t("Common.edit") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_BinLocation.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'PO.PO_BarCode.Print' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-printer"
        :disabled="printDisable"
        @click="handlePrint"
      >{{ $t("Common.print") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_BinLocation.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t("Common.export") }}</el-button>
      <!-- <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>-->
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        :label="$t('ui.MD.MD_BinLocation.BinLocationCode')"
        prop="BinLocationCode"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_BinLocation.BinLocationName')"
        prop="BinLocationName"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.BinLocationName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_BinLocation.RegionCode')" prop="RegionCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.RegionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_BinLocation.RegionName')" prop="RegionName" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.RegionName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsCode')" prop="WhsCode" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.WhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsName')" prop="WhsName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('ui.MD.MD_BinLocation.IsFreeTax')"
        prop="IsFreeTax"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsFreeTax | yesnoFilter }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        :label="$t('ui.MD.MD_BinLocation.IsConsign')"
        prop="IsConsign"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsConsign | yesnoFilter }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        :label="$t('ui.MD.MD_BinLocation.PLine')"
        prop="PLine"

        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.PLine }}</span>
        </template>
      </el-table-column> -->

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="true" :label="$t('Common.CUser')" prop="CUser" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="true" :label="$t('Common.CTime')" prop="CTime" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
        <el-form-item :label="$t('ui.MD.MD_BinLocation.BinLocationCode')" prop="BinLocationCode">
          <el-input v-model="temp.BinLocationCode" :disabled="edit" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_BinLocation.BinLocationName')" prop="BinLocationName">
          <el-input v-model="temp.BinLocationName" />
        </el-form-item>
        <!-- <el-form-item :label="$t('ui.MD.MD_BinLocation.IsFreeTax')" prop="IsFreeTax">
          <el-switch v-model="temp.IsFreeTax" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item> -->
        <!-- <el-form-item :label="$t('ui.MD.MD_BinLocation.IsConsign')" prop="IsConsign">
          <el-switch v-model="temp.IsConsign" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item> -->

        <!-- <el-form-item :label="$t('ui.MD.MD_BinLocation.PLine')" prop="PLine">
          <el-select v-model="temp.PLine" class="filter-item">
            <el-option v-for="item in PlineOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item> -->

        <el-form-item :label="$t('ui.MD.MD_BinLocation.RegionName1')" prop="RegionName">
          <el-input v-model="temp.RegionName" disabled>
            <el-button slot="append" icon="el-icon-more" @click="chooseRegion" />
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_BinLocation.WhsName1')" prop="WhsName">
          <el-input v-model="temp.WhsName" disabled />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">
          {{
            $t("Common.cancel")
          }}
        </el-button>
        <el-button v-waves type="primary" @click="handleSave">
          {{
            $t("Common.save")
          }}
        </el-button>
      </span>
    </el-dialog>

    <!--选择区域及仓库信息-->
    <el-dialog :title="subtitle" :visible.sync="regionVisible" width="60%">
      <div class="filter-container">
        <el-input
          v-model="sublistQuery.keyword"
          class="filter-item"
          style="width: 140px"
          :placeholder="$t('Common.keyword')"
          @keyup.enter.native="handleFilterRe"
        />
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilterRe">
          {{ $t("Common.search") }}</el-button>
      </div>
      <el-table
        :data="regionList"
        border
        highlight-current-row
        style="width: 100%;"
        size="mini"
        @selection-change="handleRegionSelectionChange"
      >
        <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
        <el-table-column type="index" align="center" width="50" label="行号" />
        <el-table-column :label="$t('ui.MD.MD_BinLocation.RegionCode')" prop="RegionCode" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.RegionCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.MD_BinLocation.RegionName')" prop="RegionName" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.RegionName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsCode')" prop="WhsCode" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.WhsCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsName')" prop="WhsName" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.WhsName }}</span>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-show="rtotal > 0"
        :total="rtotal"
        :page.sync="listQuery.pageNumber"
        :limit.sync="listQuery.pageSize"
        @pagination="getRegionList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="regionVisible = false">
          {{ $t("Common.close") }}
        </el-button>
        <el-button v-waves type="primary" icon="el-icon-check" :disabled="Select" @click="handleSelect">
          {{ $t("Common.select") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchList,
  add,
  update,
  batchDelete,
  printToPDF,
  exportExcelFile
} from '../../../api/MD/MD_BinLocation';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchList as fetchRegionList
} from '../../../api/MD/MD_Region';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  // eslint-disable-next-line vue/name-property-casing
  name: 'MD.MD_BinLocation',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      subtitle: '选择仓库区域',
      regionVisible: false,
      regionList: [],
      rtotal: 0,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      sublistQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      PlineOptions: [],
      temp: {
        BinID: '',
        BinLocationCode: '',
        BinLocationName: '',
        RegionCode: '',
        RegionName: '',
        WhsCode: '',
        WhsName: '',
        IsFreeTax: false,
        IsConsign: false,
        PLine: ''
      },
      edit: false,
      multipleSelection: [],
      multipleRegion: [],
      rules: {
        BinLocationCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        BinLocationName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        // PLine: [{ required: true, message: this.$i18n.t('Common.IsRequired'), trigger: 'blur' }],
        RegionName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        WhsName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      }
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    },
    Select() {
      return this.multipleRegion.length === 0 || this.multipleRegion.length > 1;
    },
    printDisable() {
      return this.multipleSelection.length <= 0;
    }
  },
  created() {
    this.getDict('PP005').then(data => {
      const plineArr = data.map(obj => {
        return obj.EnumValue;
      });
      this.PlineOptions = [...new Set(plineArr)];
      this.getList();
    });
  },
  mounted() {
    /* this.$nextTick(function() {
        this.tableHeight = window.innerHeight - this.$refs.table.$el.offsetTop - 110
        // 监听窗口大小变化
        const self = this
        window.onresize = function() {
          self.tableHeight = window.innerHeight - self.$refs.table.$el.offsetTop - 110
        }
      }) */
    // this.$refs.table.$el.offsetTop：表格距离浏览器的高度
  },
  methods: {
    getList() {
      // 获取库位信息表格数据
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    resetFormData() {
      this.temp = {
        BinID: '',
        BinLocationCode: '',
        BinLocationName: '',
        RegionCode: '',
        RegionName: '',
        WhsCode: '',
        WhsName: '',
        IsFreeTax: false,
        IsConsign: false,
        PLine: ''
      };
    },
    handleCreate() {
      // 点击新增按钮之后的操作
      this.resetFormData();
      this.title = this.$t('Common.add');
      this.formMode = 'Create';
      this.dialogVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleEdit() {
      // 点击编辑按钮之后的操作
      this.resetFormData();
      this.title = this.$t('Common.edit');
      this.formMode = 'Edit';
      this.dialogVisible = true;
      this.edit = true;
      Object.assign(this.temp, this.multipleSelection[0]);
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleSave() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.startLoading();
          if (this.formMode === 'Create') {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading();
              this.handleFilter();
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading();
              this.handleFilter();
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          }
          this.dialogVisible = false;
        } else {
          return false;
        }
      });
    },
    handleDelete() {
      // 点击删除按钮之后的操作
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.BinID);

        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.getList();
        });
      });
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => {
        exportToExcel(res.data, '区域库位管理');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handlePrint() {
      this.isProcessing = true;
      const selectRows = this.multipleSelection;
      const binlocationCodes = selectRows.map(v => v.BinLocationCode);
      console.log(binlocationCodes);
      printToPDF(binlocationCodes).then(response => {
        console.log(response);
        window.open(this.API.BaseURL + response.Data.PrintedPDF);
        this.isProcessing = false;
      }).catch(error => {
        this.isProcessing = false;
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.handleFilter();
    },
    chooseRegion() {
      this.regionVisible = true;
      this.sublistQuery.keyword = '';
      this.getRegionList();
    },
    getRegionList() {
      // 获取区域及仓库信息列表
      fetchRegionList(this.sublistQuery).then(res => {
        this.regionList = res.Data.items;
        this.rtotal = res.Data.total;
      });
    },
    handleFilterRe() {
      // 查找区域及仓库信息弹框数据
      this.getRegionList();
    },
    handleSelect() {
      // 选择区域及仓库信息列表中信息
      Object.assign(this.temp, this.multipleRegion[0]);
      this.regionVisible = false;
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order !== undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRegionSelectionChange(val) {
      this.multipleRegion = val;
    }
  }
};
</script>
