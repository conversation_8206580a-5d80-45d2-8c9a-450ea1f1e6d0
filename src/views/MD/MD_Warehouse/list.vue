<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Warehouse.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Warehouse.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t("Common.edit") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Warehouse.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Warehouse.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column :label="$t('ui.MD.MD_BinLocation.WhsCode')" prop="WhsCode" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.WhsCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Warehouse.WhsName')" prop="WhsName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.WhsName }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="true" :label="$t('Common.CUser')" prop="CUser" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="true" :label="$t('Common.CTime')" prop="CTime" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNumber"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="65%">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Warehouse.WhsCode')" prop="WhsCode">
              <el-input v-model="temp.WhsCode" :disabled="temp.edit" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Warehouse.WhsName')" prop="WhsName">
              <el-input v-model="temp.WhsName" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogVisible = false">
          {{ $t("Common.close") }}
        </el-button>
        <el-button v-waves type="primary" icon="el-icon-check" @click="handleSave">{{ $t("Common.save") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchList,
  batchDelete,
  add,
  update,
  exportExcelFile
} from '../../../api/MD/MD_Warehouse';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Warehouse',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      title: '',
      formMode: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        pageNumber: 1,
        pageSize: 20
      },
      temp: {
        WhsCode: '',
        WhsName: '',
        edit: false
      },
      rules: {
        WhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        WhsName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      multipleSelection: []
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    resetForm() {
      this.temp = {
        WhsID: '',
        WhsCode: '',
        WhsName: ''
      };
    },
    handleCreate() {
      this.resetForm();
      this.title = this.$t('Common.add');
      this.formMode = 'Create';
      this.dialogVisible = true;
      this.temp.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleEdit() {
      this.resetForm();
      this.title = this.$t('Common.edit');
      this.formMode = 'edit';
      Object.assign(this.temp, this.multipleSelection[0]);
      this.dialogVisible = true;
      this.temp.edit = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleSave() {
      console.log(123);
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.startLoading();
          if (this.formMode === 'Create') {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false;
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false;
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          }
        } else {
          return false;
        }
      });
    },
    handleDelete() {
      const selectRows = this.multipleSelection;

      console.log('batchDelete');
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.WhsID);

        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.updateSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.getList();
        });
      });
    },
    handleExport() {
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res =>
        exportToExcel(res.data, '仓库主数据')
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
