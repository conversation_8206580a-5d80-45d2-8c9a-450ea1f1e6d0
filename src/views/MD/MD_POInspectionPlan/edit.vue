<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t("route.MD.MD_POInspectionPlanDetailed") }}</label>
    </p>
    <div class="filter-container">
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-delete"
        @click="resetFormData"
      >{{ $t("Common.empty") }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-edit"
        @click="handleCommit"
      >{{ $t("Common.confirm") }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-back"
        @click="handleBack"
      >{{ $t("Common.cancel") }}</el-button>
    </div>

    <el-form
      ref="dataForm"
      :inline="false"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <!-- 供应商 -->
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.SupplierCode')" prop="SupplierCode">
            <el-input
              v-model="temp.SupplierCode"
              :placeholder="
                $t('ui.MD.MD_POInspectionPlan.Placeholder.Supplier')
              "
              readonly
            >
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="editStatus === 'edit'"
                @click="selectSupplier"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.SupplierName')" prop="SupplierName">
            <el-input v-model="temp.SupplierName" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 物料 -->
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.ItemCode')" prop="ItemCode">
            <el-input
              v-model="temp.ItemCode"
              :placeholder="$t('ui.MD.MD_POInspectionPlan.Placeholder.Item')"
              readonly
            >
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="editStatus === 'edit'"
                @click="selectItem"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 采样范围 -->
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.SampleRange')" prop="SampleRange">
            <el-select v-model="temp.SampleRange" class="filter-item" filterable>
              <el-option
                v-for="item in SampleRangeOptions"
                :key="item.EnumKey"
                :label="item.EnumValue"
                :value="item.EnumKey"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 检验等级 -->
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.GradeCode')" prop="GradeCode">
            <el-select
              v-model="temp.GradeCode"
              class="filter-item"
              filterable
              :disabled="inspectionGradeDisable"
            >
              <el-option
                v-for="item in InspectionGradeList"
                :key="item.GradeCode"
                :label="item.GradeName"
                :value="item.GradeCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 固定样本数 -->
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.SampleNum')" prop="SampleNum">
            <el-input-number
              v-model="temp.SampleNum"
              :step="1"
              :disabled="inspectionFixNumberDisable"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 固定样本比例 -->
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_POInspectionPlan.SampleProportion')">
            <el-input-number
              v-model="temp.SampleProportion"
              :disabled="inspectionFixRateDisable"
              :precision="2"
              :step="1"
              :max="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 选择物料 -->
    <MaterialDlg
      ref="materialDlg"
      :show.sync="dialogItemFormVisible"
      :title="$t('Common.selectMaterial')"
      :is-multiple="false"
      @close="materialSelected"
    />
    <!-- 选择供应商 -->
    <SupplierDlg
      ref="materialDlg"
      :show.sync="dialogSupplierFormVisible"
      :title="$t('Common.selectMaterial')"
      :is-multiple="false"
      @close="supplierSelected"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves' // waves directive
// import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { fetchList as fetchGradeList } from '../../../api/MD/MD_POInspectionGrade'
import {
  // fetchSupplierList,
  // fetchItemList,
  add,
  update,
  fetchItemNextPage,
  fetchSupplierNextPage
} from '../../../api/MD/MD_POInspectionPlan'
// import InfiniteLoading from "@/components/InfiniteLoading/vue-infinite-loading";
import InfiniteLoading from 'vue-infinite-loading'
import MaterialDlg from '@/components/FLD/MaterialDlg';
import SupplierDlg from '@/components/FLD/SupplierDlg';

export default {
  name: 'MD.MD_POInspectionPlanDetailed',
  components: {
    InfiniteLoading,
    MaterialDlg,
    SupplierDlg
  },
  directives: {
    waves
  },
  data() {
    return {
      infiniteItemId: +new Date(),
      listLoading: false,
      SampleRangeOptions: [],
      InspectionGradeList: [],
      SupplierDataList: [],
      SelectedSupplierRowData: [],
      listSupplierQuery: {
        keyword: '',
        pageSize: 30,
        firstID: ''
      },
      firstSupplierID: '',
      ItemDataList: [], // 物料主数据(列表)
      SelectedItemRowData: [], // 物料主数据(选中行)
      listItemQuery: {
        keyword: '',
        pageSize: 10,
        firstID: ''
      },
      firstItemID: '',
      temp: {
        PlanID: '',
        SupplierCode: '',
        SupplierName: '',
        ItemCode: '',
        ItemName: '',
        SampleRange: '',
        GradeCode: '',
        GradeName: '',
        SampleNum: '',
        SampleProportion: '',
        ITime: ''
      },
      editStatus: '',
      dialogSupplierFormVisible: false,
      dialogItemFormVisible: false,
      rules: {
        SupplierCode: [
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ],
        // 表单校验逻辑
        ItemCode: [
          {
            required: true,
            message: this.$t('Common.ValidatorMessage.MustInput'),
            trigger: 'blur'
          }
        ],
        SampleRange: [
          {
            required: true,
            message: this.$t('Common.ValidatorMessage.MustInput'),
            trigger: 'blur'
          }
        ]
      },
      downloadLoading: false
    }
  },
  computed: {
    supplierSelectDisable() {
      return this.SelectedSupplierRowData.length !== 1 || this.editStatus === 'edit'
    },
    itemSelectDisable() {
      return this.SelectedItemRowData.length !== 1 || this.editStatus === 'edit'
    },
    inspectionGradeDisable() {
      // 预定义采样方案
      return this.temp.SampleRange !== 5
    },
    inspectionFixNumberDisable() {
      // 预定义采样方案
      return this.temp.SampleRange !== 3
    },
    inspectionFixRateDisable() {
      // 预定义采样方案
      return this.temp.SampleRange !== 2
    }
  },
  created() {
    this.getPageParams();
    this.getDict('SYS005').then(data => {
      this.SampleRangeOptions = data;
      this.listLoading = false
    });

    this.getGradeList()
    // this.getSupplierList()
  },
  methods: {
    getGradeList() {
      this.listLoading = true;
      fetchGradeList().then(response => {
        this.InspectionGradeList = response.Data;
        this.listLoading = false
      })
    },

    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      if (this.temp.PlanID) {
        this.editStatus = 'edit'
      } else {
        this.editStatus = 'create'
      }
    },

    resetFormData() {
      // 清空表单数据
      this.temp = {
        PlanID: '',
        SupplierCode: '',
        SupplierName: '',
        ItemCode: '',
        ItemName: '',
        SampleRange: '',
        GradeCode: '',
        GradeName: '',
        SampleNum: '',
        SampleProportion: '',
        ITime: ''
      }
    },

    handleBack() {
      this.backTo('MD.MD_POInspectionPlan')
    },

    // 选择物料
    selectItem() {
      this.dialogItemFormVisible = true
      // this.getItemList()
    },
    materialSelected(materials) {
      this.temp.ItemCode = materials.ItemCode;
      this.temp.ItemName = materials.ItemName;
      this.dialogItemFormVisible = false
    },
    // 选择供应商
    selectSupplier() {
      this.dialogSupplierFormVisible = true
    },
    supplierSelected(suppliers) {
      this.temp.SupplierCode = suppliers.SupplierCode;
      this.temp.SupplierName = suppliers.SupplierName;
      this.dialogSupplierFormVisible = false
    },

    handleCommit() {
      // 提交
      this.$refs['dataForm'].validate(val => {
        if (val) {
          if (this.editStatus === 'create') {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.handleBack()
              } else {
                this.showNotify('error', res.Message)
              }
            })
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.handleBack()
              } else {
                this.showNotify('error', res.Message)
              }
            })
          }
        }
      })
    }
  }
}
</script>
