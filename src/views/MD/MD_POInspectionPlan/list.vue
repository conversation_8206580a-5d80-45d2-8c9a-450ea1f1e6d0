<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionPlan.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionPlan.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t("Common.edit") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionPlan.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionPlan.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
      <el-table-column
        v-if="false"
        :label="$t('ui.MD.MD_POInspectionPlan.ScanID')"
        prop="ScanID"
        align="center"
        width="120"
        hidden
      >
        <template slot-scope="scope">
          <span>{{ scope.row.ScanID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.SupplierCode')"
        prop="SupplierCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.SupplierName')"
        prop="SupplierName"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.ItemCode')" prop="ItemCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.ItemName')" prop="ItemName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.SampleRange')"
        prop="SampleRange"
        align="center"
        width="120"
        :formatter="formatSampleRange"
      />
      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.GradeCode')" prop="GradeCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.GradeCode }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.GradeName')" prop="GradeName" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.GradeName }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('ui.MD.MD_POInspectionPlan.SampleNum')" prop="SampleNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SampleNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.MD.MD_POInspectionPlan.SampleProportion')"
        prop="SampleProportion"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SampleProportion }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="false" :label="$t('Common.Remark')" prop="Remark" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="true" :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="180"
        :formatter="formatDateTime"
      />
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNumber"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves'
import {
  getDictDescription,
  formatDateTime
} from '@/utils' // 列表内容格式化
import Pagination from '../../../components/Pagination/index'
import {
  fetchList,
  batchDelete,
  add,
  update,
  exportExcelFile
} from '../../../api/MD/MD_POInspectionPlan'
import {
  exportToExcel
} from '@/utils/excel-export'

// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_POInspectionPlan',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      // dialogVisible: false,
      listQuery: {
        keyword: '',
        pageNumber: 1,
        pageSize: 20
      },
      SampleRangeOptions: [],
      temp: {
        WhsCode: '',
        WhsName: '',
        edit: false
      },
      rules: {
        WhsCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        WhsName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      multipleSelection: []
    }
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1
    },
    deletable() {
      return this.multipleSelection.length === 0
    }
  },
  created() {
    this.getDict('SYS005').then(data => {
      this.SampleRangeOptions = data;
      this.getList()
    })
  },
  methods: {
    formatDateTime,
    formatSampleRange: function(row, column, currentValue) {
      const findedSampleRangeOptions = this.SampleRangeOptions.find((element) => {
        return element.EnumKey === currentValue
      });
      return findedSampleRangeOptions != null ? findedSampleRangeOptions.EnumValue : ''
    },
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(res => {
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false
      })
    },

    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    handleCreate() {
      this.routeTo('MD.MD_POInspectionPlanDetailed')
    },
    handleEdit() {
      console.log(this.multipleSelection[0]);
      this.routeTo('MD.MD_POInspectionPlanDetailed', this.multipleSelection[0])
    },

    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }).then(() => {
        const arrRowsID = selectRows.map(v => v.PlanID);
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.updateSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList();
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      })
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => {
        exportToExcel(res.data, '检验计划');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      console.log(data);
      if (order !== null) {
        // console.log('sort')
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    },
    sortByID(order) {
      // if (order === 'ascending') {
      //   this.listQuery.sort = 'ScanID asc'
      // } else {
      //   this.listQuery.sort = 'ScanID desc'
      // }
      // this.handleFilter()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
