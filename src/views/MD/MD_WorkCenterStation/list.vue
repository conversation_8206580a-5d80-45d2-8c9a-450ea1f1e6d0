<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.WorkCenterName"
        size="small"
        placeholder="工作中心名称"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.WorkCenterCode"
        size="small"
        placeholder="工作中心编码"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.StationCode"
        size="small"
        placeholder="站点代码"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.StationName"
        size="small"
        placeholder="站点名称"
        style="width: 180px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
      <hr>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-plus" size="small" @click="handleCreate">新增</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >编辑</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >删除</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        @click="handleExportExcel"
      >导出</el-button>
      <el-upload
        class="filter-item"
        :action="'#'"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button v-waves type="primary" icon="el-icon-upload" size="small">导入</el-button>
      </el-upload>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        @click="handleExportTemplate"
      >导出模板</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="工作中心名称" prop="WorkCenterName" align="center" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作中心编码" prop="WorkCenterCode" align="center" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="站点代码" prop="StationCode" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.StationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="站点名称" prop="StationName" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.StationName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="CUser" align="center" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="CTime" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item label="工作中心名称" prop="WorkCenterName">
          <el-input v-model="temp.WorkCenterName" />
        </el-form-item>
        <el-form-item label="工作中心编码" prop="WorkCenterCode">
          <el-input v-model="temp.WorkCenterCode" />
        </el-form-item>
        <el-form-item label="站点代码" prop="StationCode">
          <el-input v-model="temp.StationCode" />
        </el-form-item>
        <el-form-item label="站点名称" prop="StationName">
          <el-input v-model="temp.StationName" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-waves type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  insert,
  update,
  batchDelete,
  exportToExcelFile,
  exportToExcelModel,
  importExcelToData
} from '@/api/MD/MD_WorkCenterStation';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { exportToExcel } from '@/utils/excel-export';
import { parseExcelFile } from '@/utils/excel-import';

export default {
  name: 'MD_WorkCenterStation',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      isProcessing: false,
      selective: true,
      deletable: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        WorkCenterName: '',
        WorkCenterCode: '',
        StationCode: '',
        StationName: ''
      },
      temp: {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        StationCode: '',
        StationName: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '新增'
      },
      multipleSelection: [],
      rules: {
        WorkCenterName: [{ required: true, message: '工作中心名称不能为空', trigger: 'blur' }],
        WorkCenterCode: [{ required: true, message: '工作中心编码不能为空', trigger: 'blur' }],
        StationCode: [{ required: true, message: '站点代码不能为空', trigger: 'blur' }],
        StationName: [{ required: true, message: '站点名称不能为空', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        const { Data } = response;
        if (Data) {
          this.list = Data.items;
          this.total = Data.total;
        } else {
          this.list = [];
          this.total = 0;
        }
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.selective = !(this.multipleSelection.length === 1);
      this.deletable = this.multipleSelection.length === 0;
    },
    resetTemp() {
      this.temp = {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        StationCode: '',
        StationName: ''
      };
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = 'create';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    handleEdit() {
      this.resetTemp();
      const tempData = this.multipleSelection[0];
      this.temp = Object.assign({}, tempData);
      this.dialogStatus = 'update';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'create') {
            this.isProcessing = true;
            insert(this.temp)
              .then(response => {
                if (response.Code === 2000) {
                  this.$notify({
                    title: '成功',
                    message: '创建成功',
                    type: 'success',
                    duration: 2000
                  });
                  this.dialogFormVisible = false;
                  this.getList();
                } else {
                  this.$notify({
                    title: '失败',
                    message: response.Message || '创建失败',
                    type: 'error',
                    duration: 2000
                  });
                }
                this.isProcessing = false;
              })
              .catch(() => {
                this.isProcessing = false;
              });
          } else {
            this.isProcessing = true;
            update(this.temp)
              .then(response => {
                if (response.Code === 2000) {
                  this.$notify({
                    title: '成功',
                    message: '更新成功',
                    type: 'success',
                    duration: 2000
                  });
                  this.dialogFormVisible = false;
                  this.getList();
                } else {
                  this.$notify({
                    title: '失败',
                    message: response.Message || '更新失败',
                    type: 'error',
                    duration: 2000
                  });
                }
                this.isProcessing = false;
              })
              .catch(() => {
                this.isProcessing = false;
              });
          }
        }
      });
    },
    handleDelete() {
      this.$confirm('确认删除所选记录?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.isProcessing = true;
          const ids = this.multipleSelection.map(item => item.Id);
          batchDelete(ids)
            .then(response => {
              if (response.Code === 2000) {
                this.$notify({
                  title: '成功',
                  message: '删除成功',
                  type: 'success',
                  duration: 2000
                });
                this.getList();
              } else {
                this.$notify({
                  title: '失败',
                  message: response.Message || '删除失败',
                  type: 'error',
                  duration: 2000
                });
              }
              this.isProcessing = false;
            })
            .catch(() => {
              this.isProcessing = false;
            });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    handleExportExcel() {
      this.isProcessing = true;
      exportToExcelFile(this.listQuery)
        .then(response => {
          exportToExcel(response, '工作中心站点导出')
          this.isProcessing = false
        })
        .catch(() => {
          this.isProcessing = false;
        });
    },
    beforeUpload(file) {
      this.isProcessing = true

      // 定义字段映射关系，Excel表头到实体属性的映射
      const fieldMapping = {
        '工作中心名称': 'WorkCenterName',
        '工作中心编码': 'WorkCenterCode',
        '站点代码': 'StationCode',
        '站点名称': 'StationName'
      }

      // 解析Excel文件，表头在第一行，所以索引是0
      parseExcelFile(file, 0)
        .then(data => {
          // 将解析后的数据映射到实体
          const entities = data.map(row => {
            const entity = {}

            // 遍历字段映射关系，将Excel数据映射到实体属性
            Object.keys(fieldMapping).forEach(excelHeader => {
              const entityProperty = fieldMapping[excelHeader]
              if (row[excelHeader] !== undefined && row[excelHeader] !== null) {
                entity[entityProperty] = row[excelHeader]
              }
            })

            return entity
          }).filter(entity => Object.keys(entity).length > 0) // 过滤掉空行

          // 发送解析后的数据到后端
          return importExcelToData(entities)
        })
        .then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '导入成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '导入失败',
              type: 'error',
              duration: 2000
            })
          }
          this.isProcessing = false
        })
        .catch(error => {
          console.error('导入失败:', error)
          this.$notify({
            title: '错误',
            message: '解析Excel文件失败，请检查文件格式',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        })

      return false
    },
    handleExportTemplate() {
      this.isProcessing = true
      exportToExcelModel().then(res => {
        exportToExcel(res, '工作中心站点模板')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export template error:', error)
        this.isProcessing = false
      })
    },
    sortChange(data) {
      const { prop, order } = data;
      // 暂时不处理排序
      this.handleFilter();
    }
  }
};
</script>
