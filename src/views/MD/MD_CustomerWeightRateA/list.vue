<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_CustomerWeightRateA.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_CustomerWeightRateA.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_CustomerWeightRateA.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_CustomerWeightRateA.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="供应商编号" prop="SupplierCode" align="center" show-overflow-tooltip />
      <el-table-column label="供应商名称" prop="SupplierName" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="重量＜3吨" prop="Weight3" align="center" show-overflow-tooltip/>
      <el-table-column label="重量3-8吨" prop="Weight38" align="center" show-overflow-tooltip/>
      <el-table-column label="重量≥8吨" prop="Weight8" align="center" show-overflow-tooltip/> -->
      <el-table-column label="重量从（吨）" prop="WeightFrom" align="center" show-overflow-tooltip />
      <el-table-column label="重量至（吨）" prop="WeightTo" align="center" show-overflow-tooltip />
      <el-table-column label="费率" prop="Rate" align="center" show-overflow-tooltip />
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item label="供应商编号" prop="SupplierCode">
          <el-select
            v-model="temp.SupplierCode"
            placeholder="请选择"
            filterable
            style="width: 100%;"
            @change="changeCustomer"
          >
            <el-option
              v-for="item in options"
              :key="item.SupplierCode"
              :label="item.SupplierCode+'-'+item.SupplierName"
              :value="item.SupplierCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商名称">
          <el-input v-model="temp.SupplierName" disabled />
        </el-form-item>
        <!-- <el-form-item label="重量＜3吨" prop="Weight3">
          <el-input v-model="temp.Weight3" />
        </el-form-item>
        <el-form-item label="重量3-8吨" prop="Weight38">
          <el-input v-model="temp.Weight38" />
        </el-form-item>
        <el-form-item label=" 重量≥8吨" prop="Weight8">
          <el-input v-model="temp.Weight8" />
        </el-form-item> -->
        <el-form-item label="重量从（吨）" prop="WeightFrom">
          <el-input v-model="temp.WeightFrom" />
        </el-form-item>
        <el-form-item label="重量至（吨）" prop="WeightTo">
          <el-input v-model="temp.WeightTo" />
        </el-form-item>
        <el-form-item label="费率" prop="Rate">
          <el-input v-model="temp.Rate" />
        </el-form-item>
        <el-form-item label="结算地址" prop="SettlementAdd">
          <el-input v-model="temp.SettlementAdd" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import adaptive from '@/directive/el-table/adaptive'
import Pagination from '@/components/Pagination/index'
import {
  exportToExcel
} from '@/utils/excel-export'
import {
  fetchList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  GetXZ_SAP_KNA1
} from '@/api/MD/MD_CustomerWeightRateA'
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_CustomerWeightRateA',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      temp: {
        SupplierCode: '',
        SupplierName: '',
        Weight3: '',
        Weight38: '',
        Weight8: '',
        WeightFrom: '',
        WeightTo: '',
        Rate: '',
        SettlementAdd: '',
        Remark: ''
      },
      options: [],
      edit: false,
      multipleSelection: [],
      rules: {
        SupplierCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        SupplierName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        // Weight3: [{
        //   required: true,
        //   message: this.$i18n.t('Common.IsRequired'),
        //   trigger: 'change'
        // }],
        // Weight38: [{
        //   required: true,
        //   message: this.$i18n.t('Common.IsRequired'),
        //   trigger: 'blur'
        // }],
        // Weight8: [{
        //   required: true,
        //   message: this.$i18n.t('Common.IsRequired'),
        //   trigger: 'blur'
        // }],
        WeightFrom: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        WeightTo: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Rate: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        SettlementAdd: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }]
      }
    }
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1
    },
    deletable() {
      return this.multipleSelection.length === 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false
        })
    },
    handleFilter() { // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    resetFormData() {
      this.temp = {
        SupplierCode: '',
        SupplierName: '',
        WeightFrom: '',
        WeightTo: '',
        Rate: '',
        Remark: ''
      }
    },
    handleCreate() {
      this.resetFormData();
      this.GetXZ_SAP_KNA1();
      this.title = this.$t('Common.add');
      this.dialogVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleEdit() {
      this.resetFormData();
      this.GetXZ_SAP_KNA1();
      this.title = this.$t('Common.edit');
      this.dialogVisible = true;
      this.edit = true;
      Object.assign(this.temp, this.multipleSelection[0]);
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.startLoading();
          if (this.edit === false) {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }).then(() => {
        const arrRowsID = selectRows.map(v => v.RateID);
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList();
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      })
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => {
        exportToExcel(res.data, '销售供应商重量费率-A');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.handleFilter()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order != undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    GetXZ_SAP_KNA1() {
      this.options = [];
      GetXZ_SAP_KNA1().then(res => {
        if (res.Code === 2000) {
          this.options = res.Data
        }
      })
    },
    changeCustomer(e) {
      const obj = this.options.find(v => v.SupplierCode === e);
      this.temp.SupplierName = obj.SupplierName
    }
  }
}
</script>
