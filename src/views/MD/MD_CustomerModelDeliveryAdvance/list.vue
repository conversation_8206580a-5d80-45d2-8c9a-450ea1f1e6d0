<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        @click="handleExport"
      >导出</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-download"
        size="small"
        @click="handleExportTemplate"
      >导出模板</el-button>
      <el-upload
        class="filter-item"
        :action="'#'"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button v-waves type="primary" icon="el-icon-upload" size="small">导入</el-button>
      </el-upload>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column label="客户名称" prop="CustomerName" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="客户代码" prop="CustomerCode" align="center" width="120" />
      <el-table-column label="产品型号" prop="ProduceModel" align="center" width="120" />
      <el-table-column label="提前天数" prop="AdvanceDays" align="center" width="120" />
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog title="添加" :visible.sync="dialogFormVisible">
      <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item label="客户名称" prop="CustomerName">
          <el-input v-model="temp.CustomerName" />
        </el-form-item>
        <el-form-item label="客户代码" prop="CustomerCode">
          <el-input v-model="temp.CustomerCode" />
        </el-form-item>
        <el-form-item label="产品型号" prop="ProduceModel">
          <el-input v-model="temp.ProduceModel" />
        </el-form-item>
        <el-form-item label="提前天数" prop="AdvanceDays">
          <el-input v-model="temp.AdvanceDays" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button v-waves type="primary" @click="handleEditConfirm">{{ $t('Common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  add,
  batchDelete,
  exportExcelFile,
  exportExcelTemplate,
  importExcelData
} from '@/api/MD/MD_CustomerModelDeliveryAdvance';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
// import ImportExport from "@/components/ImportExport";
import { exportToExcel } from '@/utils/excel-export';
import InfiniteLoading from 'vue-infinite-loading';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Item',
  components: {
    Pagination,
    InfiniteLoading
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        keyword: '',
        PageSize: 10,
        PageNumber: 1,
        StockManWay: ''
      },
      StockManWayOptions: [],
      isProcessing: false,
      regionOptions: [],
      multipleSelection: [],
      dialogFormVisible: false,
      rules: {
        StockingQty: [
          {
            required: true,
            validator: this.QtyValidator,
            trigger: 'change'
          },
          {
            required: true,
            message: this.$i18n.t('Common.IsRequired'),
            trigger: 'change'
          }
        ]
      },
      temp: {
        CustomerName: undefined,
        CustomerCode: undefined,
        ProduceModel: undefined,
        AdvanceDays: undefined
      },
      firstID: ''
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.handleFilter()
    this.GetDictionary()
  },
  methods: {
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          if (response.Code === 2000) {
            this.list = response.Data.items;
            this.total = response.Data.total;
          } else {
            this.showNotify('error', response.Message);
          }
          this.listLoading = false;
        })
        .catch(err => {
          console.log(err);
          this.listLoading = false;
        });
    },
    handleCreate() {
      this.clearForm()
      this.dialogFormVisible = true;
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        ItemID: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        StockManWay: '',
        StockManWayName: '',
        Power: '',
        IsPackaging: false,
        IsConfirm: false
      };
    },
    clearForm() {
      this.temp.CustomerName = undefined;
      this.temp.CustomerCode = undefined;
      this.temp.ProduceModel = undefined;
      this.temp.AdvanceDays = undefined;
    },
    handleFilter() {
      this.list = [];
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleEdit() {
      this.resetTemp();
      Object.assign(this.temp, this.multipleSelection[0]);
      this.dialogFormVisible = true;
    },
    handleDelete() {
      this.$confirm('是否确认要删除选中行数据？', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        // 删除逻辑处理
        batchDelete(this.multipleSelection.map(t => t.Id)).then(response => {
          if (response.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', response.Message);
          }
        });
      });
    },
    handleEditConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          add(this.temp).then(response => {
            if (response.Code === 2000) {
              this.showNotify('success', 'Common.updateSuccess');
              this.handleFilter();
              this.dialogFormVisible = false;
            } else {
              this.showNotify('error', response.Message);
            }
          });
        }
      });
    },

    handleExport() {
      this.isProcessing = true
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res, '客户产品型号提前天数')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export error:', error)
        this.isProcessing = false
      })
    },

    handleExportTemplate() {
      this.isProcessing = true
      exportExcelTemplate().then(res => {
        exportToExcel(res, '客户产品型号提前天数模板')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export template error:', error)
        this.isProcessing = false
      })
    },

    beforeUpload(file) {
      this.isProcessing = true
      const formData = new FormData()
      formData.append('file', file)
      importExcelData(formData).then(response => {
        if (response.Code === 2000) {
          this.$notify({
            title: '成功',
            message: '导入成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        } else {
          this.$notify({
            title: '错误',
            message: response.Message,
            type: 'error',
            duration: 2000
          })
        }
        this.isProcessing = false
      }).catch((error) => {
        console.error('Import error:', error)
        this.isProcessing = false
      })
      return false
    }
  }
};
</script>
