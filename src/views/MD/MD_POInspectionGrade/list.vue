<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionGrade.MasterEdit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        plain
        @click="handleUpdateMaster"
      >{{ $t("ui.MD.MD_POInspectionGrade.Buttons.MasterEdit") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionGrade.DetailAdd' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-plus"
        @click="handleCreateDetail"
      >{{ $t("ui.MD.MD_POInspectionGrade.Buttons.DetailAdd") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionGrade.DetailEdit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="canNotUpdate"
        @click="handleUpdateDetail"
      >{{ $t("ui.MD.MD_POInspectionGrade.Buttons.DetailEdit") }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_POInspectionGrade.DetailDelete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="canNotDelete"
        @click="handleDeleteDetail"
      >{{ $t("ui.MD.MD_POInspectionGrade.Buttons.DetailDelete") }}</el-button>
    </div>

    <el-table
      ref="multipleGradeTable"
      v-loading="listLoading"
      :data="InspectionGradeList"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%;"
      height="213"
      @row-click="showRowDetail"
    >
      <el-table-column
        width="120px"
        align="center"
        prop="GradeCode"
        :label="$t('ui.MD.MD_POInspectionGrade.GradeCode')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.GradeCode }}</span>
        </template>
      </el-table-column>

      <el-table-column
        width="200px"
        align="center"
        prop="GradeName"
        :label="$t('ui.MD.MD_POInspectionGrade.GradeName')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.GradeName }}</span>
        </template>
      </el-table-column>

      <el-table-column
        width="200"
        align="center"
        prop="OKNum"
        :label="$t('ui.MD.MD_POInspectionGrade.OKNum')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.OKNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        width="200px"
        align="center"
        prop="NextGradeCode"
        :label="$t('ui.MD.MD_POInspectionGrade.NextGradeCode')"
        :formatter="formatGrade"
      />

      <el-table-column
        width="200px"
        align="center"
        prop="NoNum"
        :label="$t('ui.MD.MD_POInspectionGrade.NoNum')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.NoNum }}</span>
        </template>
      </el-table-column>

      <el-table-column
        width="200px"
        align="center"
        prop="NoNextGradeCode"
        :label="$t('ui.MD.MD_POInspectionGrade.NoNextGradeCode')"
        :formatter="formatGrade"
      />
    </el-table>

    <p>
      <span>{{ $t("ui.MD.MD_POInspectionGrade.DetailTitle") }}</span>
    </p>

    <el-table
      ref="multipleGradeDetailTable"
      v-loading="listDetailLoading"
      :data="InspectionGradeDetailList"
      border
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleDetailSelectionChange"
    >
      <el-table-column type="selection" width="40" fixed />
      <el-table-column v-if="false" align="center" :label="主键ID" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.GradeID }}</span>
        </template>
      </el-table-column>

      <el-table-column
        width="120px"
        align="center"
        prop="IQty"
        :label="$t('ui.MD.MD_POInspectionGradeDetailed.IQty')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IQty }}</span>
        </template>
      </el-table-column>

      <el-table-column
        width="120px"
        align="center"
        prop="SampleNum"
        :label="$t('ui.MD.MD_POInspectionGradeDetailed.SampleNum')"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.SampleNum }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 检验等级主数据维护 -->
    <el-dialog
      :title="$t('ui.MD.MD_POInspectionGrade.MasterTitle')"
      :visible.sync="dialogMasterFormVisible"
      top="10vh"
      width="600px"
    >
      <el-form
        ref="masterDataForm"
        :model="formMasterData"
        label-position="right"
        label-width="150px"
      >
        <!-- 等级编号 -->
        <el-form-item :label="$t('ui.MD.MD_POInspectionGrade.GradeCode')" prop="GradeCode">
          <el-input v-model="formMasterData.GradeCode" autocomplete="off" disabled="true" />
        </el-form-item>
        <!-- 等级名称 -->
        <el-form-item :label="$t('ui.MD.MD_POInspectionGrade.GradeName')" prop="GradeName">
          <el-input v-model="formMasterData.GradeName" autocomplete="off" />
        </el-form-item>
        <!-- 连续检验合格批数 -->
        <el-form-item :label="$t('ui.MD.MD_POInspectionGrade.OKNum')" prop="OKNum">
          <el-input-number v-model="formMasterData.OKNum" :min="1" />
        </el-form-item>
        <!-- 下个检验级别 -->
        <el-form-item :label="$t('ui.MD.MD_POInspectionGrade.NextGradeCode')" prop="NextGradeCode">
          <!-- <el-input v-model="formMasterData.NextGradeCode" autocomplete="off" /> -->
          <el-select v-model="formMasterData.NextGradeCode" class="filter-item" filterable>
            <el-option
              v-for="item in InspectionGradeList"
              :key="item.GradeCode"
              :label="item.GradeName"
              :value="item.GradeCode"
            />
          </el-select>
        </el-form-item>
        <!-- 连续检验不合格批数 -->
        <el-form-item :label="$t('ui.MD.MD_POInspectionGrade.NoNum')" prop="NoNum">
          <el-input-number v-model="formMasterData.NoNum" :min="1" />
        </el-form-item>
        <!-- 不合格下个检验级别 -->
        <el-form-item
          :label="$t('ui.MD.MD_POInspectionGrade.NoNextGradeCode')"
          prop="NoNextGradeCode"
        >
          <el-select v-model="formMasterData.NoNextGradeCode" class="filter-item" filterable>
            <el-option
              v-for="item in InspectionGradeList"
              :key="item.GradeCode"
              :label="item.GradeName"
              :value="item.GradeCode"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMasterFormVisible = false">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button type="primary" @click="updateMaster()">
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 等级明细数据维护 -->
    <el-dialog
      :title="$t('ui.MD.MD_POInspectionGrade.DetailTitle')"
      :visible.sync="dialogDetailFormVisible"
      top="10vh"
      width="500px"
    >
      <el-form
        ref="detailDataForm"
        :model="formDetailData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item :label="$t('ui.MD.MD_POInspectionGradeDetailed.IQty')" prop="IQty">
          <el-input-number v-model="formDetailData.IQty" :min="1" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_POInspectionGradeDetailed.SampleNum')" prop="SampleNum">
          <el-input-number v-model="formDetailData.SampleNum" :min="1" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDetailFormVisible = false">
          {{ $t("Common.cancel") }}
        </el-button>
        <el-button
          type="primary"
          @click="detailFormMode === 'Create' ? createDetail() : updateDetail()"
        >
          {{ $t("Common.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  update as updateMaster
} from '@/api/MD/MD_POInspectionGrade';
import {
  fetchDetailList,
  add as addDetail,
  update as updateDetail,
  batchDelete as deleteDetail
} from '@/api/MD/MD_POInspectionGradeDetailed';

import { convertToKeyValue } from '@/utils';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { MessageBox } from 'element-ui'; // 提示框
// import { exportToExcel } from '@/utils/excel-export'
import { formatDate, formatDateTime } from '@/utils'; // 列表内容格式化
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_POInspectionGrade',
  components: { Pagination },
  directives: { waves, permission },
  data() {
    return {
      InspectionGradeList: [],
      InspectionGradeDetailList: [],
      selectedInspectionGrade: '',
      selectedGradeDetailRowsData: [],
      formMasterData: {
        GradeID: '',
        GradeCode: '',
        GradeName: '',
        OKNum: '',
        NextGradeCode: '',
        NoNum: '',
        NoNextGradeCode: ''
      },
      formDetailData: {
        GradeID: null,
        GradeCode: '',
        IQty: '',
        SampleNum: ''
      },
      dialogMasterFormVisible: false,
      dialogDetailFormVisible: false,
      listLoading: true,
      detailFormMode: '',
      listDetailLoading: false,
      downloadLoading: false
    };
  },
  computed: {
    canNotUpdate() {
      var selectRows = this.selectedGradeDetailRowsData || [];
      return selectRows.length !== 1;
    },
    canNotDelete() {
      var selectRows = this.selectedGradeDetailRowsData || [];
      return selectRows.length <= 0;
    }
  },
  created() {
    this.getInspectionGradeList();
  },
  methods: {
    formatGrade: function(row, column, currentValue) {
      console.log(this.InspectionGradeList);

      const fmtOption = this.InspectionGradeList.find(element => {
        return element.GradeCode === currentValue;
      });

      return fmtOption != null
        ? fmtOption.GradeCode + '-' + fmtOption.GradeName
        : '';
    },
    getInspectionGradeList() {
      this.listLoading = true;
      fetchList().then(response => {
        console.log('getInspectionGradeList', response.Data);
        this.InspectionGradeList = response.Data;
        if (response.Data != null && response.Data.length > 0) {
          this.selectedInspectionGrade = response.Data[0].GradeCode;
          this.$refs.multipleGradeTable.setCurrentRow(
            this.InspectionGradeList[0]
          );
          this.formMasterData = Object.assign({}, this.InspectionGradeList[0]); // 对象拷贝

          this.getInspectionGradeDetailList(this.selectedInspectionGrade);
        }

        this.listLoading = false;
      });
    },
    showRowDetail(row) {
      this.formMasterData = Object.assign({}, row); // 对象拷贝
      this.getInspectionGradeDetailList(row.GradeCode);
    },
    getInspectionGradeDetailList(gradeCode) {
      this.listDetailLoading = true;
      fetchDetailList({ gradeCode: gradeCode }).then(response => {
        this.InspectionGradeDetailList = response.Data;
        this.listDetailLoading = false;
      });
    },
    handleDetailSelectionChange(selection) {
      this.selectedGradeDetailRowsData = selection;
    },
    handleUpdateMaster() {
      // this.resetMasterFormData()
      // this.formMasterData = Object.assign({}, this.sel) // 对象拷贝
      this.dialogMasterFormVisible = true;
      this.$nextTick(() => {
        this.$refs['masterDataForm'].clearValidate(); // 清除校验
      });
    },
    updateMaster() {
      updateMaster(this.formMasterData).then(response => {
        this.dialogMasterFormVisible = false;
        this.$notify({
          title: this.$t('Common.success'),
          message: this.$t('Common.operationSuccess'),
          type: 'success',
          duration: 2000
        });
        this.getInspectionGradeList(this.selectedInspectionGrade);
      });
    },
    resetMasterFormData() {
      this.formMasterData = {
        GradeID: '',
        GradeCode: '',
        GradeName: '',
        OKNum: '',
        NextGradeCode: '',
        NoNum: '',
        NoNextGradeCode: ''
      };
    },
    resetDetailFormData() {
      this.formDetailData = {
        GradeID: null,
        GradeCode: '',
        IQty: '',
        SampleNum: ''
      };
    },
    handleCreateDetail() {
      console.log(this.formMasterData);
      this.resetDetailFormData();
      this.detailFormMode = 'Create';
      this.dialogDetailFormVisible = true;
      this.$nextTick(() => {
        this.$refs['detailDataForm'].clearValidate(); // 清除校验
      });
    },
    createDetail() {
      // 添加明细行
      this.formDetailData.GradeCode = this.formMasterData.GradeCode;
      console.log(this.formDetailData);
      this.$refs['detailDataForm'].validate(valid => {
        if (valid) {
          // 确认添加
          addDetail(this.formDetailData).then(response => {
            this.dialogDetailFormVisible = false;
            this.$notify({
              title: this.$t('Common.success'),
              message: this.$t('Common.operationSuccess'),
              type: 'success',
              duration: 2000
            });
            this.getInspectionGradeDetailList(this.selectedInspectionGrade);
          });
        }
      });
    },
    handleUpdateDetail() {
      this.resetDetailFormData();
      this.detailFormMode = 'Update';
      this.dialogDetailFormVisible = true;
      this.formDetailData = Object.assign(
        {},
        this.selectedGradeDetailRowsData[0]
      ); // 对象拷贝
      this.$nextTick(() => {
        this.$refs['detailDataForm'].clearValidate(); // 清除校验
      });
    },
    updateDetail() {
      // 添加明细行
      console.log(this.formDetailData);
      this.$refs['detailDataForm'].validate(valid => {
        if (valid) {
          updateDetail(this.formDetailData).then(res => {
            if (res.Code === 2000) {
              this.showNotify('success', 'Common.operationSuccess');
            } else {
              this.showNotify('error', res.Message);
            }
            this.getInspectionGradeDetailList(this.selectedInspectionGrade);
            this.dialogDetailFormVisible = false;
          });
        }
      });
    },
    handleDeleteDetail() {
      // 删除功能，可能有bug，待测试
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$t('Common.tip'),
        {
          confirmButtonText: this.$t('Common.confirm'),
          cancelButtonText: this.$t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const selectRows = this.$refs.multipleGradeDetailTable.selection;
        const selectNums = selectRows.map(v => v.GradeID); // 第二个参数给一个固定的ID值
        // 删除逻辑处理
        deleteDetail(selectNums).then(() => {
          this.$notify({
            title: this.$t('Common.success'),
            message: this.$t('Common.deleteSuccess'),
            type: 'success',
            duration: 2000
          });
          this.getInspectionGradeDetailList(this.selectedInspectionGrade);
        });
      });
    }
  }
};
</script>

