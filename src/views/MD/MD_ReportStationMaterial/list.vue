<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="'系统处理中，请稍后...'">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        placeholder="关键字"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ReportStationMaterial.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ReportStationMaterial.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >编辑</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ReportStationMaterial.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >删除</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ReportStationMaterial.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >导出</el-button>
      <el-upload
        v-permission="{ name: 'MD.MD_ReportStationMaterial.Import' }"
        class="filter-item import-upload"
        action=""
        :http-request="httpRequest"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button
          v-waves
          size="small"
          type="primary"
          icon="el-icon-upload2"
        >导入</el-button>
      </el-upload>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ReportStationMaterial.Import' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-download"
        @click="handleExportModel"
      >下载模板</el-button>
    </div>

    <el-table
      ref="table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="选择"
        type="selection"
        align="center"
        width="40"
        fixed
      />

      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        label="工作中心名称"
        prop="WorkCenterName"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="工作中心编码"
        prop="WorkCenterCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="站点编码"
        prop="StationCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.StationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="站点名称"
        prop="StationName"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.StationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="物料编码"
        prop="MaterialCode"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MaterialCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="物料描述"
        prop="MaterialDesc"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MaterialDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="数量"
        prop="Quantity"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="单位"
        prop="Unit"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Unit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="站点序号"
        prop="StationSort"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.StationSort }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否多站点配送"
        prop="IsMultistation"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.IsMultistation }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="创建人"
        prop="CUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="修改人"
        prop="MUser"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="修改时间"
        prop="MTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.MTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="formTitle" :visible.sync="dialogFormVisible" width="65%">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="150px"
      >
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="工作中心编码" prop="WorkCenterCode">
              <el-select v-model="formData.WorkCenterCode" filterable placeholder="请选择工作中心" @change="handleWorkCenterChange">
                <el-option v-for="item in workCenterOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点编码" prop="StationCode">
              <el-select v-model="formData.StationCode" filterable placeholder="请选择站点" @change="handleStationChange">
                <el-option v-for="item in stationOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="MaterialCode">
              <el-input v-model="formData.MaterialCode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料描述" prop="MaterialDesc">
              <el-input v-model="formData.MaterialDesc" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="数量" prop="Quantity">
              <el-input-number v-model="formData.Quantity" :min="0" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="Unit">
              <el-input v-model="formData.Unit" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="站点序号" prop="StationSort">
              <el-input-number v-model="formData.StationSort" :min="0" :precision="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否多站点配送" prop="IsMultistation">
              <el-switch v-model="formData.IsMultistation" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="备注" prop="Unit">
              <el-input v-model="formData.Remark" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-waves
          type="primary"
          icon="el-icon-check"
          @click="formMode === 'Create' ? createData() : updateData()"
        >保存</el-button>
        <el-button v-waves @click="dialogFormVisible = false">
          关闭
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves';
import Pagination from '@/components/Pagination/index';
import {
  fetchList,
  add,
  update,
  batchDelete,
  exportToExcelFile,
  importExcelToData,
  exportToExcelModel
} from '@/api/MD/MD_ReportStationMaterial';
import permission from '@/directive/permission/index.js'; // 权限判断指令
import { exportToExcel } from '@/utils/excel-export';
import XLSX from 'xlsx';
import { fetchList as fetchWorkCenterStationList } from '@/api/MD/MD_WorkCenterStation';

export default {
  name: 'ReportStationMaterial',
  directives: {
    waves,
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      isProcessing: false,
      total: 0,
      formTitle: '',
      formMode: '',
      dialogFormVisible: false,
      edit: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      formData: {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        ProcessNo: null,
        ProcessShortText: '',
        StationCode: '',
        StationName: '',
        MaterialCode: '',
        MaterialDesc: '',
        Quantity: null,
        Unit: '',
        StationSort: null,
        IsMultistation: false
      },
      rules: {
        WorkCenterCode: [
          {
            required: true,
            message: '此项不能为空',
            trigger: 'change'
          }
        ],
        WorkCenterName: [
          {
            required: true,
            message: '此项不能为空',
            trigger: 'blur'
          }
        ],
        StationCode: [
          {
            required: true,
            message: '此项不能为空',
            trigger: 'change'
          }
        ],
        StationName: [
          {
            required: true,
            message: '此项不能为空',
            trigger: 'blur'
          }
        ],
        MaterialCode: [
          {
            required: true,
            message: '此项不能为空',
            trigger: 'blur'
          }
        ],
        MaterialDesc: [
          {
            required: true,
            message: '此项不能为空',
            trigger: 'blur'
          }
        ]
      },
      multipleSelection: [],
      workCenterOptions: [],
      stationOptions: []
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
    this.getWorkCenterOptions();
  },
  methods: {
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(result => {
        if (result.Code === 2000) {
          this.list = result.Data.items;
          this.total = result.Data.total;
        } else {
          this.$message.error(result.Message || '未知错误');
        }
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.getList();
    },
    handleCreate() {
      this.resetDataForm();
      this.formTitle = '添加';
      this.formMode = 'Create';
      this.dialogFormVisible = true;
      this.stationOptions = [];
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate(); // 清除校验
      });
    },
    handleUpdate() {
      this.resetDataForm();
      this.formData = Object.assign({}, this.multipleSelection[0]); // 对象拷贝
      this.formTitle = '编辑';
      this.formMode = 'Update';
      this.dialogFormVisible = true;
      // 如果有工作中心编码，加载对应的站点
      if (this.formData.WorkCenterCode) {
        this.getStationOptions(this.formData.WorkCenterCode);
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate(); // 清除校验
      });
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.isProcessing = true;
          add(this.formData).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false;
              this.$notify({
                title: '成功',
                message: '操作成功',
                type: 'success',
                duration: 2000
              });
              this.getList();
            } else {
              this.$message.error(response.Message || '未知错误');
            }
            this.isProcessing = false;
          }).catch(() => {
            this.isProcessing = false;
          });
        }
      });
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.isProcessing = true;
          update(this.formData).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false;
              this.$notify({
                title: '成功',
                message: '操作成功',
                type: 'success',
                duration: 2000
              });
              this.getList();
            } else {
              this.$message.error(response.Message || '未知错误');
            }
            this.isProcessing = false;
          }).catch(() => {
            this.isProcessing = false;
          });
        }
      });
    },
    resetDataForm() {
      this.formData = {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        ProcessNo: null,
        ProcessShortText: '',
        StationCode: '',
        StationName: '',
        MaterialCode: '',
        MaterialDesc: '',
        Quantity: null,
        Unit: '',
        StationSort: null,
        IsMultistation: false
      };
    },
    handleDelete() {
      this.$confirm(
        '确认要批量删除选中的记录吗?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        const ids = this.multipleSelection.map(v => v.Id);
        this.isProcessing = true;
        batchDelete(ids).then(res => {
          if (res.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            });
            this.getList();
          } else {
            this.$message.error(res.Message || '未知错误');
          }
          this.isProcessing = false;
        }).catch(() => {
          this.isProcessing = false;
        });
      });
    },
    handleExport() {
      this.isProcessing = true;
      exportToExcelFile({ keyword: this.listQuery.keyword }).then(res => {
        exportToExcel(res.data, '报工站点物料');
        this.isProcessing = false;
      }).catch(() => {
        this.isProcessing = false;
      });
    },
    handleExportModel() {
      this.isProcessing = true;
      exportToExcelModel().then(res => {
        exportToExcel(res.data, '报工站点物料模板');
        this.isProcessing = false;
      }).catch(() => {
        this.isProcessing = false;
      });
    },
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        this.$message.error('只支持Excel文件');
        return false;
      }
      return true;
    },
    httpRequest({ file }) {
      this.isProcessing = true;
      const reader = new FileReader();
      reader.onload = e => {
        const data = e.target.result;
        const fixedData = this.fixData(data);
        const workbook = XLSX.read(btoa(fixedData), { type: 'base64' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const excelData = XLSX.utils.sheet_to_json(worksheet);
        importExcelToData(excelData).then(res => {
          if (res.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '导入成功',
              type: 'success',
              duration: 2000
            });
            this.getList();
          } else {
            this.$message.error(res.Message || '未知错误');
          }
          this.isProcessing = false;
        }).catch(() => {
          this.isProcessing = false;
        });
      };
      reader.readAsArrayBuffer(file);
    },
    fixData(data) {
      let o = '';
      let l = 0;
      const w = 10240;
      for (;l < data.byteLength / w;++l) o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w, l * w + w)));
      o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w)));
      return o;
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        this.listQuery.sort = prop + (order === 'ascending' ? ' asc' : ' desc');
      } else {
        this.listQuery.sort = '';
      }
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    showNotify(type, title) {
      this.$notify({
        title: '成功',
        message: title,
        type: type,
        duration: 2000
      });
    },
    getWorkCenterOptions() {
      const params = {
        PageNumber: 1,
        PageSize: 999999 // 获取所有数据
      };
      fetchWorkCenterStationList(params).then(response => {
        if (response.Code === 2000 && response.Data && response.Data.items) {
          // 提取出唯一的工作中心
          const workCenters = [];
          const workCenterSet = new Set();
          response.Data.items.forEach(item => {
            if (!workCenterSet.has(item.WorkCenterCode)) {
              workCenterSet.add(item.WorkCenterCode);
              workCenters.push({
                code: item.WorkCenterCode,
                name: item.WorkCenterName
              });
            }
          });
          this.workCenterOptions = workCenters;
        }
      });
    },
    getStationOptions(workCenterCode) {
      const params = {
        PageNumber: 1,
        PageSize: 999999,
        WorkCenterCode: workCenterCode
      };
      fetchWorkCenterStationList(params).then(response => {
        if (response.Code === 2000 && response.Data && response.Data.items) {
          // 筛选出该工作中心对应的站点
          const stations = response.Data.items
            .map(item => ({
              code: item.StationCode,
              name: item.StationName
            }));
          this.stationOptions = stations;
        }
      });
    },
    handleWorkCenterChange(val) {
      if (val) {
        const selectedWorkCenter = this.workCenterOptions.find(item => item.code === val);
        if (selectedWorkCenter) {
          this.formData.WorkCenterName = selectedWorkCenter.name;
        }
        this.formData.StationCode = '';
        this.formData.StationName = '';
        this.getStationOptions(val);
      } else {
        this.stationOptions = [];
        this.formData.WorkCenterName = '';
      }
    },
    handleStationChange(val) {
      if (val) {
        const selectedStation = this.stationOptions.find(item => item.code === val);
        if (selectedStation) {
          this.formData.StationName = selectedStation.name;
        }
      } else {
        this.formData.StationName = '';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 15px;
}
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-right: 10px;
  }
  .import-upload {
    display: inline-block;
    margin-right: 10px;
  }
}
</style>
