<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.StockManWay"
        size="small"
        filterable
        placeholder="库存管理方式"
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="item in StockManWayOptions"
          :key="item.EnumKey"
          :label="item.EnumValue"
          :value="item.EnumKey"
        />
      </el-select>
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Item.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Item.Enable' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        :disabled="deletable"
        @click="handleEnable"
      >启用序列号</el-button>
      <!-- <el-button v-permission="{ name: 'MD.MD_Item.SyncProductCategory' }" v-waves class="filter-item" type="success"
        icon="el-icon-download" size="small" @click="handleSyncProductCategory">{{ $t('ui.MD.MD_Item.syncProductCategory') }}
      </el-button>
      <el-button v-waves v-permission="{ name: 'MD.MD_Item.SyncMaterial' }" class="filter-item" type="success"
        icon="el-icon-download" size="small" @click="handleSyncMaterial">{{ $t('ui.MD.MD_Item.syncMaterial') }}</el-button> -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />

      <el-table-column type="index" align="center" width="50" label="行号" />
      <!-- <el-table-column v-if="false" :label="$t('ui.MD.M_Item.ItemID')" prop="ItemID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemID}}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('ui.MD.MD_Item.ItemCode')" prop="ItemCode" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.ItemName')" prop="ItemName" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料组" prop="MATKL"  width="120"  align="center"  show-overflow-tooltip/> -->
      <el-table-column label="外部物料组" prop="EXTWG" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="库存管理方式名称" prop="StockManWayName" align="center" width="120" />
      <el-table-column label="功率" prop="Power" align="center" show-overflow-tooltip />
      <el-table-column label="是否包装物料" prop="IsPackaging" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsPackaging | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否确认" prop="IsConfirm" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsConfirm | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <!--        <el-table-column :label="$t('ui.MD.MD_Item.ItmsGrpCode')" prop="ItmsGrpCode"   align="center" width="140"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpCode}}</span> </template> </el-table-column>-->
      <!--        <el-table-column :label="$t('ui.MD.MD_Item.ItmsGrpName')" prop="ItmsGrpName"   align="center" width="220"> <template slot-scope="scope"> <span>{{ scope.row.ItmsGrpName}}</span> </template> </el-table-column>-->
      <!-- <el-table-column :label="$t('ui.MD.MD_Item.Unit')" prop="Unit" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Unit}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.IsFreeTax')" prop="IsFreeTax" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsFreeTax|yesnoFilter}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.StockingQty')" prop="StockingQty" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.StockingQty}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.AgeQty')" prop="AgeQty" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.AgeQty}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.ULimitWarning')" prop="ULimitWarning" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.ULimitWarning}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.LLimitWarning')" prop="LLimitWarning" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.LLimitWarning}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.MD.MD_Item.RegionCode')" prop="RegionCode" align="center"
        width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.RegionCode}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.ReqWarning')" prop="ReqWarning" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.ReqWarning}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('ui.MD.MD_Item.IsBarCode')" prop="IsBarCode" align="center" width="190">
        <template slot-scope="scope">
          <span>{{ scope.row.IsBarCode|yesnoFilter}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.DType')" prop="DType" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.DType}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark}}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column v-if="false" :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.CTime')" prop="CTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CTime|datetime}}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column v-if="false" :label="$t('Common.IsDelete')" prop="IsDelete" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.IsDelete}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MUser')" prop="MUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MUser}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.MTime')" prop="MTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.MTime}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DUser')" prop="DUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DUser}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" :label="$t('Common.DTime')" prop="DTime" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.DTime}}</span>
        </template>
      </el-table-column> -->
      <!-- <template slot="append">
            <infinite-loading force-use-infinite-wrapper=".el-table__body-wrapper" @infinite="handleInfinite" spinner="spiral">
            </infinite-loading>
      </template>-->
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="$t('Common.edit')" :visible.sync="dialogFormVisible">
      <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item :label="$t('ui.MD.MD_Item.ItemCode')" prop="ItemCode">
          <el-input v-model="temp.ItemCode" disabled />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.ItemName')" prop="ItemName">
          <el-input v-model="temp.ItemName" disabled />
        </el-form-item>
        <!-- <el-form-item :label="$t('ui.MD.MD_Item.StockingQty')" prop="StockingQty">
          <el-input-number v-model="temp.StockingQty" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.AgeQty')" prop="AgeQty">
          <el-input-number v-model="temp.AgeQty" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.ULimitWarning')" prop="ULimitWarning">
          <el-input-number v-model="temp.ULimitWarning" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.LLimitWarning')" prop="LLimitWarning">
          <el-input-number v-model="temp.LLimitWarning" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.ReqWarning')" prop="IsFreeTax">
          <el-input-number v-model="temp.ReqWarning" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.RegionCode')" prop="RegionCode">
          <el-select v-model="temp.RegionCode">
            <el-option v-for="item in regionOptions" :key="item.RegionCode" :label="item.RegionName"
              :value="item.RegionCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_Item.IsFreeTax')" prop="IsFreeTax">
          <el-checkbox v-model="temp.IsFreeTax" />
        </el-form-item>-->
        <!-- <el-form-item :label="$t('ui.MD.MD_Item.IsBarCode')" prop="IsBarCode">
          <el-checkbox v-model="temp.IsBarCode" />
        </el-form-item> -->
        <!-- <el-form-item :label="$t('ui.MD.MD_Item.DType')" prop="DType">
          <el-input v-model="temp.DType" />
        </el-form-item>
        <el-form-item :label="$t('Common.Remark')" prop="Remark">
          <el-input type="textarea" v-model="temp.Remark" />
        </el-form-item> -->
        <el-form-item label="库存管理方式">
          <el-select
            v-model="temp.StockManWay"
            placeholder="库存管理方式"
            filterable
            class="filter-item"
            style="width: 100%;"
            @change="changeStockManWay"
          >
            <el-option
              v-for="item in StockManWayOptions"
              :key="item.EnumKey"
              :label="item.EnumValue"
              :value="item.EnumKey"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="功率">
          <el-input v-model="temp.Power" />
        </el-form-item>
        <el-form-item label="是否包装物料">
          <el-switch
            v-model="temp.IsPackaging"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="是否确认">
          <el-switch
            v-model="temp.IsConfirm"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            :disabled="!temp.IsPackaging"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button v-waves type="primary" @click="handleEditConfirm">{{ $t('Common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  updateOrInsert,
  fetchAllList,
  fetchNextPage,
  syncProductCategory,
  fetchPage,
  syncMaterial,
  GetDictionary,
  UpdateOrInsertStockManWay
} from '@/api/MD/MD_Item';
import {
  fetchAllList as fetchAllRegionList
} from '@/api/MD/MD_Region';
import {
  convertToKeyValue
} from '@/utils';
// import { exportToExcel } from '@/utils/excel-export'
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页

import InfiniteLoading from 'vue-infinite-loading';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Item',
  components: {
    Pagination,
    InfiniteLoading
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        keyword: '',
        PageSize: 10,
        PageNumber: 1,
        StockManWay: ''
      },
      StockManWayOptions: [],
      isProcessing: false,
      regionOptions: [],
      multipleSelection: [],
      dialogFormVisible: false,
      rules: {
        // StockingQty: [
        //   {
        //     required: true,
        //     validator: this.QtyValidator,
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   }
        // ],
        // AgeQty: [
        //   {
        //     required: true,
        //     validator: this.QtyValidator,
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   }
        // ],
        // ULimitWarning: [
        //   {
        //     required: true,
        //     validator: this.QtyValidator,
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   }
        // ],
        // LLimitWarning: [
        //   {
        //     required: true,
        //     validator: this.QtyValidator,
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   }
        // ],
        // ReqWarning: [
        //   {
        //     required: true,
        //     validator: this.QtyValidator,
        //     trigger: "change"
        //   },
        //   {
        //     required: true,
        //     message: this.$i18n.t("Common.IsRequired"),
        //     trigger: "change"
        //   }
        // ]
      },
      temp: {
        ItemID: undefined,
        // UUID: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        StockManWay: '',
        StockManWayName: '',
        Power: '',
        IsPackaging: false,
        IsConfirm: false
        // Unit: undefined,
        // IsFreeTax: false,
        // StockingQty: 0,
        // AgeQty: 0,
        // ULimitWarning: 0,
        // LLimitWarning: 0,
        // RegionCode: undefined,
        // ReqWarning: 0,
        // IsBarCode: false,
        // Remark: undefined,
        // DType: undefined
      },
      firstID: ''
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.handleFilter();
    this.GetDictionary()
  },
  methods: {
    getPage() {
      this.listLoading = true;
      fetchNextPage({
        keyword: this.listQuery.keyword,
        pageSize: 30,
        firstID: null
      }).then(response => {
        if (response.Code === 2000) {
          this.list = this.list.concat(response.Data);
          this.firstID = this.list[this.list.length - 1].UUID;
        } else {
          this.showNotify('error', response.Message);
        }
        this.listLoading = false;
      });
    },
    getList() {
      this.listLoading = true;
      fetchPage(this.listQuery)
        .then(response => {
          if (response.Code === 2000) {
            this.list = response.Data.items;
            this.total = response.Data.total;
          } else {
            this.showNotify('error', response.Message);
          }
          this.listLoading = false;
        })
        .catch(err => {
          console.log(err);
          this.listLoading = false;
        });
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    getRegionList() {
      fetchAllRegionList().then(response => {
        if (response.Code === 2000) {
          this.regionOptions = response.Data;
        }
      });
    },
    resetTemp() {
      this.temp = {
        ItemID: undefined,
        ItemCode: undefined,
        ItemName: undefined,
        StockManWay: '',
        StockManWayName: '',
        Power: '',
        IsPackaging: false,
        IsConfirm: false
        // Unit: undefined,
        // IsFreeTax: false,
        // StockingQty: 0,
        // AgeQty: 0,
        // ULimitWarning: 0,
        // LLimitWarning: 0,
        // RegionCode: undefined,
        // ReqWarning: 0,
        // IsBarCode: false,
        // Remark: undefined,
        // DType: undefined
      };
    },
    handleFilter() {
      this.list = [];
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleEdit() {
      this.resetTemp();
      Object.assign(this.temp, this.multipleSelection[0]);
      this.dialogFormVisible = true;
    },
    handleEditConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateOrInsert(this.temp).then(response => {
            if (response.Code === 2000) {
              this.showNotify('success', 'Common.updateSuccess');
              this.handleFilter();
              this.dialogFormVisible = false;
            } else {
              this.showNotify('error', response.Message);
            }
          });
        }
      });
    },
    handleInfinite($state) {
      this.listLoading = true;
      fetchNextPage({
        keyword: this.listQuery.keyword,
        pageSize: 30,
        firstID: this.firstID
      }).then(response => {
        if (response.Code === 2000) {
          var listTmp = Object.assign(response.Data);
          this.list.forEach(element => {
            listTmp.splice(
              listTmp.findIndex(item => item.ItemCode == element.ItemCode),
              1
            );
          });
          if (listTmp && listTmp.length > 0) {
            this.list = this.list.concat(response.Data);
          }
          this.firstID = this.list[this.list.length - 1].UUID;
          $state.loaded();
        } else {
          this.showNotify('error', response.Message);
          $state.complete();
        }
        this.listLoading = false;
      });
    },
    handleSyncProductCategory() {
      this.isProcessing = true;
      // 同步产品类别
      this.showNotify('warning', 'Common.syncStart');
      syncProductCategory()
        .then(response => {
          if (response && response.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
          this.isProcessing = false;
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    handleSyncMaterial() {
      this.isProcessing = true;
      // 同步物料信息
      var ids = this.multipleSelection.map(x => x.ItemCode);
      this.showNotify('warning', 'Common.syncStart');
      syncMaterial(ids)
        .then(response => {
          if (response && response.Code === 2000) {
            this.showNotify('success', 'ui.MD.MD_Item.syncMaterialSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
          this.isProcessing = false;
        })
        .catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
    },
    // 数据字典
    GetDictionary() {
      GetDictionary().then(res => {
        if (res.Code === 2000) {
          this.StockManWayOptions = res.Data
        }
      })
    },
    changeStockManWay(e) {
      const obj = this.StockManWayOptions.find(v => v.EnumKey === e);
      this.temp.StockManWayName = obj.EnumValue;
      this.$forceUpdate();
    },
    handleEnable() {
      const selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        UpdateOrInsertStockManWay(selectRows).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.operationSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', 'Common.operationFailed');
          }
        })
      }
    }
  }
};
</script>
