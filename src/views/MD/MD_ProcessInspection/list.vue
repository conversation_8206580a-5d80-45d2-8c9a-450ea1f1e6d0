<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProcessInspection.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProcessInspection.Edit' }"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_ProcessInspection.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="生产线编号" prop="ProductionLineCode" align="center" show-overflow-tooltip />
      <el-table-column label="生产线名称" prop="ProductionLineDes" align="center" show-overflow-tooltip />
      <el-table-column label="工序编号" prop="WorkingProcedureCode" align="center" show-overflow-tooltip />
      <el-table-column label="工序描述" prop="WorkingProcedureDes" align="center" show-overflow-tooltip />
      <el-table-column label="检验项" prop="InspectionItem" align="center" show-overflow-tooltip />
      <el-table-column label="合格值范围上限" prop="UpperLimit" align="center" show-overflow-tooltip />
      <el-table-column label="合格值范围下限" prop="LowerLimit" align="center" show-overflow-tooltip />
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" show-overflow-tooltip />

    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item label="生产线编号" prop="ProductionLineCode">
          <el-select
            v-model="temp.ProductionLineCode"
            placeholder="请选择"
            filterable
            style="width: 100%;"
            @change="changeProductionLine"
          >
            <el-option
              v-for="item in ProductionLineOptions"
              :key="item.ProductionLineCode"
              :label="item.ProductionLineCode+'-'+item.ProductionLineDes"
              :value="item.ProductionLineCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生产线名称">
          <el-input v-model="temp.ProductionLineDes" disabled />
        </el-form-item>
        <el-form-item label="工序编号" prop="WorkingProcedureCode">
          <el-select
            v-model="temp.WorkingProcedureCode"
            placeholder="请选择"
            filterable
            style="width: 100%;"
            :disabled="WorkingProcedureDisabled"
            @change="changeWorkingProcedure"
          >
            <el-option
              v-for="item in WorkingProcedureOptions"
              :key="item.WorkingProcedureCode"
              :label="item.WorkingProcedureCode+'-'+item.WorkingProcedureDes"
              :value="item.WorkingProcedureCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工序描述">
          <el-input v-model="temp.WorkingProcedureDes" disabled />
        </el-form-item>
        <el-form-item label="检验项" prop="InspectionItem">
          <el-input v-model="temp.InspectionItem" />
        </el-form-item>
        <el-form-item label="合格值范围上限" prop="UpperLimit">
          <el-input-number v-model="temp.UpperLimit" :precision="2" :step="0.1" :max="10000" />
        </el-form-item>
        <el-form-item label="合格值范围下限" prop="LowerLimit">
          <el-input-number v-model="temp.LowerLimit" :precision="2" :step="0.1" :max="10000" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import adaptive from '@/directive/el-table/adaptive'
import Pagination from '@/components/Pagination/index'
import {
  exportToExcel
} from '@/utils/excel-export'
import {
  fetchList,
  add,
  update,
  batchDelete,
  GetProductionLine,
  GetWorkingProcedure
} from '@/api/MD/MD_ProcessInspection'
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_ProcessInspection',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      temp: {
        ProductionLineCode: '',
        ProductionLineDes: '',
        WorkingProcedureCode: '',
        WorkingProcedureDes: '',
        InspectionItem: '',
        UpperLimit: '',
        LowerLimit: '',
        Remark: ''
      },
      ProductionLineOptions: [],
      WorkingProcedureOptions: [],
      edit: false,
      multipleSelection: [],
      rules: {
        ProductionLineCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        WorkingProcedureCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        InspectionItem: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        UpperLimit: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        LowerLimit: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      WorkingProcedureDisabled: true
    }
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1
    },
    deletable() {
      return this.multipleSelection.length === 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false
        })
    },
    handleFilter() { // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    resetFormData() {
      this.temp = {
        ProductionLineCode: '',
        ProductionLineDes: '',
        WorkingProcedureCode: '',
        WorkingProcedureDes: '',
        InspectionItem: '',
        UpperLimit: '',
        LowerLimit: '',
        Remark: ''
      }
    },
    handleCreate() {
      this.resetFormData();
      this.getProductionLine();
      this.title = this.$t('Common.add');
      this.dialogVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleEdit() {
      this.resetFormData();
      this.getProductionLine();
      this.title = this.$t('Common.edit');
      this.dialogVisible = true;
      this.edit = true;
      Object.assign(this.temp, this.multipleSelection[0]);
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate() // 清除校验
      })
    },
    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          console.log(0, this.temp.UpperLimit < this.temp.LowerLimit);
          if (this.temp.UpperLimit < this.temp.LowerLimit) {
            this.showNotify('warning', '合格值范围的上限需要大于等于下限');
            return
          }
          this.startLoading();
          if (this.edit === false) {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }).then(() => {
        const arrRowsID = selectRows.map(v => v.ID);
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList();
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      })
    },

    handleClose() {
      this.dialogVisible = false;
      this.handleFilter()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order != undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    getProductionLine() {
      GetProductionLine().then(res => {
        if (res.Code === 2000) {
          this.ProductionLineOptions = res.Data
        }
      })
    },
    changeProductionLine(e) {
      const obj = this.ProductionLineOptions.find(v => v.ProductionLineCode === e);
      this.temp.ProductionLineDes = obj.ProductionLineDes;
      this.WorkingProcedureDisabled = false;
      this.getWorkingProcedure()
    },
    getWorkingProcedure() {
      const query = {
        LineNo: this.temp.ProductionLineCode
      };
      GetWorkingProcedure(query).then(res => {
        if (res.Code === 2000) {
          this.WorkingProcedureOptions = res.Data
        }
      })
    },
    changeWorkingProcedure(e) {
      const obj = this.WorkingProcedureOptions.find(v => v.WorkingProcedureCode === e);
      this.temp.WorkingProcedureDes = obj.WorkingProcedureDes
    }
  }
}
</script>
