<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        class="filter-item"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        clearable
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_FreightMileage.Add' }"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_FreightMileage.Delete' }"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_FreightMileage.Export' }"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="物流供应商编号" prop="SupplierCode" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="物流供应商名称" prop="SupplierName" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="省" prop="ProvinceName" align="center" show-overflow-tooltip />
      <el-table-column label="市" prop="CityName" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="地区" prop="RegionName" align="center" show-overflow-tooltip/> -->
      <el-table-column label="运费结算地址" prop="SettlementAdd" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="零担时效(天）" prop="Prescription" align="center" width="160" show-overflow-tooltip />
      <el-table-column label="结算里程" prop="Mileage" align="center" show-overflow-tooltip />
      <el-table-column label="到达天数" prop="Arrival" align="center" show-overflow-tooltip />
      <el-table-column label="回单天数" prop="Receipt" align="center" show-overflow-tooltip />
      <el-table-column label="发货人" prop="UserName" align="center" show-overflow-tooltip />
      <el-table-column label="是否直发" prop="IsZF" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.IsZF | yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" align="center" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">

        <el-form-item label="物流供应商编码" prop="SupplierCode">
          <el-select v-model="temp.SupplierCode" filterable style="width: 100%;" @change="getWhs">
            <el-option
              v-for="item in WhsList"
              :key="item.SupplierCode"
              :value="item.SupplierCode"
              :label="item.SupplierCode+'-'+item.SupplierName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物流供应商名称">
          <el-input v-model="temp.SupplierName" disabled />
        </el-form-item>
        <el-form-item label="省" prop="Province">
          <el-select
            v-model="temp.Province"
            filterable
            style="width: 100%;"
            @change="getChangeProvince(temp.Province,1)"
          >
            <el-option v-for="item in ProvinceList" :key="item.Id" :value="item.Id" :label="item.Name" />
          </el-select>
        </el-form-item>
        <el-form-item label="市" prop="City">
          <el-select v-model="temp.City" filterable style="width: 100%;" @change="getChangeCity(temp.City,1)">
            <el-option v-for="item in CityList" :key="item.Id" :value="item.Id" :label="item.Name" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="地区" prop="Region">
          <el-select v-model="temp.Region" filterable @change="getChangeRegion(temp.Region)" style="width: 100%;">
            <el-option v-for="item in RegionList" :key="item.Id" :value="item.Id" :label="item.Name" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="运费结算地址" prop="SettlementAdd">
          <el-input v-model="temp.SettlementAdd" />
        </el-form-item>
        <el-form-item label="零担时效(天）" prop="Prescription">
          <el-input v-model="temp.Prescription" />
        </el-form-item>
        <el-form-item label="结算里程" prop="Mileage">
          <el-input v-model="temp.Mileage" />
        </el-form-item>
        <el-form-item label="到达天数" prop="Arrival">
          <el-input v-model="temp.Arrival" />
        </el-form-item>
        <el-form-item label="回单天数" prop="Receipt">
          <el-input v-model="temp.Receipt" />
        </el-form-item>
        <el-form-item label="发货人" prop="UserCode">
          <el-select v-model="temp.UserCode" filterable style="width: 100%;" @change="getChangeUserName">
            <el-option
              v-for="item in AllUser"
              :key="item.LoginAccount"
              :value="item.LoginAccount"
              :label="item.LoginAccount+'-'+item.UserName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否直发">
          <el-switch
            v-model="temp.IsZF"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.Remark" placeholder="" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import adaptive from '@/directive/el-table/adaptive'
import Pagination from '@/components/Pagination/index'
import {
  exportToExcel
} from '@/utils/excel-export'
import {
  fetchList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  GetXZ_SAP,
  GetSRM_WLSupplierInfo,
  getProvinceCityRegion
} from '@/api/MD/MD_FreightMileage'
import {
  fetchAllUser
} from '@/api/Sys/Sys_User'
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_FreightMileage',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      listLoading: false,
      isProcessing: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      temp: {
        SupplierCode: '',
        SupplierName: '',
        Province: '',
        ProvinceName: '',
        City: '',
        CityName: '',
        Region: '',
        RegionName: '',
        SettlementAdd: '',
        Prescription: '',
        Mileage: '',
        Arrival: '',
        Receipt: '',
        IsZF: true,
        Remark: '',
        UserName: '',
        UserCode: ''
      },
      WhsList: [],
      ProvinceList: [],
      CityList: [],
      RegionList: [],
      edit: false,
      multipleSelection: [],
      AllUser: [],
      rules: {
        SupplierCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        Province: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        City: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        Region: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }],
        SettlementAdd: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Prescription: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Mileage: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Arrival: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        Receipt: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'blur'
        }],
        UserCode: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      }
    }
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1
    },
    deletable() {
      return this.multipleSelection.length === 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false
        })
    },
    handleFilter() { // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    resetFormData() {
      this.temp = {
        SupplierCode: '',
        SupplierName: '',
        Province: '',
        City: '',
        Region: '',
        SettlementAdd: '',
        Prescription: '',
        Mileage: '',
        Arrival: '',
        Receipt: '',
        IsZF: true,
        Remark: '',
        UserName: '',
        UserCode: ''
      }
    },
    fetchWhs() {
      GetSRM_WLSupplierInfo().then(res => {
        if (res.Code === 2000) {
          this.WhsList = res.Data
        }
      })
    },
    getWhs(e) {
      const obj = this.WhsList.find(v => v.SupplierCode === e);
      this.temp.SupplierName = obj.SupplierName
    },
    handleCreate() {
      this.resetFormData();
      this.fetchWhs();
      this.getProvince();
      this.fetchAllUser();
      this.title = this.$t('Common.add');
      this.dialogVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate(); // 清除校验
        this.$refs['dataForm'].resetFields()
      })
    },
    handleEdit() {
      this.resetFormData();
      this.fetchWhs();
      this.getProvince();
      this.fetchAllUser();
      this.title = this.$t('Common.edit');
      this.dialogVisible = true;
      this.edit = true;
      Object.assign(this.temp, this.multipleSelection[0]);
      this.getChangeProvince();
      this.getChangeCity();
      this.getChangeRegion();
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate(); // 清除校验
        this.$refs['dataForm'].resetFields()
      })
    },
    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.startLoading();
          if (this.edit === false) {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess')
              } else {
                this.showNotify('error', res.Message)
              }
              this.endLoading();
              this.getList();
              this.dialogVisible = false
            }).catch(err => {
              console.log(err);
              this.endLoading()
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }).then(() => {
        const arrRowsID = selectRows.map(v => v.RegionID);
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess')
          } else {
            this.showNotify('error', res.Message)
          }
          this.getList();
          this.isProcessing = false;
        }).catch(err => {
          console.log(err);
          this.isProcessing = false;
        });
      })
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => {
        exportToExcel(res.data, '销售运费里程');
        this.isProcessing = false;
      }).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.handleFilter()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order != undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    getProvince() {
      const query = {
        ParentId: 0
      };
      getProvinceCityRegion(query).then(res => {
        if (res.Code === 2000) {
          this.ProvinceList = res.Data
        }
      })
    },
    getChangeProvince(e, type) {
      if (type === 1) {
        this.CityList = [];
        this.RegionList = [];
        this.temp.City = '';
        this.temp.CityName = '';
        this.temp.Region = '';
        this.temp.RegionName = '';
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate();
        });
        const obj = this.ProvinceList.find(v => v.Id === e);
        this.temp.ProvinceName = obj.Name
      }

      const query = {
        ParentId: this.temp.Province
      };
      getProvinceCityRegion(query).then(res => {
        if (res.Code === 2000) {
          this.CityList = res.Data
        }
      })
    },
    getChangeCity(e, type) {
      if (type === 1) {
        this.RegionList = [];
        this.temp.Region = '';
        this.temp.RegionName = '';
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate();
        });
        const obj = this.CityList.find(v => v.Id === e);
        this.temp.CityName = obj.Name
      }

      const query = {
        ParentId: this.temp.City
      };

      getProvinceCityRegion(query).then(res => {
        if (res.Code === 2000) {
          this.RegionList = res.Data
        }
      })
    },
    getChangeRegion(e) {
      const obj = this.RegionList.find(v => v.Id === e);
      this.temp.RegionName = obj.Name;
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate();
      });
      this.$forceUpdate();
    },
    fetchAllUser() {
      fetchAllUser().then(res => {
        if (res.Code === 2000) {
          this.AllUser = res.Data
        }
      })
    },
    getChangeUserName(e) {
      const obj = this.AllUser.find(v => v.LoginAccount === e);
      this.temp.UserName = obj.UserName
    }
  }
}
</script>
