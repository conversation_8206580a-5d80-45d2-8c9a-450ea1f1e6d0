<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="100px"
      style="width: 700px; margin-left: 50px;"
    >
      <el-form-item label="模板名称" prop="TemplateName">
        <el-input
          v-model="temp.TemplateName"
          placeholder="请输入模板名称"
          maxlength="100"
          show-word-limit
          size="mini"
        />
      </el-form-item>

      <el-form-item label="模板Key" prop="TemplateKey">
        <el-input
          v-model="temp.TemplateKey"
          placeholder="请输入模板Key"
          maxlength="50"
          show-word-limit
          size="mini"
        />
      </el-form-item>

      <el-form-item label="启用状态" prop="Enable">
        <el-switch
          v-model="temp.Enable"
          active-text="启用"
          inactive-text="禁用"
          size="mini"
        />
      </el-form-item>

      <el-form-item label="参数内容" prop="ParameterJson">
        <!-- 参数类型选择 -->
        <el-tabs v-model="activeTab" type="card" style="margin-bottom: 10px;">
          <el-tab-pane label="基础参数" name="parameters">
            <!-- 键值对表格 -->
            <div class="parameter-table-container">
              <div class="table-header">
                <el-button size="mini" type="primary" @click="addParameter">
                  添加参数
                </el-button>
                <el-button size="mini" type="success" @click="importParameterFile">
                  导入JSON文件
                </el-button>
                <el-button size="mini" type="info" @click="exportParameters">
                  导出JSON
                </el-button>
                <el-button size="mini" type="warning" @click="addSampleParameters">
                  添加示例参数
                </el-button>
              </div>

              <el-table
                :data="parameterList"
                border
                size="mini"
                style="width: 100%; margin-top: 10px;"
                max-height="300"
              >
                <el-table-column
                  prop="label"
                  label="参数标签"
                  width="200"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.label"
                      placeholder="请输入参数标签"
                      size="mini"
                      @input="updateParameterJson"
                    />
                  </template>
                </el-table-column>

                <el-table-column
                  prop="value"
                  label="参数值"
                  min-width="200"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.value"
                      placeholder="请输入参数值"
                      size="mini"
                      @input="updateParameterJson"
                    />
                  </template>
                </el-table-column>

                <el-table-column
                  label="操作"
                  width="80"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      @click="removeParameter(scope.$index)"
                    />
                  </template>
                </el-table-column>
              </el-table>

              <div v-if="parameterList.length === 0" class="empty-tip">
                <p>暂无参数，点击"添加参数"按钮开始添加</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="子表配置" name="subtables">
            <!-- 子表配置 -->
            <div class="subtable-container">
              <div class="table-header">
                <el-button size="mini" type="primary" @click="addSubtable">
                  添加子表
                </el-button>
                <el-button size="mini" type="warning" @click="addSampleSubtables">
                  添加示例子表
                </el-button>
              </div>

              <div v-if="subtableList.length === 0" class="empty-tip">
                <p>暂无子表，点击"添加子表"按钮开始添加</p>
              </div>

              <div v-for="(subtable, subtableIndex) in subtableList" :key="subtableIndex" class="subtable-item">
                <el-card shadow="hover" style="margin-bottom: 15px;">
                  <div slot="header" class="subtable-header">
                    <div class="subtable-info">
                      <el-input
                        v-model="subtable.name"
                        placeholder="子表名称"
                        size="mini"
                        style="width: 200px; margin-right: 10px;"
                        @input="updateParameterJson"
                      />
                      <el-input
                        v-model="subtable.key"
                        placeholder="子表Key"
                        size="mini"
                        style="width: 200px; margin-right: 10px;"
                        @input="updateParameterJson"
                      />
                    </div>
                    <el-button
                      size="mini"
                      type="danger"
                      @click="removeSubtable(subtableIndex)"
                    >
                      删除子表
                    </el-button>
                  </div>

                  <div class="subtable-columns">
                    <div class="columns-header">
                      <span>表格列配置：</span>
                      <el-button size="mini" type="primary" @click="addSubtableColumn(subtableIndex)">
                        添加列
                      </el-button>
                    </div>

                    <el-table
                      :data="subtable.columns"
                      border
                      size="mini"
                      style="width: 100%; margin-top: 10px;"
                      max-height="200"
                    >
                      <el-table-column
                        prop="title"
                        label="列标题"
                        width="150"
                      >
                        <template slot-scope="scope">
                          <el-input
                            v-model="scope.row.title"
                            placeholder="列标题"
                            size="mini"
                            @input="updateParameterJson"
                          />
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="field"
                        label="字段名"
                        width="150"
                      >
                        <template slot-scope="scope">
                          <el-input
                            v-model="scope.row.field"
                            placeholder="字段名"
                            size="mini"
                            @input="updateParameterJson"
                          />
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="width"
                        label="列宽"
                        width="100"
                      >
                        <template slot-scope="scope">
                          <el-input-number
                            v-model="scope.row.width"
                            :min="50"
                            :max="500"
                            size="mini"
                            @change="updateParameterJson"
                          />
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="align"
                        label="对齐方式"
                        width="120"
                      >
                        <template slot-scope="scope">
                          <el-select
                            v-model="scope.row.align"
                            size="mini"
                            @change="updateParameterJson"
                          >
                            <el-option label="左对齐" value="left" />
                            <el-option label="居中" value="center" />
                            <el-option label="右对齐" value="right" />
                          </el-select>
                        </template>
                      </el-table-column>

                      <el-table-column
                        label="操作"
                        width="80"
                        align="center"
                      >
                        <template slot-scope="scope">
                          <el-button
                            size="mini"
                            type="danger"
                            icon="el-icon-delete"
                            @click="removeSubtableColumn(subtableIndex, scope.$index)"
                          />
                        </template>
                      </el-table-column>
                    </el-table>

                    <div v-if="subtable.columns.length === 0" class="empty-tip" style="margin-top: 10px;">
                      <p>暂无列配置，点击"添加列"按钮开始添加</p>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 隐藏的JSON字段，用于与后端交互 -->
        <el-input
          v-model="temp.ParameterJson"
          type="hidden"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="temp.Remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
          size="mini"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" size="mini">
        取消
      </el-button>
      <el-button type="primary" @click="handleConfirm" size="mini">
        确定
      </el-button>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileImport"
    >
  </el-dialog>
</template>

<script>
import { add, update, getById } from '@/api/MD/MD_PrintTemplateParameter'

export default {
  name: 'ParameterDialog',
  data() {
    return {
      dialogVisible: false,
      dialogStatus: '',
      activeTab: 'parameters', // 当前激活的标签页
      temp: {
        Id: '',
        TemplateName: '',
        TemplateKey: '',
        Enable: true,
        ParameterJson: '',
        Remark: ''
      },
      parameterList: [], // 键值对列表
      subtableList: [], // 子表列表
      rules: {
        TemplateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        TemplateKey: [
          { required: true, message: '请输入模板Key', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_-]+$/, message: '模板Key只能包含字母、数字、下划线和横线', trigger: 'blur' }
        ]
      },
      sampleParameterList: [
        { label: '公司名称', value: 'companyName' },
        { label: '报告日期', value: 'reportDate' },
        { label: '页码', value: 'pageNumber' },
        { label: '客户名称', value: 'customerName' },
        { label: '订单号', value: 'orderNumber' },
        { label: '产品名称', value: 'productName' },
        { label: '数量', value: 'quantity' },
        { label: '单价', value: 'unitPrice' },
        { label: '总金额', value: 'totalAmount' }
      ],
      sampleSubtableList: [
        {
          name: '商品明细表',
          key: 'productTable',
          columns: [
            { title: '序号', field: 'index', width: 60, align: 'center' },
            { title: '商品名称', field: 'productName', width: 150, align: 'left' },
            { title: '规格型号', field: 'specification', width: 120, align: 'center' },
            { title: '数量', field: 'quantity', width: 80, align: 'center' },
            { title: '单价', field: 'unitPrice', width: 100, align: 'right' },
            { title: '金额', field: 'amount', width: 100, align: 'right' }
          ]
        },
        {
          name: '费用明细表',
          key: 'feeTable',
          columns: [
            { title: '费用项目', field: 'feeItem', width: 150, align: 'left' },
            { title: '费用金额', field: 'feeAmount', width: 100, align: 'right' },
            { title: '备注', field: 'remark', width: 200, align: 'left' }
          ]
        }
      ]
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogStatus === 'create' ? '新增参数模板' : '编辑参数模板'
    }
  },
  methods: {
    // 显示对话框
    show(status, row = null) {
      this.dialogStatus = status
      this.dialogVisible = true
      this.activeTab = 'parameters' // 重置到基础参数标签页

      if (status === 'create') {
        this.resetTemp()
        if (row) {
          // 复制模式
          this.temp = Object.assign({}, row)
          this.temp.Id = ''
          this.parseParameterJson()
        } else {
          this.parameterList = []
          this.subtableList = []
        }
      } else if (status === 'edit' && row) {
        this.temp = Object.assign({}, row)
        this.parseParameterJson()
      }

      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },

    // 重置表单
    resetTemp() {
      this.temp = {
        Id: '',
        TemplateName: '',
        TemplateKey: '',
        Enable: true,
        ParameterJson: '',
        Remark: ''
      }
      this.parameterList = []
      this.subtableList = []
    },

    // 解析参数JSON为键值对列表和子表列表
    parseParameterJson() {
      this.parameterList = []
      this.subtableList = []

      if (this.temp.ParameterJson) {
        try {
          const parsed = JSON.parse(this.temp.ParameterJson)
          if (parsed && typeof parsed === 'object') {
            // 解析基础参数
            if (parsed.parameters && typeof parsed.parameters === 'object') {
              for (const [key, value] of Object.entries(parsed.parameters)) {
                this.parameterList.push({
                  label: key,
                  value: String(value)
                })
              }
            }

            // 解析子表配置
            if (parsed.subtables && Array.isArray(parsed.subtables)) {
              this.subtableList = parsed.subtables.map(subtable => ({
                name: subtable.name || '',
                key: subtable.key || '',
                columns: Array.isArray(subtable.columns) ? subtable.columns.map(col => ({
                  title: col.title || '',
                  field: col.field || '',
                  width: col.width || 100,
                  align: col.align || 'center'
                })) : []
              }))
            }

            // 兼容旧格式：如果没有parameters和subtables结构，则认为是旧的键值对格式
            if (!parsed.parameters && !parsed.subtables) {
              for (const [key, value] of Object.entries(parsed)) {
                this.parameterList.push({
                  label: key,
                  value: String(value)
                })
              }
            }
          }
        } catch (error) {
          console.warn('参数内容JSON格式错误:', error)
          this.$message.warning('参数JSON格式错误，已重置为空')
        }
      }
    },

    // 更新参数JSON
    updateParameterJson() {
      const result = {}

      // 添加基础参数
      if (this.parameterList.length > 0) {
        result.parameters = {}
        this.parameterList.forEach(param => {
          if (param.label && param.label.trim()) {
            result.parameters[param.label.trim()] = param.value || ''
          }
        })
      }

      // 添加子表配置
      if (this.subtableList.length > 0) {
        result.subtables = this.subtableList.map(subtable => ({
          name: subtable.name || '',
          key: subtable.key || '',
          columns: subtable.columns.map(col => ({
            title: col.title || '',
            field: col.field || '',
            width: col.width || 100,
            align: col.align || 'center'
          }))
        }))
      }

      // 如果只有基础参数且没有子表，为了兼容性保持旧格式
      if (this.parameterList.length > 0 && this.subtableList.length === 0) {
        const paramObj = {}
        this.parameterList.forEach(param => {
          if (param.label && param.label.trim()) {
            paramObj[param.label.trim()] = param.value || ''
          }
        })
        this.temp.ParameterJson = JSON.stringify(paramObj)
      } else {
        this.temp.ParameterJson = JSON.stringify(result)
      }
    },

    // 添加参数
    addParameter() {
      this.parameterList.push({
        label: '',
        value: ''
      })
    },

    // 删除参数
    removeParameter(index) {
      this.parameterList.splice(index, 1)
      this.updateParameterJson()
    },

    // 添加子表
    addSubtable() {
      this.subtableList.push({
        name: '',
        key: '',
        columns: []
      })
    },

    // 删除子表
    removeSubtable(index) {
      this.subtableList.splice(index, 1)
      this.updateParameterJson()
    },

    // 添加子表列
    addSubtableColumn(subtableIndex) {
      if (this.subtableList[subtableIndex]) {
        this.subtableList[subtableIndex].columns.push({
          title: '',
          field: '',
          width: 100,
          align: 'center'
        })
      }
    },

    // 删除子表列
    removeSubtableColumn(subtableIndex, columnIndex) {
      if (this.subtableList[subtableIndex] && this.subtableList[subtableIndex].columns) {
        this.subtableList[subtableIndex].columns.splice(columnIndex, 1)
        this.updateParameterJson()
      }
    },

    // 添加示例子表
    addSampleSubtables() {
      this.$confirm('这将覆盖当前的子表配置，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.subtableList = JSON.parse(JSON.stringify(this.sampleSubtableList))
        this.updateParameterJson()
        this.$message.success('示例子表已添加')
      })
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.resetTemp()
    },

    // 确认操作
    handleConfirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 更新参数JSON
          this.updateParameterJson()

          // 验证参数内容格式
          if (this.temp.ParameterJson) {
            try {
              JSON.parse(this.temp.ParameterJson)
            } catch (error) {
              this.$message.error('参数内容JSON格式错误，请检查')
              return
            }
          }

          if (this.dialogStatus === 'create') {
            this.createData()
          } else {
            this.updateData()
          }
        }
      })
    },

    // 创建数据
    createData() {
      add(this.temp).then(() => {
        this.$message.success('创建成功')
        this.dialogVisible = false
        this.$emit('refresh')
      }).catch(error => {
        this.$message.error('创建失败：' + (error.message || '未知错误'))
      })
    },

    // 更新数据
    updateData() {
      update(this.temp).then(() => {
        this.$message.success('更新成功')
        this.dialogVisible = false
        this.$emit('refresh')
      }).catch(error => {
        this.$message.error('更新失败：' + (error.message || '未知错误'))
      })
    },

    // 导入参数文件
    importParameterFile() {
      this.$refs.fileInput.click()
    },

    // 处理文件导入
    handleFileImport(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const parameters = JSON.parse(e.target.result)
          this.temp.ParameterJson = JSON.stringify(parameters)
          this.parseParameterJson()
          this.$message.success('参数文件导入成功')
        } catch (error) {
          this.$message.error('参数文件格式错误：' + error.message)
        }
      }
      reader.readAsText(file)

      // 清空文件输入
      event.target.value = ''
    },

    // 导出参数为JSON
    exportParameters() {
      if (this.parameterList.length === 0) {
        this.$message.warning('没有参数可导出')
        return
      }

      this.updateParameterJson()
      try {
        const formatted = JSON.stringify(JSON.parse(this.temp.ParameterJson), null, 2)
        const blob = new Blob([formatted], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${this.temp.TemplateName || '参数模板'}.json`
        a.click()
        URL.revokeObjectURL(url)
        this.$message.success('参数已导出')
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      }
    },

    // 添加示例参数
    addSampleParameters() {
      this.$confirm('这将覆盖当前的参数内容，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.parameterList = [...this.sampleParameterList]
        this.updateParameterJson()
        this.$message.success('示例参数已添加')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.parameter-table-container {
  .table-header {
    margin-bottom: 10px;

    .el-button {
      margin-right: 10px;
    }
  }

  .empty-tip {
    text-align: center;
    color: #999;
    padding: 20px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    margin-top: 10px;

    p {
      margin: 0;
    }
  }
}

.subtable-container {
  .table-header {
    margin-bottom: 15px;

    .el-button {
      margin-right: 10px;
    }
  }

  .empty-tip {
    text-align: center;
    color: #999;
    padding: 20px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    margin-top: 10px;

    p {
      margin: 0;
    }
  }

  .subtable-item {
    .subtable-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .subtable-info {
        display: flex;
        align-items: center;
      }
    }

    .subtable-columns {
      .columns-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-weight: bold;
        color: #606266;
      }
    }
  }
}
</style>
