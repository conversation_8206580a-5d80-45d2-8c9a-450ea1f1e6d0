# MD_PrintTemplateParameter 打印参数模板管理

## 概述

MD_PrintTemplateParameter 是一个用于管理打印参数模板的模块，它与 MD_PrintTemplate 模块关联，为打印模板提供参数配置支持。

## 功能特性

### 1. 参数模板管理
- **CRUD操作**: 完整的增删改查功能
- **启用状态管理**: 支持启用/禁用参数模板
- **参数内容编辑**: JSON格式的参数配置编辑
- **参数验证**: JSON格式验证和格式化功能

### 2. 参数配置功能
- **示例参数**: 提供标准的参数模板示例
- **文件导入**: 支持从JSON文件导入参数配置
- **参数查看**: 格式化显示参数内容
- **参数复制**: 一键复制参数到剪贴板

### 3. 数据导入导出
- **Excel导出**: 导出参数模板数据到Excel
- **Excel导入**: 从Excel文件批量导入参数模板
- **模板下载**: 提供Excel导入模板下载

### 4. 与打印模板集成
- **下拉选择**: 在打印模板中可选择关联的参数模板
- **参数应用**: 在模板设计时可应用参数配置
- **参数查看**: 在设计界面查看关联的参数配置

## 文件结构

```
src/views/MD/MD_PrintTemplateParameter/
├── list.vue                           # 参数模板列表页面
├── components/
│   └── ParameterDialog.vue            # 参数模板编辑对话框
└── README.md                          # 说明文档

src/api/MD/
└── MD_PrintTemplateParameter.js       # API服务文件
```

## 数据结构

### MD_PrintTemplateParameter 实体
```csharp
public class MD_PrintTemplateParameter : BaseEntity
{
    public string Id { get; set; }              // 主键ID
    public string TemplateName { get; set; }    // 模板名称
    public bool? Enable { get; set; }           // 是否启用
    public string ParameterJson { get; set; }   // 参数JSON
}
```

### 示例参数配置
```json
{
  "printSettings": {
    "paperSize": "A4",
    "orientation": "portrait",
    "margins": {
      "top": 20,
      "bottom": 20,
      "left": 20,
      "right": 20
    }
  },
  "dataSource": {
    "type": "api",
    "url": "/api/data/source",
    "method": "GET",
    "headers": {
      "Content-Type": "application/json"
    }
  },
  "variables": {
    "companyName": "公司名称",
    "reportDate": "报告日期",
    "pageNumber": "页码"
  },
  "formatting": {
    "dateFormat": "YYYY-MM-DD",
    "numberFormat": "#,##0.00",
    "currency": "CNY"
  }
}
```

## API接口

### 基础CRUD接口
- `GET /MD/MD_PrintTemplateParameter/GetPageList` - 获取分页列表
- `GET /MD/MD_PrintTemplateParameter/GetById` - 根据ID获取实体
- `POST /MD/MD_PrintTemplateParameter/Add` - 新增参数模板
- `POST /MD/MD_PrintTemplateParameter/Update` - 更新参数模板
- `POST /MD/MD_PrintTemplateParameter/Delete` - 删除参数模板

### 导入导出接口
- `GET /MD/MD_PrintTemplateParameter/ExportToExcelFile` - 导出数据
- `GET /MD/MD_PrintTemplateParameter/ExportToExcelModel` - 导出模板
- `POST /MD/MD_PrintTemplateParameter/ImportExcelToData` - 导入数据

## 使用指南

### 1. 创建参数模板
1. 访问参数模板管理页面
2. 点击"新增"按钮
3. 填写模板名称
4. 编辑参数JSON内容或导入参数文件
5. 保存模板

### 2. 编辑参数配置
1. 在参数内容区域编辑JSON
2. 使用"格式化JSON"按钮美化格式
3. 使用"验证参数"按钮检查格式
4. 使用"添加示例参数"快速添加标准配置

### 3. 在打印模板中使用
1. 在打印模板编辑对话框中选择参数模板
2. 在设计界面查看关联的参数配置
3. 点击"应用参数到设计"使用参数配置

### 4. 数据管理
1. 使用搜索功能快速查找参数模板
2. 批量导出数据到Excel进行备份
3. 使用Excel模板批量导入参数配置

## 技术实现

### 前端技术栈
- **Vue.js 2.x**: 主框架
- **Element UI**: UI组件库
- **Axios**: HTTP请求库
- **Vue Router**: 路由管理

### 关键功能实现
1. **JSON编辑器**: 使用textarea + 格式化功能
2. **参数验证**: JSON.parse() 进行格式验证
3. **文件导入**: FileReader API读取JSON文件
4. **剪贴板操作**: navigator.clipboard API

### 与打印模板集成
1. **下拉选择**: 通过API获取启用的参数模板列表
2. **参数传递**: 在打印模板中保存TemplateParameterId
3. **参数应用**: 在设计界面根据参数配置调整设置

## 注意事项

1. **参数格式**: 参数内容必须是有效的JSON格式
2. **模板名称**: 参数模板名称不能重复
3. **启用状态**: 只有启用的参数模板才会在下拉列表中显示
4. **权限控制**: 根据需要添加相应的操作权限验证

## 扩展建议

1. **参数分类**: 可以添加参数模板分类功能
2. **参数校验**: 增加更严格的参数结构校验
3. **版本管理**: 支持参数模板的版本控制
4. **预设模板**: 提供更多行业标准的参数模板
5. **参数继承**: 支持参数模板之间的继承关系

## 故障排除

### 常见问题
1. **JSON格式错误**: 检查参数内容的JSON语法
2. **保存失败**: 确认模板名称不重复
3. **下拉列表为空**: 检查是否有启用的参数模板
4. **参数应用无效**: 确认参数配置格式正确

### 调试方法
1. 使用浏览器开发者工具查看网络请求
2. 检查控制台错误信息
3. 验证JSON格式的正确性
4. 确认后端API接口正常响应
