<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <!-- <el-date-picker
        v-model="listQuery.dateValue"
        class="filter-item"
        type="daterange"
        style="width: 220px"
        :picker-options="pickerOptions"
        range-separator="-" :unlink-panels="true"
        :start-placeholder="$t('Common.startTime')"
        :end-placeholder="$t('Common.endTime')"
        @change="handleFilter"
      />-->

      <el-input
        v-model="listQuery.keyword"
        :placeholder="$t('Common.keyword')"
        style="width: 140px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{name:'MD.MD_SalePacking.Add'}"
        class="filter-item"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="handleCreate"
      >{{ $t('Common.add') }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'MD.MD_SalePacking.Edit'}"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-waves
        v-permission="{name:'MD.MD_SalePacking.Delete'}"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}
      </el-button>
      <!-- <el-button
        v-waves
        v-permission="{name:'MD.MD_SalePacking.Export'}"
        class="filter-item" size="small"
        type="primary"
        icon="el-icon-document"
      >{{ $t('Common.export') }}</el-button>-->

      <el-button
        v-waves
        v-permission="{name:'MD.MD_SalePacking.Export'}"
        :loading="downloadLoading"
        size="small"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >{{ $t("Common.export") }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column v-if="false" :label="$t('ui.MD.MD_SalePacking.SPID')" prop="SPID" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.SPID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.CustomerCode')" prop="CustomerCode" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.CustomerName')" prop="CustomerName" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.CustomerName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.ItemCode')" prop="ItemCode" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.ItemName')" prop="ItemName" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.ItemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.Volume')" prop="Volume" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Volume }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.NetWeight')" prop="NetWeight" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.NetWeight }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.GrossWeight')" prop="GrossWeight" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.GrossWeight }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.PalletNum')" prop="PalletNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PalletNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.Metre')" prop="Metre" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.Metre }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_SalePacking.PNum')" prop="PNum" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.PNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.IsBarCode')" prop="IsBarCode" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.IsBarCode|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Item.Isabroad')" prop="Isabroad" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.Isabroad|yesnoFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.Remark')" prop="Remark" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('Common.CUser')" prop="CUser" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
        :formatter="formatDateTime"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  fetchList,
  batchDelete,
  exportExcelFile
} from '../../../api/MD/MD_SalePacking';
import {
  exportToExcel
} from '@/utils/excel-export';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import {
  formatDateTime
} from '../../../utils';
// import permission from '../../../directive/permission/permission'
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_SalePacking',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      isProcessing: false,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: '',
        isDelivery: '',
        dateValue: [
          new Date(),
          new Date()
        ]
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$i18n.t('Common.previousWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: this.$i18n.t('Common.previousThreeMonths'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }
        ]
      },
      multipleSelection: []
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    formatDateTime,
    getList() {
      // 获取数据
      this.listLoading = false;
      this.listQuery.dateValue = [];
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleCreate() {
      this.routeTo('MD.MD_SalePackingDetailed');
    },
    handleUpdate() {
      this.routeTo(
        'MD.MD_SalePackingDetailed',
        Object.assign(this.multipleSelection[0])
      );
    },
    handleDelete() {
      const selectRows = this.multipleSelection;
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'), {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const arrRowsID = selectRows.map(v => v.SPID);

        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
            this.handleFilter();
          } else {
            this.showNotify('error', res.Message);
          }
        });
      });
    },
    handleExport() {
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res =>
        exportToExcel(res.data, '客户物料包装规格')
      );
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
