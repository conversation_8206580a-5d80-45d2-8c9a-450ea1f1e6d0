<template>
  <div class="app-container">
    <p>
      <label style="width:100%">{{ $t('route.MD.MD_SalePackingDetailed') }}</label>
    </p>
    <div class="filter-container">
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-delete" @click="resetFormData">
        {{ $t('Common.empty') }}</el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-edit" @click="handleCommit">
        {{ $t('Common.confirm') }}</el-button>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-back" @click="handleBack">
        {{ $t('Common.cancel') }}</el-button>
    </div>

    <el-form ref="dataForm" :inline="false" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item v-if="false" :label="$t('ui.MD.MD_SalePacking.SPID')" prop="SPID">
            <el-input v-model="temp.SPID" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item v-if="false" :label="$t('ui.MD.MD_SalePacking.CustomerCode')" prop="CustomerCode">
            <el-input v-model="temp.CustomerCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.CustomerName')" prop="CustomerName">
            <el-input v-model="temp.CustomerName" :disabled="editStatus==='edit'" readonly>
              <el-button slot="append" icon="el-icon-more" :disabled="editStatus==='edit'" @click="selectCustomer" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.ItemName')" prop="ItemName">
            <el-input v-model="temp.ItemName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.ItemCode')" prop="ItemCode">
            <el-input v-model="temp.ItemCode" :disabled="editStatus==='edit'" readonly>
              <el-button
                slot="append"
                icon="el-icon-more"
                :disabled="editStatus==='edit'"
                @click="oitmdialogVisible = true"
              />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.Volume')" prop="Volume">
            <el-input-number v-model="temp.Volume" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.NetWeight')" prop="NetWeight">
            <el-input-number v-model="temp.NetWeight" :min="0" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.GrossWeight')" prop="GrossWeight">
            <el-input-number v-model="temp.GrossWeight" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.PalletNum')" prop="PalletNum">
            <el-input-number v-model="temp.PalletNum" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('ui.MD.MD_SalePacking.Metre')" prop="Metre">
            <el-input-number v-model="temp.Metre" :min="0" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item :label="$t('ui.MD.MD_Item.IsBarCode')" prop="IsBarCode">
        <el-checkbox v-model="temp.IsBarCode" />
      </el-form-item>
      <el-form-item :label="$t('ui.MD.MD_Item.Isabroad')" prop="Isabroad">
        <el-checkbox v-model="temp.Isabroad" />
      </el-form-item>
      <el-form-item :label="$t('Common.Remark')">
        <el-input v-model="temp.Remark" :autosize="{minRows: 3, maxRows: 5}" type="textarea" />
      </el-form-item>
    </el-form>
    <!-- 选择客户 -->
    <el-dialog :title="$t('ui.MD.MD_SalePacking.title')" :visible.sync="dialogVisible" width="65%">
      <div class="filter-container">
        <el-input
          v-model="listCustomerQuery.keyword"
          :placeholder="$t('Common.keyword')"
          style="width: 220px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleCustomerFilter"
        />
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleCustomerFilter" />
      </div>
      <el-table
        v-loading="listLoading"
        :data="CustomerDataList"
        border
        fit
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        highlight-current-row
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" />
        <el-table-column
          :label="$t('ui.MD.MD_SalePacking.CustomerCode')"
          prop="CustomerCode"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.InternalID }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('ui.MD.MD_SalePacking.CustomerName')"
          prop="CustomerName"
          align="center"
          width="240"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.FirstLineName }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogVisible = false">{{ $t('Common.close') }}</el-button>
        <el-button v-waves type="primary" icon="el-icon-check" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </div>
    </el-dialog>
    <!-- 选择物料 -->
    <MaterialDlg
      ref="materialDlg"
      :show.sync="oitmdialogVisible"
      :title="$t('Common.selectMaterial')"
      :is-multiple="false"
      @close="materialSelected"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import {
  add,
  update,
  fetchOitmNextPage
} from '../../../api/MD/MD_SalePacking';
import {
  fetchCustomerNextPage
} from '../../../api/SD/SD_DeliveryWave';
import MaterialDlg from '@/components/FLD/MaterialDlg';
import {
  formatDateTime
} from '../../../utils';

export default {
  name: 'POPOBarCodeDetailed',
  components: {
    MaterialDlg
  },
  directives: {
    waves
  },
  data() {
    return {
      listLoading: false,
      list: [],
      CustomerDataList: [],
      listQuery: [],
      listCustomerQuery: {
        keyword: '',
        pageSize: 10,
        firstID: ''
      },
      itemkeyword: '',
      temp: {
        SPID: '',
        CustomerCode: '',
        CustomerName: '',
        ItemCode: '',
        ItemName: '',
        Volume: '',
        NetWeight: '',
        GrossWeight: '',
        PalletNum: '',
        IsBarCode: false,
        Isabroad: false,
        Metre: '',
        Remark: ''
      },
      dialogVisible: false,
      oitmdialogVisible: false,
      editStatus: '',
      rules: {
        CustomerName: [{
          required: true,
          message: this.$i18n.t('Common.IsRequired'),
          trigger: 'change'
        }]
      },
      multiCustomer: [],
      multiOitm: []
    };
  },
  created() {
    this.getPageParams();
  },
  methods: {
    getPageParams() {
      Object.assign(this.temp, this.$route.params);
      if (this.temp.SPID) {
        this.editStatus = 'edit';
      } else {
        this.editStatus = 'create';
      }
    },
    resetFormData() {
      // 清空表单数据
      this.temp = {
        SPID: '',
        CustomerCode: '',
        CustomerName: '',
        ItemCode: '',
        ItemName: '',
        Volume: '',
        NetWeight: '',
        GrossWeight: '',
        PalletNum: '',
        Metre: '',
        Remark: ''
      };
    },
    handleCommit() {
      this.$refs['dataForm'].validate(val => {
        if (val) {
          this.startLoading();
          if (this.editStatus === 'create') {
            add(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.createSuccess');
                this.handleBack();
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading()
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          } else {
            update(this.temp).then(res => {
              if (res.Code === 2000) {
                this.showNotify('success', 'Common.updateSuccess');
                this.handleBack();
              } else {
                this.showNotify('error', res.Message);
              }
              this.endLoading()
            }).catch(err => {
              console.log(err);
              this.endLoading()
            });
          }
        } else {
          return false;
        }
      });
    },
    selectCustomer() {
      // 从SAP获取客户信息
      this.dialogVisible = true;
    },
    handleCustomerFilter() {
      this.getCustomerList();
    },
    materialSelected(materials) {
      Object.assign(this.temp, materials);
      this.oitmdialogVisible = false;
    },
    handleSelectionChange(val) {
      this.multiCustomer = val;
    },
    handleSave() {
      this.dialogVisible = false;
      var selectRow = this.multiCustomer[0];
      this.temp.CustomerCode = selectRow.InternalID;
      this.temp.CustomerName = selectRow.FirstLineName;
      Object.assign(this.temp, this.multiCustomer[0]);
    },

    handleBack() {
      this.backTo('MD.MD_SalePacking');
    },
    getCustomerList() {
      // if (this.listCustomerQuery.keyword == "") {
      //   this.showNotify("warning", "Common.writeCustomerNameOrOrder");
      //   return;
      // }
      this.listLoading = true;
      // if (this.CustomerDataList.length == 1) {
      //   this.listCustomerQuery.firstID = "";
      // }

      fetchCustomerNextPage(this.listCustomerQuery).then(response => {
        if (response.Data) {
          this.CustomerDataList = response.Data.Customers;
          // this.listCustomerQuery.firstID = response.Data.LastReturnUUID;
        }
        this.listLoading = false;
      });
    }
  }
};
</script>
