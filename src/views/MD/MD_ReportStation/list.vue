<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-plus" size="small" @click="handleCreate">
        {{ $t('Common.add') }}
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        size="small"
        :disabled="selective"
        @click="handleEdit"
      >{{ $t('Common.edit') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t('Common.delete') }}</el-button>
      <el-button
        v-waves
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        @click="handleExportExcel"
      >{{ $t('Common.export') }}</el-button>
      <el-upload
        style="margin-left: 10px"
        class="filter-item"
        :action="'#'"
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button v-waves type="primary" icon="el-icon-upload" size="small">{{ $t('Common.import') }}</el-button>
      </el-upload>
      <el-button
        v-waves
        style="margin-left: 10px"
        class="filter-item"
        type="success"
        icon="el-icon-download"
        size="small"
        @click="handleExportTemplate"
      >导出模板</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('Common.select')" type="selection" align="center" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column label="工作中心名称" prop="WorkCenterName" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作中心编码" prop="WorkCenterCode" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.WorkCenterCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工序号" prop="ProcessNo" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProcessNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工序短文本" prop="ProcessShortText" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ProcessShortText }}</span>
        </template>
      </el-table-column>
      <el-table-column label="站点代码" prop="StationCode" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.StationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="站点名称" prop="StationName" align="center" width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.StationName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="站点序号" prop="StationSort" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.StationSort }}</span>
        </template>
      </el-table-column>
      <el-table-column label="启用报工" prop="Enable" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag :type="scope.row.Enable === 1 ? 'success' : 'info'">
            {{ scope.row.Enable === 1 ? '已启用' : '未启用' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="form" :rules="rules" :model="temp" label-position="right" label-width="120px">
        <el-form-item label="工作中心" prop="WorkCenterCode">
          <el-select
            v-model="temp.WorkCenterCode"
            filterable
            placeholder="请选择工作中心"
            @change="handleWorkCenterChange"
          >
            <el-option
              v-for="item in workCenterOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工序号" prop="ProcessNo">
          <el-input-number v-model="temp.ProcessNo" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="工序短文本" prop="ProcessShortText">
          <el-input v-model="temp.ProcessShortText" />
        </el-form-item>
        <el-form-item label="站点" prop="StationCode">
          <el-select
            v-model="temp.StationCode"
            filterable
            placeholder="请选择站点"
            @change="handleStationChange"
          >
            <el-option
              v-for="item in stationOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="站点序号" prop="StationSort">
          <el-input-number v-model="temp.StationSort" :min="0" :precision="2" :step="0.01" controls-position="right" />
        </el-form-item>
        <el-form-item label="启用报工" prop="Enable">
          <el-select v-model="temp.Enable" placeholder="请选择">
            <el-option label="未启用" :value="0" />
            <el-option label="已启用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联用户" prop="userIds">
          <el-select
            v-model="selectedUserIds"
            multiple
            filterable
            placeholder="请选择关联用户"
            style="width: 100%"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.UserID"
              :label="item.UserName"
              :value="String(item.UserID)"
            >
              <span>{{ item.UserName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.LoginAccount }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handleConfirm">{{ $t('Common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  batchDelete,
  exportToExcelFile,
  exportToExcelModel,
  importExcelToData,
  getStationUsers,
  addWithUsers,
  updateWithUsers
} from '@/api/MD/MD_ReportStation';
import { fetchList as fetchWorkCenterStationList } from '@/api/MD/MD_WorkCenterStation';
import { fetchAllUser } from '@/api/Sys/Sys_User';
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { exportToExcel } from '@/utils/excel-export';
import { parseExcelFile } from '@/utils/excel-import';

export default {
  name: 'MD_ReportStation',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      isProcessing: false,
      selective: true,
      deletable: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: ''
      },
      temp: {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        ProcessNo: 0,
        ProcessShortText: '',
        StationCode: '',
        StationName: '',
        StationSort: 0,
        Enable: 0
      },
      selectedUserIds: [], // 选中的用户ID列表
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '新增'
      },
      multipleSelection: [],
      workCenterOptions: [],
      stationOptions: [],
      userOptions: [], // 用户选项列表
      rules: {
        WorkCenterCode: [{ required: true, message: '工作中心不能为空', trigger: 'change' }],
        ProcessNo: [{ required: true, message: '工序号不能为空', trigger: 'blur' }],
        ProcessShortText: [{ required: true, message: '工序短文本不能为空', trigger: 'blur' }],
        StationCode: [{ required: true, message: '站点不能为空', trigger: 'change' }]
      }
    };
  },
  created() {
    this.getList();
    this.getWorkCenterOptions();
    this.getUserOptions();
  },
  methods: {
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        console.log(response);
        const { Data } = response;
        if (Data) {
          this.list = Data.items;
          this.total = Data.total;
        } else {
          this.list = [];
          this.total = 0;
        }
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.PageNumber = 1;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.selective = !(this.multipleSelection.length === 1);
      this.deletable = this.multipleSelection.length === 0;
    },
    resetTemp() {
      this.temp = {
        Id: '',
        WorkCenterName: '',
        WorkCenterCode: '',
        ProcessNo: 0,
        ProcessShortText: '',
        StationCode: '',
        StationName: '',
        StationSort: 0,
        Enable: 0
      };
      this.stationOptions = [];
      this.selectedUserIds = [];
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = 'create';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    handleEdit() {
      this.resetTemp();
      const tempData = this.multipleSelection[0];
      this.temp = Object.assign({}, tempData);
      this.dialogStatus = 'update';
      // 获取站点关联的用户列表
      if (this.temp.Id) {
        this.getStationUsers(this.temp.Id);
      }
      this.dialogFormVisible = true;
      if (this.temp.WorkCenterCode) {
        this.getStationOptions(this.temp.WorkCenterCode);
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'create') {
            this.isProcessing = true;
            addWithUsers(this.temp, this.selectedUserIds)
              .then(response => {
                if (response.Code === 2000) {
                  this.$notify({
                    title: '成功',
                    message: '创建成功',
                    type: 'success',
                    duration: 2000
                  });
                  this.dialogFormVisible = false;
                  this.getList();
                } else {
                  this.$notify({
                    title: '失败',
                    message: response.Message || '创建失败',
                    type: 'error',
                    duration: 2000
                  });
                }
                this.isProcessing = false;
              })
              .catch(() => {
                this.isProcessing = false;
              });
          } else {
            this.isProcessing = true;
            updateWithUsers(this.temp, this.selectedUserIds)
              .then(response => {
                if (response.Code === 2000) {
                  this.$notify({
                    title: '成功',
                    message: '更新成功',
                    type: 'success',
                    duration: 2000
                  });
                  this.dialogFormVisible = false;
                  this.getList();
                } else {
                  this.$notify({
                    title: '失败',
                    message: response.Message || '更新失败',
                    type: 'error',
                    duration: 2000
                  });
                }
                this.isProcessing = false;
              })
              .catch(() => {
                this.isProcessing = false;
              });
          }
        }
      });
    },
    handleDelete() {
      this.$confirm('确认删除所选记录?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.isProcessing = true;
          const ids = this.multipleSelection.map(item => item.Id);
          batchDelete(ids)
            .then(response => {
              if (response.Code === 2000) {
                this.$notify({
                  title: '成功',
                  message: '删除成功',
                  type: 'success',
                  duration: 2000
                });
                this.getList();
              } else {
                this.$notify({
                  title: '失败',
                  message: response.Message || '删除失败',
                  type: 'error',
                  duration: 2000
                });
              }
              this.isProcessing = false;
            })
            .catch(() => {
              this.isProcessing = false;
            });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    handleExportExcel() {
      this.isProcessing = true;
      exportToExcelFile({ keyword: this.listQuery.keyword })
        .then(response => {
          exportToExcel(response, '报工站点导出')
          this.isProcessing = false
        })
        .catch(() => {
          this.isProcessing = false;
        });
    },
    beforeUpload(file) {
      this.isProcessing = true

      // 定义字段映射关系，Excel表头到实体属性的映射
      const fieldMapping = {
        '工作中心名称': 'WorkCenterName',
        '工作中心编码': 'WorkCenterCode',
        '工序号': 'ProcessNo',
        '工序短文本': 'ProcessShortText',
        '站点代码': 'StationCode',
        '站点名称': 'StationName',
        '站点序号': 'StationSort',
        '启用报工': 'Enable'
      }

      // 解析Excel文件，表头在第一行，所以索引是0
      parseExcelFile(file, 0)
        .then(data => {
          // 将解析后的数据映射到实体
          const entities = data.map(row => {
            const entity = {}

            // 遍历字段映射关系，将Excel数据映射到实体属性
            Object.keys(fieldMapping).forEach(excelHeader => {
              const entityProperty = fieldMapping[excelHeader]
              if (row[excelHeader] !== undefined && row[excelHeader] !== null) {
                entity[entityProperty] = row[excelHeader]
              }
            })

            return entity
          }).filter(entity => Object.keys(entity).length > 0) // 过滤掉空行

          // 发送解析后的数据到后端
          return importExcelToData(entities)
        })
        .then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '导入成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '导入失败',
              type: 'error',
              duration: 2000
            })
          }
          this.isProcessing = false
        })
        .catch(error => {
          console.error('导入失败:', error)
          this.$notify({
            title: '错误',
            message: '解析Excel文件失败，请检查文件格式',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        })

      return false
    },
    handleExportTemplate() {
      this.isProcessing = true
      exportToExcelModel().then(res => {
        exportToExcel(res, '报工站点模板')
        this.isProcessing = false
      }).catch((error) => {
        console.error('Export template error:', error)
        this.isProcessing = false
      })
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop === 'id') {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id';
      } else {
        this.listQuery.sort = '-id';
      }
      this.handleFilter();
    },
    getWorkCenterOptions() {
      const params = {
        PageNumber: 1,
        PageSize: 999999 // 最大参数，获取所有数据
      };
      fetchWorkCenterStationList(params).then(response => {
        if (response.Code === 2000 && response.Data && response.Data.items) {
          // 提取出唯一的工作中心
          const workCenters = [];
          const workCenterSet = new Set();
          response.Data.items.forEach(item => {
            if (!workCenterSet.has(item.WorkCenterCode)) {
              workCenterSet.add(item.WorkCenterCode);
              workCenters.push({
                code: item.WorkCenterCode,
                name: item.WorkCenterName
              });
            }
          });
          this.workCenterOptions = workCenters;
        }
      });
    },
    getStationOptions(workCenterCode) {
      const params = {
        PageNumber: 1,
        PageSize: 999999, // 最大参数，获取所有数据
        WorkCenterCode: workCenterCode // 使用WorkCenterCode参数来过滤
      };
      fetchWorkCenterStationList(params).then(response => {
        if (response.Code === 2000 && response.Data && response.Data.items) {
          // 筛选出该工作中心对应的站点
          const stations = response.Data.items
            .map(item => ({
              code: item.StationCode,
              name: item.StationName
            }));
          this.stationOptions = stations;
        }
      });
    },
    handleWorkCenterChange(val) {
      if (val) {
        const selectedWorkCenter = this.workCenterOptions.find(item => item.code === val);
        if (selectedWorkCenter) {
          this.temp.WorkCenterName = selectedWorkCenter.name;
        }
        this.temp.StationCode = '';
        this.temp.StationName = '';
        this.getStationOptions(val);
      } else {
        this.stationOptions = [];
        this.temp.WorkCenterName = '';
      }
    },
    handleStationChange(val) {
      if (val) {
        const selectedStation = this.stationOptions.find(item => item.code === val);
        if (selectedStation) {
          this.temp.StationName = selectedStation.name;
        }
      } else {
        this.temp.StationName = '';
      }
    },
    // 获取所有用户列表
    getUserOptions() {
      fetchAllUser().then(response => {
        if (response.Code === 2000 && response.Data) {
          this.userOptions = response.Data;
        }
      });
    },
    // 获取站点关联的用户列表
    getStationUsers(stationId) {
      getStationUsers(stationId).then(response => {
        if (response.Code === 2000 && response.Data) {
          // 确保ID类型一致，统一转换为字符串
          this.selectedUserIds = response.Data.map(user => String(user.UserID));
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/* 修复多选下拉框选中项的样式问题 */
::v-deep .el-select-dropdown.is-multiple .el-select-dropdown__item {
  &.selected {
    color: #409EFF !important;
    background-color: #f5f7fa !important;
    font-weight: 700;

    &::after {
      position: absolute;
      right: 20px;
      font-family: 'element-icons';
      content: "\e6da";
      font-size: 12px;
      font-weight: 700;
      color: #409EFF;
    }

    &:hover {
      background-color: #f5f7fa !important;
    }
  }

  &:not(.selected) {
    color: #606266;
    background-color: transparent;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

/* 确保下拉框中的用户信息样式正确 */
::v-deep .el-select-dropdown__item {
  span {
    &:first-child {
      margin-right: 10px;
    }
  }
}

/* 修复多选标签的样式 */
::v-deep .el-select .el-tag {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;

  .el-tag__close {
    color: #909399;

    &:hover {
      color: #409EFF;
      background-color: #ecf5ff;
    }
  }
}

/* 确保选中的选项在下拉框中正确显示 */
::v-deep .el-select-dropdown__item.selected {
  color: #409EFF !important;
  font-weight: 700 !important;
}
</style>
