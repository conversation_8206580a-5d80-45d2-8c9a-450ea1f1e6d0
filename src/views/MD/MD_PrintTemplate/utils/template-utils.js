/**
 * 模板编辑器工具函数
 */

/**
 * 默认模板数据
 */
export const defaultTemplateData = {
  title: '打印模板',
  date: new Date().toLocaleDateString(),
  time: new Date().toLocaleTimeString(),
  content: '这是模板内容',
  remark: '备注信息',
  table: [
    { name: '项目1', value: '值1', remark: '备注1' },
    { name: '项目2', value: '值2', remark: '备注2' },
    { name: '项目3', value: '值3', remark: '备注3' }
  ],
  // 常用字段
  companyName: '公司名称',
  address: '公司地址',
  phone: '联系电话',
  fax: '传真号码',
  email: '邮箱地址',
  website: '网站地址',
  // 单据信息
  docNo: 'DOC001',
  docDate: new Date().toLocaleDateString(),
  docType: '单据类型',
  // 客户信息
  customerName: '客户名称',
  customerCode: 'CUST001',
  customerAddress: '客户地址',
  customerPhone: '客户电话',
  // 供应商信息
  supplierName: '供应商名称',
  supplierCode: 'SUPP001',
  supplierAddress: '供应商地址',
  supplierPhone: '供应商电话'
}

/**
 * 纸张尺寸配置
 */
export const paperSizes = {
  'A3': { width: 420, height: 296.6, name: 'A3 (420×297mm)' },
  'A4': { width: 210, height: 296.6, name: 'A4 (210×297mm)' },
  'A5': { width: 210, height: 147.6, name: 'A5 (210×148mm)' },
  'B3': { width: 500, height: 352.6, name: 'B3 (500×353mm)' },
  'B4': { width: 250, height: 352.6, name: 'B4 (250×353mm)' },
  'B5': { width: 250, height: 175.6, name: 'B5 (250×176mm)' },
  'Letter': { width: 215.9, height: 279.4, name: 'Letter (216×279mm)' },
  'Legal': { width: 215.9, height: 355.6, name: 'Legal (216×356mm)' }
}

/**
 * 组件类型配置
 */
export const componentTypes = {
  basic: [
    { type: 'text', name: '文本', icon: 'el-icon-edit-outline', description: '普通文本组件' },
    { type: 'image', name: '图片', icon: 'el-icon-picture-outline', description: '图片组件' },
    { type: 'longText', name: '长文本', icon: 'el-icon-document', description: '多行文本组件' },
    { type: 'table', name: '表格', icon: 'el-icon-s-grid', description: '数据表格组件' },
    { type: 'emptyTable', name: '空白表格', icon: 'el-icon-menu', description: '空白表格组件' },
    { type: 'html', name: 'HTML', icon: 'el-icon-connection', description: 'HTML内容组件' },
    { type: 'customText', name: '自定义文本', icon: 'el-icon-edit', description: '自定义文本组件' }
  ],
  auxiliary: [
    { type: 'hline', name: '横线', icon: 'el-icon-minus', description: '水平分割线' },
    { type: 'vline', name: '竖线', icon: 'el-icon-minus', description: '垂直分割线' },
    { type: 'rect', name: '矩形', icon: 'el-icon-crop', description: '矩形边框' },
    { type: 'oval', name: '椭圆', icon: 'el-icon-more-outline', description: '椭圆边框' }
  ],
  barcode: [
    { type: 'barcode', name: '条形码', icon: 'el-icon-postcard', description: '一维条形码' },
    { type: 'qrcode', name: '二维码', icon: 'el-icon-cpu', description: '二维码' }
  ]
}

/**
 * 验证模板JSON格式
 * @param {string} jsonStr - JSON字符串
 * @returns {Object} 验证结果
 */
export function validateTemplateJson(jsonStr) {
  try {
    const template = JSON.parse(jsonStr)

    // 基本结构验证
    if (!template || typeof template !== 'object') {
      return { valid: false, error: '模板格式不正确' }
    }

    // 检查必要字段
    if (!template.panels && !template.template) {
      return { valid: false, error: '缺少模板内容' }
    }

    return { valid: true, template }
  } catch (error) {
    return { valid: false, error: 'JSON格式错误: ' + error.message }
  }
}

/**
 * 格式化模板JSON
 * @param {Object} template - 模板对象
 * @returns {string} 格式化的JSON字符串
 */
export function formatTemplateJson(template) {
  return JSON.stringify(template, null, 2)
}

/**
 * 生成模板文件名
 * @param {string} prefix - 文件名前缀
 * @returns {string} 文件名
 */
export function generateTemplateFileName(prefix = 'template') {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
  return `${prefix}_${timestamp}.json`
}

/**
 * 下载文件
 * @param {string} content - 文件内容
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
export function downloadFile(content, filename, mimeType = 'application/json') {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  URL.revokeObjectURL(url)
}

/**
 * 读取文件内容
 * @param {File} file - 文件对象
 * @returns {Promise<string>} 文件内容
 */
export function readFileContent(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      resolve(e.target.result)
    }

    reader.onerror = (e) => {
      reject(new Error('文件读取失败'))
    }

    reader.readAsText(file)
  })
}

/**
 * 深拷贝对象
 * @param {Object} obj - 要拷贝的对象
 * @returns {Object} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  return function(...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastTime = 0
  return function(...args) {
    const now = Date.now()
    if (now - lastTime >= delay) {
      lastTime = now
      func.apply(this, args)
    }
  }
}

/**
 * 获取元素的绝对位置
 * @param {HTMLElement} element - DOM元素
 * @returns {Object} 位置信息
 */
export function getElementPosition(element) {
  const rect = element.getBoundingClientRect()
  return {
    x: rect.left + window.scrollX,
    y: rect.top + window.scrollY,
    width: rect.width,
    height: rect.height
  }
}

/**
 * 检查浏览器是否支持某个功能
 * @param {string} feature - 功能名称
 * @returns {boolean} 是否支持
 */
export function checkBrowserSupport(feature) {
  const features = {
    canvas: () => {
      const canvas = document.createElement('canvas')
      return !!(canvas.getContext && canvas.getContext('2d'))
    },
    localStorage: () => {
      try {
        const test = 'test'
        localStorage.setItem(test, test)
        localStorage.removeItem(test)
        return true
      } catch (e) {
        return false
      }
    },
    fileReader: () => {
      return !!(window.File && window.FileReader && window.FileList && window.Blob)
    }
  }

  return features[feature] ? features[feature]() : false
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
