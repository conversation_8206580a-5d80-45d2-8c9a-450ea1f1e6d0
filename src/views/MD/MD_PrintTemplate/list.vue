<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.Keyword"
        placeholder="模板名称/关联模块"
        style="width: 200px;"
        class="filter-item"
        size="mini"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.Enable"
        placeholder="启用状态"
        clearable
        style="width: 120px"
        class="filter-item"
        size="mini"
      >
        <el-option
          v-for="item in enableOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="listQuery.RelateModule"
        placeholder="关联模块"
        style="width: 150px;"
        class="filter-item"
        size="mini"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增
      </el-button>
      <el-button
        v-waves
        :loading="downloadLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        size="mini"
        @click="handleDownload"
      >
        导出
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="el-icon-upload2"
        size="mini"
        @click="handleImportTemplate"
      >
        导入模板
      </el-button>
      <el-button
        class="filter-item"
        type="warning"
        icon="el-icon-upload"
        size="mini"
        @click="handleImportData"
      >
        导入数据
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%;"
      height="calc(100vh - 320px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" fixed />
      <el-table-column type="index" align="center" width="50" label="序号" />

      <el-table-column
        prop="TemplateName"
        label="模板名称"
        width="200"
        align="center"
        show-overflow-tooltip
      />

      <el-table-column
        prop="TemplateParameterId"
        label="模板参数"
        width="150"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="scope.row.TemplateParameterName">
            {{ scope.row.TemplateParameterName }}
          </span>
          <span v-else class="text-muted">未设置</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="Enable"
        label="启用状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag :type="scope.row.Enable ? 'success' : 'danger'">
            {{ scope.row.Enable ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="Remark"
        label="备注"
        min-width="200"
        align="center"
        show-overflow-tooltip
      />

      <el-table-column
        prop="CUser"
        label="创建人"
        width="100"
        align="center"
      />

      <el-table-column
        prop="CTime"
        label="创建时间"
        width="160"
        align="center"
        :formatter="formatDateTime"
      />

      <el-table-column
        label="操作"
        align="center"
        width="400"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="mini" @click="handleDesign(row)">
            设计
          </el-button>
          <el-button type="info" size="mini" @click="handleCopy(row)">
            复制
          </el-button>
          <el-button type="info" size="mini" @click="handleExportTemplate(row)">
            导出
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 编辑对话框 -->
    <template-dialog
      ref="templateDialog"
      @refresh="getList"
    />

    <!-- 导入文件输入 -->
    <input
      ref="importFileInput"
      type="file"
      accept=".xlsx,.xls"
      style="display: none"
      @change="handleImportFile"
    >

    <input
      ref="templateFileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleTemplateFile"
    >
  </div>
</template>

<script>
import {
  fetchPageList,
  add,
  update,
  batchDelete,
  exportExcelFile,
  exportExcelModel,
  importExcelData
} from '@/api/MD/MD_PrintTemplate'
import { exportToExcel } from '@/utils/excel-export'
import { parseExcelFile } from '@/utils/excel-import'
import { formatDateTime } from '@/utils'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import TemplateDialog from './components/TemplateDialog.vue'
import permission from '@/directive/permission/index.js'

export default {
  name: 'MD.MD_PrintTemplate.List',
  components: {
    Pagination,
    TemplateDialog
  },
  directives: {
    waves,
    permission
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      downloadLoading: false,
      multipleSelection: [],
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        Keyword: '',
        Enable: null,
        RelateModule: ''
      },
      enableOptions: [
        { value: true, label: '启用' },
        { value: false, label: '禁用' }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.listLoading = true
      fetchPageList(this.listQuery).then(response => {
        this.list = response.Data.rows
        this.total = response.Data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },

    // 搜索过滤
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.getList()
    },

    // 选择变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 新增
    handleCreate() {
      this.$refs.templateDialog.show('create')
    },

    // 编辑
    handleEdit(row) {
      this.$refs.templateDialog.show('edit', row)
    },

    // 复制
    handleCopy(row) {
      const copyData = {
        ...row,
        Id: '',
        TemplateName: row.TemplateName + '_副本'
      }
      this.$refs.templateDialog.show('create', copyData)
    },

    // 设计模板
    handleDesign(row) {
      this.$router.push({
        path: '/MD/MD_PrintTemplateEdit',
        query: {
          templateId: row.Id,
          templateName: row.TemplateName,
          enable: row.Enable
        }
      })
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确定删除该模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchDelete([row.Id]).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },

    // 导出数据
    handleDownload() {
      this.downloadLoading = true
      exportExcelFile(this.listQuery).then(response => {
        exportToExcel(response, '打印模板数据')
        this.downloadLoading = false
      }).catch(() => {
        this.downloadLoading = false
      })
    },

    // 导出模板
    handleExportTemplate(row) {
      if (row.TemplateJson) {
        try {
          const template = JSON.parse(row.TemplateJson)
          const dataStr = JSON.stringify(template, null, 2)
          const blob = new Blob([dataStr], { type: 'application/json' })
          const url = URL.createObjectURL(blob)

          const link = document.createElement('a')
          link.href = url
          link.download = `${row.TemplateName}_template.json`
          link.style.display = 'none'

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          URL.revokeObjectURL(url)
          this.$message.success('模板导出成功')
        } catch (error) {
          this.$message.error('模板格式错误，无法导出')
        }
      } else {
        this.$message.warning('该模板没有设计内容')
      }
    },

    // 导入Excel模板
    handleImportTemplate() {
      exportExcelModel().then(response => {
        exportToExcel(response, '打印模板导入模板')
      }).catch(error => {
        this.$message.error('导出模板失败：' + (error.message || '未知错误'))
      })
    },

    // 导入数据
    handleImportData() {
      this.$refs.importFileInput.click()
    },

    // 处理导入文件
    handleImportFile(event) {
      const file = event.target.files[0]
      if (!file) return

      parseExcelFile(file).then(data => {
        if (data && data.length > 0) {
          importExcelData(data).then(() => {
            this.$message.success('导入成功')
            this.getList()
          })
        } else {
          this.$message.warning('文件内容为空')
        }
      }).catch(error => {
        this.$message.error('文件解析失败：' + error.message)
      })

      // 清空文件输入
      event.target.value = ''
    },

    // 处理模板文件
    handleTemplateFile(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const template = JSON.parse(e.target.result)
          this.$refs.templateDialog.show('create', null, template)
        } catch (error) {
          this.$message.error('模板文件格式错误')
        }
      }
      reader.readAsText(file)

      // 清空文件输入
      event.target.value = ''
    },

    // 格式化日期时间
    formatDateTime
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    padding-bottom: 10px;

    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
      margin-right: 10px;
    }
  }

  .text-muted {
    color: #999;
  }
}
</style>
