<template>
  <el-dialog
    title="模板预览"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose"
    class="preview-dialog"
  >
    <div class="preview-container">
      <!-- 预览工具栏 -->
      <div class="preview-toolbar">
        <el-button-group>
          <el-button size="small" icon="el-icon-zoom-out" @click="previewScale(false)" />
          <el-input
            :value="previewScaleDisplay"
            disabled
            size="small"
            style="width: 70px; text-align: center"
          />
          <el-button size="small" icon="el-icon-zoom-in" @click="previewScale(true)" />
        </el-button-group>

        <el-button size="small" type="primary" icon="el-icon-printer" @click="printPreview">
          打印
        </el-button>

        <el-button size="small" type="success" icon="el-icon-download" @click="exportPdf">
          导出PDF
        </el-button>

        <el-button size="small" type="info" icon="el-icon-picture" @click="exportImage">
          导出图片
        </el-button>
      </div>

      <!-- 预览内容 -->
      <div ref="previewContent" class="preview-content">
        <div id="hiprint-preview" class="hiprint-preview" />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { hiprint, defaultElementTypeProvider } from 'vue-plugin-hiprint'

export default {
  name: 'PrintPreview',
  data() {
    return {
      visible: false,
      previewTemplate: null,
      previewScaleValue: 1,
      previewData: {
        // 示例数据
        title: '打印模板预览',
        date: new Date().toLocaleDateString(),
        content: '这是一个示例内容',
        table: [
          { name: '项目1', value: '值1', remark: '备注1' },
          { name: '项目2', value: '值2', remark: '备注2' },
          { name: '项目3', value: '值3', remark: '备注3' }
        ]
      }
    }
  },
  computed: {
    previewScaleDisplay() {
      return `${Math.round(this.previewScaleValue * 100)}%`
    }
  },
  methods: {
    // 显示预览
    show(template) {
      this.visible = true
      this.$nextTick(() => {
        this.renderPreview(template)
      })
    },

    // 渲染预览
    renderPreview(template) {
      if (!template) return

      try {
        this.previewTemplate = new hiprint.PrintTemplate({
          template: template
        })

        // 清空预览容器
        const previewContainer = document.getElementById('hiprint-preview')
        if (previewContainer) {
          previewContainer.innerHTML = ''
        }

        // 渲染预览
        this.previewTemplate.render(previewContainer, this.previewData)

        // 设置预览样式
        this.setPreviewStyle()
      } catch (error) {
        console.error('预览渲染失败:', error)
        this.$message.error('预览渲染失败，请检查模板配置')
      }
    },

    // 设置预览样式
    setPreviewStyle() {
      const previewContainer = document.getElementById('hiprint-preview')
      if (previewContainer) {
        previewContainer.style.transform = `scale(${this.previewScaleValue})`
        previewContainer.style.transformOrigin = 'top left'
      }
    },

    // 预览缩放
    previewScale(isZoomIn) {
      if (isZoomIn) {
        this.previewScaleValue = Math.min(this.previewScaleValue + 0.1, 3)
      } else {
        this.previewScaleValue = Math.max(this.previewScaleValue - 0.1, 0.3)
      }
      this.setPreviewStyle()
    },

    // 打印预览
    printPreview() {
      if (this.previewTemplate) {
        try {
          this.previewTemplate.print(this.previewData)
        } catch (error) {
          console.error('打印失败:', error)
          this.$message.error('打印失败，请检查打印设置')
        }
      }
    },

    // 导出PDF
    exportPdf() {
      if (this.previewTemplate) {
        try {
          this.previewTemplate.toPdf(this.previewData, 'template.pdf')
          this.$message.success('PDF导出成功')
        } catch (error) {
          console.error('PDF导出失败:', error)
          this.$message.error('PDF导出失败')
        }
      }
    },

    // 导出图片
    exportImage() {
      if (this.previewTemplate) {
        try {
          this.previewTemplate.toImg(this.previewData, 'template.png')
          this.$message.success('图片导出成功')
        } catch (error) {
          console.error('图片导出失败:', error)
          this.$message.error('图片导出失败')
        }
      }
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.previewScaleValue = 1

      // 清空预览容器
      const previewContainer = document.getElementById('hiprint-preview')
      if (previewContainer) {
        previewContainer.innerHTML = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-dialog {
  .preview-container {
    .preview-toolbar {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 0;
      border-bottom: 1px solid #e6e6e6;
      margin-bottom: 20px;
    }

    .preview-content {
      max-height: 60vh;
      overflow: auto;
      border: 1px solid #e6e6e6;
      background: #f9f9f9;
      padding: 20px;

      .hiprint-preview {
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin: 0 auto;
        transition: transform 0.3s ease;
      }
    }
  }
}

// 全局样式覆盖
:global(.preview-dialog .el-dialog__body) {
  padding: 20px;
}

:global(.hiprint-preview .hiprint-printPaper) {
  margin: 0 auto !important;
}
</style>
