<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="100px"
      style="width: 500px; margin-left: 50px;"
    >
      <el-form-item label="模板名称" prop="TemplateName">
        <el-input
          v-model="temp.TemplateName"
          placeholder="请输入模板名称"
          maxlength="100"
          show-word-limit
          size="mini"
        />
      </el-form-item>

      <el-form-item label="模板参数" prop="TemplateParameterId">
        <el-select
          v-model="temp.TemplateParameterId"
          placeholder="请选择模板参数"
          clearable
          filterable
          style="width: 100%"
          size="mini"
          @change="handleParameterChange"
        >
          <el-option
            v-for="item in parameterList"
            :key="item.Id"
            :label="item.TemplateName"
            :value="item.Id"
          >
            <span style="float: left">{{ item.TemplateName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.Enable ? '启用' : '禁用' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; font-size: 12px; color: #999;">
          选择模板参数后可以在设计时使用预定义的参数配置
        </div>
      </el-form-item>

      <el-form-item label="启用状态" prop="Enable">
        <el-switch
          v-model="temp.Enable"
          active-text="启用"
          inactive-text="禁用"
          size="mini"
        />
      </el-form-item>

      <el-form-item label="模板内容" prop="TemplateJson">
        <el-input
          v-model="temp.TemplateJson"
          type="textarea"
          :rows="8"
          placeholder="请输入模板JSON内容或点击导入模板文件"
          size="mini"
        />
        <div style="margin-top: 10px;">
          <el-button size="mini" type="primary" @click="importTemplateFile">
            导入模板文件
          </el-button>
          <el-button size="mini" type="success" @click="formatTemplate">
            格式化JSON
          </el-button>
          <el-button size="mini" type="info" @click="validateTemplate">
            验证模板
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="temp.Remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
          size="mini"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" size="mini">
        取消
      </el-button>
      <el-button type="primary" @click="handleConfirm" size="mini">
        确定
      </el-button>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileImport"
    >
  </el-dialog>
</template>

<script>
import { add, update, getById } from '@/api/MD/MD_PrintTemplate'
import { getEnabledList } from '@/api/MD/MD_PrintTemplateParameter'

export default {
  name: 'TemplateDialog',
  data() {
    return {
      dialogVisible: false,
      dialogStatus: '',
      temp: {
        Id: '',
        TemplateName: '',
        Enable: true,
        TemplateJson: '',
        TemplateParameterId: '',
        Remark: ''
      },
      parameterList: [],
      rules: {
        TemplateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        RelateModule: [
          { required: true, message: '请输入关联模块', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogStatus === 'create' ? '新增模板' : '编辑模板'
    }
  },
  methods: {
    // 显示对话框
    show(status, row = null, templateContent = null) {
      this.dialogStatus = status
      this.dialogVisible = true

      // 加载参数模板列表
      this.loadParameterList()

      if (status === 'create') {
        this.resetTemp()
        if (row) {
          // 复制模式：复制现有数据但清空ID
          this.temp = Object.assign({}, row)
          this.temp.Id = '' // 清空ID以创建新记录
          if (this.temp.TemplateJson) {
            try {
              // 格式化JSON显示
              const parsed = JSON.parse(this.temp.TemplateJson)
              this.temp.TemplateJson = JSON.stringify(parsed, null, 2)
            } catch (error) {
              // 如果解析失败，保持原样
              console.warn('模板内容JSON格式错误:', error)
            }
          }
        } else if (templateContent) {
          this.temp.TemplateJson = JSON.stringify(templateContent, null, 2)
        }
      } else if (status === 'edit' && row) {
        this.temp = Object.assign({}, row)
        if (this.temp.TemplateJson) {
          try {
            // 格式化JSON显示
            const parsed = JSON.parse(this.temp.TemplateJson)
            this.temp.TemplateJson = JSON.stringify(parsed, null, 2)
          } catch (error) {
            // 如果解析失败，保持原样
            console.warn('模板内容JSON格式错误:', error)
          }
        }
      }

      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },

    // 重置表单
    resetTemp() {
      this.temp = {
        Id: '',
        TemplateName: '',
        Enable: true,
        TemplateJson: '',
        TemplateParameterId: '',
        Remark: ''
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.resetTemp()
    },

    // 确认操作
    handleConfirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 验证模板内容格式
          if (this.temp.TemplateJson) {
            try {
              JSON.parse(this.temp.TemplateJson)
            } catch (error) {
              this.$message.error('模板内容JSON格式错误，请检查')
              return
            }
          }

          if (this.dialogStatus === 'create') {
            this.createData()
          } else {
            this.updateData()
          }
        }
      })
    },

    // 创建数据
    createData() {
      add(this.temp).then(() => {
        this.$message.success('创建成功')
        this.dialogVisible = false
        this.$emit('refresh')
      }).catch(error => {
        this.$message.error('创建失败：' + (error.message || '未知错误'))
      })
    },

    // 更新数据
    updateData() {
      update(this.temp).then(() => {
        this.$message.success('更新成功')
        this.dialogVisible = false
        this.$emit('refresh')
      }).catch(error => {
        this.$message.error('更新失败：' + (error.message || '未知错误'))
      })
    },

    // 导入模板文件
    importTemplateFile() {
      this.$refs.fileInput.click()
    },

    // 处理文件导入
    handleFileImport(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const template = JSON.parse(e.target.result)
          this.temp.TemplateJson = JSON.stringify(template, null, 2)
          this.$message.success('模板文件导入成功')
        } catch (error) {
          this.$message.error('模板文件格式错误：' + error.message)
        }
      }
      reader.readAsText(file)

      // 清空文件输入
      event.target.value = ''
    },

    // 格式化模板JSON
    formatTemplate() {
      if (!this.temp.TemplateJson) {
        this.$message.warning('请先输入模板内容')
        return
      }

      try {
        const parsed = JSON.parse(this.temp.TemplateJson)
        this.temp.TemplateJson = JSON.stringify(parsed, null, 2)
        this.$message.success('JSON格式化成功')
      } catch (error) {
        this.$message.error('JSON格式错误：' + error.message)
      }
    },

    // 验证模板格式
    validateTemplate() {
      if (!this.temp.TemplateJson) {
        this.$message.warning('请先输入模板内容')
        return
      }

      try {
        const template = JSON.parse(this.temp.TemplateJson)

        // 基本结构验证
        if (!template || typeof template !== 'object') {
          this.$message.error('模板格式不正确')
          return
        }

        // 检查必要字段
        if (!template.panels && !template.template) {
          this.$message.error('缺少模板内容（panels或template字段）')
          return
        }

        this.$message.success('模板格式验证通过')
      } catch (error) {
        this.$message.error('JSON格式错误：' + error.message)
      }
    },

    // 加载参数模板列表
    loadParameterList() {
      getEnabledList().then(response => {
        this.parameterList = response.Data.rows || []
      }).catch(error => {
        console.error('加载参数模板列表失败:', error)
        this.parameterList = []
      })
    },

    // 处理参数模板变化
    handleParameterChange(parameterId) {
      if (parameterId) {
        const selectedParameter = this.parameterList.find(item => item.Id === parameterId)
        if (selectedParameter) {
          this.$message.success(`已选择参数模板: ${selectedParameter.TemplateName}`)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
