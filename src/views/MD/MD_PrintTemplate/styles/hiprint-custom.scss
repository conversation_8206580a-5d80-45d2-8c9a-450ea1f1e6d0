// hiprint 自定义样式

// 设计器容器样式 - 使用hiprint内置标尺
.hiprint-printTemplate {
  height: 100% !important;
  width: 100% !important;
  min-height: 600px !important;
  position: relative;
  margin: 0 !important;

  // 移除自定义网格背景，让hiprint自己处理

  // 纸张样式 - 让hiprint自己处理尺寸和样式
  .hiprint-printPaper {
    // 只保留基本样式，让hiprint控制尺寸
    position: relative;
    z-index: 1;
  }

  // 让hiprint自己处理标尺样式
}

// 拖拽元素样式
.ep-draggable-item {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s ease;
  background: white;
  text-decoration: none;
  color: #303133;

  &:hover {
    border-color: #409eff;
    background-color: #ecf5ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  .glyphicon,
  i {
    font-size: 20px;
    margin-bottom: 4px;
    color: #606266;
  }

  .glyphicon-class,
  span {
    font-size: 12px;
    color: #303133;
    text-align: center;
    line-height: 1.2;
  }
}

// 属性面板样式优化
#PrintElementOptionSetting {
  .hiprint-option-item {
    margin-bottom: 12px;

    .hiprint-option-item-label {
      font-size: 12px;
      color: #606266;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .hiprint-option-item-field {
      input,
      select,
      textarea {
        width: 100%;
        padding: 4px 8px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        font-size: 12px;
        transition: border-color 0.3s;

        &:focus {
          border-color: #409eff;
          outline: none;
        }
      }

      input[type="color"] {
        height: 28px;
        padding: 2px;
      }

      input[type="checkbox"] {
        width: auto;
        margin-right: 6px;
      }
    }
  }

  // 分组样式
  .hiprint-option-group {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 12px;

    .hiprint-option-group-title {
      background: #f5f7fa;
      padding: 8px 12px;
      border-bottom: 1px solid #e4e7ed;
      font-size: 12px;
      font-weight: 600;
      color: #303133;
    }

    .hiprint-option-group-body {
      padding: 12px;
    }
  }

  // 按钮样式
  .hiprint-option-button {
    padding: 4px 12px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: white;
    color: #606266;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      color: #409eff;
    }

    &.active {
      background: #409eff;
      border-color: #409eff;
      color: white;
    }
  }
}

// 选中元素样式
.hiprint-printElement.hiprint-printElement-selected {
  border: 2px solid #409eff !important;

  .hiprint-printElement-handle {
    background: #409eff;
    border: 1px solid #409eff;
  }
}

// 悬停元素样式
.hiprint-printElement:hover {
  border: 1px dashed #409eff !important;
}

// 页眉页脚线样式
.hiprint-headerLine,
.hiprint-footerLine {
  border-color: #409eff !important;

  &:hover {
    border-top: 2px dashed #409eff !important;

    &::before {
      content: attr(data-title);
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      background: white;
      color: #409eff;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 2px;
      top: -12px;
      white-space: nowrap;
    }
  }
}

.hiprint-headerLine:hover::before {
  content: "页眉线";
}

.hiprint-footerLine:hover::before {
  content: "页脚线";
}

// 右键菜单样式
.hiprint-contextMenu {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 4px 0;

  .hiprint-contextMenu-item {
    padding: 8px 16px;
    font-size: 12px;
    color: #606266;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background: #f5f7fa;
      color: #409eff;
    }

    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #c0c4cc;
      }
    }
  }

  .hiprint-contextMenu-divider {
    height: 1px;
    background: #e4e7ed;
    margin: 4px 0;
  }
}

// 工具提示样式
.hiprint-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  position: absolute;
  z-index: 1000;
  pointer-events: none;
}

// 加载动画
.hiprint-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .hiprint-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409eff;
    border-radius: 50%;
    animation: hiprint-spin 1s linear infinite;
  }

  .hiprint-loading-text {
    margin-top: 10px;
    font-size: 14px;
    color: #606266;
    text-align: center;
  }
}

@keyframes hiprint-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 1200px) {
  .hiprint-printTemplate {
    .hiprint-printPaper {
      margin: 10px auto;
    }
  }

  #PrintElementOptionSetting {
    .hiprint-option-item {
      margin-bottom: 8px;
    }

    .hiprint-option-group {
      margin-bottom: 8px;

      .hiprint-option-group-body {
        padding: 8px;
      }
    }
  }
}

// 打印预览样式
.hiprint-preview {
  .hiprint-printPaper {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
  }
}

// 滚动条样式
.canvas-container,
.hiprint-printTemplate,
#PrintElementOptionSetting {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 确保画布容器可以滚动
.canvas-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

// 对齐按钮样式
.el-button-group {
  .el-button {
    padding: 8px 12px;

    .glyphicon {
      font-size: 14px;
    }
  }
}

// Glyphicon图标样式（如果项目中没有引入）
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 对齐图标
.glyphicon-object-align-left:before { content: "\e236"; }
.glyphicon-object-align-vertical:before { content: "\e235"; }
.glyphicon-object-align-right:before { content: "\e237"; }
.glyphicon-object-align-top:before { content: "\e238"; }
.glyphicon-object-align-horizontal:before { content: "\e234"; }
.glyphicon-object-align-bottom:before { content: "\e239"; }
.glyphicon-resize-horizontal:before { content: "\e120"; }
.glyphicon-resize-vertical:before { content: "\e121"; }

// 隐藏hiprint的添加功能
.hiprint-printTemplate {
  // 隐藏添加按钮和相关功能
  .hiprint-addPrintElement,
  .hiprint-addPrintElementTypes,
  .hiprint-addPanel,
  .hiprint-pagination,
  .hiprint-addPage,
  .hiprint-addPageBtn,
  [class*="add"],
  [class*="Add"],
  [title*="添加"],
  [title*="新增"] {
    display: none !important;
  }

  // 隐藏包含"+"的元素
  *:before,
  *:after {
    &[content="+"],
    &[content="＋"] {
      display: none !important;
    }
  }

  // 隐藏可能的添加图标
  .fa-plus,
  .glyphicon-plus,
  .el-icon-plus,
  .anticon-plus {
    display: none !important;
  }

  // 更强力的隐藏规则 - 隐藏所有可能的添加相关元素
  div[title*="添加"],
  div[title*="新增"],
  div[title*="Add"],
  div[title*="+"],
  span[title*="添加"],
  span[title*="新增"],
  span[title*="Add"],
  span[title*="+"],
  button[title*="添加"],
  button[title*="新增"],
  button[title*="Add"],
  button[title*="+"],
  a[title*="添加"],
  a[title*="新增"],
  a[title*="Add"],
  a[title*="+"] {
    display: none !important;
    visibility: hidden !important;
  }

  // 隐藏包含"+"文本的元素
  *:contains("+"),
  *:contains("＋"),
  *:contains("添加"),
  *:contains("新增") {
    &:not(.component-item):not(.el-button):not(.toolbar *) {
      display: none !important;
    }
  }
}

// 强制移除所有空白区域
.design-canvas {
  .el-card,
  .el-card__body,
  .canvas-container,
  .hiprint-printTemplate {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
  }
}

// 移除hiprint可能的默认空白
.hiprint-printTemplate {
  * {
    box-sizing: border-box;
  }

  // 移除可能的容器空白
  .hiprint-printPaper-container,
  .hiprint-printPanel,
  .hiprint-design-container {
    padding: 0 !important;
    margin: 0 !important;
  }

  // 移除纸张的默认边距，让它紧贴容器
  .hiprint-printPaper {
    margin: 0 !important;
  }
}
