<template>
  <div class="hiprint-test">
    <h2>Hiprint 拖拽测试</h2>

    <!-- 组件库 -->
    <div class="component-panel">
      <h3>组件库</h3>
      <div class="components">
        <div class="ep-draggable-item" tid="defaultModule.text">
          <i class="el-icon-edit-outline" />
          <span>文本</span>
        </div>
        <div class="ep-draggable-item" tid="defaultModule.image">
          <i class="el-icon-picture-outline" />
          <span>图片</span>
        </div>
        <div class="ep-draggable-item" tid="defaultModule.table">
          <i class="el-icon-s-grid" />
          <span>表格</span>
        </div>
        <div class="ep-draggable-item" tid="defaultModule.hline">
          <i class="el-icon-minus" />
          <span>横线</span>
        </div>
      </div>
    </div>

    <!-- 设计画布 -->
    <div class="design-area">
      <h3>设计画布</h3>
      <div id="hiprint-printTemplate" class="hiprint-printTemplate" />
    </div>

    <!-- 属性面板 -->
    <div class="property-panel">
      <h3>属性设置</h3>
      <div id="PrintElementOptionSetting" />
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="initTest">初始化</el-button>
      <el-button @click="buildDrag">构建拖拽</el-button>
      <el-button @click="testPrint">测试打印</el-button>
    </div>
  </div>
</template>

<script>
import { hiprint, defaultElementTypeProvider } from 'vue-plugin-hiprint'
import $ from 'jquery'
import 'vue-plugin-hiprint/dist/print-lock.css'

// 挂载到全局
window.$ = window.jQuery = $
window.hiprint = hiprint

export default {
  name: 'HiprintTest',
  data() {
    return {
      hiprintTemplate: null
    }
  },
  mounted() {
    this.initTest()
  },
  methods: {
    initTest() {
      try {
        console.log('开始初始化hiprint...')

        // 初始化hiprint，配置默认provider
        hiprint.init({
          providers: [new defaultElementTypeProvider()]
        })
        console.log('hiprint初始化完成')

        // 创建模板实例
        this.hiprintTemplate = new hiprint.PrintTemplate({
          template: {},
          settingContainer: '#PrintElementOptionSetting',
          paginationContainer: '.hiprint-printTemplate'
        })
        console.log('模板实例创建完成')

        // 启用设计模式
        this.hiprintTemplate.design('#hiprint-printTemplate')
        console.log('设计模式启用完成')

        // 设置纸张
        this.hiprintTemplate.setPaper(210, 297)
        console.log('纸张设置完成')

        // 构建拖拽元素
        this.$nextTick(() => {
          this.buildDrag()
        })
      } catch (error) {
        console.error('初始化失败:', error)
        this.$message.error('初始化失败: ' + error.message)
      }
    },

    buildDrag() {
      try {
        console.log('开始构建拖拽元素...')

        // 检查hiprint是否可用
        if (!window.hiprint || !window.hiprint.PrintElementTypeManager) {
          console.error('hiprint.PrintElementTypeManager 不可用')
          return
        }

        // 获取拖拽元素
        const dragElements = $('.ep-draggable-item')
        console.log('找到拖拽元素数量:', dragElements.length)

        if (dragElements.length > 0) {
          // 构建拖拽元素
          window.hiprint.PrintElementTypeManager.buildByHtml(dragElements)
          console.log('拖拽元素构建完成')
          this.$message.success('拖拽功能已启用')
        } else {
          console.error('未找到拖拽元素')
        }
      } catch (error) {
        console.error('构建拖拽元素失败:', error)
        this.$message.error('构建拖拽失败: ' + error.message)
      }
    },

    testPrint() {
      if (this.hiprintTemplate) {
        try {
          this.hiprintTemplate.print({
            title: '测试标题',
            content: '测试内容'
          })
        } catch (error) {
          console.error('打印失败:', error)
          this.$message.error('打印失败: ' + error.message)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hiprint-test {
  padding: 20px;

  .component-panel {
    margin-bottom: 20px;

    .components {
      display: flex;
      gap: 10px;

      .ep-draggable-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: move;
        background: white;

        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
        }

        i {
          font-size: 20px;
          margin-bottom: 5px;
        }

        span {
          font-size: 12px;
        }
      }
    }
  }

  .design-area {
    margin-bottom: 20px;

    .hiprint-printTemplate {
      min-height: 400px;
      border: 1px solid #ddd;
      background: #f5f5f5;
    }
  }

  .property-panel {
    margin-bottom: 20px;

    #PrintElementOptionSetting {
      min-height: 200px;
      border: 1px solid #ddd;
      padding: 10px;
    }
  }

  .actions {
    .el-button {
      margin-right: 10px;
    }
  }
}
</style>
