<template>
  <div class="print-template-editor">
    <!-- 模板信息栏 -->
    <div class="template-info-bar">
      <el-row :gutter="20" type="flex" align="middle">
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">模板名称：</span>
            <span class="info-value">{{ currentTemplate.TemplateName || '未命名模板' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">启用状态：</span>
            <el-tag :type="currentTemplate.Enable ? 'success' : 'danger'" size="small">
              {{ currentTemplate.Enable ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">模板参数：</span>
            <span class="info-value">{{ currentTemplate.TemplateParameterName || '未设置' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" type="flex" align="middle" style="margin-top: 10px;">
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">最后更新：</span>
            <span class="info-value">{{ formatDateTime(currentTemplate.MTime) || '未保存' }}</span>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-if="currentParameters" class="info-item">
            <el-button size="mini" type="info" @click="viewParameters">
              查看参数配置
            </el-button>
            <el-button size="mini" type="primary" @click="showSubtableDialog">
              查看子表信息
            </el-button>
            <el-button size="mini" type="success" @click="applyParameters">
              应用参数到设计
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-row :gutter="10" type="flex" align="middle">
        <!-- 纸张设置 -->
        <el-col :span="8">
          <el-button-group>
            <el-button
              v-for="(value, type) in paperTypes"
              :key="type"
              :type="curPaperType === type ? 'primary' : 'default'"
              size="small"
              @click="setPaper(type, value)"
            >
              {{ type }}
            </el-button>
          </el-button-group>

          <el-popover
            v-model="paperPopVisible"
            placement="bottom"
            title="设置纸张宽高(mm)"
            trigger="click"
            width="280"
          >
            <div class="paper-setting">
              <el-input-group>
                <el-input
                  v-model="paperWidth"
                  type="number"
                  placeholder="宽(mm)"
                  style="width: 100px"
                />
                <el-input
                  value="×"
                  disabled
                  style="width: 30px; text-align: center"
                />
                <el-input
                  v-model="paperHeight"
                  type="number"
                  placeholder="高(mm)"
                  style="width: 100px"
                />
              </el-input-group>
              <el-button
                type="primary"
                size="small"
                style="width: 100%; margin-top: 10px"
                @click="setCustomPaper"
              >
                确定
              </el-button>
            </div>
            <el-button
              slot="reference"
              :type="curPaperType === 'custom' ? 'primary' : 'default'"
              size="small"
            >
              自定义
            </el-button>
          </el-popover>
        </el-col>

        <!-- 操作按钮 -->
        <el-col :span="16">
          <el-button size="small" type="primary" icon="el-icon-s-grid" @click="goToTemplateList">
            模板管理
          </el-button>
          <el-button size="small" type="success" icon="el-icon-check" @click="saveTemplate">
            保存模板
          </el-button>
          <el-divider direction="vertical" />
          <el-button size="small" type="primary" icon="el-icon-refresh-right" @click="rotatePaper">
            旋转
          </el-button>
          <el-button size="small" type="success" icon="el-icon-view" @click="previewTemplate">
            预览
          </el-button>
          <el-button size="small" type="warning" icon="el-icon-printer" @click="printTemplate">
            打印
          </el-button>
          <el-button size="small" type="info" icon="el-icon-download" @click="exportTemplate">
            导出
          </el-button>
          <el-button size="small" type="info" icon="el-icon-upload2" @click="importTemplate">
            导入
          </el-button>
          <el-button size="small" type="default" icon="el-icon-info" @click="debugCanvas">
            调试
          </el-button>

          <el-button size="small" type="danger" icon="el-icon-delete" @click="confirmClearTemplate">
            清空
          </el-button>
        </el-col>
      </el-row>

      <!-- 第二行：对齐功能和缩放控制 -->
      <el-row :gutter="10" style="margin-top: 10px; padding: 0 10px;">
        <el-col :span="24">
          <div class="toolbar-second-row">
            <span class="btn-text-desc">元素对齐/间距(需先选中):</span>
            <el-button size="small" type="primary" @click="setElsSpace(true)">
              水平间距10
            </el-button>
            <el-button size="small" type="primary" @click="setElsSpace(false)">
              垂直间距10
            </el-button>

            <!-- 对齐按钮组 - 修正图标 -->
            <el-button-group>
              <el-button size="small" title="左对齐" @click="setElsAlign('left')">
                <i class="el-icon-back" />
              </el-button>
              <el-button size="small" title="水平居中" @click="setElsAlign('vertical')">
                <i class="el-icon-minus" />
              </el-button>
              <el-button size="small" title="右对齐" @click="setElsAlign('right')">
                <i class="el-icon-right" />
              </el-button>
              <el-button size="small" title="顶部对齐" @click="setElsAlign('top')">
                <i class="el-icon-top" />
              </el-button>
              <el-button size="small" title="垂直居中" @click="setElsAlign('horizontal')">
                <i class="el-icon-rank" />
              </el-button>
              <el-button size="small" title="底部对齐" @click="setElsAlign('bottom')">
                <i class="el-icon-bottom" />
              </el-button>
              <el-button size="small" title="横向分散" @click="setElsAlign('distributeHor')">
                <i class="el-icon-d-arrow-left" />
              </el-button>
              <el-button size="small" title="纵向分散" @click="setElsAlign('distributeVer')">
                <i class="el-icon-d-arrow-right" />
              </el-button>
            </el-button-group>

            <!-- 缩放控制移到第二行 -->
            <el-divider direction="vertical" />
            <span class="btn-text-desc">缩放:</span>
            <el-button-group>
              <el-button size="small" icon="el-icon-zoom-out" @click="changeScale(false)" />
              <el-input
                :value="scaleDisplay"
                disabled
                size="small"
                style="width: 70px; text-align: center"
              />
              <el-button size="small" icon="el-icon-zoom-in" @click="changeScale(true)" />
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-container">
      <el-row :gutter="10" style="height: 100%">
        <!-- 组件库 -->
        <el-col :span="4" class="component-panel">
          <el-card shadow="never" style="height: 100%">
            <div slot="header" class="panel-header">
              <span>组件库</span>
            </div>
            <div class="component-list">
              <!-- 基础组件 -->
              <div class="component-group">
                <div class="group-title">基础组件</div>
                <div class="component-grid">
                  <div
                    v-for="component in basicComponents"
                    :key="component.type"
                    class="component-item ep-draggable-item"
                    :tid="`defaultModule.${component.type}`"
                  >
                    <i :class="component.icon" />
                    <span>{{ component.name }}</span>
                  </div>
                </div>
              </div>

              <!-- 辅助组件 -->
              <div class="component-group">
                <div class="group-title">辅助组件</div>
                <div class="component-grid">
                  <div
                    v-for="component in auxiliaryComponents"
                    :key="component.type"
                    class="component-item ep-draggable-item"
                    :tid="`defaultModule.${component.type}`"
                  >
                    <i :class="component.icon" />
                    <span>{{ component.name }}</span>
                  </div>
                </div>
              </div>

              <!-- 条码组件 -->
              <div class="component-group">
                <div class="group-title">条码组件</div>
                <div class="component-grid">
                  <div
                    v-for="component in barcodeComponents"
                    :key="component.type"
                    class="component-item ep-draggable-item"
                    :tid="`defaultModule.${component.type}`"
                  >
                    <i :class="component.icon" />
                    <span>{{ component.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 设计画布 -->
        <el-col :span="15" class="design-canvas">
          <el-card shadow="never" style="height: 100%; padding: 0; margin: 0;" class="design-card">
            <div class="canvas-container">
              <div id="hiprint-printTemplate" class="hiprint-printTemplate" />
            </div>
          </el-card>
        </el-col>

        <!-- 属性面板 -->
        <el-col :span="5" class="property-panel">
          <el-card shadow="never" style="height: 100%">
            <div slot="header" class="panel-header">
              <span>属性设置</span>
            </div>

            <!-- 参数选择区域 - 只在选中元素时显示 -->

            <div v-if="currentParameters && isElementReallySelected" class="parameter-selector">
              <!-- 基础参数选择 -->
              <div v-if="!isTableElement(currentSelectedElement)" class="basic-parameter-selector">
                <div class="selector-header">
                  <span class="selector-title">参数选择</span>
                  <el-tooltip content="选择参数后将自动设置到当前选中元素的字段名" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </div>
                <el-select
                  v-model="selectedParameter"
                  placeholder="请选择参数"
                  size="small"
                  style="width: 100%"
                  @change="applyParameterToElement"
                >
                  <el-option
                    v-for="param in parameterOptions"
                    :key="param.value"
                    :label="`${param.label} (${param.value})`"
                    :value="param.value"
                  />
                </el-select>
              </div>

              <!-- 表格元素的子表选择 -->
              <div v-if="isTableElement(currentSelectedElement)" class="table-parameter-selector">
                <div class="selector-header">
                  <span class="selector-title">子表选择</span>
                  <el-tooltip content="为表格元素选择对应的子表数据源" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </div>
                <el-select
                  v-model="selectedSubtable"
                  placeholder="请选择子表"
                  size="small"
                  style="width: 100%"
                  @change="applySubtableToElement"
                >
                  <el-option
                    v-for="subtable in subtableOptions"
                    :key="subtable.key"
                    :label="`${subtable.name} (${subtable.key})`"
                    :value="subtable.key"
                  />
                </el-select>
              </div>
            </div>

            <div id="PrintElementOptionSetting" class="property-container" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 预览对话框 -->
    <print-preview ref="previewDialog" />

    <!-- 子表信息弹窗 -->
    <el-dialog
      title="子表信息"
      :visible.sync="subtableDialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="subtableOptions.length > 0" class="subtable-dialog-content">
        <div v-for="subtable in subtableOptions" :key="subtable.key" class="subtable-item">
          <div class="subtable-header">
            <h4>{{ subtable.name }} ({{ subtable.key }})</h4>
          </div>
          <div class="subtable-fields">
            <el-table :data="subtable.columns" border size="small">
              <el-table-column prop="title" label="字段名称" width="200" />
              <el-table-column prop="field" label="字段标识" width="200" />
              <el-table-column label="完整标识" min-width="200">
                <template slot-scope="scope">
                  <el-tag size="small">{{ scope.row.title }}#{{ scope.row.field }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="applyFieldToElement(scope.row, subtable)"
                  >
                    应用到元素
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div v-else class="no-subtable-info">
        <el-empty description="暂无子表信息" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="subtableDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 导入文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileImport"
    >

  </div>
</template>

<script>
// 导入hiprint相关模块
import { hiprint, defaultElementTypeProvider } from 'vue-plugin-hiprint'
import $ from 'jquery'
import PrintPreview from './components/PrintPreview.vue'
import {
  paperSizes,
  componentTypes,
  defaultTemplateData,
  validateTemplateJson,
  formatTemplateJson,
  generateTemplateFileName,
  downloadFile,
  readFileContent
} from './utils/template-utils'
// 导入API服务
import { getById, update } from '@/api/MD/MD_PrintTemplate'
import { getById as getParameterById } from '@/api/MD/MD_PrintTemplateParameter'

// 导入hiprint样式
import 'vue-plugin-hiprint/dist/print-lock.css'

// 将jQuery和hiprint挂载到全局
window.$ = window.jQuery = $
window.hiprint = hiprint

export default {
  name: 'PrintTemplateEditor',
  components: {
    PrintPreview
  },
  data() {
    return {
      // 模板实例
      hiprintTemplate: null,

      // 纸张设置
      paperTypes: paperSizes,
      curPaper: {
        type: 'A4',
        width: 210,
        height: 296.6
      },
      paperPopVisible: false,
      paperWidth: '220',
      paperHeight: '80',

      // 缩放设置
      scaleValue: 1,
      scaleMax: 5,
      scaleMin: 0.5,

      // 组件定义
      basicComponents: componentTypes.basic,
      auxiliaryComponents: componentTypes.auxiliary,
      barcodeComponents: componentTypes.barcode,

      // 模板数据
      templateData: defaultTemplateData,

      // 数据库模板相关
      currentTemplateId: null,
      currentTemplate: {
        Id: '',
        TemplateName: '',
        Enable: true,
        TemplateJson: '',
        TemplateParameterId: '',
        Remark: '',
        CUser: '',
        CTime: null,
        MUser: '',
        MTime: null
      },
      currentParameters: null, // 当前模板的参数配置
      parameterOptions: [], // 参数选项列表
      subtableOptions: [], // 子表选项列表
      selectedParameter: '', // 当前选中的参数
      selectedSubtable: '', // 当前选中的子表
      currentSelectedElement: null, // 当前选中的设计元素
      titleWatcherInterval: null, // 标题值监听器
      isElementReallySelected: false, // 标记元素是否真正被点击选中
      isManuallySelected: false, // 是否是手动设置的选中状态
      subtableDialogVisible: false, // 子表信息弹窗显示状态
      selectionPollingTimer: null, // 选中状态轮询定时器

      // 属性面板文本追踪
      lastPanelText: '', // 上次的属性面板文本

      // 用户点击追踪
      userClickedElement: null, // 用户最后点击的元素
      userClickTime: 0 // 用户最后点击的时间

    }
  },
  computed: {
    curPaperType() {
      const { width, height } = this.curPaper
      // eslint-disable-next-line no-unused-vars
      for (const [type, paper] of Object.entries(this.paperTypes)) {
        if (paper.width === width && paper.height === height) {
          return type
        }
      }
      return 'custom'
    },
    scaleDisplay() {
      return `${Math.round(this.scaleValue * 100)}%`
    }
  },
  mounted() {
    this.initHiprint()

    // 设置全局点击监听器
    this.setupGlobalClickListener()

    // 检查URL参数，如果有templateId则加载对应模板
    if (this.$route.query.templateId) {
      this.currentTemplateId = this.$route.query.templateId

      // 如果URL中有模板基本信息，先设置到currentTemplate中
      if (this.$route.query.templateName) {
        this.currentTemplate.Id = this.$route.query.templateId
        this.currentTemplate.TemplateName = this.$route.query.templateName
        this.currentTemplate.Enable = this.$route.query.enable === 'true'
      }

      this.loadTemplateFromDatabase(this.currentTemplateId)
    }
  },
  beforeDestroy() {
    // 清理事件监听器
    this.clearEventListeners()

    // 清理jQuery事件监听器
    $(document).off('click.hiprintElement')
    $(document).off('click.hiprintTable')
    $(document).off('click.hiprintTableHeader')
    $(document).off('click.hiprintTableContent')
    $(document).off('click.hiprintTableCell')

    // 清理DOM事件监听器
    if (this.canvasClickHandler) {
      const canvas = document.querySelector('#hiprint-printTemplate')
      if (canvas) {
        canvas.removeEventListener('click', this.canvasClickHandler)
      }
    }

    if (this.globalClickHandler) {
      document.removeEventListener('click', this.globalClickHandler)
    }

    // 清理全局事件监听器
    if (typeof window.hinnn !== 'undefined' && window.hinnn.event) {
      window.hinnn.event.off('onSelectPanel')
      window.hinnn.event.off('onElementClick')
    }
  },
  methods: {
    // 初始化hiprint
    initHiprint() {
      try {
        console.log('开始初始化hiprint...')

        // 初始化hiprint，配置默认provider和标尺
        hiprint.init({
          // eslint-disable-next-line new-cap
          providers: [new defaultElementTypeProvider()],
          // 启用标尺相关配置
          ruler: true,
          showRuler: true
        })
        console.log('hiprint初始化完成（带标尺）')

        // 等待DOM渲染完成
        this.$nextTick(() => {
          // 创建模板实例，参考原项目配置
          this.hiprintTemplate = new hiprint.PrintTemplate({
            template: {},
            settingContainer: '#PrintElementOptionSetting',
            // 添加数据变化监听器
            onDataChanged: (type, json) => {
              console.log('hiprint onDataChanged 事件触发:', type, json)

              // 延迟检查选中状态，确保hiprint内部状态已更新
              setTimeout(() => {
                this.handleHiprintDataChanged(type, json)
              }, 100)
            }
            // 不设置paginationContainer，避免显示分页和添加功能
          })
          console.log('模板实例创建完成')

          // 启用设计模式，启用网格和标尺
          this.hiprintTemplate.design('#hiprint-printTemplate', {
            grid: true, // 启用网格
            ruler: true, // 启用标尺
            showRuler: true, // 显示标尺
            addElementTypes: false, // 禁用添加元素类型
            showAddPanel: false, // 禁用添加面板
            showPagination: false // 禁用分页
          })
          console.log('设计模式启用完成（带网格，无添加功能）')

          // 设置默认纸张 - 确保画布显示
          const defaultPaper = this.paperTypes.A4
          this.hiprintTemplate.setPaper(defaultPaper.width, defaultPaper.height)
          this.curPaper = {
            type: 'A4',
            width: defaultPaper.width,
            height: defaultPaper.height
          }
          console.log('纸张设置完成: A4 (210×297mm)')

          // 添加调试信息
          console.log('=== 调试信息 ===')
          console.log('hiprintTemplate:', this.hiprintTemplate)
          console.log('hiprintTemplate.getSelectEls:', typeof this.hiprintTemplate.getSelectEls)
          console.log('jQuery可用:', typeof $ !== 'undefined')
          console.log('window.hinnn可用:', typeof window.hinnn !== 'undefined')
          console.log('==================')

          // 添加元素选择变化监听
          this.setupElementSelectionListener()

          // 延迟构建拖拽元素，确保DOM完全渲染
          setTimeout(() => {
            this.buildDragElements()
            this.forceCanvasHeight()
            this.removeAddFunctions()

            // 在拖拽元素构建完成后，再次设置监听器
            console.log('重新设置监听器...')
            this.setupElementSelectionListener()
          }, 500)
        })
      } catch (error) {
        console.error('Hiprint初始化失败:', error)
        this.$message.error('模板编辑器初始化失败，请刷新页面重试')
      }
    },

    // 构建拖拽元素
    buildDragElements() {
      try {
        console.log('开始构建拖拽元素...')

        // 检查hiprint是否可用
        if (!window.hiprint || !window.hiprint.PrintElementTypeManager) {
          console.error('hiprint.PrintElementTypeManager 不可用')
          this.$message.error('hiprint未正确初始化')
          return
        }

        // 获取拖拽元素
        const dragElements = $('.ep-draggable-item')
        console.log('找到拖拽元素数量:', dragElements.length)

        if (dragElements.length > 0) {
          // 构建拖拽元素
          window.hiprint.PrintElementTypeManager.buildByHtml(dragElements)
          console.log('拖拽元素构建完成')
          this.$message.success('模板编辑器初始化完成，可以开始拖拽设计了！')
        } else {
          console.error('未找到拖拽元素')
          this.$message.warning('未找到可拖拽的组件')
        }
      } catch (error) {
        console.error('构建拖拽元素失败:', error)
        this.$message.error('拖拽功能初始化失败: ' + error.message)
      }
    },

    // 设置纸张
    setPaper(type, paper) {
      this.curPaper = {
        type,
        width: paper.width,
        height: paper.height
      }

      if (this.hiprintTemplate) {
        // 设置纸张尺寸（单位：mm）
        this.hiprintTemplate.setPaper(paper.width, paper.height)
        console.log(`纸张设置为: ${type} (${paper.width}×${paper.height}mm)`)

        // 确保画布高度足够
        setTimeout(() => {
          this.forceCanvasHeight()
        }, 200)
      }
      this.paperPopVisible = false
    },

    // 设置自定义纸张
    setCustomPaper() {
      const width = parseFloat(this.paperWidth)
      const height = parseFloat(this.paperHeight)

      if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
        this.$message.error('请输入有效的纸张尺寸')
        return
      }

      this.setPaper('custom', { width, height })
    },

    // 缩放控制
    changeScale(isZoomIn) {
      if (isZoomIn) {
        this.scaleValue = Math.min(this.scaleValue + 0.1, this.scaleMax)
      } else {
        this.scaleValue = Math.max(this.scaleValue - 0.1, this.scaleMin)
      }

      if (this.hiprintTemplate) {
        this.hiprintTemplate.zoom(this.scaleValue)
      }
    },

    // 旋转纸张
    rotatePaper() {
      try {
        const { width, height } = this.curPaper
        console.log(`旋转前纸张尺寸: ${width}×${height}mm`)

        // 交换宽高
        this.curPaper.width = height
        this.curPaper.height = width

        if (this.hiprintTemplate) {
          // 设置新的纸张尺寸
          this.hiprintTemplate.setPaper(height, width)
          console.log(`旋转后纸张尺寸: ${height}×${width}mm`)

          // 尝试刷新模板
          try {
            // 获取当前模板数据
            const currentTemplate = this.hiprintTemplate.getJson()
            // 重新设置模板
            this.hiprintTemplate.update(currentTemplate)
          } catch (e) {
            console.log('模板刷新失败，使用基础方法')
          }

          // 强制刷新画布高度
          setTimeout(() => {
            this.forceCanvasHeight()
          }, 300)

          this.$message.success(`纸张已旋转为 ${height}×${width}mm`)
        } else {
          this.$message.error('模板未初始化，无法旋转')
        }
      } catch (error) {
        console.error('旋转纸张失败:', error)
        this.$message.error('旋转失败: ' + error.message)
      }
    },

    // 预览模板
    previewTemplate() {
      if (!this.hiprintTemplate) {
        this.$message.warning('请先设计模板')
        return
      }

      const template = this.hiprintTemplate.getJson()
      this.$refs.previewDialog.show(template)
    },

    // 打印模板
    printTemplate() {
      if (!this.hiprintTemplate) {
        this.$message.warning('请先设计模板')
        return
      }

      try {
        this.hiprintTemplate.print(this.templateData)
      } catch (error) {
        console.error('打印失败:', error)
        this.$message.error('打印失败，请检查打印设置')
      }
    },

    // 导出模板
    exportTemplate() {
      if (!this.hiprintTemplate) {
        this.$message.warning('请先设计模板')
        return
      }

      try {
        const template = this.hiprintTemplate.getJson()
        const dataStr = formatTemplateJson(template)
        const filename = generateTemplateFileName('print_template')

        downloadFile(dataStr, filename)
        this.$message.success('模板导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },

    // 导入模板
    importTemplate() {
      this.$refs.fileInput.click()
    },

    // 处理文件导入
    async handleFileImport(event) {
      const file = event.target.files[0]
      if (!file) return

      try {
        const content = await readFileContent(file)
        const validation = validateTemplateJson(content)

        if (!validation.valid) {
          this.$message.error(validation.error)
          return
        }

        this.loadTemplate(validation.template)
        this.$message.success('模板导入成功')
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败，请检查文件格式')
      } finally {
        // 清空input值，允许重复选择同一文件
        event.target.value = ''
      }
    },

    // 加载模板
    loadTemplate(template) {
      if (this.hiprintTemplate) {
        this.hiprintTemplate.update(template)
      }
    },

    // 检查画布状态
    forceCanvasHeight() {
      try {
        // 简化检查，让hiprint自己处理布局
        setTimeout(() => {
          const paper = document.querySelector('.hiprint-printPaper')
          if (paper) {
            console.log('纸张尺寸:', paper.offsetWidth + 'x' + paper.offsetHeight)
            console.log('纸张位置:', paper.offsetTop, paper.offsetLeft)
          }

          const container = document.getElementById('hiprint-printTemplate')
          if (container) {
            console.log('画布容器尺寸:', container.offsetWidth + 'x' + container.offsetHeight)
          }
        }, 500)
      } catch (error) {
        console.error('检查画布状态失败:', error)
      }
    },

    // 调试画布尺寸
    debugCanvas() {
      const canvasContainer = document.querySelector('.canvas-container')
      const hiprintContainer = document.getElementById('hiprint-printTemplate')
      const paper = document.querySelector('.hiprint-printPaper')

      console.log('=== 画布调试信息 ===')
      if (canvasContainer) {
        console.log('画布容器尺寸:', canvasContainer.offsetWidth + 'x' + canvasContainer.offsetHeight)
        console.log('画布容器滚动尺寸:', canvasContainer.scrollWidth + 'x' + canvasContainer.scrollHeight)
      }

      if (hiprintContainer) {
        console.log('hiprint容器尺寸:', hiprintContainer.offsetWidth + 'x' + hiprintContainer.offsetHeight)
        console.log('hiprint容器样式高度:', hiprintContainer.style.height)
      }

      if (paper) {
        console.log('A4纸张尺寸:', paper.offsetWidth + 'x' + paper.offsetHeight)
        console.log('A4纸张位置:', 'top:' + paper.offsetTop + ', left:' + paper.offsetLeft)
      } else {
        console.log('未找到A4纸张元素')
      }

      this.$message.info('调试信息已输出到Console')
    },

    // 移除添加功能
    removeAddFunctions() {
      try {
        // 延迟执行，确保hiprint完全渲染
        setTimeout(() => {
          const container = document.getElementById('hiprint-printTemplate')
          if (container) {
            // 查找并移除所有可能的添加按钮
            const addElements = container.querySelectorAll(`
              [title*="添加"],
              [title*="新增"],
              [title*="Add"],
              [title*="+"],
              .hiprint-addPrintElement,
              .hiprint-addPanel,
              .hiprint-pagination,
              .hiprint-addPage
            `)

            addElements.forEach(el => {
              el.style.display = 'none'
              el.remove()
            })

            // 查找包含"+"文本的元素
            const allElements = container.querySelectorAll('*')
            allElements.forEach(el => {
              const text = el.textContent || el.innerText || ''
              if ((text.includes('+') || text.includes('＋') || text.includes('添加')) &&
                  !el.closest('.component-item') &&
                  !el.closest('.toolbar')) {
                el.style.display = 'none'
              }
            })

            console.log('已移除添加功能相关元素')
          }
        }, 1000)
      } catch (error) {
        console.error('移除添加功能失败:', error)
      }
    },

    // 元素对齐 - 使用hiprint内置方法
    setElsAlign(alignType) {
      try {
        console.log('setElsAlign被调用，参数:', alignType)

        if (!this.hiprintTemplate) {
          this.$message.warning('模板未初始化')
          return
        }

        // 检查hiprint是否有setElsAlign方法
        if (typeof this.hiprintTemplate.setElsAlign !== 'function') {
          console.error('hiprintTemplate.setElsAlign 方法不存在')
          this.$message.error('对齐功能不可用，请检查hiprint版本')
          return
        }

        console.log('调用 hiprintTemplate.setElsAlign:', alignType)

        // 使用hiprint内置的对齐方法
        this.hiprintTemplate.setElsAlign(alignType)

        // 显示成功消息
        const alignNames = {
          'left': '左对齐',
          'vertical': '居中对齐',
          'right': '右对齐',
          'top': '顶部对齐',
          'horizontal': '垂直居中',
          'bottom': '底部对齐',
          'distributeHor': '横向分散',
          'distributeVer': '纵向分散'
        }

        this.$message.success(`${alignNames[alignType] || alignType}完成`)
      } catch (error) {
        console.error('对齐失败:', error)
        this.$message.error('对齐失败，请先选中元素: ' + error.message)
      }
    },

    // 设置元素间距 - 使用hiprint内置方法
    setElsSpace(isHorizontal) {
      try {
        console.log('setElsSpace被调用，参数:', isHorizontal)

        if (!this.hiprintTemplate) {
          this.$message.warning('模板未初始化')
          return
        }

        // 检查hiprint是否有setElsSpace方法
        if (typeof this.hiprintTemplate.setElsSpace !== 'function') {
          console.error('hiprintTemplate.setElsSpace 方法不存在')
          this.$message.error('间距功能不可用，请检查hiprint版本')
          return
        }

        console.log('调用 hiprintTemplate.setElsSpace:', 10, isHorizontal)

        // 使用hiprint内置的间距方法
        // setElsSpace(spacing, isHorizontal)
        this.hiprintTemplate.setElsSpace(10, isHorizontal)

        this.$message.success(`${isHorizontal ? '水平' : '垂直'}间距10设置完成`)
      } catch (error) {
        console.error('设置间距失败:', error)
        this.$message.error('设置间距失败，请先选中元素: ' + error.message)
      }
    },

    // 确认清空模板
    confirmClearTemplate() {
      this.$confirm('确定要清空画布吗？', '提示', {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.clearTemplate()
      }).catch(() => {
        console.log('用户取消清空操作')
      })
    },

    // 清空模板 - 参考原项目实现
    clearTemplate() {
      try {
        console.log('=== clearTemplate被调用 ===')

        if (!this.hiprintTemplate) {
          console.error('hiprintTemplate不存在')
          this.$message.warning('模板未初始化')
          return
        }

        console.log('hiprintTemplate存在:', this.hiprintTemplate)
        console.log('hiprintTemplate.clear方法存在:', typeof this.hiprintTemplate.clear)

        // 获取清空前的模板数据
        const beforeClear = this.hiprintTemplate.getJson()
        console.log('清空前的模板数据:', beforeClear)

        // 直接调用clear方法，参考原项目实现
        console.log('调用hiprintTemplate.clear()方法')
        this.hiprintTemplate.clear()

        // 获取清空后的模板数据
        const afterClear = this.hiprintTemplate.getJson()
        console.log('清空后的模板数据:', afterClear)

        this.$message.success('画布已清空')
        console.log('=== 清空完成 ===')
      } catch (error) {
        console.error('清空失败:', error)
        this.$message.error(`操作失败: ${error}`)
      }
    },

    // 跳转到模板管理页面
    goToTemplateList() {
      this.$router.push('/MD/MD_PrintTemplate')
    },

    // 保存模板到数据库
    saveTemplate() {
      if (!this.hiprintTemplate) {
        this.$message.warning('请先设计模板')
        return
      }

      const template = this.hiprintTemplate.getJson()
      if (!template || !template.panels || template.panels.length === 0) {
        this.$message.warning('模板内容为空，请先设计模板')
        return
      }

      // 如果没有当前模板ID，说明这是从模板列表进入的新模板，不应该保存
      if (!this.currentTemplateId) {
        this.$message.warning('请从模板管理页面选择模板进行设计')
        return
      }

      // 构建完整的模板数据，包含所有字段
      const templateData = {
        Id: this.currentTemplate.Id,
        TemplateName: this.currentTemplate.TemplateName,
        Enable: this.currentTemplate.Enable,
        TemplateJson: JSON.stringify(template),
        TemplateParameterId: this.currentTemplate.TemplateParameterId,
        Remark: this.currentTemplate.Remark
      }

      update(templateData).then(response => {
        this.$message.success('模板保存成功')
        // 更新最后修改时间显示
        this.currentTemplate.MTime = new Date().toISOString()
      }).catch(error => {
        this.$message.error('保存失败：' + (error.message || '未知错误'))
      })
    },

    // 从数据库加载模板
    loadTemplateFromDatabase(templateId) {
      if (!templateId) return

      getById(templateId).then(response => {
        const templateData = response.Data
        if (!templateData) {
          this.$message.warning('模板不存在')
          return
        }

        // 保存模板信息
        this.currentTemplate = Object.assign({}, templateData)
        this.currentTemplateId = templateData.Id

        // 如果有参数模板ID，加载参数配置
        if (templateData.TemplateParameterId) {
          this.loadParameterConfiguration(templateData.TemplateParameterId)
        }

        // 如果有模板内容，则加载到编辑器
        if (templateData.TemplateJson) {
          try {
            const template = JSON.parse(templateData.TemplateJson)

            // 等待hiprint初始化完成后再加载模板
            const loadTemplate = () => {
              if (this.hiprintTemplate) {
                this.hiprintTemplate.update(template)
                this.$message.success(`模板 "${templateData.TemplateName}" 加载成功`)
              } else {
                // 如果hiprint还没初始化完成，等待一下再试
                setTimeout(loadTemplate, 500)
              }
            }

            loadTemplate()
          } catch (error) {
            this.$message.error('模板格式错误：' + error.message)
          }
        } else {
          this.$message.info(`模板 "${templateData.TemplateName}" 加载成功，可以开始设计`)
        }
      }).catch(error => {
        this.$message.error('加载模板失败：' + (error.message || '未知错误'))
      })
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      try {
        const date = new Date(dateTime)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return ''
      }
    },

    // 加载参数配置
    loadParameterConfiguration(parameterId) {
      if (!parameterId) return

      getParameterById(parameterId).then(response => {
        const parameterData = response.Data
        if (parameterData && parameterData.ParameterJson) {
          try {
            this.currentParameters = JSON.parse(parameterData.ParameterJson)
            this.currentTemplate.TemplateParameterName = parameterData.TemplateName

            // 解析参数为选项列表
            this.parseParametersToOptions()
          } catch (error) {
            console.error('参数配置JSON格式错误:', error)
            this.currentParameters = null
            this.parameterOptions = []
          }
        }
      }).catch(error => {
        console.error('加载参数配置失败:', error)
        this.currentParameters = null
        this.parameterOptions = []
      })
    },

    // 解析参数为选项列表
    parseParametersToOptions() {
      this.parameterOptions = []
      this.subtableOptions = []

      if (this.currentParameters && typeof this.currentParameters === 'object') {
        // 解析基础参数
        if (this.currentParameters.parameters && typeof this.currentParameters.parameters === 'object') {
          // 新格式：有parameters和subtables结构
          for (const [label, value] of Object.entries(this.currentParameters.parameters)) {
            this.parameterOptions.push({
              label: label,
              value: String(value)
            })
          }

          // 解析子表配置
          if (this.currentParameters.subtables && Array.isArray(this.currentParameters.subtables)) {
            this.subtableOptions = this.currentParameters.subtables.map(subtable => ({
              name: subtable.name || '',
              key: subtable.key || '',
              columns: subtable.columns || []
            }))
          }
        } else {
          // 兼容旧格式：直接是键值对
          for (const [label, value] of Object.entries(this.currentParameters)) {
            this.parameterOptions.push({
              label: label,
              value: String(value)
            })
          }
        }
      }
    },

    // 应用参数到当前选中的元素
    applyParameterToElement(parameterValue) {
      if (!this.hiprintTemplate || !parameterValue) {
        return
      }

      try {
        // 获取当前选中的元素
        const selectedElements = this.hiprintTemplate.getSelectEls()

        if (!selectedElements || selectedElements.length === 0) {
          this.$message.warning('请先选择一个元素')
          return
        }

        // 找到对应的参数标签
        const selectedParam = this.parameterOptions.find(param => param.value === parameterValue)
        const paramLabel = selectedParam ? selectedParam.label : parameterValue

        // 使用 hiprint 的 updateOption 方法来更新选中元素的属性
        this.hiprintTemplate.updateOption('field', parameterValue)
        this.hiprintTemplate.updateOption('title', paramLabel)

        // 不要更新 text 属性，因为那是显示内容，我们只想更新标题属性

        this.$message.success(`已将参数 "${paramLabel}" 应用到选中元素`)

        // 更新当前选中元素的引用，确保参数选择状态正确
        this.currentSelectedElement = selectedElements[0]

        // 强制刷新属性面板，确保字段名和标题正确显示
        setTimeout(() => {
          this.forceRefreshPropertyPanel()
          // 启动持续监听和设置标题值
          this.startTitleValueWatcher(paramLabel)
        }, 200)
      } catch (error) {
        console.error('应用参数失败:', error)
        this.$message.error('应用参数失败: ' + error.message)
      }
    },

    // 应用子表到当前选中的表格元素
    applySubtableToElement(subtableKey) {
      if (!this.hiprintTemplate || !subtableKey) {
        return
      }

      try {
        // 获取当前选中的元素
        const selectedElements = this.hiprintTemplate.getSelectEls()

        if (!selectedElements || selectedElements.length === 0) {
          this.$message.warning('请先选择一个表格元素')
          return
        }

        // 检查是否为表格元素
        const element = selectedElements[0]
        if (!this.isTableElement(element)) {
          this.$message.warning('请选择表格元素')
          return
        }

        // 找到对应的子表信息
        const selectedSubtable = this.subtableOptions.find(subtable => subtable.key === subtableKey)
        const subtableName = selectedSubtable ? selectedSubtable.name : subtableKey

        // 使用 hiprint 的 updateOption 方法来更新选中元素的属性
        this.hiprintTemplate.updateOption('field', subtableKey)

        this.$message.success(`已将子表 "${subtableName}" 应用到选中的表格元素`)

        // 更新当前选中元素的引用
        this.currentSelectedElement = element

        // 强制刷新属性面板
        setTimeout(() => {
          this.forceRefreshPropertyPanel()
        }, 200)
      } catch (error) {
        console.error('应用子表失败:', error)
        this.$message.error('应用子表失败: ' + error.message)
      }
    },

    // 清空参数选择
    clearParameterSelection() {
      this.$nextTick(() => {
        this.selectedParameter = ''
        this.selectedSubtable = ''
      })
    },

    // 判断是否为表格元素
    isTableElement(element) {
      console.log('isTableElement 检查:', element)
      if (!element || !element.printElementType) {
        console.log('元素为空或没有 printElementType')
        return false
      }
      const isTable = element.printElementType.type === 'table'
      console.log('元素类型:', element.printElementType.type, '是否为表格:', isTable)
      return isTable
    },

    // 尝试直接从DOM元素获取hiprint数据
    tryDirectElementSelection(domElement) {
      try {
        console.log('尝试直接获取元素数据:', domElement)

        // 检查DOM元素是否有hiprint相关的数据
        if (domElement && domElement.classList.contains('hiprint-printElement-table')) {
          console.log('发现表格元素，尝试模拟选中状态')

          // 创建一个模拟的hiprint元素对象
          const mockElement = {
            printElementType: {
              type: 'table'
            },
            options: {
              field: '',
              columns: []
            }
          }

          // 手动设置选中状态
          this.isElementReallySelected = true
          this.currentSelectedElement = mockElement
          this.isManuallySelected = true // 标记为手动选中

          console.log('手动设置选中状态完成')

          // 强制更新Vue响应式数据
          this.$forceUpdate()
        }
      } catch (error) {
        console.error('直接元素选择失败:', error)
      }
    },

    // 设置DOM点击事件监听器
    setupDOMClickListener() {
      console.log('设置DOM点击监听器')

      // 延迟设置，确保DOM已渲染
      setTimeout(() => {
        // 尝试多种选择器来找到画布容器
        const selectors = [
          '.hiprint-printTemplate',
          '#hiprint-printTemplate',
          '.design-container',
          '.canvas-container',
          '.hiprint-printPanel'
        ]

        let canvasContainer = null
        for (const selector of selectors) {
          canvasContainer = document.querySelector(selector)
          if (canvasContainer) {
            console.log(`找到画布容器: ${selector}`, canvasContainer)
            break
          }
        }

        if (canvasContainer) {
          console.log('添加点击监听器到画布容器')
          canvasContainer.addEventListener('click', (event) => {
            console.log('DOM点击事件触发:', event.target)
            this.handleElementClick(event)
          })
        } else {
          console.log('未找到画布容器，使用document监听器')
        }
      }, 2000) // 延迟2秒确保DOM完全渲染
    },

    // 处理元素点击（已废弃，使用全局监听器代替）
    handleElementClick(event) {
      // 这个方法已经被全局监听器替代，保留以防兼容性问题
      console.log('handleElementClick 已废弃，使用全局监听器')
    },

    // 设置全局点击监听器
    setupGlobalClickListener() {
      console.log('设置全局点击监听器')

      // 移除之前的监听器
      if (this.globalClickHandler) {
        document.removeEventListener('click', this.globalClickHandler)
      }

      // 创建新的全局点击处理器
      this.globalClickHandler = (event) => {
        console.log('全局点击事件触发，目标:', event.target.className || event.target.tagName)

        // 检查是否点击了hiprint元素
        const hiprintElement = event.target.closest('.hiprint-printElement')

        if (hiprintElement) {
          console.log('✓ 点击了hiprint元素，设置选中状态')
          this.handleElementSelection(hiprintElement)
        } else {
          // 检查是否点击了画布空白区域
          const isClickOnCanvas = event.target.closest('#hiprint-printTemplate') !== null
          const isClickOnPropertyPanel = event.target.closest('#PrintElementOptionSetting') !== null
          const isClickOnParameterSelector = event.target.closest('.parameter-selector') !== null
          const isClickOnSubtableInfo = event.target.closest('.subtable-info-display') !== null
          const isClickOnToolbar = event.target.closest('.toolbar') !== null
          const isClickOnComponentPanel = event.target.closest('.component-panel') !== null
          const isClickOnTemplateInfo = event.target.closest('.template-info-bar') !== null

          if (isClickOnCanvas && !isClickOnPropertyPanel && !isClickOnParameterSelector &&
              !isClickOnSubtableInfo && !isClickOnToolbar && !isClickOnComponentPanel && !isClickOnTemplateInfo) {
            console.log('✓ 点击画布空白区域，清除选中状态')
            this.clearSelection()
          }
        }
      }

      // 添加监听器
      document.addEventListener('click', this.globalClickHandler)
    },

    // 处理元素选中
    handleElementSelection(hiprintElement) {
      console.log('处理元素选中:', hiprintElement)

      // 检查是否是表格元素
      const isTable = hiprintElement.classList.contains('hiprint-printElement-table')

      // 创建模拟的元素对象
      const mockElement = {
        printElementType: {
          type: isTable ? 'table' : 'text'
        },
        options: {
          field: '',
          columns: isTable ? [] : undefined
        }
      }

      // 设置选中状态
      this.isElementReallySelected = true
      this.currentSelectedElement = mockElement
      this.isManuallySelected = true

      // 更新参数选择状态
      this.updateParameterSelectionForElement(mockElement)

      console.log(`✓ ${isTable ? '表格' : '文本'}元素选中状态已设置`)
    },

    // 显示子表信息弹窗
    showSubtableDialog() {
      this.subtableDialogVisible = true
    },

    // 设置元素选择监听器
    setupElementSelectionListener() {
      if (!this.hiprintTemplate) return

      console.log('设置元素选择监听器')

      // 清除之前的监听器
      this.clearEventListeners()

      // 设置多种监听方式确保能够捕获选中事件
      this.setupHiprintNativeListeners()
      this.setupDOMEventListeners()
      // 启用轮询监听器，确保能够检测到选中状态变化
      this.setupPollingListener()
    },

    // 清除所有事件监听器
    clearEventListeners() {
      if (this.selectionPollingTimer) {
        clearInterval(this.selectionPollingTimer)
        this.selectionPollingTimer = null
      }
      if (this.selectionCheckInterval) {
        clearInterval(this.selectionCheckInterval)
        this.selectionCheckInterval = null
      }

      // 清理DOM观察器
      if (this.domObserver) {
        this.domObserver.disconnect()
        this.domObserver = null
      }

      // 清理属性面板观察器
      if (this.propertyObserver) {
        this.propertyObserver.disconnect()
        this.propertyObserver = null
      }
    },

    // 设置hiprint原生监听器
    setupHiprintNativeListeners() {
      console.log('设置hiprint原生监听器')

      try {
        // 监听hiprint内部的选择事件
        if (this.hiprintTemplate && this.hiprintTemplate.on) {
          // 监听模板数据变化事件
          this.hiprintTemplate.on('dataChanged', (type, json) => {
            console.log('hiprint dataChanged 事件:', type, json)
            this.handleElementSelectionChange()
          })

          // 监听元素选择事件
          this.hiprintTemplate.on('selectElement', (element) => {
            console.log('hiprint selectElement 事件:', element)
            this.handleElementSelected(element)
          })
        }

        // 尝试监听全局hiprint事件
        if (typeof window.hinnn !== 'undefined' && window.hinnn.event) {
          console.log('设置hinnn全局事件监听')

          // 监听面板选中事件
          window.hinnn.event.on('onSelectPanel', (panel, index, li) => {
            console.log('hinnn onSelectPanel 事件触发:', panel, index, li)
            setTimeout(() => {
              this.handleElementSelectionChange()
            }, 100)
          })

          // 监听元素点击事件
          window.hinnn.event.on('onElementClick', (element) => {
            console.log('hinnn onElementClick 事件触发:', element)
            this.handleElementSelected(element)
          })
        }

        // 设置DOM变化监听器
        this.setupDOMObserver()

        // 设置属性面板监听器
        this.setupPropertyPanelObserver()

        console.log('hiprint原生监听器设置完成')
      } catch (error) {
        console.error('设置hiprint原生监听器失败:', error)
      }
    },

    // 设置DOM变化监听器
    setupDOMObserver() {
      console.log('设置DOM变化监听器')

      // 监听画布DOM变化
      const canvas = document.querySelector('#hiprint-printTemplate')
      if (canvas) {
        if (this.domObserver) {
          this.domObserver.disconnect()
        }

        this.domObserver = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            // 监听class变化（选中状态变化）
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
              const target = mutation.target
              if (target.classList.contains('hiprint-printElement')) {
                console.log('✓ DOM监听器：元素class变化', target)
                console.log('新的class:', target.className)

                // 检查是否是选中状态变化
                if (target.classList.contains('selected') || target.classList.contains('design')) {
                  console.log('✓ 检测到元素选中状态变化')
                  setTimeout(() => {
                    this.handleElementSelectionChange()
                  }, 50)
                }
              }
            }

            // 监听子节点变化
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1 && node.classList && node.classList.contains('hiprint-printElement')) {
                  console.log('✓ DOM监听器：新增hiprint元素', node)
                }
              })
            }
          })
        })

        this.domObserver.observe(canvas, {
          attributes: true,
          childList: true,
          subtree: true,
          attributeFilter: ['class', 'style']
        })

        console.log('DOM变化监听器设置完成')
      }
    },

    // 设置属性面板监听器
    setupPropertyPanelObserver() {
      console.log('设置属性面板监听器')

      const propertyPanel = document.querySelector('#PrintElementOptionSetting')
      if (propertyPanel) {
        if (this.propertyObserver) {
          this.propertyObserver.disconnect()
        }

        this.propertyObserver = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              console.log('✓ 属性面板内容变化')

              // 延迟检查，确保DOM完全更新
              setTimeout(() => {
                this.analyzePropertyPanel(propertyPanel)
              }, 50)
            }
          })
        })

        this.propertyObserver.observe(propertyPanel, {
          childList: true,
          subtree: true
        })

        console.log('属性面板监听器设置完成')
      }
    },

    // 分析属性面板内容
    analyzePropertyPanel(propertyPanel) {
      console.log('分析属性面板内容...')

      // 获取面板内容文本
      const panelText = propertyPanel.textContent || propertyPanel.innerText || ''
      console.log('面板文本预览:', panelText.substring(0, 300))

      // 检查当前面板文本是否与上次相同，避免重复处理
      if (this.lastPanelText === panelText) {
        console.log('○ 属性面板内容未变化，跳过处理')
        return
      }
      this.lastPanelText = panelText

      // 检查你发现的特征文本（更严格的检查）
      const hasSpecificTableText = panelText.includes('基础样式列高级') && panelText.includes('字段名')

      console.log('属性面板分析结果:')
      console.log('- 包含特定表格文本:', hasSpecificTableText)
      console.log('- 面板文本预览:', panelText.substring(0, 100))

      if (hasSpecificTableText) {
        console.log('✓ 确认是表格属性面板（特定文本匹配）')

        // 检查是否有真正的表格元素被选中（DOM状态检查）
        const selectedTableInDOM = document.querySelector('.hiprint-printElement-table.selected, .hiprint-printElement-table.design')
        console.log('DOM中的表格选中状态:', selectedTableInDOM ? '有选中' : '无选中')

        if (selectedTableInDOM) {
          console.log('✓ DOM中确实有表格被选中')

          // 检查用户是否最近点击了表格元素
          const isRecentTableClick = this.userClickedElement &&
                                   this.userClickedElement.closest('.hiprint-printElement-table') &&
                                   (Date.now() - this.userClickTime < 2000) // 2秒内的点击

          console.log('用户最近点击的元素:', this.userClickedElement)
          console.log('是否最近点击了表格:', isRecentTableClick)

          if (isRecentTableClick) {
            console.log('✓ 用户最近点击了表格元素，触发表格选中模拟')
            // 只有在当前没有表格选中状态时才触发模拟
            if (!this.isElementReallySelected || !this.isTableElement(this.currentSelectedElement)) {
              this.simulateTableSelection()
            } else {
              console.log('○ 表格已经选中，无需重复处理')
            }
          } else {
            console.log('○ 用户没有最近点击表格元素，可能是残留状态')
            // 暂时禁用自动清除逻辑，避免误清除选中状态
            // if (this.isElementReallySelected && this.currentSelectedElement && this.isTableElement(this.currentSelectedElement)) {
            //   console.log('清除残留的表格选中状态')
            //   this.clearSelection()
            // }
          }
        } else {
          console.log('○ 属性面板显示表格内容，但DOM中没有表格被选中（可能是残留内容）')
          // 暂时禁用自动清除逻辑，避免误清除选中状态
          // if (this.isElementReallySelected && this.currentSelectedElement && this.isTableElement(this.currentSelectedElement)) {
          //   console.log('清除残留的表格选中状态')
          //   this.clearSelection()
          // }
        }
      } else {
        console.log('○ 非表格属性面板（不包含特定表格文本）')
        // 暂时禁用自动清除逻辑，避免误清除选中状态
        // if (this.isElementReallySelected && this.currentSelectedElement && this.isTableElement(this.currentSelectedElement)) {
        //   console.log('清除表格选中状态')
        //   this.clearSelection()
        // }
      }

      // 强制更新Vue组件
      this.$forceUpdate()
    },

    // 清除选中状态
    clearSelection() {
      console.log('清除选中状态')
      this.isElementReallySelected = false
      this.currentSelectedElement = null
      this.isManuallySelected = false
      this.selectedParameter = ''
      this.selectedSubtable = ''
      this.currentSelectedColumn = null
      this.selectedField = ''
      this.availableFields = []
      this.lastPanelText = ''

      // 强制更新Vue组件，确保UI立即响应
      this.$forceUpdate()
      console.log('✓ 选中状态已清除，显示子表信息')
    },

    // 改进的模拟表格选中状态
    simulateTableSelection() {
      console.log('=== 模拟表格选中状态 ===')

      const tableElements = document.querySelectorAll('.hiprint-printElement-table')
      console.log('找到表格元素数量:', tableElements.length)

      if (tableElements.length > 0) {
        // 尝试找到当前实际选中的表格元素
        let selectedTable = null

        // 方法1: 查找有选中状态的表格
        for (const table of tableElements) {
          if (table.classList.contains('selected') || table.classList.contains('design')) {
            selectedTable = table
            console.log('✓ 找到选中状态的表格:', table)
            break
          }
        }

        // 方法2: 如果没找到，使用第一个表格
        if (!selectedTable) {
          selectedTable = tableElements[0]
          console.log('○ 使用第一个表格元素:', selectedTable)
        }

        // 创建表格元素数据
        const mockTableElement = {
          printElementType: {
            type: 'table'
          },
          options: {
            field: '',
            columns: [],
            title: '表格'
          },
          // 添加DOM引用
          domElement: selectedTable
        }

        // 更新选中状态
        this.isElementReallySelected = true
        this.currentSelectedElement = mockTableElement
        this.isManuallySelected = true // 标记为用户主动选择
        this.updateParameterSelectionForElement(mockTableElement)

        // 强制更新Vue组件
        this.$forceUpdate()

        console.log('✓ 表格选中状态模拟完成')
        console.log('当前选中元素:', this.currentSelectedElement)
        console.log('是否表格元素:', this.isTableElement(this.currentSelectedElement))

        // 显示成功消息
        this.$message({
          message: '表格元素已选中，属性面板已更新',
          type: 'success',
          duration: 2000
        })
      } else {
        console.log('○ 没有找到表格元素')
      }

      console.log('=== 表格选中模拟完成 ===')
    },

    // 设置DOM事件监听器（简化版，避免与全局监听器冲突）
    setupDOMEventListeners() {
      console.log('设置DOM事件监听器（简化版）')

      // 清除之前的事件监听器
      $(document).off('click.hiprintElement')
      $(document).off('click.hiprintTable')
      $(document).off('click.hiprintTableHeader')
      $(document).off('click.hiprintTableContent')
      $(document).off('click.hiprintTableCell')

      console.log('DOM事件监听器清理完成，使用全局监听器处理点击事件')
    },

    // 处理表格元素点击
    handleTableElementClick(tableElement) {
      console.log('处理表格元素点击:', tableElement)

      // 创建表格元素数据
      const tableElementData = {
        printElementType: {
          type: 'table'
        },
        options: {
          field: '',
          columns: [],
          title: '表格'
        }
      }

      // 设置选中状态
      this.isElementReallySelected = true
      this.currentSelectedElement = tableElementData
      this.isManuallySelected = true

      // 更新参数选择
      this.updateParameterSelectionForElement(tableElementData)

      console.log('✓ 表格元素点击处理完成')
    },

    // 改进的处理元素选择变化
    handleElementSelectionChange() {
      console.log('=== handleElementSelectionChange 被调用 ===')

      if (!this.hiprintTemplate) {
        console.warn('hiprintTemplate 不存在，跳过处理')
        return
      }

      try {
        console.log('调用 hiprintTemplate.getSelectEls()...')
        let selectedElements = this.hiprintTemplate.getSelectEls()
        console.log('getSelectEls() 返回结果:', selectedElements)
        console.log('选中元素数量:', selectedElements ? selectedElements.length : 0)

        // 如果hiprint没有选中元素，检查是否有真正的DOM选中状态
        if (!selectedElements || selectedElements.length === 0) {
          console.log('hiprint未返回选中元素，检查DOM选中状态...')

          // 只查找真正有选中状态的DOM元素（更严格的选择器）
          const selectedDOMElements = document.querySelectorAll('.hiprint-printElement.selected:not(.hiprint-printElement-table), .hiprint-printElement.design:not(.hiprint-printElement-table)')
          console.log('DOM中找到的非表格选中元素:', selectedDOMElements.length)

          // 单独检查表格元素的选中状态
          const selectedTableElements = document.querySelectorAll('.hiprint-printElement-table.selected, .hiprint-printElement-table.design')
          console.log('DOM中找到的表格选中元素:', selectedTableElements.length)

          if (selectedDOMElements.length > 0) {
            const selectedDOM = selectedDOMElements[0]
            console.log('DOM选中元素:', selectedDOM)

            // 尝试获取DOM元素对应的hiprint元素数据
            const hiprintElement = this.getHiprintElementFromDOM(selectedDOM)
            if (hiprintElement) {
              console.log('✓ 从DOM获取到hiprint元素:', hiprintElement)
              selectedElements = [hiprintElement]
            }
          } else if (selectedTableElements.length > 0 && this.isManuallySelected) {
            // 只有在手动选中状态下才处理表格元素
            const selectedTableDOM = selectedTableElements[0]
            console.log('DOM选中表格元素（手动选中）:', selectedTableDOM)

            // 对于表格元素，更严格地检查是否真正被选中
            const rect = selectedTableDOM.getBoundingClientRect()
            if (rect.width > 0 && rect.height > 0) {
              console.log('✓ 表格元素确实可见，创建表格元素数据')
              selectedElements = [{
                printElementType: {
                  type: 'table'
                },
                options: {
                  field: '',
                  columns: [],
                  title: '表格'
                }
              }]
            }
          }
        }

        if (selectedElements && selectedElements.length > 0) {
          const element = selectedElements[0]
          console.log('✓ 发现选中元素:', element)
          console.log('元素类型:', element.printElementType ? element.printElementType.type : '未知')
          console.log('元素选项:', element.options)

          // 检查是否是表格元素
          if (element.printElementType && element.printElementType.type === 'table') {
            console.log('✓ 确认是表格元素')
            console.log('表格字段:', element.options ? element.options.field : '无')
            console.log('表格列配置:', element.options ? element.options.columns : '无')

            // 表格元素处理逻辑（移除手动选中限制）
            console.log('✓ 处理表格元素选中状态')
          } else {
            console.log('○ 非表格元素，类型:', element.printElementType ? element.printElementType.type : '未知')
          }

          // 更新选中状态
          this.isElementReallySelected = true
          this.currentSelectedElement = element
          // 不重置isManuallySelected，保持用户选择状态
          console.log('✓ 选中状态已更新')

          // 更新参数选择
          console.log('调用 updateParameterSelectionForElement...')
          this.updateParameterSelectionForElement(element)

          console.log('✓ 选中状态更新完成')
        } else {
          console.log('○ 没有选中元素')

          // 清除选中状态
          console.log('清除选中状态...')
          this.isElementReallySelected = false
          this.currentSelectedElement = null
          this.selectedParameter = ''
          this.selectedSubtable = ''
          this.isManuallySelected = false
          console.log('✓ 选中状态已清除')
        }
      } catch (error) {
        console.error('❌ 处理元素选择变化失败:', error)
        console.error('错误堆栈:', error.stack)
      }

      console.log('=== handleElementSelectionChange 处理完成 ===')
    },

    // 从DOM元素获取hiprint元素数据
    getHiprintElementFromDOM(domElement) {
      console.log('尝试从DOM元素获取hiprint数据:', domElement)

      try {
        // 方法1: 检查DOM元素是否有hiprint数据属性
        if (domElement.hiprintElement) {
          console.log('✓ 从DOM元素直接获取hiprint数据')
          return domElement.hiprintElement
        }

        // 方法2: 尝试从hiprint模板中查找匹配的元素
        if (this.hiprintTemplate) {
          const allElements = this.hiprintTemplate.getAllElements ? this.hiprintTemplate.getAllElements() : []
          console.log('模板中所有元素:', allElements)

          // 这里可以根据DOM元素的位置、大小等属性来匹配
          // 禁用自动创建表格元素
          if (domElement.classList.contains('hiprint-printElement-table')) {
            console.log('○ 识别为表格元素，但不自动创建（需要用户主动点击）')
            return null
          }
        }

        // 方法3: 根据DOM元素类名判断类型（禁用自动创建表格元素）
        if (domElement.classList.contains('hiprint-printElement-table')) {
          console.log('○ 检测到表格元素，但不自动创建（需要用户主动点击）')
          return null
        } else if (domElement.classList.contains('hiprint-printElement-text')) {
          console.log('✓ 根据类名创建文本元素数据')
          return {
            printElementType: {
              type: 'text'
            },
            options: {
              field: '',
              title: '文本'
            }
          }
        }

        console.log('○ 无法从DOM元素获取hiprint数据')
        return null
      } catch (error) {
        console.error('从DOM获取hiprint数据失败:', error)
        return null
      }
    },

    // 处理特定元素被选中
    handleElementSelected(element) {
      if (!element) return

      console.log('处理元素选中:', element)

      this.isElementReallySelected = true
      this.currentSelectedElement = element
      this.isManuallySelected = false
      this.updateParameterSelectionForElement(element)

      console.log('元素选中处理完成')
    },

    // 设置定时器轮询监听器（作为备用方案）
    setupPollingListener() {
      console.log('设置定时器轮询监听器')

      // 清除之前的定时器
      if (this.selectionPollingTimer) {
        clearInterval(this.selectionPollingTimer)
      }

      // 记录上一次的选中状态
      let lastSelectedElementsCount = 0
      let lastSelectedElement = null

      // 每1秒检查一次选中状态（降低频率，避免性能问题）
      this.selectionPollingTimer = setInterval(() => {
        if (!this.hiprintTemplate) return

        try {
          const selectedElements = this.hiprintTemplate.getSelectEls()
          const currentCount = selectedElements ? selectedElements.length : 0

          // 检查选中状态是否发生变化
          if (currentCount !== lastSelectedElementsCount ||
              (currentCount > 0 && selectedElements[0] !== lastSelectedElement)) {
            console.log('轮询检测到选中状态变化:', {
              之前数量: lastSelectedElementsCount,
              当前数量: currentCount,
              选中元素: selectedElements
            })

            if (currentCount > 0) {
              // 有元素被选中
              const element = selectedElements[0]
              console.log('轮询检测到元素选中:', element)
              console.log('元素类型:', element.printElementType ? element.printElementType.type : '未知')

              // 更新选中状态（移除手动选中限制）
              this.isElementReallySelected = true
              this.currentSelectedElement = element
              this.updateParameterSelectionForElement(element)
              console.log('轮询更新选中状态完成')

              lastSelectedElement = element
            } else {
              // 没有元素被选中
              console.log('轮询检测到取消选中')

              // 清除选中状态（但保留手动选中的状态）
              if (!this.isManuallySelected) {
                this.isElementReallySelected = false
                this.currentSelectedElement = null
                this.selectedParameter = ''
                this.selectedSubtable = ''
              }

              lastSelectedElement = null
            }

            lastSelectedElementsCount = currentCount
          }
        } catch (error) {
          console.error('轮询检查选中状态失败:', error)
        }
      }, 1000) // 每1秒检查一次

      console.log('定时器轮询监听器设置完成')
    },

    // 处理hiprint数据变化事件
    handleHiprintDataChanged(type, json) {
      console.log('处理hiprint数据变化:', type)
      // 委托给新的处理方法
      this.handleElementSelectionChange()
    },

    // 检查元素选中状态 - 保留作为备用方法
    checkElementSelection() {
      if (!this.hiprintTemplate) return

      try {
        const selectedElements = this.hiprintTemplate.getSelectEls()

        if (selectedElements && selectedElements.length > 0) {
          if (!this.isElementReallySelected || this.currentSelectedElement !== selectedElements[0]) {
            console.log('备用检查 - 发现选中元素:', selectedElements[0])
            this.isElementReallySelected = true
            this.currentSelectedElement = selectedElements[0]
            this.updateParameterSelectionForElement(selectedElements[0])
          }
        } else {
          if (this.isManuallySelected && this.isElementReallySelected) {
            console.log('保持手动设置的选中状态')
            return
          }

          if (this.isElementReallySelected) {
            console.log('备用检查 - 清除选中状态')
            this.isElementReallySelected = false
            this.currentSelectedElement = null
            this.isManuallySelected = false
            this.selectedParameter = ''
            this.selectedSubtable = ''
          }
        }
      } catch (error) {
        console.error('检查元素选中状态失败:', error)
      }
    },

    // 为特定元素更新参数选择状态
    updateParameterSelectionForElement(element) {
      try {
        // 清空之前的选择
        this.selectedParameter = ''
        this.selectedSubtable = ''

        if (element.options && element.options.field) {
          const fieldValue = element.options.field

          // 判断是否为表格元素
          if (this.isTableElement(element)) {
            // 表格元素：在子表选项中查找匹配的子表
            const matchedSubtable = this.subtableOptions.find(subtable => subtable.key === fieldValue)
            if (matchedSubtable) {
              this.selectedSubtable = fieldValue
            }
          } else {
            // 非表格元素：在参数选项中查找匹配的参数
            const matchedParam = this.parameterOptions.find(param => param.value === fieldValue)
            if (matchedParam) {
              this.selectedParameter = fieldValue
            }
          }
        }

        console.log('✓ 参数选择状态已更新')
      } catch (error) {
        console.error('更新元素参数选择状态失败:', error)
      }
    },

    // 更新参数选择状态（保留原方法作为备用）
    updateParameterSelection() {
      if (!this.hiprintTemplate) return

      try {
        const selectedElements = this.hiprintTemplate.getSelectEls()

        // 这个方法现在主要用于备用检查
        if (selectedElements && selectedElements.length > 0) {
          const element = selectedElements[0]

          // 只有在真正选中状态下才处理
          if (this.isElementReallySelected && this.currentSelectedElement !== element) {
            this.currentSelectedElement = element
            this.updateParameterSelectionForElement(element)
          }
        }
      } catch (error) {
        console.error('更新参数选择状态失败:', error)
      }
    },

    // 强制更新元素显示（简化版本，主要用于备用）
    forceUpdateElementDisplay(element, newText) {
      if (!element) return

      try {
        // 使用 hiprint 的内置方法更新元素
        if (element.refresh) {
          element.refresh()
        }

        // 如果有更新方法，调用它
        if (element.update) {
          element.update()
        }

        // 触发元素重绘
        if (element.dirty) {
          element.dirty()
        }
      } catch (error) {
        console.error('强制更新元素显示失败:', error)
      }
    },

    // 强制刷新属性面板
    forceRefreshPropertyPanel(element) {
      if (!element || !this.hiprintTemplate) return

      try {
        // 使用温和的刷新方式，不清空属性面板
        setTimeout(() => {
          // 先取消选中
          if (element.setSelected) {
            element.setSelected(false)
          }

          // 短暂延迟后重新选中
          setTimeout(() => {
            if (element.setSelected) {
              element.setSelected(true)
            }

            // 尝试触发属性面板更新
            setTimeout(() => {
              // 查找属性面板中的字段名输入框并更新其值
              const propertyPanel = document.querySelector('#PrintElementOptionSetting')
              if (propertyPanel) {
                // 查找字段名相关的输入框
                const fieldInputs = propertyPanel.querySelectorAll('input[placeholder*="字段"], input[placeholder*="field"], input[type="text"]')
                fieldInputs.forEach(input => {
                  if (input.value !== element.options.field) {
                    input.value = element.options.field
                    // 触发input事件
                    const event = new Event('input', { bubbles: true })
                    input.dispatchEvent(event)
                  }
                })

                // 查找标题相关的输入框并强制更新
                const titleInputs = propertyPanel.querySelectorAll('input[placeholder*="标题"], input[placeholder*="title"], input[placeholder*="Title"]')
                titleInputs.forEach(input => {
                  // 强制设置输入框的值为参数标签
                  input.value = element.options.title

                  // 设置input的原生value属性
                  input.setAttribute('value', element.options.title)

                  // 触发多种事件确保标题更新生效
                  const events = ['input', 'change', 'blur', 'keyup', 'focus']
                  events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true, cancelable: true })
                    input.dispatchEvent(event)
                  })

                  // 如果有Vue实例，直接更新Vue数据
                  if (input.__vue__) {
                    input.__vue__.$emit('input', element.options.title)
                    input.__vue__.$emit('change', element.options.title)
                  }

                  // 强制触发input的oninput事件
                  if (input.oninput) {
                    input.oninput({ target: input })
                  }

                  // 强制触发input的onchange事件
                  if (input.onchange) {
                    input.onchange({ target: input })
                  }
                })

                // 查找并点击确定按钮来强制应用标题更改
                setTimeout(() => {
                  const confirmButtons = propertyPanel.querySelectorAll('button, .el-button')
                  confirmButtons.forEach(button => {
                    const buttonText = button.textContent || button.innerText
                    if (buttonText && (buttonText.includes('确定') || buttonText.includes('应用') || buttonText.includes('OK'))) {
                      button.click()
                    }
                  })
                }, 100)
              }
            }, 200)
          }, 100)
        }, 50)
      } catch (error) {
        console.error('强制刷新属性面板失败:', error)
        // 如果强制刷新失败，使用备用方法
        this.refreshPropertyPanel(element)
      }
    },

    // 刷新属性面板（保留原方法作为备用）
    refreshPropertyPanel(element) {
      if (!element || !this.hiprintTemplate) return

      try {
        // 最简单的刷新方法，只重新选择元素
        setTimeout(() => {
          if (element.setSelected) {
            element.setSelected(false)
            setTimeout(() => {
              element.setSelected(true)
            }, 50)
          }
        }, 50)
      } catch (error) {
        console.error('刷新属性面板失败:', error)
      }
    },

    // 强制刷新属性面板
    forceRefreshPropertyPanel() {
      try {
        const selectedElements = this.hiprintTemplate.getSelectEls()
        if (!selectedElements || selectedElements.length === 0) {
          return
        }

        const element = selectedElements[0]

        // 方法1：重新选择元素来刷新属性面板
        if (element.setSelected) {
          element.setSelected(false)

          setTimeout(() => {
            element.setSelected(true)
          }, 100)
        } else {
          // 方法2：如果没有 setSelected 方法，尝试触发选择事件
          this.triggerElementSelection(element)
        }
      } catch (error) {
        console.error('强制刷新属性面板失败:', error)
      }
    },

    // 触发元素选择事件
    triggerElementSelection(element) {
      try {
        // 尝试通过点击元素来触发选择
        if (element.el) {
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
          })
          element.el.dispatchEvent(clickEvent)
        } else if (element.$el && element.$el[0]) {
          // 如果是jQuery对象
          element.$el.trigger('click')
        }
      } catch (error) {
        console.error('触发元素选择事件失败:', error)
      }
    },

    // 刷新标题输入框的值（强力版本）
    refreshTitleInputValue(newTitle) {
      try {
        // 查找属性面板容器
        const propertyPanel = document.querySelector('#PrintElementOptionSetting')
        if (!propertyPanel) {
          return
        }

        // 查找标题输入框 - 使用多种策略
        let titleInput = null

        // 策略1：通过placeholder查找
        const titleSelectors = [
          'input[placeholder*="标题"]',
          'input[placeholder*="title"]',
          'input[placeholder*="Title"]'
        ]

        for (const selector of titleSelectors) {
          titleInput = propertyPanel.querySelector(selector)
          if (titleInput) {
            break
          }
        }

        // 策略2：通过标签文本查找
        if (!titleInput) {
          const labels = propertyPanel.querySelectorAll('label')
          for (const label of labels) {
            if (label.textContent && label.textContent.includes('标题')) {
              // 查找标签后面的输入框
              let nextElement = label.nextElementSibling
              while (nextElement) {
                if (nextElement.tagName === 'INPUT' && nextElement.type === 'text') {
                  titleInput = nextElement
                  break
                }
                nextElement = nextElement.nextElementSibling
              }
              if (titleInput) break
            }
          }
        }

        // 策略3：查找textarea（长文本输入框）
        if (!titleInput) {
          const allTextareas = propertyPanel.querySelectorAll('textarea')

          // 查找标题相关的textarea
          for (let i = 0;i < allTextareas.length;i++) {
            const textarea = allTextareas[i]
            if (textarea.placeholder && (
              textarea.placeholder.includes('标题') ||
              textarea.placeholder.includes('title') ||
              textarea.placeholder.includes('Title')
            )) {
              titleInput = textarea
              break
            }
          }

          // 如果还没找到，尝试通过父元素的标签查找textarea
          if (!titleInput) {
            for (let i = 0;i < allTextareas.length;i++) {
              const textarea = allTextareas[i]
              const parentElement = textarea.parentElement
              if (parentElement) {
                const labelText = parentElement.textContent || ''
                if (labelText.includes('标题')) {
                  titleInput = textarea
                  break
                }
              }
            }
          }
        }

        // 策略4：如果还没找到，查找所有input[type="text"]作为备用
        if (!titleInput) {
          const allInputs = propertyPanel.querySelectorAll('input[type="text"]')

          // 尝试不同的位置
          for (let i = 0;i < allInputs.length;i++) {
            const input = allInputs[i]
            // 检查是否是标题相关的输入框
            if (input.placeholder && (
              input.placeholder.includes('标题') ||
              input.placeholder.includes('title') ||
              input.placeholder.includes('Title')
            )) {
              titleInput = input
              break
            }
          }

          // 如果还没找到，尝试通过父元素的标签查找
          if (!titleInput) {
            for (let i = 0;i < allInputs.length;i++) {
              const input = allInputs[i]
              const parentElement = input.parentElement
              if (parentElement) {
                const labelText = parentElement.textContent || ''
                if (labelText.includes('标题')) {
                  titleInput = input
                  break
                }
              }
            }
          }
        }

        if (titleInput) {
          // 强力设置值的方法
          this.forceSetInputValue(titleInput, newTitle)
        }
      } catch (error) {
        console.error('刷新标题输入框值失败:', error)
      }
    },

    // 强力设置输入框值的方法
    forceSetInputValue(input, value) {
      try {
        // 保存原始的事件处理器
        const originalOnInput = input.oninput
        const originalOnChange = input.onchange

        // 临时移除事件处理器，防止被覆盖
        input.oninput = null
        input.onchange = null

        // 设置值
        input.value = value
        input.setAttribute('value', value)

        // 如果有Vue实例，直接更新Vue的数据
        if (input.__vue__) {
          // 尝试找到对应的数据属性
          const vueInstance = input.__vue__
          if (vueInstance.$data && vueInstance.$data.title !== undefined) {
            vueInstance.$data.title = value
          }
          if (vueInstance.title !== undefined) {
            vueInstance.title = value
          }
        }

        // 恢复事件处理器
        setTimeout(() => {
          input.oninput = originalOnInput
          input.onchange = originalOnChange

          // 触发事件
          const inputEvent = new Event('input', { bubbles: true, cancelable: true })
          input.dispatchEvent(inputEvent)

          const changeEvent = new Event('change', { bubbles: true, cancelable: true })
          input.dispatchEvent(changeEvent)

          // 强制触发焦点事件
          input.focus()
          input.blur()
        }, 50)

        // 设置一个监听器，防止值被改回去
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
              if (input.value !== value) {
                input.value = value
              }
            }
          })
        })

        observer.observe(input, { attributes: true, attributeFilter: ['value'] })

        // 5秒后停止监听
        setTimeout(() => {
          observer.disconnect()
        }, 5000)
      } catch (error) {
        console.error('强力设置输入框值失败:', error)
      }
    },

    // 启动标题值监听器
    startTitleValueWatcher(targetTitle) {
      // 清除之前的监听器
      if (this.titleWatcherInterval) {
        clearInterval(this.titleWatcherInterval)
      }

      let attemptCount = 0
      const maxAttempts = 50 // 最多尝试50次，约10秒

      this.titleWatcherInterval = setInterval(() => {
        attemptCount++

        try {
          const propertyPanel = document.querySelector('#PrintElementOptionSetting')
          if (!propertyPanel) {
            return
          }

          // 查找标题输入框 - 优先查找textarea
          let titleInput = null

          // 首先查找textarea
          const allTextareas = propertyPanel.querySelectorAll('textarea')
          for (let i = 0;i < allTextareas.length;i++) {
            const textarea = allTextareas[i]
            if (textarea.placeholder && (
              textarea.placeholder.includes('标题') ||
              textarea.placeholder.includes('title') ||
              textarea.placeholder.includes('Title')
            )) {
              titleInput = textarea
              break
            }
          }

          // 如果没找到textarea，尝试通过父元素标签查找textarea
          if (!titleInput) {
            for (let i = 0;i < allTextareas.length;i++) {
              const textarea = allTextareas[i]
              const parentElement = textarea.parentElement
              if (parentElement) {
                const labelText = parentElement.textContent || ''
                if (labelText.includes('标题')) {
                  titleInput = textarea
                  break
                }
              }
            }
          }

          // 如果还没找到，查找input[type="text"]作为备用
          if (!titleInput) {
            const allInputs = propertyPanel.querySelectorAll('input[type="text"]')
            for (let i = 0;i < allInputs.length;i++) {
              const input = allInputs[i]
              if (input.placeholder && (
                input.placeholder.includes('标题') ||
                input.placeholder.includes('title') ||
                input.placeholder.includes('Title')
              )) {
                titleInput = input
                break
              }
            }
          }

          if (titleInput) {
            if (titleInput.value !== targetTitle) {
              // 强制设置值
              titleInput.value = targetTitle

              // 触发事件
              const inputEvent = new Event('input', { bubbles: true })
              titleInput.dispatchEvent(inputEvent)

              const changeEvent = new Event('change', { bubbles: true })
              titleInput.dispatchEvent(changeEvent)

              // 如果有Vue实例，也更新
              if (titleInput.__vue__) {
                titleInput.__vue__.$emit('input', targetTitle)
              }
            }
          }
        } catch (error) {
          console.error('标题值监听器执行失败:', error)
        }

        // 达到最大尝试次数后停止
        if (attemptCount >= maxAttempts) {
          console.log('标题值监听器达到最大尝试次数，停止监听')
          clearInterval(this.titleWatcherInterval)
          this.titleWatcherInterval = null
        }
      }, 200) // 每200ms检查一次

      console.log('标题值监听器已启动，将持续监听约10秒')
    },

    // 温和的属性面板刷新方法
    refreshPropertyPanelGently() {
      try {
        // 获取当前选中的元素
        const selectedElements = this.hiprintTemplate.getSelectEls()
        if (!selectedElements || selectedElements.length === 0) return

        const element = selectedElements[0]

        // 使用更温和的方式刷新属性面板
        // 先触发一个微小的属性更新来刷新面板
        if (element.options) {
          // 保存当前标题
          const currentTitle = element.options.title

          // 临时设置一个不同的值，然后立即恢复
          // 这会触发属性面板的更新
          setTimeout(() => {
            this.hiprintTemplate.updateOption('title', currentTitle + ' ')
            setTimeout(() => {
              this.hiprintTemplate.updateOption('title', currentTitle)
            }, 50)
          }, 50)
        }
      } catch (error) {
        console.error('温和刷新属性面板失败:', error)
        // 如果温和方法失败，回退到原方法
        const selectedElements = this.hiprintTemplate.getSelectEls()
        if (selectedElements && selectedElements.length > 0) {
          this.refreshPropertyPanel(selectedElements[0])
        }
      }
    },

    // 查看参数配置
    viewParameters() {
      if (!this.currentParameters) {
        this.$message.warning('没有参数配置')
        return
      }

      this.$alert(
        `<pre>${JSON.stringify(this.currentParameters, null, 2)}</pre>`,
        '参数配置',
        {
          dangerouslyUseHTMLString: true,
          customClass: 'parameter-view-dialog'
        }
      )
    },

    // 应用参数到设计
    applyParameters() {
      if (!this.currentParameters) {
        this.$message.warning('没有参数配置')
        return
      }

      this.$confirm('应用参数配置可能会影响当前的设计，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里可以根据参数配置来调整设计器的设置
        // 例如纸张大小、边距等
        if (this.currentParameters.printSettings) {
          const settings = this.currentParameters.printSettings
          if (settings.paperSize) {
            // 应用纸张大小设置
            console.log('应用纸张大小:', settings.paperSize)
          }
          if (settings.margins) {
            // 应用边距设置
            console.log('应用边距设置:', settings.margins)
          }
        }
        this.$message.success('参数配置已应用')
      })
    },

    // 应用子表到当前选中的表格元素
    applySubtableToElement(subtableKey) {
      console.log('开始应用子表:', subtableKey)

      if (!subtableKey) {
        console.log('子表key为空')
        return
      }

      try {
        // 优先使用我们自己维护的选中元素
        let element = null

        if (this.isElementReallySelected && this.currentSelectedElement) {
          element = this.currentSelectedElement
          console.log('使用自维护的选中元素:', element)
        } else if (this.hiprintTemplate) {
          // 备用方案：从hiprint获取选中元素
          const selectedElements = this.hiprintTemplate.getSelectEls()
          if (selectedElements && selectedElements.length > 0) {
            element = selectedElements[0]
            console.log('使用hiprint选中元素:', element)
          }
        }

        if (!element) {
          this.$message.warning('请先选择一个表格元素')
          console.log('没有找到选中的元素')
          return
        }

        // 确认是表格元素
        if (!this.isTableElement(element)) {
          this.$message.warning('请选择表格元素')
          console.log('选中的不是表格元素')
          return
        }

        // 找到对应的子表配置
        const subtable = this.subtableOptions.find(st => st.key === subtableKey)
        if (!subtable) {
          this.$message.error('未找到子表配置')
          console.log('未找到子表配置:', subtableKey)
          return
        }

        console.log('找到子表配置:', subtable)

        // 如果有hiprint模板，尝试更新真实的表格元素
        if (this.hiprintTemplate) {
          try {
            // 构建hiprint表格的columns配置
            const columns = [[]]
            subtable.columns.forEach(col => {
              columns[0].push({
                title: col.title,
                field: col.field,
                width: col.width || 100,
                align: col.align || 'center',
                checked: true
              })
            })

            console.log('生成的columns配置:', columns)

            // 更新表格的field和columns配置
            this.hiprintTemplate.updateOption('field', subtableKey)
            this.hiprintTemplate.updateOption('columns', columns)

            // 也尝试更新title属性
            this.hiprintTemplate.updateOption('title', subtable.name)

            console.log('hiprint配置更新完成')
          } catch (error) {
            console.error('更新hiprint配置失败:', error)
          }
        }

        // 更新我们自己维护的元素对象
        if (this.currentSelectedElement && this.currentSelectedElement.options) {
          this.currentSelectedElement.options.field = subtableKey
          this.currentSelectedElement.options.columns = subtable.columns
          this.currentSelectedElement.options.title = subtable.name
          console.log('更新自维护元素配置完成')
        }

        this.$message.success(`已将子表 "${subtable.name}" 应用到选中的表格元素`)

        // 强制刷新Vue组件
        this.$forceUpdate()

        // 验证更新结果
        setTimeout(() => {
          if (this.hiprintTemplate) {
            const updatedElements = this.hiprintTemplate.getSelectEls()
            if (updatedElements && updatedElements.length > 0) {
              console.log('更新后的hiprint元素配置:', updatedElements[0].options)
            }
          }
          console.log('更新后的自维护元素配置:', this.currentSelectedElement)
        }, 500)
      } catch (error) {
        console.error('应用子表失败:', error)
        this.$message.error('应用子表失败: ' + error.message)
      }
    },

    // 应用字段到元素（新方法）
    applyFieldToElement(field, subtable) {
      if (!this.hiprintTemplate || !field || !subtable) {
        return
      }

      try {
        // 获取当前选中的元素
        const selectedElements = this.hiprintTemplate.getSelectEls()

        if (!selectedElements || selectedElements.length === 0) {
          this.$message.warning('请先选择一个元素')
          return
        }

        // 判断是否为表格元素
        const element = selectedElements[0]
        if (this.isTableElement(element)) {
          // 表格元素：设置子表信息
          this.hiprintTemplate.updateOption('field', subtable.key)
          this.hiprintTemplate.updateOption('title', subtable.name)

          // 构建hiprint表格的columns配置
          const columns = [[]]
          subtable.columns.forEach(col => {
            columns[0].push({
              title: col.title,
              field: col.field,
              width: col.width || 100,
              align: col.align || 'center',
              checked: true
            })
          })
          this.hiprintTemplate.updateOption('columns', columns)

          this.$message.success(`已将子表 "${subtable.name}" 应用到表格元素`)
        } else {
          // 非表格元素：设置字段信息
          this.hiprintTemplate.updateOption('field', field.field)
          this.hiprintTemplate.updateOption('title', field.title)

          this.$message.success(`已将字段 "${field.title}#${field.field}" 应用到选中元素`)
        }

        // 更新当前选中元素的引用
        this.currentSelectedElement = element

        // 强制刷新属性面板
        setTimeout(() => {
          this.forceRefreshPropertyPanel()
        }, 200)
      } catch (error) {
        console.error('应用字段失败:', error)
        this.$message.error('应用字段失败: ' + error.message)
      }
    }
  }
}
</script>

<style lang="scss">
// 引入自定义hiprint样式
@import './styles/hiprint-custom.scss';
</style>

<style lang="scss" scoped>
.print-template-editor {
  min-height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;

  .template-info-bar {
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 10px;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }

      .info-value {
        color: #303133;
        font-weight: 400;
      }
    }
  }

  .toolbar {
    padding: 10px;
    background: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;

    .paper-setting {
      .el-input-group {
        display: flex;
        align-items: center;
      }
    }
  }

  .editor-container {
    flex: 1;
    padding: 10px;
    min-height: 0;

    .component-panel,
    .property-panel {
      height: calc(200vh - 280px); // 高度调高两倍，与中间区域保持一致
      min-height: 1600px; // 最小高度也调高两倍

      .panel-header {
        font-weight: bold;
        color: #303133;
      }

      .component-list {
        .component-group {
          margin-bottom: 20px;

          .group-title {
            font-size: 12px;
            color: #909399;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid #e4e7ed;
          }

          .component-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;

            .component-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 8px;
              border: 1px solid #e4e7ed;
              border-radius: 4px;
              cursor: move;
              transition: all 0.3s;
              user-select: none;

              &:hover {
                border-color: #409eff;
                background-color: #ecf5ff;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
              }

              &.dragging {
                opacity: 0.5;
                transform: scale(0.95);
              }

              i {
                font-size: 20px;
                margin-bottom: 4px;
                color: #606266;
                pointer-events: none;
              }

              span {
                font-size: 12px;
                color: #303133;
                pointer-events: none;
              }
            }
          }
        }
      }
    }

    .design-canvas {
      height: calc(200vh - 280px); // 高度调高两倍
      min-height: 1600px; // 最小高度也调高两倍

      .canvas-container {
        height: 100%;
        width: 100%;
        position: relative;
        // 为标尺预留空间，标尺在画布外部
        padding-top: 20px !important;  // 顶部标尺空间
        padding-left: 20px !important; // 左侧标尺空间
        margin: 0 !important;

        .hiprint-printTemplate {
          // 恢复hiprint原始布局
          height: 100% !important;
          width: 100% !important;
          min-height: 600px !important;
          padding: 0 !important;
          margin: 0 !important;
          position: relative;
        }
      }
    }

    .property-container {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}

// 设计卡片滚动样式 - 移除所有空白区域
.design-card {
  overflow: hidden;
  overflow-x: auto;
  overflow-y: auto;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;

  // 确保卡片内容区域也可以滚动，移除所有空白
  :deep(.el-card__body) {
    height: 100%;
    overflow: hidden;
    overflow-x: auto;
    overflow-y: auto;
    padding: 0 !important; // 完全移除padding
    margin: 0 !important;  // 完全移除margin
  }

  // 移除卡片头部区域
  :deep(.el-card__header) {
    display: none !important;
  }
}

// 对齐功能样式
.btn-text-desc {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
  white-space: nowrap;
}

// 第二行工具栏样式
.toolbar-second-row {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-top: 1px solid #e6e6e6;
  background: #fafafa;
  border-radius: 4px;
  padding: 8px 12px;
}

// 对齐按钮组样式
.el-button-group {
  .el-button {
    padding: 8px 12px;

    i {
      font-size: 14px;
    }
  }
}

// 参数选择器样式
.parameter-selector {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;

  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    .selector-title {
      font-size: 13px;
      font-weight: 500;
      color: #303133;
    }

    .el-icon-question {
      color: #909399;
      cursor: help;
      font-size: 14px;
    }
  }

  // 子表信息展示
  .subtable-info {
    margin-top: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    .info-header {
      font-size: 12px;
      font-weight: 500;
      color: #495057;
      margin-bottom: 6px;
    }

    .column-list {
      .column-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2px 0;
        font-size: 11px;

        .column-title {
          color: #303133;
          font-weight: 500;
        }

        .column-field {
          color: #909399;
          font-style: italic;
        }
      }
    }
  }
}

// 参数查看对话框样式
:global(.parameter-view-dialog) {
  .el-message-box__content {
    max-height: 400px;
    overflow-y: auto;
  }

  pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
  }
}

// 字段选择器样式
.field-selector {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;

  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    .selector-title {
      font-size: 13px;
      font-weight: 500;
      color: #303133;
    }

    .el-icon-question {
      color: #909399;
      cursor: help;
      font-size: 14px;
    }
  }

  // 子表信息展示
  .subtable-info {
    margin-top: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    .info-header {
      font-size: 12px;
      font-weight: 500;
      color: #495057;
      margin-bottom: 6px;
    }

    .column-list {
      .column-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2px 0;
        font-size: 11px;

        .column-title {
          color: #303133;
          font-weight: 500;
        }

        .column-field {
          color: #909399;
          font-style: italic;
        }
      }
    }
  }
}

// 选中状态指示器样式
.selected-indicator {
  color: #409eff;
  font-weight: bold;
}

// 子表信息展示样式
.subtable-info-display {
  .subtable-details {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #fafafa;
  }

  .subtable-item {
    margin-bottom: 12px;
    padding: 8px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #fff;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .subtable-header {
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    color: #303133;
    font-size: 13px;
  }

  .subtable-fields {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .field-item {
    padding: 4px 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 3px;
    font-size: 12px;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e3f2fd;
      border-color: #2196f3;
      color: #1976d2;
      transform: translateX(2px);
    }

    &:active {
      background: #bbdefb;
      transform: translateX(0);
    }
  }

  // 无子表信息提示样式
  .no-subtable-info {
    text-align: center;
    padding: 20px 10px;

    p {
      margin: 0;
      font-size: 13px;
      color: #909399;
    }
  }

  // 子表信息弹窗样式
  .subtable-dialog-content {
    .subtable-item {
      margin-bottom: 20px;

      .subtable-header {
        margin-bottom: 10px;

        h4 {
          margin: 0;
          color: #303133;
          font-size: 16px;
        }
      }

      .subtable-fields {
        .el-table {
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
