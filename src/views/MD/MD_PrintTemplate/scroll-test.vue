<template>
  <div class="scroll-test">
    <h2>画布滚动测试</h2>

    <div class="test-container">
      <div class="canvas-container">
        <div class="mock-canvas">
          <div class="mock-paper">
            <h3>A4 画布 (210×297mm)</h3>
            <p>这是模拟的A4纸张</p>
            <p>高度: 1123px (297mm)</p>
            <p>宽度: 794px (210mm)</p>
            <div class="content-area">
              <p>请测试滚动功能：</p>
              <ul>
                <li>垂直滚动查看整个画布</li>
                <li>水平滚动（如果需要）</li>
                <li>确保能看到画布的底部</li>
              </ul>
            </div>
            <div class="bottom-marker">
              <p><strong>画布底部</strong></p>
              <p>如果您能看到这行文字，说明滚动功能正常</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="instructions">
      <h3>测试说明：</h3>
      <ol>
        <li>上方的灰色区域应该有滚动条</li>
        <li>白色的"纸张"应该完全可见</li>
        <li>可以滚动到"画布底部"文字</li>
        <li>滚动应该流畅，没有卡顿</li>
      </ol>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScrollTest'
}
</script>

<style lang="scss" scoped>
.scroll-test {
  padding: 20px;

  .test-container {
    border: 2px solid #409eff;
    border-radius: 8px;
    margin: 20px 0;

    .canvas-container {
      height: 400px; // 固定高度，强制显示滚动条
      overflow-y: auto;
      overflow-x: auto;
      background: #f0f0f0;
      border: 1px solid #e6e6e6;
      position: relative;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;

        &:hover {
          background: #a8a8a8;
        }
      }

      .mock-canvas {
        min-height: 600px; // 比容器高，确保有滚动
        width: 100%;
        padding: 50px;

        .mock-paper {
          background: white;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          margin: 20px auto;
          position: relative;
          z-index: 1;
          // A4纸张尺寸
          width: 794px;
          height: 1123px;
          padding: 40px;

          h3 {
            color: #409eff;
            text-align: center;
            margin-bottom: 20px;
          }

          .content-area {
            margin: 40px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;

            ul {
              margin: 10px 0;
              padding-left: 20px;
            }
          }

          .bottom-marker {
            position: absolute;
            bottom: 40px;
            left: 40px;
            right: 40px;
            background: #e6f7ff;
            border: 2px solid #409eff;
            border-radius: 8px;
            padding: 20px;
            text-align: center;

            p {
              margin: 5px 0;

              &:first-child {
                color: #409eff;
                font-size: 18px;
              }
            }
          }
        }
      }
    }
  }

  .instructions {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;

    h3 {
      color: #409eff;
      margin-bottom: 15px;
    }

    ol {
      margin: 0;
      padding-left: 20px;

      li {
        margin: 8px 0;
        line-height: 1.5;
      }
    }
  }
}
</style>
