<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>

      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Dictionary.Add' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >{{ $t("Common.add") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Dictionary.Edit' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-edit"
        :disabled="selective"
        @click="handleUpdate"
      >{{ $t("Common.edit") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Dictionary.Delete' }"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="deletable"
        @click="handleDelete"
      >{{ $t("Common.delete") }}</el-button>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Dictionary.Export' }"
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t("Common.export") }}</el-button>
    </div>

    <el-table
      ref="table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('Common.select')"
        type="selection"
        align="center"
        width="40"
        fixed
      />

      <el-table-column type="index" align="center" width="50" label="行号" />
      <el-table-column
        :label="$t('ui.MD.MD_Dictionary.TypeCode')"
        prop="TypeCode"
        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.TypeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_Dictionary.TypeDisc')"
        prop="TypeDisc"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.TypeDisc }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_Dictionary.EnumKey')"
        prop="EnumKey"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.EnumKey }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_Dictionary.EnumValue')"
        prop="EnumValue"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.EnumValue }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('ui.MD.MD_Dictionary.EnumValue1')"
        prop="EnumValue1"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.EnumValue1 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('ui.MD.MD_Dictionary.EnumValue2')"
        prop="EnumValue2"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.EnumValue2 }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.Remark')"
        prop="Remark"
        align="center"
        width="240"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('Common.CUser')"
        prop="CUser"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('Common.CTime')"
        prop="CTime"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.CTime | datetime }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <el-dialog :title="formTitle" :visible.sync="dialogFormVisible" width="65%">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.TypeCode')" prop="TypeCode">
              <el-select v-model="formData.TypeCode" filterable class="filter-item" @change="selectTypeCode">
                <el-option
                  v-for="item in TypeOptions"
                  :key="item.TypeCode"
                  :label="item.TypeCode"
                  :value="item.TypeCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.TypeDisc')" prop="TypeDisc">
              <el-input v-model="formData.TypeDisc" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.EnumKey')" prop="EnumKey">
              <el-input-number v-model="formData.EnumKey" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.EnumValue')" prop="EnumValue">
              <el-input v-model="formData.EnumValue" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.EnumValue1')">
              <el-input v-model="formData.EnumValue1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.EnumValue2')">
              <el-input v-model="formData.EnumValue2" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="24">
            <el-form-item :label="$t('ui.MD.MD_Dictionary.Remark')" props="Remark">
              <el-input
                v-model="formData.Remark"
                :autosize="{ minRows: 3, maxRows: 5 }"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-waves
          type="primary"
          icon="el-icon-check"
          @click="formMode === 'Create' ? createData() : updateData()"
        >{{ $t("Common.save") }}</el-button>
        <el-button v-waves @click="dialogFormVisible = false">
          {{ $t("Common.close") }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import Pagination from '../../../components/Pagination/index';
import {
  fetchPage,
  add,
  update,
  batchDelete,
  getAllType,
  exportExcelFile
} from '@/api/Sys/Sys_Dictionary';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令
import { exportToExcel } from '@/utils/excel-export';

export default {
  name: 'Dictionary',
  directives: {
    waves,
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      isProcessing: false,
      total: 0,
      formTitle: '',
      formMode: '',
      dialogFormVisible: false,
      edit: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      },
      formData: {
        DictionaryID: '',
        TypeCode: '',
        TypeDisc: '',
        EnumKey: '',
        EnumValue: '',
        EnumValue1: '',
        EnumValue2: '',
        SortNum: '',
        Remark: ''
      },
      value: '',
      rules: {
        TypeCode: [
          {
            required: true,
            message: this.$t('Common.ValidatorMessage.MustInput'),
            trigger: 'blur'
          }
        ],
        TypeDisc: [
          {
            required: true,
            message: this.$t('Common.ValidatorMessage.MustInput'),
            trigger: 'blur'
          }
        ],
        EnumKey: [
          {
            required: true,
            message: this.$t('Common.ValidatorMessage.MustInput'),
            trigger: 'change'
          }
        ],
        EnumValue: [
          {
            required: true,
            message: this.$t('Common.ValidatorMessage.MustInput'),
            trigger: 'blur'
          }
        ]
      },
      multipleSelection: [],
      TypeOptions: []
    };
  },
  computed: {
    selective() {
      return this.multipleSelection.length !== 1;
    },
    deletable() {
      return this.multipleSelection.length === 0;
    }
  },
  created() {
    this.getList();
    this.getTypeCode();
  },
  mounted() {},
  methods: {
    selectTypeCode() {
      var row = this.TypeOptions.find(
        x => x.TypeCode == this.formData.TypeCode
      );
      console.log('1', row);
      this.formData.TypeDisc = row.TypeDisc;
    },
    getTypeCode() {
      this.TypeOptions = [];
      getAllType().then(response => {
        this.TypeOptions = response.Data;
        console.log(response.Data);
      });
    },
    getList() {
      this.listLoading = true;
      // 初始化表格
      fetchPage(this.listQuery).then(result => {
        this.list = result.Data.items;
        this.total = result.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      // 获取搜索数据
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },

    handleCreate() {
      this.resetDataForm();
      this.formTitle = this.$t('Common.add');
      this.formMode = 'Create';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        // this.$refs['dataForm'].clearValidate() // 清除校验
      });
    },
    handleUpdate() {
      this.resetDataForm();
      this.formData = Object.assign({}, this.multipleSelection[0]); // 对象拷贝
      this.formTitle = this.$t('Common.edit');
      this.formMode = 'Update';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        // this.$refs['dataForm'].clearValidate() // 清除校验
      });
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          // 添加
          add(this.formData).then(response => {
            // this.list.unshift(this.temp)
            this.dialogFormVisible = false;
            this.$notify({
              title: this.$t('Common.success'),
              message: this.$t('Common.operationSuccess'),
              type: 'success',
              duration: 2000
            });
            this.getList();
          });
        }
      });
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          update(this.formData).then(() => {
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: this.$t('Common.success'),
              message: this.$t('Common.operationSuccess'),
              type: 'success',
              duration: 2000
            });
          });
        }
      });
    },
    resetDataForm() {
      // 重置表单数据
      this.formData = {
        DictionaryID: '',
        TypeCode: '',
        TypeDisc: '',
        EnumKey: '',
        EnumValue: '',
        EnumValue1: '',
        EnumValue2: '',
        SortNum: '',
        Remark: ''
      };
    },
    handleDelete() {
      // 删除一条数据
      this.$confirm(
        this.$i18n.t('Common.batchDeletingConfirm'),
        this.$i18n.t('Common.tip'),
        {
          confirmButtonText: this.$i18n.t('Common.affirm'),
          cancelButtonText: this.$i18n.t('Common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        var arrRowsID = this.multipleSelection.map(v => v.DictionaryID);
        this.isProcessing = true;
        // 删除逻辑处理
        batchDelete(arrRowsID).then(res => {
          if (res.Code === 2000) {
            this.showNotify('success', 'Common.deleteSuccess');
          } else {
            this.showNotify('error', res.Message);
          }
          this.getList();
        }).catch(err => {
			  console.log(err);
			  this.isProcessing = false;
        });
      });
    },
    handleExport() {
      this.isProcessing = true;
      exportExcelFile({ Keyword: this.listQuery.keyword }).then(res => {
        exportToExcel(res.data, '数据字典');
        this.isProcessing = false;
      }).catch(err => {
			  console.log(err);
			  this.isProcessing = false;
      });
    },

    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.handleFilter();
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = 'DicID asc';
      } else {
        this.listQuery.sort = 'DicID desc';
      }
      this.handleFilter();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>
