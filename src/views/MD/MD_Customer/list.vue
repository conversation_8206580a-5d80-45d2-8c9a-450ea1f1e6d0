<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Permission.SyncCustomer' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-download"
        @click="handleSyncCustomer"
      >{{ $t('ui.MD.MD_Customer.SyncCustomer') }}
      </el-button>

      <!-- <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Region.Export' }"
        class="filter-item" size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>-->
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      size="mini"
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column :label="$t('ui.MD.MD_Customer.InternalID')" prop="InternalID" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.InternalID }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.FamilyName')" prop="FamilyName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.FamilyName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.FirstLineName')" prop="FirstLineName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.FirstLineName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.EMailURI')" prop="EMailURI" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.EMailURI }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.Address')" prop="Address" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Address }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.CityName')" prop="CityName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.CityName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.ContactPerson')" prop="ContactPerson" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ContactPerson }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.Fax')" prop="Fax" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Fax }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Customer.Phone')" prop="Phone" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Phone }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves';
import adaptive from '../../../directive/el-table/adaptive';
import Pagination from '../../../components/Pagination/index';
import {
  exportToExcel
} from '@/utils/excel-export';
import {
  fetchList,
  SyncCustomer
} from '@/api/MD/MD_Customer';
// import permission from '../../../directive/permission/permission'
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Customer',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      isProcessing: false,
      listLoading: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.list = response.Data.items;
        this.total = response.Data.total;
        this.listLoading = false;
      });
    },
    handleFilter() {
      // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList();
    },
    handleSyncCustomer() {
      this.isProcessing = true;
      SyncCustomer()
        .then(res => {
          console.log('同步客户数据');
          this.isProcessing = false;
          this.showNotify('success', 'Common.operationSuccess');
          this.getList();
        })
        .catch(err => {
          console.log(err);
          this.showNotify('success', 'Common.operationFailed');
          this.isProcessing = false;
          this.getList();
        });
    },
    handleExport() {
      this.isProcessing = true;
      // eslint-disable-next-line no-undef
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res => {
        exportToExcel(res.data, '客户主数据');
        this.isProcessing = false;
      }

      ).catch(err => {
        console.log(err);
        this.isProcessing = false;
      });
    },

    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order !== undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter();
      }
    }
  }
};
</script>
