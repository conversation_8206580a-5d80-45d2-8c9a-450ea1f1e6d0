<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <el-container>
      <el-aside style="width:250px;background:#fff;border-right:solid 1px #ddd; height: 620px">
        <el-menu style="border:0px" default-active="100" @select="handleSelectModule">
          <el-submenu index="main">
            <template slot="title">
              <i class="el-icon-menu" />
              <span>{{ $t("ui.MD.MD_BinLimit.functionModule") }}</span>
            </template>
            <el-submenu v-for="subItem in pdaModule" :key="subItem.id" :index="subItem.id">
              <span slot="title">{{ subItem.label }}</span>
              <el-menu-item
                v-for="childItem in subItem.children"
                :key="childItem.id"
                :index="childItem.id"
              >
                <!-- <i class="el-icon-menu"></i> -->
                <span slot="title">{{ childItem.label }}</span>
              </el-menu-item>
            </el-submenu>
          </el-submenu>
        </el-menu>
      </el-aside>
      <!-- ui.MD.MD_BinLimit -->
      <el-container style="padding-left:20px">
        <el-main>
          <el-input
            v-model="listQuery.keyword"
            class="filter-item"
            :placeholder="$t('ui.MD.MD_BinLimit.keyword')"
            style="width: 180px"
            clearable
            @keydown.enter.native="getBinList"
          />
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getBinList" />

          <hr>

          <!-- <el-button type="primary" @click="handleSaveBinLimitSetting">
            {{
            $t("Common.save")
            }}
          </el-button>-->
          <!-- v-permission="{ name: 'MM.MM_OutScan.Add' }" -->
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
          >{{ $t('Common.add') }}</el-button>
          <!-- v-permission="{ name: 'MM.MM_OutScan.Delete' }" -->
          <el-button
            v-waves
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            :disabled="deletable"
            @click="handleDelete"
          >{{ $t('Common.delete') }}</el-button>

          <!-- v-permission="{ name: 'MM.MM_InScan.Export' }" -->
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            @click="handleExport"
          >{{ $t("Common.export") }}</el-button>
          <el-table
            ref="multipleTable"
            v-loading="listLoading"
            :data="list"
            :row-key="bindRowKey"
            border
            :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
            highlight-current-row
            style="width: 100%; margin-top: 5px"
            height="470"
            @sort-change="sortChange"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              :label="$t('Common.select')"
              type="selection"
              align="center"
              width="40"
              fixed
            />
            <el-table-column
              v-if="false"
              :label="$t('ui.MD.MD_BinLocation.BinID')"
              prop="BinID"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.BinID }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLimit.functionModule')"
              prop="MenuCode"

              align="center"
              width="120"
              :formatter="formatModel"
            />
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.BinLocationCode')"
              prop="BinLocationCode"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.BinLocationCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.BinLocationName')"
              prop="BinLocationName"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.BinLocationName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.RegionCode')"
              prop="RegionCode"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.RegionCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('ui.MD.MD_BinLocation.RegionName')"
              prop="RegionName"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.RegionName }}</span>
              </template>
            </el-table-column>
            <!--<el-table-column
              v-if="false"
              :label="$t('ui.MD.MD_BinLocation.WhsCode')"
              prop="WhsCode"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.WhsCode }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="false"
              :label="$t('ui.MD.MD_BinLocation.WhsName')"
              prop="WhsName"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.WhsName }}</span>
              </template>
            </el-table-column>-->

            <el-table-column
              v-if="false"
              :label="$t('Common.Remark')"
              prop="Remark"

              align="center"
              width="140"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.Remark }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="true"
              :label="$t('Common.CUser')"
              prop="CUser"

              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.CUser }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="true"
              :label="$t('Common.CTime')"
              prop="CTime"

              align="center"
              width="160"
              :formatter="formatDateTime"
            />
          </el-table>
          <Pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.PageNumber"
            :limit.sync="listQuery.PageSize"
            @pagination="getBinList"
          />
        </el-main>
      </el-container>
    </el-container>
    <el-dialog
      :title="$t('Common.select')"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="handleClose"
    >
      <el-form>
        <el-form-item :label="$t('ui.MD.MD_BinLimit.functionModule')" prop="functionModule">
          <el-select v-model="menuValue" filterable>
            <el-option
              v-for="item in menuModule"
              :key="item.ResourceID"
              :value="item.ResourceID"
              :label="item.ResourceTitle"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ui.MD.MD_BinLocation.BinLocationCode')" prop="BinLocationCode">
          <el-select v-model="value" filterable :filter-method="binlocationModelSearch">
            <el-option
              v-for="item in options"
              :key="item.BinID"
              :value="item.BinID"
              :label="item.BinLocationCode"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-waves @click="handleClose">{{ $t('Common.cancel') }}</el-button>
        <el-button v-waves type="primary" @click="handledigSave">{{ $t('Common.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive 特效
import Pagination from '@/components/Pagination'; // 分页
import { convertToSelectTree } from '../../../utils';

import { fetchAllList as fetchBinList } from '../../../api/MD/MD_BinLocation';
import {
  fetchModule,
  submitBinLimit,
  fetchBinLocation,
  fetchList as fetchGetList,
  batchDelete,
  exportExcelFile
} from '../../../api/MD/MD_BinLimit';
import { GetResourcesByAppID } from '../../../api/Sys/Sys_Resource';
import { formatDate, formatDateTime } from '../../../utils'; // 列表内容格式化

import { exportToExcel } from '@/utils/excel-export';
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_BinLimit',
  components: { Pagination },
  directives: { waves, permission },
  filters: {},
  data() {
    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: {
        PageNumber: 1,
        PageSize: 20,
        keyword: ''
      },
      pdaModule: '',
      selectedModule: '',
      multipleSelection: [],
      dialogVisible: false,
      binlocationModel: [],
      options: [],
      value: '',
      menuModule: [],
      menuValue: '',
      isProcessing: false
    };
  },
  computed: {
    deletable() {
      const i = this.multipleSelection.length;
      if (i === 0) return true;
      return false;
    }
  },
  created() {
    this.initPdaAppMenus();
    this.getBinList();
    this.getBinModel();
  },
  methods: {
    formatDate,
    formatDateTime,
    binlocationModelSearch(val) {
      if (val) {
        // val存在
        this.options = this.binlocationModel.filter(item => {
          if (
            String(item.BinLocationCode)
              .toUpperCase()
              .indexOf(String(val).toUpperCase()) !== -1
          ) {
            return true;
          }
        });
      } else {
        // val为空时，还原数组
        this.options = this.binlocationModel;
      }
    },
    bindRowKey(row) {
      return row.BinID;
    },
    sortChange(data) {
      const { prop, order } = data;
      if (prop && order) {
        if (order === 'ascending') {
          this.listQuery.sort = prop + ' asc';
        } else {
          this.listQuery.sort = prop + ' desc';
        }
      } else {
        this.listQuery.sort = '';
      }
      this.getBinList();
    },
    getBinList() {
      this.listLoading = true;
      this.listQuery.selectedModule = this.selectedModule;
      fetchGetList(this.listQuery).then(res => {
        console.log(res);
        this.list = res.Data.items;
        this.total = res.Data.total;
        this.listLoading = false;
      });
    },
    getBinModel() {
      fetchBinList({ keyword: '' }).then(res => {
        this.binlocationModel = res.Data;
        this.options = res.Data;
      });
    },
    formatModel(val) {
      var row = this.menuModule.find(x => x.ResourceID == val.MenuCode);
      if (row) {
        return row.ResourceTitle;
      }
      return '';
    },
    initPdaAppMenus() {
      const pdaAppID = 'M001';
      GetResourcesByAppID({ appid: pdaAppID }).then(res => {
        this.menuModule = res.Data;
        const resDataMap = res.Data.map(item => {
          item.ResourceTitle = this.$t('route.' + item.ResourceTitle); // 翻译菜单名称
          return item;
        });
        this.pdaModule = convertToSelectTree(
          resDataMap,
          'ResourceID',
          'FathResourceID',
          'ResourceTitle'
        );
      });
    },
    handleSelectModule(index, path) {
      this.$refs.multipleTable.clearSelection();
      this.selectedModule = index;
      this.menuValue = index;
      fetchModule({ menuCode: index }).then(res => {
        if (res.Data.length) {
          const tempBinList = res.Data;
          const listId = tempBinList.map(v => v.BinLocationCode);
          fetchBinLocation({ ids: listId }).then(res => {
            const havenSelection = res.Data;
            this.toggleTableSelection(havenSelection);
            this.getBinList();
          });
        } else {
          this.getBinList();
        }
      });
    },
    toggleTableSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, true);
        });
      }
    },
    handleSaveBinLimitSetting() {
      // 保存库位限制设置
      if (!this.selectedModule) {
        this.showNotify('error', 'ui.MD.MD_BinLimit.checkModule');
        return;
      }
      const selectRows = this.multipleSelection;
      const arrRowsID = selectRows.map(v => v.BinID);
      if (arrRowsID.length == 0) {
        this.showNotify('error', 'Common.noSelection');
        return;
      }
      submitBinLimit({ id: this.selectedModule, ids: arrRowsID }).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', 'Common.operationSuccess');
        } else {
          this.showNotify('error', res.Message);
        }
      });
    },
    handleAdd() {
      this.dialogVisible = true;
      this.options = this.binlocationModel;
    },
    handledigSave() {
      var arrRowsID = [];
      if (!this.menuValue) {
        this.showNotify('error', 'ui.MD.MD_BinLimit.checkModule');
        return;
      }
      if (this.value === '') {
        this.showNotify('error', 'Common.noSelection');
        return;
      }
      arrRowsID.push(this.value);
      submitBinLimit({ id: this.menuValue, ids: arrRowsID }).then(res => {
        if (res.Code === 2000) {
          this.showNotify('success', 'Common.operationSuccess');
        } else {
          this.showNotify('error', res.Message);
        }
        this.dialogVisible = false;
        this.value = '';
        this.getBinList();
      });
    },
    handleClose() {
      this.value = '';
      this.dialogVisible = false;
    },
    handleDelete() {
      // 删除功能，可能有bug，待测试
      var selectRows = this.multipleSelection;
      if (this.checkMultiSelection(selectRows)) {
        this.$confirm(
          this.$i18n.t('Common.batchDeletingConfirm'),
          this.$i18n.t('Common.tip'),
          {
            confirmButtonText: this.$i18n.t('Common.affirm'),
            cancelButtonText: this.$i18n.t('Common.cancel'),
            type: 'warning'
          }
        ).then(() => {
          // this.isProcessing = true;
          var arrRowsID = selectRows.map(function(v) {
            return v.limitID;
          });

          // 删除逻辑处理
          batchDelete(arrRowsID)
            .then(response => {
              if (response.Code === 2000) {
                this.showNotify('success', 'Common.deleteSuccess');
                this.getBinList();
              } else {
                this.showNotify('error', response.Message);
              }
              // this.isProcessing = false;
            })
            .catch(error => {
              // this.isProcessing = false;
            });
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleExport() {
      this.isProcessing = true;
      var exportQuery = {
        keyword: this.listQuery.keyword,
        selectedModule: this.selectedModule
      };
      exportExcelFile(exportQuery).then(res => {
        exportToExcel(res.data, '库位限制');
        this.isProcessing = false;
      }).catch(err => {
			  console.log(err);
			  this.isProcessing = false;
      });
    }
  }
};
</script>
