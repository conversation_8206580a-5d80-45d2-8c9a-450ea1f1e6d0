<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入关键字"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.PrintDirection"
        placeholder="请输入打印方向"
        style="width: 200px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ParameterCheckStr"
        placeholder="请输入参数校验"
        style="width: 200px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" size="mini" icon="el-icon-search" style="margin-left: 10px" @click="handleFilter">
        {{ $t('Common.search') }}
      </el-button>
      <hr>
      <el-button
        v-permission="['MD.MD_ShippingMarkRule.Add']"
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        {{ $t('Common.add') }}
      </el-button>
      <el-button
        v-permission="['MD.MD_ShippingMarkRule.Edit']"
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        size="mini"
        icon="el-icon-edit"
        :disabled="multipleSelection.length !== 1"
        @click="handleUpdate"
      >
        {{ $t('Common.edit') }}
      </el-button>
      <el-button
        v-permission="['MD.MD_ShippingMarkRule.Delete']"
        :disabled="multipleSelection.length === 0"
        class="filter-item"
        style="margin-left: 10px;"
        type="danger"
        size="mini"
        icon="el-icon-delete"
        @click="handleDelete"
      >
        {{ $t('Common.delete') }}
      </el-button>
      <el-button
        v-permission="['MD.MD_ShippingMarkRule.Export']"
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        size="mini"
        icon="el-icon-download"
        @click="handleExport"
      >
        {{ $t('Common.export') }}
      </el-button>
      <el-button
        v-permission="['MD.MD_ShippingMarkRule.Import']"
        class="filter-item"
        style="margin-left: 10px;"
        type="warning"
        size="mini"
        icon="el-icon-upload2"
        @click="handleExportTemplate"
      >
        {{ $t('Common.exportTemplate') }}
      </el-button>
      <el-upload
        v-permission="['MD.MD_ShippingMarkRule.Import']"
        class="filter-item upload-demo"
        style="margin-left: 10px; display: inline-block"
        action=""
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <el-button type="primary" size="mini" icon="el-icon-upload">{{ $t('Common.import') }}</el-button>
      </el-upload>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      height="calc(100vh - 320px)"
      size="mini"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发货单位" prop="DeliveryCompany" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.DeliveryCompany }}</span>
        </template>
      </el-table-column>
      <el-table-column label="模板名称" prop="TemplateName" align="center" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.TemplateName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唛头打印方式" prop="ShippingMarkPrintMethod" align="center" min-width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPrintMethod }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唛头封装方式" prop="ShippingMarkEncapsulationMethod" align="center" min-width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkEncapsulationMethod }}</span>
        </template>
      </el-table-column>
      <el-table-column label="纸张类型" prop="ShippingMarkPaper" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPaper }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唛头纸选项" prop="ShippingMarkPaperOption" align="center" min-width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPaperOption }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唛头纸尺寸" prop="ShippingMarkPaperSize" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPaperSize }}</span>
        </template>
      </el-table-column>
      <el-table-column label="打印方向" prop="PrintDirection" align="center" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.PrintDirection }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唛头打印系统" prop="ShippingMarkPrintSystem" align="center" min-width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPrintSystem }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唛头包装方式" prop="ShippingMarkPackagingMethod" align="center" min-width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ShippingMarkPackagingMethod }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否到站/港" prop="IsArrivalStationOrPort" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsArrivalStationOrPort }}</span>
        </template>
      </el-table-column>
      <el-table-column label="刷字" prop="Brush" align="center" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Brush }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否出口" prop="IsExport" align="center" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.IsExport }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参数校验" prop="ParameterCheckStr" align="center" min-width="120" show-overflow-tooltip />
      <el-table-column label="备注" prop="Remark" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.Remark }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="CUser" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="CTime" align="center" min-width="150" show-overflow-tooltip :formatter="formatDateTime" />
      <el-table-column label="修改人" prop="MUser" align="center" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.MUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" prop="MTime" align="center" min-width="150" show-overflow-tooltip :formatter="formatDateTime" />

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />

    <!-- 表单对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="form" :model="temp" :rules="rules" label-position="right" label-width="120px">
        <el-form-item label="发货单位" prop="DeliveryCompany">
          <el-input v-model="temp.DeliveryCompany" placeholder="请输入发货单位" />
        </el-form-item>
        <el-form-item label="打印模板" prop="PrintTemplateId">
          <el-select
            v-model="temp.PrintTemplateId"
            placeholder="请选择打印模板"
            filterable
            clearable
            style="width: 100%"
            @change="handleTemplateChange"
          >
            <el-option
              v-for="template in printTemplateList"
              :key="template.Id"
              :label="template.TemplateName"
              :value="template.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板名称" prop="TemplateName">
          <el-input v-model="temp.TemplateName" placeholder="模板名称（自动填充）" readonly />
        </el-form-item>
        <el-form-item label="唛头打印方式" prop="ShippingMarkPrintMethod">
          <el-input v-model="temp.ShippingMarkPrintMethod" placeholder="请输入唛头打印方式" />
        </el-form-item>
        <el-form-item label="唛头封装方式" prop="ShippingMarkEncapsulationMethod">
          <el-input v-model="temp.ShippingMarkEncapsulationMethod" placeholder="请输入唛头封装方式" />
        </el-form-item>
        <el-form-item label="纸张类型" prop="ShippingMarkPaper">
          <el-input v-model="temp.ShippingMarkPaper" placeholder="请输入唛头纸" />
        </el-form-item>
        <el-form-item label="唛头纸选项" prop="ShippingMarkPaperOption">
          <el-input v-model="temp.ShippingMarkPaperOption" placeholder="请输入唛头纸选项" />
        </el-form-item>
        <el-form-item label="唛头纸尺寸" prop="ShippingMarkPaperSize">
          <el-input v-model="temp.ShippingMarkPaperSize" placeholder="请输入唛头纸尺寸" />
        </el-form-item>
        <el-form-item label="打印方向" prop="PrintDirection">
          <el-input v-model="temp.PrintDirection" placeholder="请输入打印方向" />
        </el-form-item>
        <el-form-item label="唛头打印系统" prop="ShippingMarkPrintSystem">
          <el-input v-model="temp.ShippingMarkPrintSystem" placeholder="请输入唛头打印系统" />
        </el-form-item>
        <el-form-item label="唛头包装方式" prop="ShippingMarkPackagingMethod">
          <el-input v-model="temp.ShippingMarkPackagingMethod" placeholder="请输入唛头包装方式" />
        </el-form-item>
        <el-form-item label="是否到站/港" prop="IsArrivalStationOrPort">
          <el-input v-model="temp.IsArrivalStationOrPort" placeholder="请输入是否到站/港" />
        </el-form-item>
        <el-form-item label="刷字" prop="Brush">
          <el-input v-model="temp.Brush" placeholder="请输入刷字" />
        </el-form-item>
        <el-form-item label="是否出口" prop="IsExport">
          <el-input v-model="temp.IsExport" placeholder="请输入是否出口" />
        </el-form-item>
        <el-form-item label="参数校验" prop="ParameterCheckStr">
          <el-input v-model="temp.ParameterCheckStr" placeholder="请输入参数校验" />
        </el-form-item>
        <el-form-item label="备注" prop="Remark">
          <el-input v-model="temp.Remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('Common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSave">{{ $t('Common.save') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves/waves'
import permission from '@/directive/permission/index.js'
import Pagination from '@/components/Pagination/index'
import { formatDateTime } from '@/utils'
import { exportToExcel } from '@/utils/excel-export'
import { parseExcelFile } from '@/utils/excel-import'
import {
  fetchPageList,
  updateOrInsert,
  batchDelete,
  exportExcelFile,
  exportExcelModel,
  importExcelData
} from '@/api/MD/MD_ShippingMarkRule'
import { getEnabledList } from '@/api/MD/MD_PrintTemplate'

export default {
  name: 'MD.MD_ShippingMarkRule',
  components: {
    Pagination
  },
  directives: {
    waves,
    permission
  },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        PageNumber: 1,
        PageSize: 10,
        keyword: '',
        PrintDirection: '',
        ParameterCheckStr: '',
        SortField: 'CTime',
        SortOrder: 'descending'
      },
      multipleSelection: [],
      temp: {
        Id: '',
        DeliveryCompany: '',
        PrintTemplateId: '',
        TemplateName: '',
        ShippingMarkPrintMethod: '',
        ShippingMarkEncapsulationMethod: '',
        ShippingMarkPaper: '',
        ShippingMarkPaperOption: '',
        ShippingMarkPaperSize: '',
        PrintDirection: '',
        ShippingMarkPrintSystem: '',
        ShippingMarkPackagingMethod: '',
        IsArrivalStationOrPort: '',
        Brush: '',
        IsExport: '',
        ParameterCheckStr: '',
        Remark: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '新增'
      },
      rules: {
        DeliveryCompany: [{ required: true, message: '发货单位不能为空', trigger: 'blur' }]
      },
      isProcessing: false,
      printTemplateList: [] // 打印模板列表
    }
  },
  created() {
    this.getList()
    this.getPrintTemplateList()
  },
  methods: {
    formatDateTime,
    // 获取打印模板列表
    getPrintTemplateList() {
      getEnabledList().then(response => {
        if (response.Code === 2000) {
          // 检查返回的数据结构
          if (Array.isArray(response.Data)) {
            this.printTemplateList = response.Data
          } else if (response.Data && response.Data.rows) {
            this.printTemplateList = response.Data.rows
          } else if (response.Data && response.Data.Items) {
            this.printTemplateList = response.Data.Items
          } else {
            this.printTemplateList = response.Data || []
          }
        } else {
          this.$notify({
            title: '错误',
            message: response.Message || '获取打印模板列表失败',
            type: 'error',
            duration: 2000
          })
        }
      }).catch(() => {
        this.$notify({
          title: '错误',
          message: '获取打印模板列表失败',
          type: 'error',
          duration: 2000
        })
      })
    },
    // 处理模板选择变化
    handleTemplateChange(templateId) {
      if (templateId) {
        const selectedTemplate = this.printTemplateList.find(template => template.Id === templateId)
        if (selectedTemplate) {
          this.temp.TemplateName = selectedTemplate.TemplateName
        }
      } else {
        this.temp.TemplateName = ''
      }
    },
    getList() {
      this.listLoading = true
      fetchPageList(this.listQuery).then(response => {
        if (response.Code === 2000) {
          // 检查返回的数据结构
          if (Array.isArray(response.Data)) {
            // 如果Data直接是数组，则直接使用
            this.list = response.Data
            this.total = response.Data.length
          } else if (response.Data && response.Data.items) {
            // 如果是分页结构 {items: [], total: number}
            this.list = response.Data.items
            this.total = response.Data.total
          } else if (response.Data && response.Data.Items) {
            // 如果是分页结构 {Items: [], TotalCount: number}
            this.list = response.Data.Items
            this.total = response.Data.TotalCount
          } else {
            // 其他情况，尝试直接使用Data
            this.list = response.Data || []
            this.total = this.list.length
          }
        } else {
          this.$notify({
            title: '错误',
            message: response.Message || '获取数据失败',
            type: 'error',
            duration: 2000
          })
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.PageNumber = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop) {
        this.listQuery.SortField = prop
        this.listQuery.SortOrder = order
      }
      this.getList()
    },
    resetTemp() {
      this.temp = {
        Id: '',
        DeliveryCompany: '',
        PrintTemplateId: '',
        TemplateName: '',
        ShippingMarkPrintMethod: '',
        ShippingMarkEncapsulationMethod: '',
        ShippingMarkPaper: '',
        ShippingMarkPaperOption: '',
        ShippingMarkPaperSize: '',
        PrintDirection: '',
        ShippingMarkPrintSystem: '',
        ShippingMarkPackagingMethod: '',
        IsArrivalStationOrPort: '',
        Brush: '',
        IsExport: '',
        ParameterCheckStr: '',
        Remark: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleUpdate(row) {
      // 如果没有传入row参数，则从选中的行中获取
      const selectedRow = this.multipleSelection[0]
      if (!selectedRow) {
        this.$message.warning('请选择要编辑的数据')
        return
      }
      this.temp = Object.assign({}, selectedRow)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    handleSave() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateOrInsert(tempData).then(response => {
            if (response.Code === 2000) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: this.dialogStatus === 'create' ? '创建成功' : '更新成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$notify({
                title: '错误',
                message: response.Message || (this.dialogStatus === 'create' ? '创建失败' : '更新失败'),
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的记录')
        return
      }

      this.$confirm('确认删除选中记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.Id)
        batchDelete(ids).then(response => {
          if (response.Code === 2000 && response.Data) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '删除失败',
              type: 'error',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        // 取消删除
      })
    },
    handleExport() {
      this.isProcessing = true
      exportExcelFile(this.listQuery).then(res => {
        exportToExcel(res, '唛头规则')
        this.isProcessing = false
      }).catch(error => {
        console.error('导出失败:', error)
        this.$notify({
          title: '错误',
          message: '导出失败',
          type: 'error',
          duration: 2000
        })
        this.isProcessing = false
      })
    },
    handleExportTemplate() {
      this.isProcessing = true
      exportExcelModel().then(res => {
        exportToExcel(res, '唛头规则导入模板')
        this.isProcessing = false
      }).catch(error => {
        console.error('导出模板失败:', error)
        this.$notify({
          title: '错误',
          message: '导出模板失败',
          type: 'error',
          duration: 2000
        })
        this.isProcessing = false
      })
    },
    beforeUpload(file) {
      this.isProcessing = true

      // 解析Excel文件
      parseExcelFile(file)
        .then(data => {
          // 定义Excel表头到实体属性的映射
          const fieldMapping = {
            '发货单位': 'DeliveryCompany',
            '模板名称': 'TemplateName',
            '唛头打印方式': 'ShippingMarkPrintMethod',
            '唛头封装方式': 'ShippingMarkEncapsulationMethod',
            '打印方向': 'PrintDirection',
            '纸张类型': 'ShippingMarkPaper',
            '唛头纸选项': 'ShippingMarkPaperOption',
            '纸张尺寸': 'ShippingMarkPaperSize',
            '唛头打印系统': 'ShippingMarkPrintSystem',
            '唛头包装方式': 'ShippingMarkPackagingMethod',
            '是否到站/港': 'IsArrivalStationOrPort',
            '刷字': 'Brush',
            '是否出口': 'IsExport',
            '备注': 'Remark'
          }

          // 将解析后的数据映射到实体
          const entities = data.map(row => {
            const entity = {
              Id: '', // 新增时ID为空
              DeliveryCompany: '',
              TemplateName: '',
              ShippingMarkPrintMethod: '',
              ShippingMarkEncapsulationMethod: '',
              PrintDirection: '',
              ShippingMarkPaper: '',
              ShippingMarkPaperOption: '',
              ShippingMarkPaperSize: '',
              ShippingMarkPrintSystem: '',
              ShippingMarkPackagingMethod: '',
              IsArrivalStationOrPort: '',
              Brush: '',
              IsExport: '',
              Remark: ''
            }

            // 遍历Excel数据，根据映射关系设置实体属性
            Object.keys(row).forEach(excelHeader => {
              const entityProperty = fieldMapping[excelHeader] || excelHeader
              // eslint-disable-next-line no-prototype-builtins
              if (row[excelHeader] !== undefined && entity.hasOwnProperty(entityProperty)) {
                entity[entityProperty] = row[excelHeader] || ''
              }
            })

            return entity
          })

          // 发送解析后的数据到后端
          return importExcelData(entities)
        })
        .then(response => {
          if (response.Code === 2000) {
            this.$notify({
              title: '成功',
              message: '导入成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$notify({
              title: '错误',
              message: response.Message || '导入失败',
              type: 'error',
              duration: 2000
            })
          }
          this.isProcessing = false
        })
        .catch(error => {
          console.error('导入失败:', error)
          this.$notify({
            title: '错误',
            message: '导入失败',
            type: 'error',
            duration: 2000
          })
          this.isProcessing = false
        })

      return false // 阻止默认上传行为
    }
  }
}
</script>
