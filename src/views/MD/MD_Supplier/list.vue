<template>
  <div v-loading="isProcessing" class="app-container" :element-loading-text="$t('Common.slowRunningWarning')">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        size="small"
        :placeholder="$t('Common.keyword')"
        style="width: 220px"
        @keydown.enter.native="handleFilter"
      />
      <el-button size="small" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter" />
      <hr>
      <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Permission.SyncSupplier' }"
        class="filter-item"
        size="small"
        type="success"
        icon="el-icon-download"
        @click="handleSyncSupplier"
      >{{ $t('ui.MD.MD_Supplier.SyncSupplier') }}
      </el-button>

      <!-- <el-button
        v-waves
        v-permission="{ name: 'MD.MD_Region.Export' }"
        class="filter-item"  size="small"
        type="primary"
        icon="el-icon-document"
        @click="handleExport"
      >{{ $t('Common.export') }}</el-button>-->
    </div>

    <el-table
      ref="Table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      :header-cell-style="{background:'#eef1f6',color:'#606266'}"
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column :label="$t('ui.MD.MD_Supplier.SupplierCode')" prop="SupplierCode" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierCode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.SupplierName')" prop="SupplierName" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.SupplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.Email')" prop="Email" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Email }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.Address')" prop="Address" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Address }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.City')" prop="City" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.City }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.ContactPerson')" prop="ContactPerson" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.ContactPerson }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.Fax')" prop="Fax" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Fax }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ui.MD.MD_Supplier.Phone')" prop="Phone" align="center" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.Phone }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.PageNumber"
      :limit.sync="listQuery.PageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '../../../directive/waves/waves'
import adaptive from '../../../directive/el-table/adaptive'
import Pagination from '../../../components/Pagination/index'
import {
  exportToExcel
} from '@/utils/excel-export'
import {
  fetchList,
  SyncSupplier
} from '../../../api/MD/MD_Supplier'
import {
  fetchList as fetchWarehouse
} from '../../../api/MD/MD_Warehouse'
// import permission from '../../../directive/permission/permission'
// 当然你也可以为了方便使用，将它注册到全局
import permission from '@/directive/permission/index.js'; // 权限判断指令

export default {
  name: 'MD.MD_Region',
  components: {
    Pagination
  },
  directives: {
    waves,
    adaptive,
    permission
  },
  data() {
    return {
      isProcessing: false,
      listLoading: false,
      list: [],
      total: 0,
      title: '',
      dialogVisible: false,
      listQuery: {
        keyword: '',
        PageNumber: 1,
        PageSize: 10
      }

    }
  },

  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取表格数据
      this.listLoading = true;
      fetchList(this.listQuery)
        .then(response => {
          this.list = response.Data.items;
          this.total = response.Data.total;
          this.listLoading = false
        })
    },
    handleFilter() { // 搜索功能
      this.listQuery.PageNumber = 1;
      this.listQuery.PageSize = 10;
      this.getList()
    },
    handleSyncSupplier() {
      this.isProcessing = true;
      SyncSupplier().then(res => {
        console.log('同步供应商数据');
        this.isProcessing = false
      }).catch(err => {
        console.log(err);
        this.isProcessing = false
      })
    },
    handleExport() {
      exportExcelFile({
        Keyword: this.listQuery.keyword
      }).then(res =>
        exportToExcel(res.data, '供应商主数据')
      )
    },

    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (order != undefined) {
        this.listQuery.sort = prop + ' ' + order;
        this.handleFilter()
      }
    }
  }
}
</script>
