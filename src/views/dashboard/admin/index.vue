<template>
  <div class="dashboard-editor-container">
    <!-- <github-corner class="github-corner" /> -->

    <!-- <panel-group @handleSetLineChartData="handleSetLineChartData" /> -->

    <!-- <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <line-chart :chart-data="lineChartData" />
    </el-row>-->
  </div>
</template>

<script>
import { fetchUnreadMessageList } from '../../../api/Sys/Sys_UserMessage'

export default {
  name: 'DashboardAdmin',
  components: {
    // GithubCorner,
    // PanelGroup,
    // LineChart,
    // Raddar<PERSON>hart,
    // PieChart,
    // BarChart,
    // TransactionTable,
    // TodoList,
    // BoxCard
  },
  data() {
    return {
      unReadList: [],
      requestRemoteDataTimer: null, // 请求远程数据定时器
      requestRemoteDataInterval: 1000 * 1 * 10 // 请求远程数据间隔设定：十分钟
    }
  },
  created() {
    // this.startTimer()
  },
  methods: {
    clearTimer() {
      if (this.requestRemoteDataTimer) {
        clearInterval(this.requestRemoteDataTimer)
      }
      this.requestRemoteDataTimer = null
    },
    startTimer() {
      this.clearTimer();
      this.requestRemoteDataTimer = setInterval(() => {
        console.log('请求服务器数据...' + new Date());
        this.getList()
      }, this.requestRemoteDataInterval)
    },
    getList() {
      fetchUnreadMessageList()
        .then(result => {
          console.log(result.Data);
          result.Data.forEach((val, index, arr) => {
            this.$notify.info({
              position: 'bottom-right',
              duration: 5 * 1000,
              title: val.MessageTitle,
              message: val.MessageContent
            });
          })
        })
        .catch(err => {
          console.log(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: #fff;
  position: relative;

  .github-corner {
    position: absolute;
    top: 0;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}
</style>
