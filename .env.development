# just a flag
ENV = 'development'

# base api 客户：https://wmstest3.faradynemotors.cn:9528/FLDAPI/api 开发：http://192.168.1.147:8088/FLDAPI/api 本机:http://192.168.2.88:64220/api
// VUE_APP_BASE_API = 'http://192.168.1.33:64220/api'
VUE_APP_BASE_API ='/api'




# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true

