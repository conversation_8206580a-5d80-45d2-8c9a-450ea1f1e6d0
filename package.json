{"name": "vue-element-admin", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --open", "build:prod": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^0.24.0", "clipboard": "2.0.4", "codemirror": "^5.65.11", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^5.4.1", "element-ui": "2.12.0", "file-saver": "2.0.1", "fuse.js": "3.4.4", "jquery": "^3.7.1", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "jsonlint": "1.6.3", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "showdown": "^1.9.1", "sortablejs": "1.8.4", "vue": "^2.7.14", "vue-count-to": "1.0.13", "vue-i18n": "7.3.2", "vue-infinite-loading": "^2.4.5", "vue-json-viewer": "^2.2.22", "vue-plugin-hiprint": "^0.0.60", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "^0.17.5"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-unit-jest": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.8.8", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^27.5.1", "browserslist": "^4.21.4", "caniuse-lite": "^1.0.30001441", "chalk": "2.4.2", "chokidar": "^3.5.3", "connect": "3.6.6", "eslint": "6.8.0", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "^12.5.0", "mockjs": "1.0.1-beta3", "plop": "^2.7.6", "runjs": "^4.3.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.15.0", "svg-sprite-loader": "4.1.3", "svgo": "^2.8.0", "vue-template-compiler": "2.7.14"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}