{"formatOnSave": true, "lintOnSave": true, "defaultFormatter": "prettier", "formatters": {"vue": "prettier", "javascript": "prettier", "typescript": "prettier", "scss": "prettier", "css": "prettier", "html": "prettier", "json": "prettier", "markdown": "prettier"}, "ignore": ["node_modules/**", "dist/**", "build/**", ".git/**", "*.log"], "search": {"exclude": ["node_modules", "dist", "build", ".git"]}, "editor": {"tabSize": 2, "insertSpaces": true, "detectIndentation": true, "wordWrap": "off", "rulers": [100]}, "vue": {"useVolar": true, "validation": {"template": true, "style": true, "script": true}}, "codeActions": {"eslintRules": {"vue/no-unused-vars": "warn", "vue/no-unused-components": "warn", "vue/multi-word-component-names": "off", "vue/no-mutating-props": "warn"}, "stylelintRules": {"no-descending-specificity": null}}, "paths": {"@": "${workspaceFolder}/src"}, "recommendations": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "vue.volar", "formulahendry.auto-rename-tag", "streetsidesoftware.code-spell-checker", "wix.vscode-import-cost"]}, "quickActions": {"vue-component": {"prefix": "vue", "description": "创建Vue组件模板", "body": ["<template>", "  <div v-loading=\"isProcessing\" class=\"app-container\" :element-loading-text=\"$t('Common.slowRunningWarning')\">", "    <div class=\"filter-container\">", "      <el-input", "        v-model=\"listQuery.keyword\"", "        size=\"small\"", "        :placeholder=\"$t('Common.keyword')\"", "        style=\"width: 220px\"", "        class=\"filter-item\"", "        clearable", "        @keyup.enter.native=\"handleFilter\"", "      />", "      <el-button v-waves size=\"small\" class=\"filter-item\" type=\"primary\" icon=\"el-icon-search\" @click=\"handleFilter\" />", "      <hr>", "      <el-button v-waves class=\"filter-item\" type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleCreate\">", "        {{ $t('Common.add') }}", "      </el-button>", "      <el-button", "        v-waves", "        class=\"filter-item\"", "        type=\"primary\"", "        icon=\"el-icon-edit\"", "        size=\"small\"", "        :disabled=\"selective\"", "        @click=\"handleEdit\"", "      >{{ $t('Common.edit') }}</el-button>", "      <el-button", "        v-waves", "        class=\"filter-item\"", "        type=\"danger\"", "        icon=\"el-icon-delete\"", "        size=\"small\"", "        :disabled=\"deletable\"", "        @click=\"handleDelete\"", "      >{{ $t('Common.delete') }}</el-button>", "    </div>", "", "    <el-table", "      v-loading=\"listLoading\"", "      :data=\"list\"", "      border", "      fit", "      size=\"mini\"", "      :header-cell-style=\"{background:'#eef1f6',color:'#606266'}\"", "      highlight-current-row", "      @sort-change=\"sortChange\"", "      @selection-change=\"handleSelectionChange\"", "    >", "      <el-table-column :label=\"$t('Common.select')\" type=\"selection\" align=\"center\" width=\"40\" fixed />", "      <el-table-column label=\"$1\" prop=\"$2\" align=\"center\" width=\"150\" show-overflow-tooltip>", "        <template slot-scope=\"scope\">", "          <span>{{ scope.row.$2 }}</span>", "        </template>", "      </el-table-column>", "      $0", "    </el-table>", "", "    <pagination", "      v-show=\"total>0\"", "      :total=\"total\"", "      :page.sync=\"listQuery.PageNumber\"", "      :limit.sync=\"listQuery.PageSize\"", "      @pagination=\"getList\"", "    />", "  </div>", "</template>", "", "<script>", "import { fetchList } from '@/api/${3:module}/${4:entity}'", "import waves from '@/directive/waves' // waves directive 特效", "import Pagination from '@/components/Pagination' // 分页", "", "export default {", "  name: '${TM_FILENAME_BASE}',", "  components: { Pagination },", "  directives: { waves },", "  data() {", "    return {", "      list: [],", "      total: 0,", "      listLoading: true,", "      isProcessing: false,", "      selective: true,", "      deletable: true,", "      listQuery: {", "        PageNumber: 1,", "        PageSize: 20,", "        keyword: ''", "      },", "      multipleSelection: []", "    }", "  },", "  created() {", "    this.getList()", "  },", "  methods: {", "    getList() {", "      this.listLoading = true", "      fetchList(this.listQuery).then(response => {", "        const { Data } = response", "        if (Data) {", "          this.list = Data.items", "          this.total = Data.total", "        } else {", "          this.list = []", "          this.total = 0", "        }", "        this.listLoading = false", "      })", "    },", "    handleFilter() {", "      this.listQuery.PageNumber = 1", "      this.getList()", "    },", "    handleSelectionChange(val) {", "      this.multipleSelection = val", "      this.selective = !(this.multipleSelection.length === 1)", "      this.deletable = this.multipleSelection.length === 0", "    },", "    sortChange(data) {", "      const { prop, order } = data", "      // 处理排序逻辑", "    }", "  }", "}", "</script>", ""]}, "vue-md-list": {"prefix": "mdlist", "description": "创建MD模块列表页面", "body": ["<template>", "  <div v-loading=\"isProcessing\" class=\"app-container\" :element-loading-text=\"$t('Common.slowRunningWarning')\">", "    <div class=\"filter-container\">", "      <el-input", "        v-model=\"listQuery.keyword\"", "        size=\"small\"", "        :placeholder=\"$t('Common.keyword')\"", "        style=\"width: 220px\"", "        class=\"filter-item\"", "        clearable", "        @keyup.enter.native=\"handleFilter\"", "      />", "      <el-button v-waves size=\"small\" class=\"filter-item\" type=\"primary\" icon=\"el-icon-search\" @click=\"handleFilter\" />", "      <hr>", "      <el-button v-waves class=\"filter-item\" type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"handleCreate\">", "        {{ $t('Common.add') }}", "      </el-button>", "      <el-button", "        v-waves", "        class=\"filter-item\"", "        type=\"primary\"", "        icon=\"el-icon-edit\"", "        size=\"small\"", "        :disabled=\"selective\"", "        @click=\"handleEdit\"", "      >{{ $t('Common.edit') }}</el-button>", "      <el-button", "        v-waves", "        class=\"filter-item\"", "        type=\"danger\"", "        icon=\"el-icon-delete\"", "        size=\"small\"", "        :disabled=\"deletable\"", "        @click=\"handleDelete\"", "      >{{ $t('Common.delete') }}</el-button>", "      <el-button", "        v-waves", "        class=\"filter-item\"", "        type=\"success\"", "        icon=\"el-icon-download\"", "        size=\"small\"", "        @click=\"handleExportExcel\"", "      >{{ $t('Common.export') }}</el-button>", "      <el-upload", "        class=\"filter-item\"", "        :action=\"'#'\"", "        :show-file-list=\"false\"", "        :before-upload=\"beforeUpload\"", "      >", "        <el-button v-waves type=\"primary\" icon=\"el-icon-upload\" size=\"small\">{{ $t('Common.import') }}</el-button>", "      </el-upload>", "      <el-button", "        v-waves", "        class=\"filter-item\"", "        type=\"success\"", "        icon=\"el-icon-download\"", "        size=\"small\"", "        @click=\"handleExportTemplate\"", "      >导出模板</el-button>", "    </div>", "", "    <el-table", "      v-loading=\"listLoading\"", "      :data=\"list\"", "      border", "      fit", "      size=\"mini\"", "      :header-cell-style=\"{background:'#eef1f6',color:'#606266'}\"", "      highlight-current-row", "      @sort-change=\"sortChange\"", "      @selection-change=\"handleSelectionChange\"", "    >", "      <el-table-column :label=\"$t('Common.select')\" type=\"selection\" align=\"center\" width=\"40\" fixed />", "      <el-table-column type=\"index\" align=\"center\" width=\"50\" label=\"行号\" />", "      $0", "    </el-table>", "", "    <pagination", "      v-show=\"total>0\"", "      :total=\"total\"", "      :page.sync=\"listQuery.PageNumber\"", "      :limit.sync=\"listQuery.PageSize\"", "      @pagination=\"getList\"", "    />", "", "    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">", "      <el-form ref=\"form\" :rules=\"rules\" :model=\"temp\" label-position=\"right\" label-width=\"120px\">", "        <!-- 表单内容 -->", "      </el-form>", "      <div slot=\"footer\" class=\"dialog-footer\">", "        <el-button @click=\"dialogFormVisible = false\">{{ $t('Common.cancel') }}</el-button>", "        <el-button v-waves type=\"primary\" @click=\"handleConfirm\">{{ $t('Common.confirm') }}</el-button>", "      </div>", "    </el-dialog>", "  </div>", "</template>", "", "<script>", "import {", "  fetchList,", "  add,", "  update,", "  batchDelete,", "  exportToExcelFile,", "  exportToExcelModel,", "  importExcelToData", "} from '@/api/MD/${1:entity}';", "import waves from '@/directive/waves';", "import Pagination from '@/components/Pagination';", "import { exportToExcel } from '@/utils/excel-export';", "import { parseExcelFile } from '@/utils/excel-import';", "", "export default {", "  name: 'MD_${1:entity}',", "  components: { Pagination },", "  directives: { waves },", "  data() {", "    return {", "      list: [],", "      total: 0,", "      listLoading: true,", "      isProcessing: false,", "      selective: true,", "      deletable: true,", "      listQuery: {", "        PageNumber: 1,", "        PageSize: 20,", "        keyword: ''", "      },", "      temp: {},", "      dialogFormVisible: false,", "      dialogStatus: '',", "      textMap: {", "        update: '编辑',", "        create: '新增'", "      },", "      multipleSelection: [],", "      rules: {}", "    };", "  },", "  created() {", "    this.getList();", "  },", "  methods: {", "    getList() {", "      this.listLoading = true;", "      fetchList(this.listQuery).then(response => {", "        console.log(response);", "        const { Data } = response;", "        if (Data) {", "          this.list = Data.items;", "          this.total = Data.total;", "        } else {", "          this.list = [];", "          this.total = 0;", "        }", "        this.listLoading = false;", "      });", "    },", "    handleFilter() {", "      this.listQuery.PageNumber = 1;", "      this.getList();", "    },", "    handleSelectionChange(val) {", "      this.multipleSelection = val;", "      this.selective = !(this.multipleSelection.length === 1);", "      this.deletable = this.multipleSelection.length === 0;", "    },", "    resetTemp() {", "      this.temp = {};", "    },", "    handleCreate() {", "      this.resetTemp();", "      this.dialogStatus = 'create';", "      this.dialogFormVisible = true;", "      this.$nextTick(() => {", "        this.$refs['form'].clearValidate();", "      });", "    },", "    handleEdit() {", "      this.resetTemp();", "      const tempData = this.multipleSelection[0];", "      this.temp = Object.assign({}, tempData);", "      this.dialogStatus = 'update';", "      this.dialogFormVisible = true;", "      this.$nextTick(() => {", "        this.$refs['form'].clearValidate();", "      });", "    },", "    handleConfirm() {", "      this.$refs['form'].validate(valid => {", "        if (valid) {", "          if (this.dialogStatus === 'create') {", "            this.isProcessing = true;", "            add(this.temp)", "              .then(response => {", "                if (response.Code === 2000) {", "                  this.$notify({", "                    title: '成功',", "                    message: '创建成功',", "                    type: 'success',", "                    duration: 2000", "                  });", "                  this.dialogFormVisible = false;", "                  this.getList();", "                } else {", "                  this.$notify({", "                    title: '失败',", "                    message: response.Message || '创建失败',", "                    type: 'error',", "                    duration: 2000", "                  });", "                }", "                this.isProcessing = false;", "              })", "              .catch(() => {", "                this.isProcessing = false;", "              });", "          } else {", "            this.isProcessing = true;", "            update(this.temp)", "              .then(response => {", "                if (response.Code === 2000) {", "                  this.$notify({", "                    title: '成功',", "                    message: '更新成功',", "                    type: 'success',", "                    duration: 2000", "                  });", "                  this.dialogFormVisible = false;", "                  this.getList();", "                } else {", "                  this.$notify({", "                    title: '失败',", "                    message: response.Message || '更新失败',", "                    type: 'error',", "                    duration: 2000", "                  });", "                }", "                this.isProcessing = false;", "              })", "              .catch(() => {", "                this.isProcessing = false;", "              });", "          }", "        }", "      });", "    },", "    handleDelete() {", "      this.$confirm('确认删除所选记录?', '警告', {", "        confirmButtonText: '确定',", "        cancelButtonText: '取消',", "        type: 'warning'", "      })", "        .then(() => {", "          this.isProcessing = true;", "          const ids = this.multipleSelection.map(item => item.Id);", "          batchDelete(ids)", "            .then(response => {", "              if (response.Code === 2000) {", "                this.$notify({", "                  title: '成功',", "                  message: '删除成功',", "                  type: 'success',", "                  duration: 2000", "                });", "                this.getList();", "              } else {", "                this.$notify({", "                  title: '失败',", "                  message: response.Message || '删除失败',", "                  type: 'error',", "                  duration: 2000", "                });", "              }", "              this.isProcessing = false;", "            })", "            .catch(() => {", "              this.isProcessing = false;", "            });", "        })", "        .catch(() => {", "          this.$message({", "            type: 'info',", "            message: '已取消删除'", "          });", "        });", "    },", "    handleExportExcel() {", "      this.isProcessing = true;", "      exportToExcelFile({ keyword: this.listQuery.keyword })", "        .then(response => {", "          exportToExcel(response, '${1:entity}导出')", "          this.isProcessing = false", "        })", "        .catch(() => {", "          this.isProcessing = false;", "        });", "    },", "    beforeUpload(file) {", "      this.isProcessing = true", "", "      // 定义字段映射关系，Excel表头到实体属性的映射", "      const fieldMapping = {", "        // 示例: '表头名称': '属性名'", "      }", "", "      // 解析Excel文件，表头在第一行，所以索引是0", "      parseExcelFile(file, 0)", "        .then(data => {", "          // 将解析后的数据映射到实体", "          const entities = data.map(row => {", "            const entity = {}", "", "            // 遍历字段映射关系，将Excel数据映射到实体属性", "            Object.keys(fieldMapping).forEach(excelHeader => {", "              const entityProperty = fieldMapping[excelHeader]", "              if (row[excelHeader] !== undefined && row[excelHeader] !== null) {", "                entity[entityProperty] = row[excelHeader]", "              }", "            })", "", "            return entity", "          }).filter(entity => Object.keys(entity).length > 0) // 过滤掉空行", "", "          // 发送解析后的数据到后端", "          return importExcelToData(entities)", "        })", "        .then(response => {", "          if (response.Code === 2000) {", "            this.$notify({", "              title: '成功',", "              message: '导入成功',", "              type: 'success',", "              duration: 2000", "            })", "            this.getList()", "          } else {", "            this.$notify({", "              title: '错误',", "              message: response.Message || '导入失败',", "              type: 'error',", "              duration: 2000", "            })", "          }", "          this.isProcessing = false", "        })", "        .catch(error => {", "          console.error('导入失败:', error)", "          this.$notify({", "            title: '错误',", "            message: '解析Excel文件失败，请检查文件格式',", "            type: 'error',", "            duration: 2000", "          })", "          this.isProcessing = false", "        })", "", "      return false", "    },", "    handleExportTemplate() {", "      this.isProcessing = true", "      exportToExcelModel().then(res => {", "        exportToExcel(res, '${1:entity}模板')", "        this.isProcessing = false", "      }).catch((error) => {", "        console.error('Export template error:', error)", "        this.isProcessing = false", "      })", "    },", "    sortChange(data) {", "      const { prop, order } = data;", "      // 处理排序逻辑", "    }", "  }", "};", "</script>"]}, "md-api-full": {"prefix": "mdapi", "description": "创建完整的MD模块API文件，包含导入导出功能", "body": ["import request from '@/utils/request'", "", "// 获取分页列表", "export function fetchList(query) {", "  return request({", "    url: '/MD/${1:entity}/GetPageList',", "    method: 'get',", "    params: query", "  })", "}", "", "// 获取详情", "export function getEntity(id) {", "  return request({", "    url: '/MD/${1:entity}/GetEntity',", "    method: 'get',", "    params: { ID: id }", "  })", "}", "", "// 添加", "export function add(entity) {", "  return request({", "    url: '/MD/${1:entity}/Add',", "    method: 'post',", "    data: entity", "  })", "}", "", "// 更新", "export function update(entity) {", "  return request({", "    url: '/MD/${1:entity}/Update',", "    method: 'post',", "    data: entity", "  })", "}", "", "// 删除", "export function batchDelete(ids) {", "  return request({", "    url: '/MD/${1:entity}/Delete',", "    method: 'delete',", "    data: ids", "  })", "}", "", "// 导出", "export function exportToExcelFile(query) {", "  return request({", "    url: '/MD/${1:entity}/ExportToExcelFile',", "    method: 'get',", "    params: query,", "    responseType: 'blob'", "  })", "}", "", "// 导入数据", "export function importExcelToData(data) {", "  return request({", "    url: '/MD/${1:entity}/ImportExcelToData',", "    method: 'post',", "    data: data", "  })", "}", "", "// 导出模板", "export function exportToExcelModel() {", "  return request({", "    url: '/MD/${1:entity}/ExportToExcelModel',", "    method: 'get',", "    responseType: 'blob'", "  })", "}"]}, "el-table-column": {"prefix": "elcol", "description": "创建Element表格列", "body": ["<el-table-column label=\"$1\" prop=\"$2\" align=\"center\" width=\"$3\" show-overflow-tooltip>", "  <template slot-scope=\"scope\">", "    <span>{{ scope.row.$2 }}</span>", "  </template>", "</el-table-column>"]}, "el-form-item": {"prefix": "elform", "description": "创建Element表单项", "body": ["<el-form-item label=\"$1\" prop=\"$2\">", "  <el-input v-model=\"temp.$2\" />", "</el-form-item>"]}, "excel-import-mapping": {"prefix": "excelmap", "description": "创建Excel导入映射", "body": ["// 定义字段映射关系，Excel表头到实体属性的映射", "const fieldMapping = {", "  '$1': '$2',", "  '$3': '$4',$0", "}"]}}}